!macro customInstall
  DetailPrint "Register thinkacademy URI Handler"
  DeleteRegKey HKCR "thinkacademy"
  WriteRegStr HKCR "thinkacademy" "" "URL:thinkacademy"
  WriteRegStr HKCR "thinkacademy" "URL Protocol" ""
  WriteRegStr HKCR "thinkacademy\shell" "" ""
  WriteRegStr HKCR "thinkacademy\shell\Open" "" ""
  WriteRegStr HKCR "thinkacademy\shell\Open\command" "" "$INSTDIR\${APP_EXECUTABLE_FILENAME} %1"
!macroend