<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <title>ThinkAcademy ClassRoom</title>
    <style>
      html,
      body {
        height: 100%;
        margin: 0;
        padding: 0;
      }
    </style>
    <link
      rel="stylesheet"
      href="./libs/ui/neosui.min.css?t=<%= htmlWebpackPlugin.options.timestamp %>"
      type="text/css"
    />
    <!-- 内容云试题SDK {-->
    <link
      rel="stylesheet"
      href="./libs/talqs/index.min.css?t=<%= htmlWebpackPlugin.options.timestamp %>"
      type="text/css"
    />
    <script src="./libs/talqs/jquery.min.js"></script>
    <script src="./libs/talqs/index.min.js?t=<%= htmlWebpackPlugin.options.timestamp %>"></script>
    <!-- 内容云试题SDK }-->
    <script type="text/x-mathjax-config">
      MathJax.Hub.Register.StartupHook("TeX Jax Ready", function() {
      MathJax.InputJax.TeX.prefilterHooks.Add(function(data) { if (!data.display) { data.math="\\displaystyle{" +
      data.math + "}" } }); }); MathJax.Hub.Config({ jax: ["input/TeX", "output/SVG" ], extensions: ["tex2jax.js"], TeX: {
      extensions: ["AMSmath.js", "AMSsymbols.js" , "noErrors.js" , "noUndefined.js" ] }, skipStartupTypeset: true,
      showProcessingMessages: false, messageStyle: 'none' , tex2jax: { ignoreClass: 'ignore_math' , preview: "none" ,
      inlineMath: [["$", "$" ], ["\\", "\\" ] ]}, showMathMenu: false, displayAlign: "left" , "SVG" : { minScaleAdjust:
      100, linebreaks: { automatic: true, width: "container" } } });
    </script>
  </head>
  <body>
    <div id="live"></div>
    <script src="./libs/vue/vue.min.js"></script>
    <script src="./libs/vue/vuex.min.js"></script>
    <script src="./libs/ui/neosui.umd.min.js?t=<%= htmlWebpackPlugin.options.timestamp %>"></script>
    <script src="./libs/sdk/chat.web.3.3.3.js"></script>
    <script
      type="text/javascript"
      async
      src="https://download-pa-s3.thethinkacademy.com/static-pa/static/libs/mathjax-2.7/MathJax.js"
    ></script>
  </body>
</html>
