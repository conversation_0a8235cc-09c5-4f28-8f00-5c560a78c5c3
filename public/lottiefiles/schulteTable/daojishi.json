{"v": "5.9.4", "fr": 25, "ip": 0, "op": 99, "w": 1720, "h": 1292, "nm": "da<PERSON><PERSON><PERSON>", "ddd": 0, "assets": [{"id": "image_0", "w": 604, "h": 324, "u": "images/", "p": "go.png", "e": 0}, {"id": "image_1", "w": 180, "h": 322, "u": "images/", "p": "1.png", "e": 0}, {"id": "image_2", "w": 248, "h": 322, "u": "images/", "p": "2.png", "e": 0}, {"id": "image_3", "w": 256, "h": 330, "u": "images/", "p": "3.png", "e": 0}], "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "go.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 75, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [100]}, {"t": 105, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [860, 777, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [302, 162, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 75, "s": [200, 200, 100]}, {"t": 80, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\namp = 0.1;\nfreq = 2;\ndecay = 5;\n$bm_rt = n = numKeys;\nif (n == 0) {\n    $bm_rt = value;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n    if (t > 0) {\n        v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n        $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, 2), Math.PI), t))), Math.exp($bm_mul(decay, t))));\n    } else {\n        $bm_rt = value;\n    }\n    ;\n}"}}, "ao": 0, "ip": 0, "op": 1500, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "1.png", "cl": "png", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 75, "s": [100]}, {"t": 80, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [860, 777, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [90, 161, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 50, "s": [200, 200, 100]}, {"t": 55, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\namp = 0.1;\nfreq = 2;\ndecay = 5;\n$bm_rt = n = numKeys;\nif (n == 0) {\n    $bm_rt = value;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n    if (t > 0) {\n        v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n        $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, 2), Math.PI), t))), Math.exp($bm_mul(decay, t))));\n    } else {\n        $bm_rt = value;\n    }\n    ;\n}"}}, "ao": 0, "ip": 0, "op": 1500, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "2.png", "cl": "png", "refId": "image_2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 25, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [100]}, {"t": 55, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [860, 777, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [124, 161, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 25, "s": [200, 200, 100]}, {"t": 30, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\namp = 0.1;\nfreq = 2;\ndecay = 5;\n$bm_rt = n = numKeys;\nif (n == 0) {\n    $bm_rt = value;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n    if (t > 0) {\n        v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n        $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, 2), Math.PI), t))), Math.exp($bm_mul(decay, t))));\n    } else {\n        $bm_rt = value;\n    }\n    ;\n}"}}, "ao": 0, "ip": 0, "op": 1500, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "3.png", "cl": "png", "refId": "image_3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 5, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 25, "s": [100]}, {"t": 30, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [860, 777, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [128, 165, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [200, 200, 100]}, {"t": 5, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\namp = 0.1;\nfreq = 2;\ndecay = 5;\n$bm_rt = n = numKeys;\nif (n == 0) {\n    $bm_rt = value;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n    if (t > 0) {\n        v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n        $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, 2), Math.PI), t))), Math.exp($bm_mul(decay, t))));\n    } else {\n        $bm_rt = value;\n    }\n    ;\n}"}}, "ao": 0, "ip": 0, "op": 1500, "st": 0, "bm": 0}], "markers": []}