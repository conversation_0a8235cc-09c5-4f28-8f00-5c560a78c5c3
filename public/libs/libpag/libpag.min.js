!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).libpag={})}(this,(function(e){"use strict";const t=(()=>{if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;if("undefined"!=typeof self)return self;throw new Error("unable to locate global object")})();let r;void 0===t.globalThis&&Object.defineProperty(t,"globalThis",{get:()=>t});function n(e){const t=["length","name","prototype","wasmAsyncMethods"];let n=Object.getOwnPropertyNames(e).filter((e=>-1===t.indexOf(e)));e.wasmAsyncMethods&&e.wasmAsyncMethods.length>0&&(n=n.filter((t=>-1===e.wasmAsyncMethods.indexOf(t))));let a=Object.getOwnPropertyNames(e.prototype).filter((t=>"constructor"!==t&&"function"==typeof e.prototype[t]));e.prototype.wasmAsyncMethods&&e.prototype.wasmAsyncMethods.length>0&&(a=a.filter((t=>-1===e.prototype.wasmAsyncMethods.indexOf(t))));const i=(e,t)=>{const n=e[t];e[t]=function(...e){if(null!==r.Asyncify.currData){const t=r.Asyncify.currData;r.Asyncify.currData=null;const a=n.call(this,...e);return r.Asyncify.currData=t,a}return n.call(this,...e)}};n.forEach((t=>i(e,t))),a.forEach((t=>i(e.prototype,t)))}function a(e,t,r){e.wasmAsyncMethods||(e.wasmAsyncMethods=[]),e.wasmAsyncMethods.push(t)}function i(e){let t=Object.getOwnPropertyNames(e.prototype).filter((t=>"constructor"!==t&&"function"==typeof e.prototype[t]));const r=(t,r)=>{const n=t[r];t[r]=function(...t){if(!this.isDestroyed)return n.call(this,...t);console.error(`Don't call ${r} of the ${e.name} that is destroyed.`)}};t.forEach((t=>r(e.prototype,t)))}var o=(e=>(e[e.None=0]="None",e[e.Stretch=1]="Stretch",e[e.LetterBox=2]="LetterBox",e[e.Zoom=3]="Zoom",e))(o||{}),s=(e=>(e.onAnimationStart="onAnimationStart",e.onAnimationEnd="onAnimationEnd",e.onAnimationCancel="onAnimationCancel",e.onAnimationRepeat="onAnimationRepeat",e.onAnimationUpdate="onAnimationUpdate",e.onAnimationPlay="onAnimationPlay",e.onAnimationPause="onAnimationPause",e.onAnimationFlushed="onAnimationFlushed",e))(s||{}),l=(e=>(e[e.LeftJustify=0]="LeftJustify",e[e.CenterJustify=1]="CenterJustify",e[e.RightJustify=2]="RightJustify",e[e.FullJustifyLastLineLeft=3]="FullJustifyLastLineLeft",e[e.FullJustifyLastLineRight=4]="FullJustifyLastLineRight",e[e.FullJustifyLastLineCenter=5]="FullJustifyLastLineCenter",e[e.FullJustifyLastLineFull=6]="FullJustifyLastLineFull",e))(l||{}),u=(e=>(e[e.Default=0]="Default",e[e.Horizontal=1]="Horizontal",e[e.Vertical=2]="Vertical",e))(u||{}),c=(e=>(e[e.Unknown=0]="Unknown",e[e.Null=1]="Null",e[e.Solid=2]="Solid",e[e.Text=3]="Text",e[e.Shape=4]="Shape",e[e.Image=5]="Image",e[e.PreCompose=6]="PreCompose",e))(c||{}),f=(e=>(e[e.None=0]="None",e[e.Scale=1]="Scale",e[e.Repeat=2]="Repeat",e[e.RepeatInverted=3]="RepeatInverted",e))(f||{}),d=(e=>(e[e.a=0]="a",e[e.c=1]="c",e[e.tx=2]="tx",e[e.b=3]="b",e[e.d=4]="d",e[e.ty=5]="ty",e))(d||{}),h=(e=>(e[e.Success=0]="Success",e[e.TryAgainLater=-1]="TryAgainLater",e[e.Error=-2]="Error",e))(h||{}),m=(e=>(e[e.Unknown=0]="Unknown",e[e.ALPHA_8=1]="ALPHA_8",e[e.RGBA_8888=2]="RGBA_8888",e[e.BGRA_8888=3]="BGRA_8888",e))(m||{}),p=(e=>(e[e.Unknown=0]="Unknown",e[e.Opaque=1]="Opaque",e[e.Premultiplied=2]="Premultiplied",e[e.Unpremultiplied=3]="Unpremultiplied",e))(p||{}),y=Object.freeze({__proto__:null,PAGScaleMode:o,PAGViewListenerEvent:s,ParagraphJustification:l,TextDirection:u,LayerType:c,PAGTimeStretchMode:f,MatrixIndex:d,DecoderResult:h,ColorType:m,AlphaType:p}),v=Object.defineProperty,g=Object.getOwnPropertyDescriptor;let w=class{constructor(e){this.isDestroyed=!1,this.wasmIns=e}static makeAll(e,t,n,a,i,o,s=0,l=0,u=1){const c=r._Matrix._MakeAll(e,t,n,a,i,o,s,l,u);if(!c)throw new Error("Matrix.makeAll fail, please check parameters valid!");return new w(c)}static makeScale(e,t){let n;if(n=void 0!==t?r._Matrix._MakeScale(e,t):r._Matrix._MakeScale(e),!n)throw new Error("Matrix.makeScale fail, please check parameters valid!");return new w(n)}static makeTrans(e,t){const n=r._Matrix._MakeTrans(e,t);if(!n)throw new Error("Matrix.makeTrans fail, please check parameters valid!");return new w(n)}get a(){return this.wasmIns?this.wasmIns._get(d.a):0}set a(e){var t;null==(t=this.wasmIns)||t._set(d.a,e)}get b(){return this.wasmIns?this.wasmIns._get(d.b):0}set b(e){var t;null==(t=this.wasmIns)||t._set(d.b,e)}get c(){return this.wasmIns?this.wasmIns._get(d.c):0}set c(e){var t;null==(t=this.wasmIns)||t._set(d.c,e)}get d(){return this.wasmIns?this.wasmIns._get(d.d):0}set d(e){var t;null==(t=this.wasmIns)||t._set(d.d,e)}get tx(){return this.wasmIns?this.wasmIns._get(d.tx):0}set tx(e){var t;null==(t=this.wasmIns)||t._set(d.tx,e)}get ty(){return this.wasmIns?this.wasmIns._get(d.ty):0}set ty(e){var t;null==(t=this.wasmIns)||t._set(d.ty,e)}get(e){return this.wasmIns?this.wasmIns._get(e):0}set(e,t){var r;null==(r=this.wasmIns)||r._set(e,t)}setAll(e,t,r,n,a,i){var o;null==(o=this.wasmIns)||o._setAll(e,t,r,n,a,i,0,0,1)}setAffine(e,t,r,n,a,i){var o;null==(o=this.wasmIns)||o._setAffine(e,t,r,n,a,i)}reset(){var e;null==(e=this.wasmIns)||e._reset()}setTranslate(e,t){var r;null==(r=this.wasmIns)||r._setTranslate(e,t)}setScale(e,t,r=0,n=0){var a;null==(a=this.wasmIns)||a._setScale(e,t,r,n)}setRotate(e,t=0,r=0){var n;null==(n=this.wasmIns)||n._setRotate(e,t,r)}setSinCos(e,t,r=0,n=0){var a;null==(a=this.wasmIns)||a._setSinCos(e,t,r,n)}setSkew(e,t,r=0,n=0){var a;null==(a=this.wasmIns)||a._setSkew(e,t,r,n)}setConcat(e,t){var r;null==(r=this.wasmIns)||r._setConcat(e.wasmIns,t.wasmIns)}preTranslate(e,t){var r;null==(r=this.wasmIns)||r._preTranslate(e,t)}preScale(e,t,r=0,n=0){var a;null==(a=this.wasmIns)||a._preScale(e,t,r,n)}preRotate(e,t=0,r=0){var n;null==(n=this.wasmIns)||n._preRotate(e,t,r)}preSkew(e,t,r=0,n=0){var a;null==(a=this.wasmIns)||a._preSkew(e,t,r,n)}preConcat(e){var t;null==(t=this.wasmIns)||t._preConcat(e.wasmIns)}postTranslate(e,t){var r;null==(r=this.wasmIns)||r._postTranslate(e,t)}postScale(e,t,r=0,n=0){var a;null==(a=this.wasmIns)||a._postScale(e,t,r,n)}postRotate(e,t=0,r=0){var n;null==(n=this.wasmIns)||n._postRotate(e,t,r)}postSkew(e,t,r=0,n=0){var a;null==(a=this.wasmIns)||a._postSkew(e,t,r,n)}postConcat(e){var t;null==(t=this.wasmIns)||t._postConcat(e.wasmIns)}destroy(){this.wasmIns.delete()}};w=((e,t,r,n)=>{for(var a,i=n>1?void 0:n?g(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&v(t,r,i),i})([i,n],w);const _=(e,t,...n)=>{if(null!==r.Asyncify.currData){const a=r.Asyncify.currData;r.Asyncify.currData=null;const i=e.call(t,...n);return r.Asyncify.currData=a,i}return e.call(t,...n)},b=(e,t)=>new Proxy(e,{get(e,r,n){switch(r){case"get":return r=>{const n=_(e.get,e,r);return n?t(n):n};case"push_back":return t=>{_(e.push_back,e,t.wasmIns||t)};case"size":return()=>_(e.size,e);default:return Reflect.get(e,r,n)}}}),E=e=>{switch(_(e._layerType,e)){case c.Solid:return new r.PAGSolidLayer(e);case c.Text:return new r.PAGTextLayer(e);case c.Image:return new r.PAGImageLayer(e);default:return new r.PAGLayer(e)}},C=e=>(null==e?void 0:e.wasmIns)?e.wasmIns:e,x=(e,t)=>void 0!==t&&e instanceof t;var P=Object.defineProperty,T=Object.getOwnPropertyDescriptor;let A=class{constructor(e){this.isDestroyed=!1,this.wasmIns=e}uniqueID(){return this.wasmIns._uniqueID()}layerType(){return this.wasmIns._layerType()}layerName(){return this.wasmIns._layerName()}matrix(){const e=this.wasmIns._matrix();if(!e)throw new Error("Get matrix fail!");return new w(e)}setMatrix(e){this.wasmIns._setMatrix(e.wasmIns)}resetMatrix(){this.wasmIns._resetMatrix()}getTotalMatrix(){if(!this.wasmIns._getTotalMatrix())throw new Error("Get total matrix fail!");return new w(this.wasmIns._getTotalMatrix())}alpha(){return this.wasmIns._alpha()}setAlpha(e){this.wasmIns._setAlpha(e)}visible(){return this.wasmIns._visible()}setVisible(e){this.wasmIns._setVisible(e)}editableIndex(){return this.wasmIns._editableIndex()}parent(){const e=this.wasmIns._parent();if(!e)throw new Error("Get total matrix fail!");return new S(e)}markers(){const e=this.wasmIns._markers();if(!e)throw new Error("Get markers fail!");return b(e,(e=>e))}localTimeToGlobal(e){return this.wasmIns._localTimeToGlobal(e)}globalToLocalTime(e){return this.wasmIns._globalToLocalTime(e)}duration(){return this.wasmIns._duration()}frameRate(){return this.wasmIns._frameRate()}startTime(){return this.wasmIns._startTime()}setStartTime(e){this.wasmIns._setStartTime(e)}currentTime(){return this.wasmIns._currentTime()}setCurrentTime(e){this.wasmIns._setCurrentTime(e)}getProgress(){return this.wasmIns._getProgress()}setProgress(e){this.wasmIns._setProgress(e)}preFrame(){this.wasmIns._preFrame()}nextFrame(){this.wasmIns._nextFrame()}getBounds(){return this.wasmIns._getBounds()}trackMatteLayer(){const e=this.wasmIns._trackMatteLayer();if(!e)throw new Error("Get track matte layer fail!");return E(e)}excludedFromTimeline(){return this.wasmIns._excludedFromTimeline()}setExcludedFromTimeline(e){this.wasmIns._setExcludedFromTimeline(e)}isPAGFile(){return this.wasmIns._isPAGFile()}asTypeLayer(){return E(this)}isDelete(){return this.wasmIns.isDelete()}destroy(){this.wasmIns.delete(),this.isDestroyed=!0}};A=((e,t,r,n)=>{for(var a,i=n>1?void 0:n?T(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&P(t,r,i),i})([i,n],A);var I=Object.defineProperty,k=Object.getOwnPropertyDescriptor;let S=class extends A{static make(e,t){const n=r._PAGComposition._Make(e,t);if(!n)throw new Error("Make PAGComposition fail!");return new S(n)}width(){return this.wasmIns._width()}height(){return this.wasmIns._height()}setContentSize(e,t){this.wasmIns._setContentSize(e,t)}numChildren(){return this.wasmIns._numChildren()}getLayerAt(e){const t=this.wasmIns._getLayerAt(e);if(!t)throw new Error(`Get layer at ${e} fail!`);return E(t)}getLayerIndex(e){return this.wasmIns._getLayerIndex(e.wasmIns)}setLayerIndex(e,t){return this.wasmIns._setLayerIndex(e.wasmIns,t)}addLayer(e){return this.wasmIns._addLayer(e.wasmIns)}addLayerAt(e,t){return this.wasmIns._addLayerAt(e.wasmIns,t)}contains(e){return this.wasmIns._contains(e.wasmIns)}removeLayer(e){const t=this.wasmIns._removeLayer(e.wasmIns);if(!t)throw new Error("Remove layer fail!");return E(t)}removeLayerAt(e){const t=this.wasmIns._removeLayerAt(e);if(!t)throw new Error(`Remove layer at ${e} fail!`);return E(t)}removeAllLayers(){this.wasmIns._removeAllLayers()}swapLayer(e,t){this.wasmIns._swapLayer(e.wasmIns,t.wasmIns)}swapLayerAt(e,t){this.wasmIns._swapLayerAt(e,t)}audioBytes(){return this.wasmIns._audioBytes()}audioMarkers(){const e=this.wasmIns._audioMarkers();if(!e)throw new Error("Get audioMarkers fail!");return b(e,(e=>e))}audioStartTime(){return this.wasmIns._audioStartTime()}getLayersByName(e){const t=this.wasmIns._getLayersByName(e);if(!t)throw new Error(`Get layers by ${e} fail!`);return b(t,E)}getLayersUnderPoint(e,t){const r=this.wasmIns._getLayersUnderPoint(e,t);if(!r)throw new Error(`Get layers under point ${e},${t} fail!`);return b(r,E)}};S=((e,t,r,n)=>{for(var a,i=n>1?void 0:n?k(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&I(t,r,i),i})([i,n],S);const F=e=>new Promise((t=>{const r=new FileReader;r.onload=()=>{t(r.result)},r.onerror=()=>{console.error(r.error.message)},r.readAsArrayBuffer(e)})),D=(e,t)=>{const r=new Uint8Array(t),n=r.byteLength,a=e._malloc(n);return new Uint8Array(e.HEAPU8.buffer,a,n).set(r),{byteOffset:a,length:n,free:()=>e._free(a)}};var L=Object.defineProperty,M=Object.getOwnPropertyDescriptor,R=(e,t,r,n)=>{for(var a,i=n>1?void 0:n?M(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&L(t,r,i),i};let G=class extends S{static async load(e){const t=await(e=>x(e,globalThis.File)?F(e):x(e,globalThis.Blob)?F(new File([e],"")):x(e,globalThis.ArrayBuffer)?Promise.resolve(e):Promise.resolve(null))(e);if(!t)throw new Error("Initialize PAGFile data type error, please put check data type must to be File ｜ Blob | ArrayBuffer!");return G.loadFromBuffer(t)}static loadFromBuffer(e){if(!(e&&e.byteLength>0))throw new Error("Initialize PAGFile data not be empty!");const{byteOffset:t,length:n,free:a}=D(r,e),i=r._PAGFile._Load(t,n);if(a(),!i)throw new Error("Load PAGFile fail!");return new G(i)}static maxSupportedTagLevel(){return r._PAGFile._MaxSupportedTagLevel()}tagLevel(){return this.wasmIns._tagLevel()}numTexts(){return this.wasmIns._numTexts()}numImages(){return this.wasmIns._numImages()}numVideos(){return this.wasmIns._numVideos()}getTextData(e){return this.wasmIns._getTextData(e)}replaceText(e,t){this.wasmIns._replaceText(e,t)}replaceImage(e,t){this.wasmIns._replaceImage(e,t.wasmIns)}getLayersByEditableIndex(e,t){const r=this.wasmIns._getLayersByEditableIndex(e,t);if(!r)throw new Error(`Get ${(e=>{switch(e){case c.Solid:return"Solid";case c.Text:return"Text";case c.Shape:return"Shape";case c.Image:return"Image";case c.PreCompose:return"PreCompose";default:return"Unknown"}})(t)} layers by ${e} fail!`);return b(r,E)}getEditableIndices(e){return this.wasmIns._getEditableIndices(e)}timeStretchMode(){return this.wasmIns._timeStretchMode()}setTimeStretchMode(e){this.wasmIns._setTimeStretchMode(e)}setDuration(e){this.wasmIns._setDuration(e)}copyOriginal(){const e=this.wasmIns._copyOriginal();if(!e)throw new Error("Copy original fail!");return new G(e)}};R([a],G,"load",1),G=R([i,n],G);var B=Object.defineProperty,O=Object.getOwnPropertyDescriptor;let j=class{constructor(e){this.isDestroyed=!1,this.wasmIns=e}static fromCanvas(e){const t=r._PAGSurface._FromCanvas(e);if(!t)throw new Error(`Make PAGSurface from canvas ${e} fail!`);return new j(t)}static fromTexture(e,t,n,a){const i=r._PAGSurface._FromTexture(e,t,n,a);if(!i)throw new Error(`Make PAGSurface from texture ${e} fail!`);return new j(i)}static fromRenderTarget(e,t,n,a){const i=r._PAGSurface._FromRenderTarget(e,t,n,a);if(!i)throw new Error(`Make PAGSurface from frameBuffer ${e} fail!`);return new j(i)}width(){return this.wasmIns._width()}height(){return this.wasmIns._height()}updateSize(){this.wasmIns._updateSize()}clearAll(){return this.wasmIns._clearAll()}freeCache(){this.wasmIns._freeCache()}readPixels(e,t){if(e===m.Unknown)return null;const n=this.width()*(e===m.ALPHA_8?1:4),a=n*this.height(),i=new Uint8Array(a),{data:o,free:s}=((e,t,r)=>{const n=new Uint8Array(t),a=e._malloc(n.byteLength);if(!r(a,n.byteLength))return{data:null,free:()=>e._free(a)};const i=new Uint8Array(e.HEAPU8.buffer,a,n.byteLength);return n.set(i),{data:n,free:()=>e._free(a)}})(r,i,(r=>this.wasmIns._readPixels(e,t,r,n)));return s(),o}destroy(){this.wasmIns.delete(),this.isDestroyed=!0}};j=((e,t,r,n)=>{for(var a,i=n>1?void 0:n?O(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&B(t,r,i),i})([i,n],j);var V=Object.defineProperty,N=Object.getOwnPropertyDescriptor,$=(e,t,r,n)=>{for(var a,i=n>1?void 0:n?N(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&V(t,r,i),i};let U=class{constructor(e){this.isDestroyed=!1,this.videoReaders=[],this.wasmIns=e}static create(){const e=new r._PAGPlayer;if(!e)throw new Error("Create PAGPlayer fail!");return new U(e)}setProgress(e){this.wasmIns._setProgress(e)}async flush(){return r.webAssemblyQueue.exec(this.wasmIns._flush,this.wasmIns)}async flushInternal(e){const t=await r.webAssemblyQueue.exec((async()=>{r.currentPlayer=this;const t=await this.wasmIns._flush();return r.currentPlayer=null,e(t),t}),this.wasmIns);for(const e of this.videoReaders){const t=await e.getError();if(null!==t)throw t}return t}duration(){return this.wasmIns._duration()}getProgress(){return this.wasmIns._getProgress()}currentFrame(){return this.wasmIns._currentFrame()}videoEnabled(){return this.wasmIns._videoEnabled()}setVideoEnabled(e){this.wasmIns._setVideoEnabled(e)}cacheEnabled(){return this.wasmIns._cacheEnabled()}setCacheEnabled(e){this.wasmIns._setCacheEnabled(e)}cacheScale(){return this.wasmIns._cacheScale()}setCacheScale(e){this.wasmIns._setCacheScale(e)}maxFrameRate(){return this.wasmIns._maxFrameRate()}setMaxFrameRate(e){this.wasmIns._setMaxFrameRate(e)}scaleMode(){return this.wasmIns._scaleMode()}setScaleMode(e){this.wasmIns._setScaleMode(e)}setSurface(e){this.wasmIns._setSurface(C(e))}getComposition(){const e=this.wasmIns._getComposition();if(!e)throw new Error("Get composition fail!");return e._isPAGFile()?new G(e):new S(e)}setComposition(e){this.wasmIns._setComposition(C(e))}getSurface(){const e=this.wasmIns._getSurface();if(!e)throw new Error("Get surface fail!");return new j(e)}matrix(){const e=this.wasmIns._matrix();if(!e)throw new Error("Get matrix fail!");return new w(e)}setMatrix(e){this.wasmIns._setMatrix(e.wasmIns)}nextFrame(){this.wasmIns._nextFrame()}preFrame(){this.wasmIns._preFrame()}autoClear(){return this.wasmIns._autoClear()}setAutoClear(e){this.wasmIns._setAutoClear(e)}getBounds(e){return this.wasmIns._getBounds(e.wasmIns)}getLayersUnderPoint(e,t){const r=this.wasmIns._getLayersUnderPoint(e,t);if(!r)throw new Error(`Get layers under point, x: ${e} y:${t} fail!`);return b(r,E)}hitTestPoint(e,t,r,n=!1){return this.wasmIns._hitTestPoint(e.wasmIns,t,r,n)}renderingTime(){return this.wasmIns._renderingTime()}imageDecodingTime(){return this.wasmIns._imageDecodingTime()}presentingTime(){return this.wasmIns._presentingTime()}graphicsMemory(){return this.wasmIns._graphicsMemory()}prepare(){return r.webAssemblyQueue.exec((async()=>{r.currentPlayer=this,await this.wasmIns._prepare(),r.currentPlayer=null}),this.wasmIns)}destroy(){this.wasmIns.delete(),this.isDestroyed=!0}linkVideoReader(e){this.videoReaders.push(e)}unlinkVideoReader(e){const t=this.videoReaders.indexOf(e);-1!==t&&this.videoReaders.splice(t,1)}};$([a],U.prototype,"flush",1),$([a],U.prototype,"flushInternal",1),U=$([i,n],U);var H=Object.defineProperty,z=Object.getOwnPropertyDescriptor,W=(e,t,r,n)=>{for(var a,i=n>1?void 0:n?z(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&H(t,r,i),i};let q=class{constructor(e){this.isDestroyed=!1,this.wasmIns=e}static async fromFile(e){return new Promise(((t,r)=>{const n=new Image;n.onload=async()=>{t(q.fromSource(n))},n.onerror=e=>{r(e)},n.src=URL.createObjectURL(e)}))}static fromSource(e){const t=r._PAGImage._FromNativeImage(e);if(!t)throw new Error("Make PAGImage from source fail!");return new q(t)}static fromPixels(e,t,n,a,i){const o=t*(a===m.ALPHA_8?1:4),{byteOffset:s,free:l}=D(r,e),u=r._PAGImage._FromPixels(s,t,n,o,a,i);if(l(),!u)throw new Error("Make PAGImage from pixels fail!");return new q(u)}static fromTexture(e,t,n,a){const i=r._PAGImage._FromTexture(e,t,n,a);if(!i)throw new Error("Make PAGImage from texture fail!");return new q(i)}width(){return this.wasmIns._width()}height(){return this.wasmIns._height()}scaleMode(){return this.wasmIns._scaleMode()}setScaleMode(e){this.wasmIns._setScaleMode(e)}matrix(){const e=this.wasmIns._matrix();if(!e)throw new Error("Get matrix fail!");return new w(e)}setMatrix(e){this.wasmIns._setMatrix(e.wasmIns)}destroy(){this.wasmIns.delete(),this.isDestroyed=!0}};W([a],q,"fromFile",1),q=W([i,n],q);class X{constructor(){this.listenerMap=new Map}on(e,t){let r=[];this.listenerMap.has(e)&&(r=this.listenerMap.get(e)),r.push(t),this.listenerMap.set(e,r)}off(e,t){if(!this.listenerMap.has(e))return!1;const r=this.listenerMap.get(e);if(0===r.length)return!1;if(!t)return this.listenerMap.delete(e),!0;const n=r.indexOf(t);return-1!==n&&(r.splice(n,1),!0)}emit(e,t){if(!this.listenerMap.has(e))return!1;const r=this.listenerMap.get(e);return 0!==r.length&&(r.forEach((e=>e(t))),!0)}}const J=2560,K={depth:!1,stencil:!1,antialias:!1},Q=(null==navigator?void 0:navigator.userAgent)||"",Y=/android|adr/i.test(Q),Z=/(mobile)/i.test(Q)&&Y;!/(mobile)/i.test(Q)&&!Z&&/Mac OS X/i.test(Q);const ee=/(iphone|ipad|ipod)/i.test(Q),te=/MicroMessenger/i.test(Q),re=/^((?!chrome|android).)*safari/i.test(Q)||ee,ne="function"==typeof globalThis.importScripts,ae=new Array,ie=e=>x(e,globalThis.OffscreenCanvas),oe=(e,t)=>{let r=ae.pop()||le();return null!==r&&(r.width=e,r.height=t),r},se=e=>{ae.length<10&&ae.push(e)},le=()=>{if(re&&!ne)return document.createElement("canvas");try{const e=new OffscreenCanvas(0,0);return"function"==typeof e.getContext("2d").measureText?e:document.createElement("canvas")}catch(e){return document.createElement("canvas")}};var ue=Object.defineProperty,ce=Object.getOwnPropertySymbols,fe=Object.prototype.hasOwnProperty,de=Object.prototype.propertyIsEnumerable,he=(e,t,r)=>t in e?ue(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;class me{constructor(e,t=!1){this.isDestroyed=!1,this.oldHandle=0,this.handle=e,this.adopted=t}static from(e){if(e instanceof me)return new me(e.handle,!0);{const t=x(e,globalThis.WebGL2RenderingContext)?2:1,{GL:n}=r;let a=0;return n.contexts.length>0&&(a=n.contexts.findIndex((t=>(null==t?void 0:t.GLctx)===e))),a<1?(a=n.registerContext(e,((e,t)=>{for(var r in t||(t={}))fe.call(t,r)&&he(e,r,t[r]);if(ce)for(var r of ce(t))de.call(t,r)&&he(e,r,t[r]);return e})({majorVersion:t,minorVersion:0},K)),new me(a)):new me(a,!0)}}getContext(){return r.GL.getContext(this.handle).GLctx}makeCurrent(){var e;return!this.isDestroyed&&(this.oldHandle=(null==(e=r.GL.currentContext)?void 0:e.handle)||0,this.oldHandle===this.handle||r.GL.makeContextCurrent(this.handle))}clearCurrent(){this.isDestroyed||this.oldHandle!==this.handle&&(r.GL.makeContextCurrent(0),this.oldHandle&&r.GL.makeContextCurrent(this.oldHandle))}registerTexture(e){return this.register(r.GL.textures,e)}getTexture(e){return r.GL.textures[e]}unregisterTexture(e){r.GL.textures[e]=null}registerRenderTarget(e){return this.register(r.GL.framebuffers,e)}getRenderTarget(e){return r.GL.framebuffers[e]}unregisterRenderTarget(e){r.GL.framebuffers[e]=null}destroy(){this.adopted||r.GL.deleteContext(this.handle)}register(e,t){const n=r.GL.getNewId(e);return e[n]=t,n}}var pe=Object.defineProperty,ye=Object.getOwnPropertySymbols,ve=Object.prototype.hasOwnProperty,ge=Object.prototype.propertyIsEnumerable,we=(e,t,r)=>t in e?pe(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,_e=(e,t)=>{for(var r in t||(t={}))ve.call(t,r)&&we(e,r,t[r]);if(ye)for(var r of ye(t))ge.call(t,r)&&we(e,r,t[r]);return e};const be=[];class Ee{constructor(e,t){this._canvas=null,this._glContext=null,this.retainCount=0,this._canvas=e;const r=e.getContext("webgl",_e(_e({},K),t));if(!r)throw new Error("Canvas context is not WebGL!");this._glContext=me.from(r)}static from(e,t){let r=be.find((t=>t.canvas===e));return r||(r=new Ee(e,t),be.push(r),r)}retain(){this.retainCount+=1}release(){if(this.retainCount-=1,0===this.retainCount){if(!this._glContext)return;this._glContext.destroy(),this._glContext=null,this._canvas=null}}get canvas(){return this._canvas}get glContext(){return this._glContext}}let Ce;try{Ce=performance.now.bind(performance)}catch(e){Ce=Date.now.bind(Date)}class xe{constructor(){this.startTime=Ce(),this.markers={}}reset(){this.startTime=Ce(),this.markers={}}mark(e){e?Object.keys(this.markers).find((t=>t===e))?console.log(`Clock.mark(): The specified marker name '${e}' already exists!`):this.markers[e]=Ce():console.log("Clock.mark(): An empty marker name was specified!")}measure(e,t){let r,n;if(e){if(!Object.keys(this.markers).find((t=>t===e)))return console.log(`Clock.measure(): The specified makerFrom '${e}' does not exist!`),0;r=this.markers[e]}else r=this.startTime;if(t){if(!Object.keys(this.markers).find((e=>e===t)))return console.log(`Clock.measure(): The specified makerTo '${t}' does not exist!`),0;n=this.markers[t]}else n=Ce();return n-r}}var Pe=Object.defineProperty,Te=Object.getOwnPropertyDescriptor,Ae=Object.getOwnPropertySymbols,Ie=Object.prototype.hasOwnProperty,ke=Object.prototype.propertyIsEnumerable,Se=(e,t,r)=>t in e?Pe(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Fe=(e,t)=>{for(var r in t||(t={}))Ie.call(t,r)&&Se(e,r,t[r]);if(Ae)for(var r of Ae(t))ke.call(t,r)&&Se(e,r,t[r]);return e};let De=class{constructor(e,t){this.repeatCount=0,this.isPlaying=!1,this.isDestroyed=!1,this.pagViewOptions={useScale:!0,useCanvas2D:!1,firstFrame:!0},this.renderCanvas=null,this.pagGlContext=null,this.frameRate=0,this.pagSurface=null,this.playFrame=-1,this.timer=null,this.flushingNextFrame=!1,this.playTime=0,this.startTime=0,this.repeatedTimes=0,this.eventManager=new X,this.rawWidth=0,this.rawHeight=0,this.debugData={FPS:0,flushTime:0},this.fpsBuffer=[],this.player=e,this.canvasElement=t}static async init(e,t,n={}){let a=null;if("string"==typeof t?a=document.getElementById(t.substr(1)):("undefined"!=typeof window&&x(t,globalThis.HTMLCanvasElement)||ie(t))&&(a=t),!a)throw new Error("Canvas is not found!");const i=r.PAGPlayer.create(),o=new De(i,a);return o.pagViewOptions=Fe(Fe({},o.pagViewOptions),n),o.pagViewOptions.useCanvas2D?(r.globalCanvas.retain(),o.pagGlContext=me.from(r.globalCanvas.glContext)):(o.renderCanvas=Ee.from(a),o.renderCanvas.retain(),o.pagGlContext=me.from(o.renderCanvas.glContext)),o.resetSize(o.pagViewOptions.useScale),o.frameRate=e.frameRate(),o.pagSurface=this.makePAGSurface(o.pagGlContext,o.rawWidth,o.rawHeight),o.player.setSurface(o.pagSurface),o.player.setComposition(e),o.setProgress(0),o.pagViewOptions.firstFrame&&(await o.flush(),o.playFrame=0),o}static makePAGSurface(e,t,r){if(!e.makeCurrent())throw new Error("Make context current fail!");const n=j.fromRenderTarget(0,t,r,!0);return e.clearCurrent(),n}duration(){return this.player.duration()}addListener(e,t){return this.eventManager.on(e,t)}removeListener(e,t){return this.eventManager.off(e,t)}async play(){if(this.isPlaying)return;this.isPlaying=!0,this.startTime=1e3*this.getNowTime()-this.playTime;for(const e of this.player.videoReaders)e.isPlaying=!0;const e=this.playTime;await this.flushLoop(!0),0===e&&this.eventManager.emit("onAnimationStart",this),this.eventManager.emit("onAnimationPlay",this),0===this.playFrame&&this.eventManager.emit("onAnimationUpdate",this)}pause(){if(this.isPlaying){this.clearTimer();for(const e of this.player.videoReaders)e.pause();this.isPlaying=!1,this.eventManager.emit("onAnimationPause",this)}}async stop(e=!0){this.clearTimer(),this.playTime=0,this.player.setProgress(0),this.playFrame=0,await this.flush();for(const e of this.player.videoReaders)e.stop();this.isPlaying=!1,e&&this.eventManager.emit("onAnimationCancel",this)}setRepeatCount(e){this.repeatCount=e<0?0:e-1}getProgress(){return this.player.getProgress()}currentFrame(){return this.player.currentFrame()}setProgress(e){return this.playTime=e*this.duration(),this.startTime=1e3*this.getNowTime()-this.playTime,this.isPlaying||this.player.setProgress(e),e}videoEnabled(){return this.player.videoEnabled()}setVideoEnabled(e){this.player.setVideoEnabled(e)}cacheEnabled(){return this.player.cacheEnabled()}setCacheEnabled(e){this.player.setCacheEnabled(e)}cacheScale(){return this.player.cacheScale()}setCacheScale(e){this.player.setCacheScale(e)}maxFrameRate(){return this.player.maxFrameRate()}setMaxFrameRate(e){this.player.setMaxFrameRate(e)}scaleMode(){return this.player.scaleMode()}setScaleMode(e){this.player.setScaleMode(e)}async flush(){const e=new xe,t=await this.player.flushInternal((t=>{var n,a;if(this.pagViewOptions.useCanvas2D&&t&&r.globalCanvas.canvas){this.canvasContext||(this.canvasContext=null==(n=this.canvasElement)?void 0:n.getContext("2d"));const e=this.canvasContext.globalCompositeOperation;this.canvasContext.globalCompositeOperation="copy",null==(a=this.canvasContext)||a.drawImage(r.globalCanvas.canvas,0,r.globalCanvas.canvas.height-this.rawHeight,this.rawWidth,this.rawHeight,0,0,this.canvasContext.canvas.width,this.canvasContext.canvas.height),this.canvasContext.globalCompositeOperation=e}e.mark("flush"),this.setDebugData({flushTime:e.measure("","flush")}),this.updateFPS()}));return this.eventManager.emit("onAnimationUpdate",this),t&&this.eventManager.emit("onAnimationFlushed",this),t}freeCache(){var e;null==(e=this.pagSurface)||e.freeCache()}getComposition(){return this.player.getComposition()}setComposition(e){this.player.setComposition(e)}matrix(){return this.player.matrix()}setMatrix(e){this.player.setMatrix(e)}getLayersUnderPoint(e,t){return this.player.getLayersUnderPoint(e,t)}updateSize(){var e;if(!this.canvasElement)throw new Error("Canvas element is not found!");if(this.rawWidth=this.canvasElement.width,this.rawHeight=this.canvasElement.height,!this.pagGlContext)return;const t=De.makePAGSurface(this.pagGlContext,this.rawWidth,this.rawHeight);this.player.setSurface(t),null==(e=this.pagSurface)||e.destroy(),this.pagSurface=t}prepare(){return this.player.prepare()}makeSnapshot(){return createImageBitmap(this.canvasElement)}destroy(){var e,t,n;this.clearTimer(),this.player.destroy(),null==(e=this.pagSurface)||e.destroy(),this.pagViewOptions.useCanvas2D?r.globalCanvas.release():null==(t=this.renderCanvas)||t.release(),null==(n=this.pagGlContext)||n.destroy(),this.pagGlContext=null,this.canvasContext=null,this.canvasElement=null,this.isDestroyed=!0}getDebugData(){return this.debugData}setDebugData(e){this.debugData=Fe(Fe({},this.debugData),e)}async flushLoop(e=!1){if(this.isPlaying&&(this.setTimer(),!this.flushingNextFrame))try{this.flushingNextFrame=!0,await this.flushNextFrame(e),this.flushingNextFrame=!1}catch(e){throw this.flushingNextFrame=!1,"The play() request was interrupted because the document was hidden!"!==e.message&&this.clearTimer(),e}}async flushNextFrame(e=!1){const t=this.duration();this.playTime=1e3*this.getNowTime()-this.startTime;const r=Math.floor(this.playTime/1e6*this.frameRate),n=Math.floor(this.playTime/t);if(!e&&this.repeatCount>=0&&n>this.repeatCount)return this.clearTimer(),this.player.setProgress(1),await this.flush(),this.playTime=0,this.isPlaying=!1,this.repeatedTimes=0,this.eventManager.emit("onAnimationEnd",this),!0;if(!e&&this.repeatedTimes===n&&this.playFrame===r)return!1;this.repeatedTimes<n&&this.eventManager.emit("onAnimationRepeat",this),this.player.setProgress(this.playTime%t/t);const a=await this.flush();return this.needResetStartTime()&&(this.startTime=1e3*this.getNowTime()-this.playTime),this.playFrame=r,this.repeatedTimes=n,a}getNowTime(){try{return performance.now()}catch(e){return Date.now()}}setTimer(){this.timer=ne?self.setTimeout((()=>{this.flushLoop()}),1/this.frameRate*1e3):globalThis.requestAnimationFrame((()=>{this.flushLoop()}))}clearTimer(){this.timer&&(ne?self.clearTimeout(this.timer):globalThis.cancelAnimationFrame(this.timer),this.timer=null)}resetSize(e=!0){if(!this.canvasElement)throw new Error("Canvas element is not found!");if(!e||ie(this.canvasElement))return this.rawWidth=this.canvasElement.width,void(this.rawHeight=this.canvasElement.height);const t=this.canvasElement,r=(e=>{const t=globalThis.getComputedStyle(e,null),r={width:Number(t.width.replace("px","")),height:Number(t.height.replace("px",""))};if(r.width>0&&r.height>0)return r;{const t={width:Number(e.style.width.replace("px","")),height:Number(e.style.height.replace("px",""))};return t.width>0&&t.height>0?t:{width:e.width,height:e.height}}})(t);t.style.width=`${r.width}px`,t.style.height=`${r.height}px`,this.rawWidth=r.width*globalThis.devicePixelRatio,this.rawHeight=r.height*globalThis.devicePixelRatio,t.width=this.rawWidth,t.height=this.rawHeight}updateFPS(){let e;try{e=performance.now()}catch(t){e=Date.now()}this.fpsBuffer=this.fpsBuffer.filter((t=>e-t<=1e3)),this.fpsBuffer.push(e),this.setDebugData({FPS:this.fpsBuffer.length})}needResetStartTime(){for(const e of this.player.videoReaders)if(e.isSought)return!0;return!1}};De=((e,t,r,n)=>{for(var a,i=n>1?void 0:n?Te(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&Pe(t,r,i),i})([i],De);const Le=["emoji"].concat(...["Arial",'"Courier New"',"Georgia",'"Times New Roman"','"Trebuchet MS"',"Verdana"]);var Me=Object.defineProperty,Re=Object.getOwnPropertyDescriptor,Ge=(e,t,r,n)=>{for(var a,i=n>1?void 0:n?Re(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&Me(t,r,i),i};let Be=class{constructor(e){this.isDestroyed=!1,this.wasmIns=e,this.fontFamily=this.wasmIns.fontFamily,this.fontStyle=this.wasmIns.fontStyle}static create(e,t){const n=r._PAGFont._create(e,t);if(!n)throw new Error("Create PAGFont fail!");return new Be(n)}static async registerFont(e,t){const r=await F(t);if(!(r&&r.byteLength>0))throw new Error("Initialize PAGFont data not be empty!");const n=new Uint8Array(r),a=new FontFace(e,n);document.fonts.add(a),await a.load()}static registerFallbackFontNames(e=[]){const t=new r.VectorString,n=e.concat(Le);for(const e of n)t.push_back(e);r._PAGFont._SetFallbackFontNames(t),t.delete()}destroy(){this.wasmIns.delete(),this.isDestroyed=!0}};Ge([a],Be,"registerFont",1),Be=Ge([i,n],Be);let Oe={};const je=(e,t,r,n=!1)=>{var a;t in Oe||(Oe[t]=[]),null==(a=Oe[t])||a.push({node:e,handler:r,capture:n}),e.addEventListener(t,r,n)},Ve=(e,t,r)=>{var n;t in Oe&&(null==(n=Oe[t])||n.filter((({node:t,handler:n})=>t===e&&n===r)).forEach((({node:e,handler:r,capture:n})=>e.removeEventListener(t,r,n))))},Ne=(e,t)=>{var r,n;t in Oe&&(null==(r=Oe[t])||r.filter((({node:t})=>t===e)).forEach((({node:e,handler:r,capture:n})=>e.removeEventListener(t,r,n))),Oe[t]=null==(n=Oe[t])?void 0:n.filter((({node:t})=>t!==e)))};var $e=(e=>(e.PAGInit="PAGInit",e.PAGView_init="PAGView.init",e.PAGView_duration="PAGView.duration",e.PAGView_play="PAGView.play",e.PAGView_pause="PAGView.pause",e.PAGView_stop="PAGView.stop",e.PAGView_setRepeatCount="PAGView.setRepeatCount",e.PAGView_getProgress="PAGView.getProgress",e.PAGView_currentFrame="PAGView.currentFrame",e.PAGView_setProgress="PAGView.setProgress",e.PAGView_scaleMode="PAGView.scaleMode",e.PAGView_setScaleMode="PAGView.setScaleMode",e.PAGView_flush="PAGView.flush",e.PAGView_getDebugData="PAGView.getDebugData",e.PAGView_destroy="PAGView.destroy",e.PAGFile_load="PAGFile.load",e.PAGFile_getTextData="PAGFile.getTextData",e.PAGFile_replaceText="PAGFile.replaceText",e.PAGFile_replaceImage="PAGFile.replaceImage",e.PAGFile_destroy="PAGFile.destroy",e.PAGImage_fromSource="PAGImage.fromSource",e.PAGImage_destroy="PAGImage.destroy",e.VideoReader_constructor="VideoReader.constructor",e.VideoReader_prepare="VideoReader.prepare",e.VideoReader_play="VideoReader.play",e.VideoReader_pause="VideoReader.pause",e.VideoReader_stop="VideoReader.stop",e.VideoReader_getError="VideoReader.getError",e.PAGModule_linkVideoReader="PAGModule.linkVideoReader",e.TextDocument_delete="TextDocument.delete",e))($e||{});let Ue=0;const He=(e,t,r,n=[])=>new Promise((a=>{const i=(e=>`${e}_${Ue++}`)(t.name),o=t=>{t.data.name===i&&(e.removeEventListener("message",o),a(r(...t.data.args)))};e.addEventListener("message",o),e.postMessage({name:i,args:t.args},n)}));class ze{constructor(e){this.bitmap=e}setBitmap(e){this.bitmap&&this.bitmap.close(),this.bitmap=e}}class We{constructor(e){this.bitmap=null,this.isSought=!1,this.isPlaying=!1,this.bitmapImage=new ze(null),this.proxyId=e}prepare(e,t){return new Promise((r=>{He(self,{name:$e.VideoReader_prepare,args:[this.proxyId,e,t]},(e=>{this.bitmapImage.setBitmap(e),r()}))}))}getVideo(){return this.bitmapImage}onDestroy(){self.postMessage({name:"VideoReader.onDestroy",args:[this.proxyId]})}play(){return new Promise((e=>{He(self,{name:$e.VideoReader_play,args:[this.proxyId]},(()=>{e()}))}))}pause(){He(self,{name:$e.VideoReader_pause,args:[this.proxyId]},(()=>{}))}stop(){He(self,{name:$e.VideoReader_stop,args:[this.proxyId]},(()=>{}))}getError(){return new Promise((e=>{He(self,{name:$e.VideoReader_getError,args:[this.proxyId]},(t=>{e(t)}))}))}}const qe=e=>new Promise((t=>{const r=()=>{Ve(e,"canplay",r),clearTimeout(n),t(!0)};je(e,"canplay",r);const n=setTimeout((()=>{Ve(e,"canplay",r),t(!1)}),1e3)}));class Xe{constructor(e,t,n,a,i,o=!1){if(this.isSought=!1,this.isPlaying=!1,this.bitmap=null,this.videoEl=null,this.frameRate=0,this.canplay=!1,this.staticTimeRanges=null,this.disablePlaybackRate=!1,this.error=null,this.player=null,this.width=0,this.height=0,this.bitmapCanvas=null,this.bitmapCtx=null,x(e,globalThis.HTMLVideoElement))this.videoEl=e,this.canplay=!0;else{this.videoEl=document.createElement("video"),this.videoEl.style.display="none",this.videoEl.muted=!0,this.videoEl.playsInline=!0,this.videoEl.preload="auto",this.videoEl.width=t,this.videoEl.height=n,qe(this.videoEl).then((()=>{this.canplay=!0}));const r=new Blob([e],{type:"video/mp4"});this.videoEl.src=URL.createObjectURL(r),ee&&this.videoEl.load()}this.frameRate=a,this.width=t,this.height=n,this.staticTimeRanges=new Je(i),(3840<t||3840<n)&&(this.disablePlaybackRate=!0),o||this.linkPlayer(r.currentPlayer)}static async create(e,t,n,a,i){var o;if(ne){const s=await new Promise((r=>{const o=e,s=o.buffer.slice(o.byteOffset,o.byteOffset+o.byteLength);He(self,{name:$e.VideoReader_constructor,args:[s,t,n,a,i,!0]},(e=>{r(e)}),[s])})),l=new We(s);return null==(o=r.currentPlayer)||o.linkVideoReader(l),l}return new Xe(e,t,n,a,i)}async prepare(e,t){var r;this.setError(null),this.isSought=!1;const{currentTime:n}=this.videoEl,a=e/this.frameRate;if(0===n&&0===a)if(this.canplay||re){try{await this.play()}catch(e){this.setError(e)}await new Promise((e=>{requestAnimationFrame((()=>{this.pause(),e()}))}))}else await qe(this.videoEl);else if(Math.round(a*this.frameRate)===Math.round(n*this.frameRate));else{if(null==(r=this.staticTimeRanges)?void 0:r.contains(e))return void await this.seek(a,!1);if(!(Math.abs(n-a)<1/this.frameRate*3))return this.isSought=!0,void await this.seek(a)}const i=Math.min(Math.max(t,.125),4);if(this.disablePlaybackRate||this.videoEl.playbackRate===i||(this.videoEl.playbackRate=i),this.isPlaying&&this.videoEl.paused)try{await this.play()}catch(e){this.setError(e)}}getVideo(){return this.videoEl}async generateBitmap(){var e,t;return this.bitmapCanvas||(this.bitmapCanvas=new OffscreenCanvas(this.width,this.height),this.bitmapCanvas.width=this.width,this.bitmapCanvas.height=this.height,this.bitmapCtx=this.bitmapCanvas.getContext("2d")),null==(e=this.bitmapCtx)||e.fillRect(0,0,this.width,this.height),null==(t=this.bitmapCtx)||t.drawImage(this.videoEl,0,0,this.width,this.height),this.bitmap=await createImageBitmap(this.bitmapCanvas),this.bitmap}async play(){var e;if(this.videoEl.paused){if(te&&window.WeixinJSBridge&&await new Promise((e=>{window.WeixinJSBridge.invoke("getNetworkType",{},(()=>{e()}),(()=>{e()}))})),"visible"!==document.visibilityState){const e=()=>{"visible"===document.visibilityState&&(this.videoEl&&this.videoEl.play(),window.removeEventListener("visibilitychange",e))};throw window.addEventListener("visibilitychange",e),new Error("The play() request was interrupted because the document was hidden!")}await(null==(e=this.videoEl)?void 0:e.play())}}pause(){var e;this.isPlaying=!1,this.videoEl.paused||null==(e=this.videoEl)||e.pause()}stop(){var e;this.isPlaying=!1,this.videoEl.paused||null==(e=this.videoEl)||e.pause(),this.videoEl.currentTime=0}getError(){return this.error}onDestroy(){this.player&&this.player.unlinkVideoReader(this),Ne(this.videoEl,"playing"),Ne(this.videoEl,"timeupdate"),this.videoEl=null,this.bitmapCanvas=null,this.bitmapCtx=null}seek(e,t=!0){return new Promise((r=>{let n=!1,a=null;const i=async()=>{var e;if(t&&this.videoEl.paused)try{await this.play()}catch(e){this.setError(e)}else t||this.videoEl.paused||null==(e=this.videoEl)||e.pause()},o=async()=>{if(!this.videoEl)return this.setError(new Error("Video element doesn't exist!")),void r();Ve(this.videoEl,"seeked",o),await i(),n=!0,clearTimeout(a),a=null,r()};if(!this.videoEl)return this.setError(new Error("Video element doesn't exist!")),void r();je(this.videoEl,"seeked",o),this.videoEl.currentTime=e,a=setTimeout((()=>{if(!n){if(!this.videoEl)return this.setError(new Error("Video element doesn't exist!")),void r();Ve(this.videoEl,"seeked",o),i(),r()}}),1e3/this.frameRate*12)}))}setError(e){this.error=e}linkPlayer(e){this.player=e,e&&e.linkVideoReader(this)}}class Je{constructor(e){this.timeRanges=e}contains(e){if(0===this.timeRanges.length)return!1;for(let t of this.timeRanges)if(t.start<=e&&e<t.end)return!0;return!1}}const Ke=e=>{const t=new Int32Array(e.data.buffer);return{left:Qe(t,e.width,e.height),top:Ye(t,e.width,e.height),right:Ze(t,e.width,e.height),bottom:et(t,e.width,e.height)}},Qe=(e,t,r)=>{const n=e.length/t,a=e.length/r;for(let r=0;r<a;r++)for(let a=0;a<n;a++)if(0!==e[r+a*t])return r;return a},Ye=(e,t,r)=>{const n=e.length/t,a=e.length/r;for(let r=0;r<n;r++)for(let n=0;n<a;n++)if(0!==e[r*t+n])return r;return n},Ze=(e,t,r)=>{const n=e.length/t;for(let a=e.length/r-1;a>0;a--)for(let r=n-1;r>0;r--)if(0!==e[a+t*r])return a;return 0},et=(e,t,r)=>{const n=e.length/t,a=e.length/r;for(let r=n-1;r>0;r--)for(let n=a-1;n>0;n--)if(0!==e[r*t+n])return r;return 0};class tt{constructor(e,t,r,n=!1,a=!1){this.fontBoundingBoxMap=[],this.fontName=e,this.fontStyle=t,this.size=r,this.fauxBold=n,this.fauxItalic=a,this.loadCanvas()}static setCanvas(e){tt.canvas=e}static setContext(e){tt.context=e}static isUnicodePropertyEscapeSupported(){try{new RegExp("\\p{L}","u");return!0}catch(e){return!1}}static isEmoji(e){let t;return t=this.isUnicodePropertyEscapeSupported()?new RegExp("\\p{Extended_Pictographic}|[#*0-9]\\uFE0F?\\u20E3|[\\uD83C\\uDDE6-\\uD83C\\uDDFF]","u"):/(\u00a9|\u00ae|[\u2000-\u3300]|\ud83c[\ud000-\udfff]|\ud83d[\ud000-\udfff]|\ud83e[\ud000-\udfff])/,t.test(e)}fontString(){const e=[];this.fauxItalic&&e.push("italic"),this.fauxBold&&e.push("bold"),e.push(`${this.size}px`);const t=Le.concat();return t.unshift(...((e,t="")=>{if(!e)return[];const r=e.split(" ");let n=[];1===r.length?n.push(e):(n.push(r.join("")),n.push(r.join(" ")));const a=n.reduce(((e,r)=>(t?(e.push(`"${r} ${t}"`),e.push(`"${r}-${t}"`)):e.push(`"${r}"`),e)),[]);return""!==t&&a.push(`"${e}"`),a})(this.fontName,this.fontStyle)),e.push(`${t.join(",")}`),e.join(" ")}getTextAdvance(e){const{context:t}=tt;return t.font=this.fontString(),t.measureText(e).width}getTextBounds(e){const{context:t}=tt;t.font=this.fontString();const r=this.measureText(t,e);return{left:Math.floor(-r.actualBoundingBoxLeft),top:Math.floor(-r.actualBoundingBoxAscent),right:Math.ceil(r.actualBoundingBoxRight),bottom:Math.ceil(r.actualBoundingBoxDescent)}}generateFontMetrics(){const{context:e}=tt;e.font=this.fontString();const t=this.measureText(e,"中"),r=t.actualBoundingBoxAscent,n=this.measureText(e,"x").actualBoundingBoxAscent;return{ascent:-t.fontBoundingBoxAscent,descent:t.fontBoundingBoxDescent,xHeight:n,capHeight:r}}generateImage(e,t){const r=oe(t.right-t.left,t.bottom-t.top),n=r.getContext("2d");return n.font=this.fontString(),n.fillText(e,-t.left,-t.top),r}loadCanvas(){tt.canvas||(tt.setCanvas(oe(10,10)),tt.setContext(tt.canvas.getContext("2d",{willReadFrequently:!0})))}measureText(e,t){const r=e.measureText(t);if(null==r?void 0:r.actualBoundingBoxAscent)return r;e.canvas.width=1.5*this.size,e.canvas.height=1.5*this.size;const n=[0,this.size];e.clearRect(0,0,e.canvas.width,e.canvas.height),e.font=this.fontString(),e.fillText(t,n[0],n[1]);const a=e.getImageData(0,0,e.canvas.width,e.canvas.height),{left:i,top:o,right:s,bottom:l}=Ke(a);let u;e.clearRect(0,0,e.canvas.width,e.canvas.height);const c=this.fontBoundingBoxMap.find((e=>e.key===this.fontName));if(c)u=c.value;else{e.font=this.fontString(),e.fillText("测",n[0],n[1]);const t=e.getImageData(0,0,e.canvas.width,e.canvas.height);u=Ke(t),this.fontBoundingBoxMap.push({key:this.fontName,value:u}),e.clearRect(0,0,e.canvas.width,e.canvas.height)}return{actualBoundingBoxAscent:n[1]-o,actualBoundingBoxRight:s-n[0],actualBoundingBoxDescent:l-n[1],actualBoundingBoxLeft:n[0]-i,fontBoundingBoxAscent:u.bottom-u.top,fontBoundingBoxDescent:0,width:u.right-u.left}}}class rt{static create(e){return new rt(e)}static getLineCap(e){switch(e){case r.TGFXLineCap.Round:return"round";case r.TGFXLineCap.Square:return"square";default:return"butt"}}static getLineJoin(e){switch(e){case r.TGFXLineJoin.Round:return"round";case r.TGFXLineJoin.Bevel:return"bevel";default:return"miter"}}constructor(e){this.canvas=e,this.context=this.canvas.getContext("2d")}updateCanvas(e){this.canvas=e}fillPath(e,t){this.context.setTransform(1,0,0,1,0,0),t===r.TGFXPathFillType.InverseWinding||t===r.TGFXPathFillType.InverseEvenOdd?(this.context.clip(e,t===r.TGFXPathFillType.InverseEvenOdd?"evenodd":"nonzero"),this.context.fillRect(0,0,this.canvas.width,this.canvas.height)):this.context.fill(e,t===r.TGFXPathFillType.EvenOdd?"evenodd":"nonzero")}fillText(e,t,r,n){const a=new tt(e.name,e.style,e.size,e.bold,e.italic),i=new w(n);this.context.setTransform(i.a,i.b,i.c,i.d,i.tx,i.ty),this.context.font=a.fontString();for(let e=0;e<t.size();e++){const n=r.get(e);this.context.fillText(t.get(e),n.x,n.y)}}strokeText(e,t,r,n,a){if(t.width<.5)return;const i=new tt(e.name,e.style,e.size,e.bold,e.italic),o=new w(a);this.context.setTransform(o.a,o.b,o.c,o.d,o.tx,o.ty),this.context.font=i.fontString(),this.context.lineJoin=rt.getLineJoin(t.join),this.context.miterLimit=t.miterLimit,this.context.lineCap=rt.getLineCap(t.cap),this.context.lineWidth=t.width;for(let e=0;e<r.size();e++){const t=n.get(e);this.context.strokeText(r.get(e),t.x,t.y)}}clear(){this.context.clearRect(0,0,this.canvas.width,this.canvas.height)}}class nt{constructor(){this._canvas=null,this._glContext=null,this.width=J,this.height=J,this.retainCount=0}retain(){if(0===this.retainCount){try{this._canvas=new OffscreenCanvas(0,0)}catch(e){this._canvas=document.createElement("canvas")}this._canvas.width=this.width,this._canvas.height=this.height;const e=this._canvas.getContext("webgl",K);if(!e)throw new Error("Canvas context is not WebGL!");this._glContext=me.from(e)}this.retainCount+=1}release(){if(this.retainCount-=1,0===this.retainCount){if(!this._glContext)return;this._glContext.destroy(),this._glContext=null,this._canvas=null}}get canvas(){return this._canvas}get glContext(){return this._glContext}setCanvasSize(e=2560,t=2560){this.width=e,this.height=t,this._glContext&&this._canvas&&(this._canvas.width=e,this._canvas.height=t)}}var at=Object.defineProperty,it=Object.getOwnPropertyDescriptor;let ot=class extends A{static make(e,t,n=0,a="",i=""){return new ot("string"==typeof t?r._PAGTextLayer._Make(e,t,n,a,i):r._PAGTextLayer._Make(e,t))}fillColor(){return this.wasmIns._fillColor()}setFillColor(e){this.wasmIns._setFillColor(e)}font(){return new Be(this.wasmIns._font())}setFont(e){this.wasmIns._setFont(e.wasmIns)}fontSize(){return this.wasmIns._fontSize()}setFontSize(e){this.wasmIns._setFontSize(e)}strokeColor(){return this.wasmIns._strokeColor()}setStrokeColor(e){this.wasmIns._setStrokeColor(e)}text(){return this.wasmIns._text()}setText(e){this.wasmIns._setText(e)}reset(){this.wasmIns._reset()}};ot=((e,t,r,n)=>{for(var a,i=n>1?void 0:n?it(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&at(t,r,i),i})([i,n],ot);var st=Object.defineProperty,lt=Object.getOwnPropertyDescriptor;let ut=class extends A{static make(e,t,n){const a=r._PAGImageLayer._Make(e,t,n);if(!a)throw new Error("Make PAGImageLayer fail!");return new ut(a)}contentDuration(){return this.wasmIns._contentDuration()}getVideoRanges(){return this.wasmIns._getVideoRanges()}replaceImage(e){this.wasmIns._replaceImage(e.wasmIns)}setImage(e){this.wasmIns._setImage(e.wasmIns)}layerTimeToContent(e){return this.wasmIns._layerTimeToContent(e)}contentTimeToLayer(e){return this.wasmIns._contentTimeToLayer(e)}imageBytes(){return this.wasmIns._imageBytes()}};ut=((e,t,r,n)=>{for(var a,i=n>1?void 0:n?lt(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&st(t,r,i),i})([i,n],ut);var ct=Object.defineProperty,ft=Object.getOwnPropertyDescriptor;let dt=class extends A{static make(e,t,n,a,i){const o=r._PAGSolidLayer._Make(e,t,n,a,i);if(!o)throw new Error("Make PAGSolidLayer fail!");return new dt(o)}solidColor(){return this.wasmIns._solidColor()}setSolidColor(e){this.wasmIns._setSolidColor(e)}};dt=((e,t,r,n)=>{for(var a,i=n>1?void 0:n?ft(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&ct(t,r,i),i})([i,n],dt);const ht=e=>new Promise((t=>{const r=new Image;r.onload=function(){t(r)},r.onerror=function(){console.error("image create from bytes error."),t(null)},r.src=e}));var mt=Object.freeze({__proto__:null,createImage:ht,createImageFromBytes:e=>{const t=new Blob([e],{type:"image/*"});return ht(URL.createObjectURL(t))},readImagePixels:(e,t,r,n)=>{if(!t)return null;const a=oe(r,n),i=a.getContext("2d");if(!i)return null;i.drawImage(t,0,0,r,n);const{data:o}=i.getImageData(0,0,r,n);return se(a),0===o.length?null:D(e,o)},hasWebpSupport:()=>{try{return 0===document.createElement("canvas").toDataURL("image/webp",.5).indexOf("data:image/webp")}catch(e){return!1}},getSourceSize:e=>x(e,globalThis.HTMLVideoElement)?{width:e.videoWidth,height:e.videoHeight}:{width:e.width,height:e.height},uploadToTexture:(e,t,r,n)=>{var a;let i=t instanceof ze?t.bitmap:t;if(!i)return;const o=null==(a=e.currentContext)?void 0:a.GLctx;o.bindTexture(o.TEXTURE_2D,e.textures[r]),n?(o.pixelStorei(o.UNPACK_ALIGNMENT,1),o.texSubImage2D(o.TEXTURE_2D,0,0,0,o.ALPHA,o.UNSIGNED_BYTE,i)):(o.pixelStorei(o.UNPACK_ALIGNMENT,4),o.pixelStorei(o.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0),o.texSubImage2D(o.TEXTURE_2D,0,0,0,o.RGBA,o.UNSIGNED_BYTE,i),o.pixelStorei(o.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1))},isAndroidMiniprogram:()=>{if("undefined"!=typeof wx&&wx.getSystemInfoSync)return"android"===wx.getSystemInfoSync().platform},releaseNativeImage:e=>{var t;x(e,globalThis.ImageBitmap)?e.close():(ie(t=e)||x(t,globalThis.HTMLCanvasElement))&&se(e)},getBytesFromPath:async(e,t)=>{const r=await fetch(t).then((e=>e.arrayBuffer()));return D(e,r)},createCanvas2D:oe});var pt=Object.defineProperty,yt=Object.getOwnPropertySymbols,vt=Object.prototype.hasOwnProperty,gt=Object.prototype.propertyIsEnumerable,wt=(e,t,r)=>t in e?pt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;const _t=e=>{(e=>{r=e})(e),e.module=e,e.PAGFile=G,e.PAGPlayer=U,e.PAGView=De,e.PAGFont=Be,e.PAGImage=q,e.PAGLayer=A,e.PAGComposition=S,e.PAGSurface=j,e.PAGTextLayer=ot,e.PAGImageLayer=ut,e.PAGSolidLayer=dt,e.VideoReader=Xe,e.ScalerContext=tt,e.WebMask=rt,e.GlobalCanvas=nt,e.BackendContext=me,e.Matrix=w,e.RenderCanvas=Ee,(e=>{e.traceImage=function(e,t){const r=document.createElement("canvas");r.width=e.width,r.height=e.height;const n=r.getContext("2d"),a=new ImageData(new Uint8ClampedArray(t),r.width,r.height);n.putImageData(a,0,0),document.body.appendChild(r)},e.registerSoftwareDecoderFactory=function(t=null){e._registerSoftwareDecoderFactory(t)},e.SDKVersion=function(){return e._SDKVersion()},e.isIPhone=()=>ee})(e),e.tgfx=((e,t)=>{for(var r in t||(t={}))vt.call(t,r)&&wt(e,r,t[r]);if(yt)for(var r of yt(t))gt.call(t,r)&&wt(e,r,t[r]);return e})({},mt)};var bt,Et=(bt="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0,function(e){var t,r,n=void 0!==(e=e||{})?e:{};n.ready=new Promise((function(e,n){t=e,r=n}));var a=Object.assign({},n),i=(e,t)=>{throw t},o="object"==typeof window,s="function"==typeof importScripts;"object"==typeof process&&"object"==typeof process.versions&&process.versions.node;var l,u,c,f="";(o||s)&&(s?f=self.location.href:"undefined"!=typeof document&&document.currentScript&&(f=document.currentScript.src),bt&&(f=bt),f=0!==f.indexOf("blob:")?f.substr(0,f.replace(/[?#].*/,"").lastIndexOf("/")+1):"",l=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},s&&(c=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),u=(e,t,r)=>{var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=()=>{200==n.status||0==n.status&&n.response?t(n.response):r()},n.onerror=r,n.send(null)});var d=n.print||console.log.bind(console),h=n.printErr||console.warn.bind(console);Object.assign(n,a),a=null,n.arguments&&n.arguments,n.thisProgram&&n.thisProgram,n.quit&&(i=n.quit);var m,p,y=4;n.wasmBinary&&(m=n.wasmBinary),n.noExitRuntime,"object"!=typeof WebAssembly&&U("no native wasm support detected");var v,g=!1;function w(e,t){e||U(t)}var _,b,E,C,x,P,T,A,I,k="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function S(e,t,r){for(var n=t+r,a=t;e[a]&&!(a>=n);)++a;if(a-t>16&&e.buffer&&k)return k.decode(e.subarray(t,a));for(var i="";t<a;){var o=e[t++];if(128&o){var s=63&e[t++];if(192!=(224&o)){var l=63&e[t++];if((o=224==(240&o)?(15&o)<<12|s<<6|l:(7&o)<<18|s<<12|l<<6|63&e[t++])<65536)i+=String.fromCharCode(o);else{var u=o-65536;i+=String.fromCharCode(55296|u>>10,56320|1023&u)}}else i+=String.fromCharCode((31&o)<<6|s)}else i+=String.fromCharCode(o)}return i}function F(e,t){return e?S(E,e,t):""}function D(e,t,r,n){if(!(n>0))return 0;for(var a=r,i=r+n-1,o=0;o<e.length;++o){var s=e.charCodeAt(o);if(s>=55296&&s<=57343&&(s=65536+((1023&s)<<10)|1023&e.charCodeAt(++o)),s<=127){if(r>=i)break;t[r++]=s}else if(s<=2047){if(r+1>=i)break;t[r++]=192|s>>6,t[r++]=128|63&s}else if(s<=65535){if(r+2>=i)break;t[r++]=224|s>>12,t[r++]=128|s>>6&63,t[r++]=128|63&s}else{if(r+3>=i)break;t[r++]=240|s>>18,t[r++]=128|s>>12&63,t[r++]=128|s>>6&63,t[r++]=128|63&s}}return t[r]=0,r-a}function L(e,t,r){return D(e,E,t,r)}function M(e){for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n<=127?t++:n<=2047?t+=2:n>=55296&&n<=57343?(t+=4,++r):t+=3}return t}function R(e){_=e,n.HEAP8=b=new Int8Array(e),n.HEAP16=C=new Int16Array(e),n.HEAP32=P=new Int32Array(e),n.HEAPU8=E=new Uint8Array(e),n.HEAPU16=x=new Uint16Array(e),n.HEAPU32=T=new Uint32Array(e),n.HEAPF32=A=new Float32Array(e),n.HEAPF64=I=new Float64Array(e)}n.INITIAL_MEMORY;var G=[],B=[],O=[],j=0,V=null;function N(e){j++,n.monitorRunDependencies&&n.monitorRunDependencies(j)}function $(e){if(j--,n.monitorRunDependencies&&n.monitorRunDependencies(j),0==j&&V){var t=V;V=null,t()}}function U(e){n.onAbort&&n.onAbort(e),h(e="Aborted("+e+")"),g=!0,v=1,e+=". Build with -sASSERTIONS for more info.";var t=new WebAssembly.RuntimeError(e);throw r(t),t}var H,z,W,q,X="data:application/octet-stream;base64,";function J(e){return e.startsWith(X)}function K(e){try{if(e==H&&m)return new Uint8Array(m);if(c)return c(e);throw"both async and sync fetching of the wasm failed"}catch(e){U(e)}}function Q(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function Y(e){for(;e.length>0;)e.shift()(n)}function Z(e){this.excPtr=e,this.ptr=e-24,this.set_type=function(e){T[this.ptr+4>>2]=e},this.get_type=function(){return T[this.ptr+4>>2]},this.set_destructor=function(e){T[this.ptr+8>>2]=e},this.get_destructor=function(){return T[this.ptr+8>>2]},this.set_refcount=function(e){P[this.ptr>>2]=e},this.set_caught=function(e){e=e?1:0,b[this.ptr+12>>0]=e},this.get_caught=function(){return 0!=b[this.ptr+12>>0]},this.set_rethrown=function(e){e=e?1:0,b[this.ptr+13>>0]=e},this.get_rethrown=function(){return 0!=b[this.ptr+13>>0]},this.init=function(e,t){this.set_adjusted_ptr(0),this.set_type(e),this.set_destructor(t),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var e=P[this.ptr>>2];P[this.ptr>>2]=e+1},this.release_ref=function(){var e=P[this.ptr>>2];return P[this.ptr>>2]=e-1,1===e},this.set_adjusted_ptr=function(e){T[this.ptr+16>>2]=e},this.get_adjusted_ptr=function(){return T[this.ptr+16>>2]},this.get_exception_ptr=function(){if(Pr(this.get_type()))return T[this.excPtr>>2];var e=this.get_adjusted_ptr();return 0!==e?e:this.excPtr}}J(H="libpag.wasm")||(z=H,H=n.locateFile?n.locateFile(z,f):f+z);var ee={isAbs:e=>"/"===e.charAt(0),splitPath:e=>/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1),normalizeArray:(e,t)=>{for(var r=0,n=e.length-1;n>=0;n--){var a=e[n];"."===a?e.splice(n,1):".."===a?(e.splice(n,1),r++):r&&(e.splice(n,1),r--)}if(t)for(;r;r--)e.unshift("..");return e},normalize:e=>{var t=ee.isAbs(e),r="/"===e.substr(-1);return(e=ee.normalizeArray(e.split("/").filter((e=>!!e)),!t).join("/"))||t||(e="."),e&&r&&(e+="/"),(t?"/":"")+e},dirname:e=>{var t=ee.splitPath(e),r=t[0],n=t[1];return r||n?(n&&(n=n.substr(0,n.length-1)),r+n):"."},basename:e=>{if("/"===e)return"/";var t=(e=(e=ee.normalize(e)).replace(/\/$/,"")).lastIndexOf("/");return-1===t?e:e.substr(t+1)},join:function(){var e=Array.prototype.slice.call(arguments);return ee.normalize(e.join("/"))},join2:(e,t)=>ee.normalize(e+"/"+t)},te={resolve:function(){for(var e="",t=!1,r=arguments.length-1;r>=-1&&!t;r--){var n=r>=0?arguments[r]:oe.cwd();if("string"!=typeof n)throw new TypeError("Arguments to path.resolve must be strings");if(!n)return"";e=n+"/"+e,t=ee.isAbs(n)}return(t?"/":"")+(e=ee.normalizeArray(e.split("/").filter((e=>!!e)),!t).join("/"))||"."},relative:(e,t)=>{function r(e){for(var t=0;t<e.length&&""===e[t];t++);for(var r=e.length-1;r>=0&&""===e[r];r--);return t>r?[]:e.slice(t,r-t+1)}e=te.resolve(e).substr(1),t=te.resolve(t).substr(1);for(var n=r(e.split("/")),a=r(t.split("/")),i=Math.min(n.length,a.length),o=i,s=0;s<i;s++)if(n[s]!==a[s]){o=s;break}var l=[];for(s=o;s<n.length;s++)l.push("..");return(l=l.concat(a.slice(o))).join("/")}};function re(e,t,r){var n=r>0?r:M(e)+1,a=new Array(n),i=D(e,a,0,a.length);return t&&(a.length=i),a}var ne={ttys:[],init:function(){},shutdown:function(){},register:function(e,t){ne.ttys[e]={input:[],output:[],ops:t},oe.registerDevice(e,ne.stream_ops)},stream_ops:{open:function(e){var t=ne.ttys[e.node.rdev];if(!t)throw new oe.ErrnoError(43);e.tty=t,e.seekable=!1},close:function(e){e.tty.ops.fsync(e.tty)},fsync:function(e){e.tty.ops.fsync(e.tty)},read:function(e,t,r,n,a){if(!e.tty||!e.tty.ops.get_char)throw new oe.ErrnoError(60);for(var i=0,o=0;o<n;o++){var s;try{s=e.tty.ops.get_char(e.tty)}catch(e){throw new oe.ErrnoError(29)}if(void 0===s&&0===i)throw new oe.ErrnoError(6);if(null==s)break;i++,t[r+o]=s}return i&&(e.node.timestamp=Date.now()),i},write:function(e,t,r,n,a){if(!e.tty||!e.tty.ops.put_char)throw new oe.ErrnoError(60);try{for(var i=0;i<n;i++)e.tty.ops.put_char(e.tty,t[r+i])}catch(e){throw new oe.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),i}},default_tty_ops:{get_char:function(e){if(!e.input.length){var t=null;if("undefined"!=typeof window&&"function"==typeof window.prompt?null!==(t=window.prompt("Input: "))&&(t+="\n"):"function"==typeof readline&&null!==(t=readline())&&(t+="\n"),!t)return null;e.input=re(t,!0)}return e.input.shift()},put_char:function(e,t){null===t||10===t?(d(S(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},fsync:function(e){e.output&&e.output.length>0&&(d(S(e.output,0)),e.output=[])}},default_tty1_ops:{put_char:function(e,t){null===t||10===t?(h(S(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},fsync:function(e){e.output&&e.output.length>0&&(h(S(e.output,0)),e.output=[])}}};function ae(e){U()}var ie={ops_table:null,mount:function(e){return ie.createNode(null,"/",16895,0)},createNode:function(e,t,r,n){if(oe.isBlkdev(r)||oe.isFIFO(r))throw new oe.ErrnoError(63);ie.ops_table||(ie.ops_table={dir:{node:{getattr:ie.node_ops.getattr,setattr:ie.node_ops.setattr,lookup:ie.node_ops.lookup,mknod:ie.node_ops.mknod,rename:ie.node_ops.rename,unlink:ie.node_ops.unlink,rmdir:ie.node_ops.rmdir,readdir:ie.node_ops.readdir,symlink:ie.node_ops.symlink},stream:{llseek:ie.stream_ops.llseek}},file:{node:{getattr:ie.node_ops.getattr,setattr:ie.node_ops.setattr},stream:{llseek:ie.stream_ops.llseek,read:ie.stream_ops.read,write:ie.stream_ops.write,allocate:ie.stream_ops.allocate,mmap:ie.stream_ops.mmap,msync:ie.stream_ops.msync}},link:{node:{getattr:ie.node_ops.getattr,setattr:ie.node_ops.setattr,readlink:ie.node_ops.readlink},stream:{}},chrdev:{node:{getattr:ie.node_ops.getattr,setattr:ie.node_ops.setattr},stream:oe.chrdev_stream_ops}});var a=oe.createNode(e,t,r,n);return oe.isDir(a.mode)?(a.node_ops=ie.ops_table.dir.node,a.stream_ops=ie.ops_table.dir.stream,a.contents={}):oe.isFile(a.mode)?(a.node_ops=ie.ops_table.file.node,a.stream_ops=ie.ops_table.file.stream,a.usedBytes=0,a.contents=null):oe.isLink(a.mode)?(a.node_ops=ie.ops_table.link.node,a.stream_ops=ie.ops_table.link.stream):oe.isChrdev(a.mode)&&(a.node_ops=ie.ops_table.chrdev.node,a.stream_ops=ie.ops_table.chrdev.stream),a.timestamp=Date.now(),e&&(e.contents[t]=a,e.timestamp=a.timestamp),a},getFileDataAsTypedArray:function(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0)},expandFileStorage:function(e,t){var r=e.contents?e.contents.length:0;if(!(r>=t)){t=Math.max(t,r*(r<1048576?2:1.125)>>>0),0!=r&&(t=Math.max(t,256));var n=e.contents;e.contents=new Uint8Array(t),e.usedBytes>0&&e.contents.set(n.subarray(0,e.usedBytes),0)}},resizeFileStorage:function(e,t){if(e.usedBytes!=t)if(0==t)e.contents=null,e.usedBytes=0;else{var r=e.contents;e.contents=new Uint8Array(t),r&&e.contents.set(r.subarray(0,Math.min(t,e.usedBytes))),e.usedBytes=t}},node_ops:{getattr:function(e){var t={};return t.dev=oe.isChrdev(e.mode)?e.id:1,t.ino=e.id,t.mode=e.mode,t.nlink=1,t.uid=0,t.gid=0,t.rdev=e.rdev,oe.isDir(e.mode)?t.size=4096:oe.isFile(e.mode)?t.size=e.usedBytes:oe.isLink(e.mode)?t.size=e.link.length:t.size=0,t.atime=new Date(e.timestamp),t.mtime=new Date(e.timestamp),t.ctime=new Date(e.timestamp),t.blksize=4096,t.blocks=Math.ceil(t.size/t.blksize),t},setattr:function(e,t){void 0!==t.mode&&(e.mode=t.mode),void 0!==t.timestamp&&(e.timestamp=t.timestamp),void 0!==t.size&&ie.resizeFileStorage(e,t.size)},lookup:function(e,t){throw oe.genericErrors[44]},mknod:function(e,t,r,n){return ie.createNode(e,t,r,n)},rename:function(e,t,r){if(oe.isDir(e.mode)){var n;try{n=oe.lookupNode(t,r)}catch(e){}if(n)for(var a in n.contents)throw new oe.ErrnoError(55)}delete e.parent.contents[e.name],e.parent.timestamp=Date.now(),e.name=r,t.contents[r]=e,t.timestamp=e.parent.timestamp,e.parent=t},unlink:function(e,t){delete e.contents[t],e.timestamp=Date.now()},rmdir:function(e,t){var r=oe.lookupNode(e,t);for(var n in r.contents)throw new oe.ErrnoError(55);delete e.contents[t],e.timestamp=Date.now()},readdir:function(e){var t=[".",".."];for(var r in e.contents)e.contents.hasOwnProperty(r)&&t.push(r);return t},symlink:function(e,t,r){var n=ie.createNode(e,t,41471,0);return n.link=r,n},readlink:function(e){if(!oe.isLink(e.mode))throw new oe.ErrnoError(28);return e.link}},stream_ops:{read:function(e,t,r,n,a){var i=e.node.contents;if(a>=e.node.usedBytes)return 0;var o=Math.min(e.node.usedBytes-a,n);if(o>8&&i.subarray)t.set(i.subarray(a,a+o),r);else for(var s=0;s<o;s++)t[r+s]=i[a+s];return o},write:function(e,t,r,n,a,i){if(t.buffer===b.buffer&&(i=!1),!n)return 0;var o=e.node;if(o.timestamp=Date.now(),t.subarray&&(!o.contents||o.contents.subarray)){if(i)return o.contents=t.subarray(r,r+n),o.usedBytes=n,n;if(0===o.usedBytes&&0===a)return o.contents=t.slice(r,r+n),o.usedBytes=n,n;if(a+n<=o.usedBytes)return o.contents.set(t.subarray(r,r+n),a),n}if(ie.expandFileStorage(o,a+n),o.contents.subarray&&t.subarray)o.contents.set(t.subarray(r,r+n),a);else for(var s=0;s<n;s++)o.contents[a+s]=t[r+s];return o.usedBytes=Math.max(o.usedBytes,a+n),n},llseek:function(e,t,r){var n=t;if(1===r?n+=e.position:2===r&&oe.isFile(e.node.mode)&&(n+=e.node.usedBytes),n<0)throw new oe.ErrnoError(28);return n},allocate:function(e,t,r){ie.expandFileStorage(e.node,t+r),e.node.usedBytes=Math.max(e.node.usedBytes,t+r)},mmap:function(e,t,r,n,a){if(!oe.isFile(e.node.mode))throw new oe.ErrnoError(43);var i,o,s=e.node.contents;if(2&a||s.buffer!==_){if((r>0||r+t<s.length)&&(s=s.subarray?s.subarray(r,r+t):Array.prototype.slice.call(s,r,r+t)),o=!0,!(i=ae()))throw new oe.ErrnoError(48);b.set(s,i)}else o=!1,i=s.byteOffset;return{ptr:i,allocated:o}},msync:function(e,t,r,n,a){if(!oe.isFile(e.node.mode))throw new oe.ErrnoError(43);return 2&a||ie.stream_ops.write(e,t,0,n,r,!1),0}}},oe={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:(e,t={})=>{if(!(e=te.resolve(oe.cwd(),e)))return{path:"",node:null};if((t=Object.assign({follow_mount:!0,recurse_count:0},t)).recurse_count>8)throw new oe.ErrnoError(32);for(var r=ee.normalizeArray(e.split("/").filter((e=>!!e)),!1),n=oe.root,a="/",i=0;i<r.length;i++){var o=i===r.length-1;if(o&&t.parent)break;if(n=oe.lookupNode(n,r[i]),a=ee.join2(a,r[i]),oe.isMountpoint(n)&&(!o||o&&t.follow_mount)&&(n=n.mounted.root),!o||t.follow)for(var s=0;oe.isLink(n.mode);){var l=oe.readlink(a);if(a=te.resolve(ee.dirname(a),l),n=oe.lookupPath(a,{recurse_count:t.recurse_count+1}).node,s++>40)throw new oe.ErrnoError(32)}}return{path:a,node:n}},getPath:e=>{for(var t;;){if(oe.isRoot(e)){var r=e.mount.mountpoint;return t?"/"!==r[r.length-1]?r+"/"+t:r+t:r}t=t?e.name+"/"+t:e.name,e=e.parent}},hashName:(e,t)=>{for(var r=0,n=0;n<t.length;n++)r=(r<<5)-r+t.charCodeAt(n)|0;return(e+r>>>0)%oe.nameTable.length},hashAddNode:e=>{var t=oe.hashName(e.parent.id,e.name);e.name_next=oe.nameTable[t],oe.nameTable[t]=e},hashRemoveNode:e=>{var t=oe.hashName(e.parent.id,e.name);if(oe.nameTable[t]===e)oe.nameTable[t]=e.name_next;else for(var r=oe.nameTable[t];r;){if(r.name_next===e){r.name_next=e.name_next;break}r=r.name_next}},lookupNode:(e,t)=>{var r=oe.mayLookup(e);if(r)throw new oe.ErrnoError(r,e);for(var n=oe.hashName(e.id,t),a=oe.nameTable[n];a;a=a.name_next){var i=a.name;if(a.parent.id===e.id&&i===t)return a}return oe.lookup(e,t)},createNode:(e,t,r,n)=>{var a=new oe.FSNode(e,t,r,n);return oe.hashAddNode(a),a},destroyNode:e=>{oe.hashRemoveNode(e)},isRoot:e=>e===e.parent,isMountpoint:e=>!!e.mounted,isFile:e=>32768==(61440&e),isDir:e=>16384==(61440&e),isLink:e=>40960==(61440&e),isChrdev:e=>8192==(61440&e),isBlkdev:e=>24576==(61440&e),isFIFO:e=>4096==(61440&e),isSocket:e=>49152==(49152&e),flagModes:{r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090},modeStringToFlags:e=>{var t=oe.flagModes[e];if(void 0===t)throw new Error("Unknown file open mode: "+e);return t},flagsToPermissionString:e=>{var t=["r","w","rw"][3&e];return 512&e&&(t+="w"),t},nodePermissions:(e,t)=>oe.ignorePermissions||(!t.includes("r")||292&e.mode)&&(!t.includes("w")||146&e.mode)&&(!t.includes("x")||73&e.mode)?0:2,mayLookup:e=>{var t=oe.nodePermissions(e,"x");return t||(e.node_ops.lookup?0:2)},mayCreate:(e,t)=>{try{return oe.lookupNode(e,t),20}catch(e){}return oe.nodePermissions(e,"wx")},mayDelete:(e,t,r)=>{var n;try{n=oe.lookupNode(e,t)}catch(e){return e.errno}var a=oe.nodePermissions(e,"wx");if(a)return a;if(r){if(!oe.isDir(n.mode))return 54;if(oe.isRoot(n)||oe.getPath(n)===oe.cwd())return 10}else if(oe.isDir(n.mode))return 31;return 0},mayOpen:(e,t)=>e?oe.isLink(e.mode)?32:oe.isDir(e.mode)&&("r"!==oe.flagsToPermissionString(t)||512&t)?31:oe.nodePermissions(e,oe.flagsToPermissionString(t)):44,MAX_OPEN_FDS:4096,nextfd:(e=0,t=oe.MAX_OPEN_FDS)=>{for(var r=e;r<=t;r++)if(!oe.streams[r])return r;throw new oe.ErrnoError(33)},getStream:e=>oe.streams[e],createStream:(e,t,r)=>{oe.FSStream||(oe.FSStream=function(){this.shared={}},oe.FSStream.prototype={},Object.defineProperties(oe.FSStream.prototype,{object:{get:function(){return this.node},set:function(e){this.node=e}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return 0!=(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}},flags:{get:function(){return this.shared.flags},set:function(e){this.shared.flags=e}},position:{get:function(){return this.shared.position},set:function(e){this.shared.position=e}}})),e=Object.assign(new oe.FSStream,e);var n=oe.nextfd(t,r);return e.fd=n,oe.streams[n]=e,e},closeStream:e=>{oe.streams[e]=null},chrdev_stream_ops:{open:e=>{var t=oe.getDevice(e.node.rdev);e.stream_ops=t.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:()=>{throw new oe.ErrnoError(70)}},major:e=>e>>8,minor:e=>255&e,makedev:(e,t)=>e<<8|t,registerDevice:(e,t)=>{oe.devices[e]={stream_ops:t}},getDevice:e=>oe.devices[e],getMounts:e=>{for(var t=[],r=[e];r.length;){var n=r.pop();t.push(n),r.push.apply(r,n.mounts)}return t},syncfs:(e,t)=>{"function"==typeof e&&(t=e,e=!1),oe.syncFSRequests++,oe.syncFSRequests>1&&h("warning: "+oe.syncFSRequests+" FS.syncfs operations in flight at once, probably just doing extra work");var r=oe.getMounts(oe.root.mount),n=0;function a(e){return oe.syncFSRequests--,t(e)}function i(e){if(e)return i.errored?void 0:(i.errored=!0,a(e));++n>=r.length&&a(null)}r.forEach((t=>{if(!t.type.syncfs)return i(null);t.type.syncfs(t,e,i)}))},mount:(e,t,r)=>{var n,a="/"===r,i=!r;if(a&&oe.root)throw new oe.ErrnoError(10);if(!a&&!i){var o=oe.lookupPath(r,{follow_mount:!1});if(r=o.path,n=o.node,oe.isMountpoint(n))throw new oe.ErrnoError(10);if(!oe.isDir(n.mode))throw new oe.ErrnoError(54)}var s={type:e,opts:t,mountpoint:r,mounts:[]},l=e.mount(s);return l.mount=s,s.root=l,a?oe.root=l:n&&(n.mounted=s,n.mount&&n.mount.mounts.push(s)),l},unmount:e=>{var t=oe.lookupPath(e,{follow_mount:!1});if(!oe.isMountpoint(t.node))throw new oe.ErrnoError(28);var r=t.node,n=r.mounted,a=oe.getMounts(n);Object.keys(oe.nameTable).forEach((e=>{for(var t=oe.nameTable[e];t;){var r=t.name_next;a.includes(t.mount)&&oe.destroyNode(t),t=r}})),r.mounted=null;var i=r.mount.mounts.indexOf(n);r.mount.mounts.splice(i,1)},lookup:(e,t)=>e.node_ops.lookup(e,t),mknod:(e,t,r)=>{var n=oe.lookupPath(e,{parent:!0}).node,a=ee.basename(e);if(!a||"."===a||".."===a)throw new oe.ErrnoError(28);var i=oe.mayCreate(n,a);if(i)throw new oe.ErrnoError(i);if(!n.node_ops.mknod)throw new oe.ErrnoError(63);return n.node_ops.mknod(n,a,t,r)},create:(e,t)=>(t=void 0!==t?t:438,t&=4095,t|=32768,oe.mknod(e,t,0)),mkdir:(e,t)=>(t=void 0!==t?t:511,t&=1023,t|=16384,oe.mknod(e,t,0)),mkdirTree:(e,t)=>{for(var r=e.split("/"),n="",a=0;a<r.length;++a)if(r[a]){n+="/"+r[a];try{oe.mkdir(n,t)}catch(e){if(20!=e.errno)throw e}}},mkdev:(e,t,r)=>(void 0===r&&(r=t,t=438),t|=8192,oe.mknod(e,t,r)),symlink:(e,t)=>{if(!te.resolve(e))throw new oe.ErrnoError(44);var r=oe.lookupPath(t,{parent:!0}).node;if(!r)throw new oe.ErrnoError(44);var n=ee.basename(t),a=oe.mayCreate(r,n);if(a)throw new oe.ErrnoError(a);if(!r.node_ops.symlink)throw new oe.ErrnoError(63);return r.node_ops.symlink(r,n,e)},rename:(e,t)=>{var r,n,a=ee.dirname(e),i=ee.dirname(t),o=ee.basename(e),s=ee.basename(t);if(r=oe.lookupPath(e,{parent:!0}).node,n=oe.lookupPath(t,{parent:!0}).node,!r||!n)throw new oe.ErrnoError(44);if(r.mount!==n.mount)throw new oe.ErrnoError(75);var l,u=oe.lookupNode(r,o),c=te.relative(e,i);if("."!==c.charAt(0))throw new oe.ErrnoError(28);if("."!==(c=te.relative(t,a)).charAt(0))throw new oe.ErrnoError(55);try{l=oe.lookupNode(n,s)}catch(e){}if(u!==l){var f=oe.isDir(u.mode),d=oe.mayDelete(r,o,f);if(d)throw new oe.ErrnoError(d);if(d=l?oe.mayDelete(n,s,f):oe.mayCreate(n,s))throw new oe.ErrnoError(d);if(!r.node_ops.rename)throw new oe.ErrnoError(63);if(oe.isMountpoint(u)||l&&oe.isMountpoint(l))throw new oe.ErrnoError(10);if(n!==r&&(d=oe.nodePermissions(r,"w")))throw new oe.ErrnoError(d);oe.hashRemoveNode(u);try{r.node_ops.rename(u,n,s)}catch(e){throw e}finally{oe.hashAddNode(u)}}},rmdir:e=>{var t=oe.lookupPath(e,{parent:!0}).node,r=ee.basename(e),n=oe.lookupNode(t,r),a=oe.mayDelete(t,r,!0);if(a)throw new oe.ErrnoError(a);if(!t.node_ops.rmdir)throw new oe.ErrnoError(63);if(oe.isMountpoint(n))throw new oe.ErrnoError(10);t.node_ops.rmdir(t,r),oe.destroyNode(n)},readdir:e=>{var t=oe.lookupPath(e,{follow:!0}).node;if(!t.node_ops.readdir)throw new oe.ErrnoError(54);return t.node_ops.readdir(t)},unlink:e=>{var t=oe.lookupPath(e,{parent:!0}).node;if(!t)throw new oe.ErrnoError(44);var r=ee.basename(e),n=oe.lookupNode(t,r),a=oe.mayDelete(t,r,!1);if(a)throw new oe.ErrnoError(a);if(!t.node_ops.unlink)throw new oe.ErrnoError(63);if(oe.isMountpoint(n))throw new oe.ErrnoError(10);t.node_ops.unlink(t,r),oe.destroyNode(n)},readlink:e=>{var t=oe.lookupPath(e).node;if(!t)throw new oe.ErrnoError(44);if(!t.node_ops.readlink)throw new oe.ErrnoError(28);return te.resolve(oe.getPath(t.parent),t.node_ops.readlink(t))},stat:(e,t)=>{var r=oe.lookupPath(e,{follow:!t}).node;if(!r)throw new oe.ErrnoError(44);if(!r.node_ops.getattr)throw new oe.ErrnoError(63);return r.node_ops.getattr(r)},lstat:e=>oe.stat(e,!0),chmod:(e,t,r)=>{var n;if(!(n="string"==typeof e?oe.lookupPath(e,{follow:!r}).node:e).node_ops.setattr)throw new oe.ErrnoError(63);n.node_ops.setattr(n,{mode:4095&t|-4096&n.mode,timestamp:Date.now()})},lchmod:(e,t)=>{oe.chmod(e,t,!0)},fchmod:(e,t)=>{var r=oe.getStream(e);if(!r)throw new oe.ErrnoError(8);oe.chmod(r.node,t)},chown:(e,t,r,n)=>{var a;if(!(a="string"==typeof e?oe.lookupPath(e,{follow:!n}).node:e).node_ops.setattr)throw new oe.ErrnoError(63);a.node_ops.setattr(a,{timestamp:Date.now()})},lchown:(e,t,r)=>{oe.chown(e,t,r,!0)},fchown:(e,t,r)=>{var n=oe.getStream(e);if(!n)throw new oe.ErrnoError(8);oe.chown(n.node,t,r)},truncate:(e,t)=>{if(t<0)throw new oe.ErrnoError(28);var r;if(!(r="string"==typeof e?oe.lookupPath(e,{follow:!0}).node:e).node_ops.setattr)throw new oe.ErrnoError(63);if(oe.isDir(r.mode))throw new oe.ErrnoError(31);if(!oe.isFile(r.mode))throw new oe.ErrnoError(28);var n=oe.nodePermissions(r,"w");if(n)throw new oe.ErrnoError(n);r.node_ops.setattr(r,{size:t,timestamp:Date.now()})},ftruncate:(e,t)=>{var r=oe.getStream(e);if(!r)throw new oe.ErrnoError(8);if(0==(2097155&r.flags))throw new oe.ErrnoError(28);oe.truncate(r.node,t)},utime:(e,t,r)=>{var n=oe.lookupPath(e,{follow:!0}).node;n.node_ops.setattr(n,{timestamp:Math.max(t,r)})},open:(e,t,r)=>{if(""===e)throw new oe.ErrnoError(44);var a;if(r=void 0===r?438:r,r=64&(t="string"==typeof t?oe.modeStringToFlags(t):t)?4095&r|32768:0,"object"==typeof e)a=e;else{e=ee.normalize(e);try{a=oe.lookupPath(e,{follow:!(131072&t)}).node}catch(e){}}var i=!1;if(64&t)if(a){if(128&t)throw new oe.ErrnoError(20)}else a=oe.mknod(e,r,0),i=!0;if(!a)throw new oe.ErrnoError(44);if(oe.isChrdev(a.mode)&&(t&=-513),65536&t&&!oe.isDir(a.mode))throw new oe.ErrnoError(54);if(!i){var o=oe.mayOpen(a,t);if(o)throw new oe.ErrnoError(o)}512&t&&!i&&oe.truncate(a,0),t&=-131713;var s=oe.createStream({node:a,path:oe.getPath(a),flags:t,seekable:!0,position:0,stream_ops:a.stream_ops,ungotten:[],error:!1});return s.stream_ops.open&&s.stream_ops.open(s),!n.logReadFiles||1&t||(oe.readFiles||(oe.readFiles={}),e in oe.readFiles||(oe.readFiles[e]=1)),s},close:e=>{if(oe.isClosed(e))throw new oe.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{oe.closeStream(e.fd)}e.fd=null},isClosed:e=>null===e.fd,llseek:(e,t,r)=>{if(oe.isClosed(e))throw new oe.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new oe.ErrnoError(70);if(0!=r&&1!=r&&2!=r)throw new oe.ErrnoError(28);return e.position=e.stream_ops.llseek(e,t,r),e.ungotten=[],e.position},read:(e,t,r,n,a)=>{if(n<0||a<0)throw new oe.ErrnoError(28);if(oe.isClosed(e))throw new oe.ErrnoError(8);if(1==(2097155&e.flags))throw new oe.ErrnoError(8);if(oe.isDir(e.node.mode))throw new oe.ErrnoError(31);if(!e.stream_ops.read)throw new oe.ErrnoError(28);var i=void 0!==a;if(i){if(!e.seekable)throw new oe.ErrnoError(70)}else a=e.position;var o=e.stream_ops.read(e,t,r,n,a);return i||(e.position+=o),o},write:(e,t,r,n,a,i)=>{if(n<0||a<0)throw new oe.ErrnoError(28);if(oe.isClosed(e))throw new oe.ErrnoError(8);if(0==(2097155&e.flags))throw new oe.ErrnoError(8);if(oe.isDir(e.node.mode))throw new oe.ErrnoError(31);if(!e.stream_ops.write)throw new oe.ErrnoError(28);e.seekable&&1024&e.flags&&oe.llseek(e,0,2);var o=void 0!==a;if(o){if(!e.seekable)throw new oe.ErrnoError(70)}else a=e.position;var s=e.stream_ops.write(e,t,r,n,a,i);return o||(e.position+=s),s},allocate:(e,t,r)=>{if(oe.isClosed(e))throw new oe.ErrnoError(8);if(t<0||r<=0)throw new oe.ErrnoError(28);if(0==(2097155&e.flags))throw new oe.ErrnoError(8);if(!oe.isFile(e.node.mode)&&!oe.isDir(e.node.mode))throw new oe.ErrnoError(43);if(!e.stream_ops.allocate)throw new oe.ErrnoError(138);e.stream_ops.allocate(e,t,r)},mmap:(e,t,r,n,a)=>{if(0!=(2&n)&&0==(2&a)&&2!=(2097155&e.flags))throw new oe.ErrnoError(2);if(1==(2097155&e.flags))throw new oe.ErrnoError(2);if(!e.stream_ops.mmap)throw new oe.ErrnoError(43);return e.stream_ops.mmap(e,t,r,n,a)},msync:(e,t,r,n,a)=>e&&e.stream_ops.msync?e.stream_ops.msync(e,t,r,n,a):0,munmap:e=>0,ioctl:(e,t,r)=>{if(!e.stream_ops.ioctl)throw new oe.ErrnoError(59);return e.stream_ops.ioctl(e,t,r)},readFile:(e,t={})=>{if(t.flags=t.flags||0,t.encoding=t.encoding||"binary","utf8"!==t.encoding&&"binary"!==t.encoding)throw new Error('Invalid encoding type "'+t.encoding+'"');var r,n=oe.open(e,t.flags),a=oe.stat(e).size,i=new Uint8Array(a);return oe.read(n,i,0,a,0),"utf8"===t.encoding?r=S(i,0):"binary"===t.encoding&&(r=i),oe.close(n),r},writeFile:(e,t,r={})=>{r.flags=r.flags||577;var n=oe.open(e,r.flags,r.mode);if("string"==typeof t){var a=new Uint8Array(M(t)+1),i=D(t,a,0,a.length);oe.write(n,a,0,i,void 0,r.canOwn)}else{if(!ArrayBuffer.isView(t))throw new Error("Unsupported data type");oe.write(n,t,0,t.byteLength,void 0,r.canOwn)}oe.close(n)},cwd:()=>oe.currentPath,chdir:e=>{var t=oe.lookupPath(e,{follow:!0});if(null===t.node)throw new oe.ErrnoError(44);if(!oe.isDir(t.node.mode))throw new oe.ErrnoError(54);var r=oe.nodePermissions(t.node,"x");if(r)throw new oe.ErrnoError(r);oe.currentPath=t.path},createDefaultDirectories:()=>{oe.mkdir("/tmp"),oe.mkdir("/home"),oe.mkdir("/home/<USER>")},createDefaultDevices:()=>{oe.mkdir("/dev"),oe.registerDevice(oe.makedev(1,3),{read:()=>0,write:(e,t,r,n,a)=>n}),oe.mkdev("/dev/null",oe.makedev(1,3)),ne.register(oe.makedev(5,0),ne.default_tty_ops),ne.register(oe.makedev(6,0),ne.default_tty1_ops),oe.mkdev("/dev/tty",oe.makedev(5,0)),oe.mkdev("/dev/tty1",oe.makedev(6,0));var e=function(){if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues){var e=new Uint8Array(1);return()=>(crypto.getRandomValues(e),e[0])}return()=>U("randomDevice")}();oe.createDevice("/dev","random",e),oe.createDevice("/dev","urandom",e),oe.mkdir("/dev/shm"),oe.mkdir("/dev/shm/tmp")},createSpecialDirectories:()=>{oe.mkdir("/proc");var e=oe.mkdir("/proc/self");oe.mkdir("/proc/self/fd"),oe.mount({mount:()=>{var t=oe.createNode(e,"fd",16895,73);return t.node_ops={lookup:(e,t)=>{var r=+t,n=oe.getStream(r);if(!n)throw new oe.ErrnoError(8);var a={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>n.path}};return a.parent=a,a}},t}},{},"/proc/self/fd")},createStandardStreams:()=>{n.stdin?oe.createDevice("/dev","stdin",n.stdin):oe.symlink("/dev/tty","/dev/stdin"),n.stdout?oe.createDevice("/dev","stdout",null,n.stdout):oe.symlink("/dev/tty","/dev/stdout"),n.stderr?oe.createDevice("/dev","stderr",null,n.stderr):oe.symlink("/dev/tty1","/dev/stderr"),oe.open("/dev/stdin",0),oe.open("/dev/stdout",1),oe.open("/dev/stderr",1)},ensureErrnoError:()=>{oe.ErrnoError||(oe.ErrnoError=function(e,t){this.node=t,this.setErrno=function(e){this.errno=e},this.setErrno(e),this.message="FS error"},oe.ErrnoError.prototype=new Error,oe.ErrnoError.prototype.constructor=oe.ErrnoError,[44].forEach((e=>{oe.genericErrors[e]=new oe.ErrnoError(e),oe.genericErrors[e].stack="<generic error, no stack>"})))},staticInit:()=>{oe.ensureErrnoError(),oe.nameTable=new Array(4096),oe.mount(ie,{},"/"),oe.createDefaultDirectories(),oe.createDefaultDevices(),oe.createSpecialDirectories(),oe.filesystems={MEMFS:ie}},init:(e,t,r)=>{oe.init.initialized=!0,oe.ensureErrnoError(),n.stdin=e||n.stdin,n.stdout=t||n.stdout,n.stderr=r||n.stderr,oe.createStandardStreams()},quit:()=>{oe.init.initialized=!1;for(var e=0;e<oe.streams.length;e++){var t=oe.streams[e];t&&oe.close(t)}},getMode:(e,t)=>{var r=0;return e&&(r|=365),t&&(r|=146),r},findObject:(e,t)=>{var r=oe.analyzePath(e,t);return r.exists?r.object:null},analyzePath:(e,t)=>{try{e=(n=oe.lookupPath(e,{follow:!t})).path}catch(e){}var r={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var n=oe.lookupPath(e,{parent:!0});r.parentExists=!0,r.parentPath=n.path,r.parentObject=n.node,r.name=ee.basename(e),n=oe.lookupPath(e,{follow:!t}),r.exists=!0,r.path=n.path,r.object=n.node,r.name=n.node.name,r.isRoot="/"===n.path}catch(e){r.error=e.errno}return r},createPath:(e,t,r,n)=>{e="string"==typeof e?e:oe.getPath(e);for(var a=t.split("/").reverse();a.length;){var i=a.pop();if(i){var o=ee.join2(e,i);try{oe.mkdir(o)}catch(e){}e=o}}return o},createFile:(e,t,r,n,a)=>{var i=ee.join2("string"==typeof e?e:oe.getPath(e),t),o=oe.getMode(n,a);return oe.create(i,o)},createDataFile:(e,t,r,n,a,i)=>{var o=t;e&&(e="string"==typeof e?e:oe.getPath(e),o=t?ee.join2(e,t):e);var s=oe.getMode(n,a),l=oe.create(o,s);if(r){if("string"==typeof r){for(var u=new Array(r.length),c=0,f=r.length;c<f;++c)u[c]=r.charCodeAt(c);r=u}oe.chmod(l,146|s);var d=oe.open(l,577);oe.write(d,r,0,r.length,0,i),oe.close(d),oe.chmod(l,s)}return l},createDevice:(e,t,r,n)=>{var a=ee.join2("string"==typeof e?e:oe.getPath(e),t),i=oe.getMode(!!r,!!n);oe.createDevice.major||(oe.createDevice.major=64);var o=oe.makedev(oe.createDevice.major++,0);return oe.registerDevice(o,{open:e=>{e.seekable=!1},close:e=>{n&&n.buffer&&n.buffer.length&&n(10)},read:(e,t,n,a,i)=>{for(var o=0,s=0;s<a;s++){var l;try{l=r()}catch(e){throw new oe.ErrnoError(29)}if(void 0===l&&0===o)throw new oe.ErrnoError(6);if(null==l)break;o++,t[n+s]=l}return o&&(e.node.timestamp=Date.now()),o},write:(e,t,r,a,i)=>{for(var o=0;o<a;o++)try{n(t[r+o])}catch(e){throw new oe.ErrnoError(29)}return a&&(e.node.timestamp=Date.now()),o}}),oe.mkdev(a,i,o)},forceLoadFile:e=>{if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!l)throw new Error("Cannot load without read() or XMLHttpRequest.");try{e.contents=re(l(e.url),!0),e.usedBytes=e.contents.length}catch(e){throw new oe.ErrnoError(29)}},createLazyFile:(e,t,r,n,a)=>{function i(){this.lengthKnown=!1,this.chunks=[]}if(i.prototype.get=function(e){if(!(e>this.length-1||e<0)){var t=e%this.chunkSize,r=e/this.chunkSize|0;return this.getter(r)[t]}},i.prototype.setDataGetter=function(e){this.getter=e},i.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",r,!1),e.send(null),!(e.status>=200&&e.status<300||304===e.status))throw new Error("Couldn't load "+r+". Status: "+e.status);var t,n=Number(e.getResponseHeader("Content-length")),a=(t=e.getResponseHeader("Accept-Ranges"))&&"bytes"===t,i=(t=e.getResponseHeader("Content-Encoding"))&&"gzip"===t,o=1048576;a||(o=n);var s=this;s.setDataGetter((e=>{var t=e*o,a=(e+1)*o-1;if(a=Math.min(a,n-1),void 0===s.chunks[e]&&(s.chunks[e]=((e,t)=>{if(e>t)throw new Error("invalid range ("+e+", "+t+") or no bytes requested!");if(t>n-1)throw new Error("only "+n+" bytes available! programmer error!");var a=new XMLHttpRequest;if(a.open("GET",r,!1),n!==o&&a.setRequestHeader("Range","bytes="+e+"-"+t),a.responseType="arraybuffer",a.overrideMimeType&&a.overrideMimeType("text/plain; charset=x-user-defined"),a.send(null),!(a.status>=200&&a.status<300||304===a.status))throw new Error("Couldn't load "+r+". Status: "+a.status);return void 0!==a.response?new Uint8Array(a.response||[]):re(a.responseText||"",!0)})(t,a)),void 0===s.chunks[e])throw new Error("doXHR failed!");return s.chunks[e]})),!i&&n||(o=n=1,n=this.getter(0).length,o=n,d("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=n,this._chunkSize=o,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!s)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var o=new i;Object.defineProperties(o,{length:{get:function(){return this.lengthKnown||this.cacheLength(),this._length}},chunkSize:{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}});var l={isDevice:!1,contents:o}}else l={isDevice:!1,url:r};var u=oe.createFile(e,t,l,n,a);l.contents?u.contents=l.contents:l.url&&(u.contents=null,u.url=l.url),Object.defineProperties(u,{usedBytes:{get:function(){return this.contents.length}}});var c={};function f(e,t,r,n,a){var i=e.node.contents;if(a>=i.length)return 0;var o=Math.min(i.length-a,n);if(i.slice)for(var s=0;s<o;s++)t[r+s]=i[a+s];else for(s=0;s<o;s++)t[r+s]=i.get(a+s);return o}return Object.keys(u.stream_ops).forEach((e=>{var t=u.stream_ops[e];c[e]=function(){return oe.forceLoadFile(u),t.apply(null,arguments)}})),c.read=(e,t,r,n,a)=>(oe.forceLoadFile(u),f(e,t,r,n,a)),c.mmap=(e,t,r,n,a)=>{oe.forceLoadFile(u);var i=ae();if(!i)throw new oe.ErrnoError(48);return f(e,b,i,t,r),{ptr:i,allocated:!0}},u.stream_ops=c,u},createPreloadedFile:(e,t,r,n,a,i,o,s,l,c)=>{var f=t?te.resolve(ee.join2(e,t)):e;function d(r){function u(r){c&&c(),s||oe.createDataFile(e,t,r,n,a,l),i&&i(),$()}Browser.handledByPreloadPlugin(r,f,u,(()=>{o&&o(),$()}))||u(r)}N(),"string"==typeof r?function(e,t,r,n){var a=n?"":"al "+e;u(e,(r=>{w(r,'Loading data file "'+e+'" failed (no arrayBuffer).'),t(new Uint8Array(r)),a&&$()}),(t=>{if(!r)throw'Loading data file "'+e+'" failed.';r()})),a&&N()}(r,(e=>d(e)),o):d(r)},indexedDB:()=>window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB,DB_NAME:()=>"EM_FS_"+window.location.pathname,DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:(e,t,r)=>{t=t||(()=>{}),r=r||(()=>{});var n=oe.indexedDB();try{var a=n.open(oe.DB_NAME(),oe.DB_VERSION)}catch(e){return r(e)}a.onupgradeneeded=()=>{d("creating db"),a.result.createObjectStore(oe.DB_STORE_NAME)},a.onsuccess=()=>{var n=a.result.transaction([oe.DB_STORE_NAME],"readwrite"),i=n.objectStore(oe.DB_STORE_NAME),o=0,s=0,l=e.length;function u(){0==s?t():r()}e.forEach((e=>{var t=i.put(oe.analyzePath(e).object.contents,e);t.onsuccess=()=>{++o+s==l&&u()},t.onerror=()=>{s++,o+s==l&&u()}})),n.onerror=r},a.onerror=r},loadFilesFromDB:(e,t,r)=>{t=t||(()=>{}),r=r||(()=>{});var n=oe.indexedDB();try{var a=n.open(oe.DB_NAME(),oe.DB_VERSION)}catch(e){return r(e)}a.onupgradeneeded=r,a.onsuccess=()=>{var n=a.result;try{var i=n.transaction([oe.DB_STORE_NAME],"readonly")}catch(e){return void r(e)}var o=i.objectStore(oe.DB_STORE_NAME),s=0,l=0,u=e.length;function c(){0==l?t():r()}e.forEach((e=>{var t=o.get(e);t.onsuccess=()=>{oe.analyzePath(e).exists&&oe.unlink(e),oe.createDataFile(ee.dirname(e),ee.basename(e),t.result,!0,!0,!0),++s+l==u&&c()},t.onerror=()=>{l++,s+l==u&&c()}})),i.onerror=r},a.onerror=r}},se={DEFAULT_POLLMASK:5,calculateAt:function(e,t,r){if(ee.isAbs(t))return t;var n;if(n=-100===e?oe.cwd():se.getStreamFromFD(e).path,0==t.length){if(!r)throw new oe.ErrnoError(44);return n}return ee.join2(n,t)},doStat:function(e,t,r){try{var n=e(t)}catch(e){if(e&&e.node&&ee.normalize(t)!==ee.normalize(oe.getPath(e.node)))return-54;throw e}return P[r>>2]=n.dev,P[r+8>>2]=n.ino,P[r+12>>2]=n.mode,T[r+16>>2]=n.nlink,P[r+20>>2]=n.uid,P[r+24>>2]=n.gid,P[r+28>>2]=n.rdev,q=[n.size>>>0,(W=n.size,+Math.abs(W)>=1?W>0?(0|Math.min(+Math.floor(W/4294967296),4294967295))>>>0:~~+Math.ceil((W-+(~~W>>>0))/4294967296)>>>0:0)],P[r+40>>2]=q[0],P[r+44>>2]=q[1],P[r+48>>2]=4096,P[r+52>>2]=n.blocks,q=[Math.floor(n.atime.getTime()/1e3)>>>0,(W=Math.floor(n.atime.getTime()/1e3),+Math.abs(W)>=1?W>0?(0|Math.min(+Math.floor(W/4294967296),4294967295))>>>0:~~+Math.ceil((W-+(~~W>>>0))/4294967296)>>>0:0)],P[r+56>>2]=q[0],P[r+60>>2]=q[1],T[r+64>>2]=0,q=[Math.floor(n.mtime.getTime()/1e3)>>>0,(W=Math.floor(n.mtime.getTime()/1e3),+Math.abs(W)>=1?W>0?(0|Math.min(+Math.floor(W/4294967296),4294967295))>>>0:~~+Math.ceil((W-+(~~W>>>0))/4294967296)>>>0:0)],P[r+72>>2]=q[0],P[r+76>>2]=q[1],T[r+80>>2]=0,q=[Math.floor(n.ctime.getTime()/1e3)>>>0,(W=Math.floor(n.ctime.getTime()/1e3),+Math.abs(W)>=1?W>0?(0|Math.min(+Math.floor(W/4294967296),4294967295))>>>0:~~+Math.ceil((W-+(~~W>>>0))/4294967296)>>>0:0)],P[r+88>>2]=q[0],P[r+92>>2]=q[1],T[r+96>>2]=0,q=[n.ino>>>0,(W=n.ino,+Math.abs(W)>=1?W>0?(0|Math.min(+Math.floor(W/4294967296),4294967295))>>>0:~~+Math.ceil((W-+(~~W>>>0))/4294967296)>>>0:0)],P[r+104>>2]=q[0],P[r+108>>2]=q[1],0},doMsync:function(e,t,r,n,a){var i=E.slice(e,e+r);oe.msync(t,i,a,r,n)},varargs:void 0,get:function(){return se.varargs+=4,P[se.varargs-4>>2]},getStr:function(e){return F(e)},getStreamFromFD:function(e){var t=oe.getStream(e);if(!t)throw new oe.ErrnoError(8);return t}},le={};function ue(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function ce(e){return this.fromWireType(P[e>>2])}var fe={},de={},he={},me=48,pe=57;function ye(e){if(void 0===e)return"_unknown";var t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return t>=me&&t<=pe?"_"+e:e}function ve(e,t){return e=ye(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(t)}function ge(e,t){var r=ve(t,(function(e){this.name=t,this.message=e;var r=new Error(e).stack;void 0!==r&&(this.stack=this.toString()+"\n"+r.replace(/^Error(:[^\n]*)?\n/,""))}));return r.prototype=Object.create(e.prototype),r.prototype.constructor=r,r.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},r}var we=void 0;function _e(e){throw new we(e)}function be(e,t,r){function n(t){var n=r(t);n.length!==e.length&&_e("Mismatched type converter count");for(var a=0;a<e.length;++a)Ae(e[a],n[a])}e.forEach((function(e){he[e]=t}));var a=new Array(t.length),i=[],o=0;t.forEach(((e,t)=>{de.hasOwnProperty(e)?a[t]=de[e]:(i.push(e),fe.hasOwnProperty(e)||(fe[e]=[]),fe[e].push((()=>{a[t]=de[e],++o===i.length&&n(a)})))})),0===i.length&&n(a)}function Ee(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}var Ce=void 0;function xe(e){for(var t="",r=e;E[r];)t+=Ce[E[r++]];return t}var Pe=void 0;function Te(e){throw new Pe(e)}function Ae(e,t,r={}){if(!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");var n=t.name;if(e||Te('type "'+n+'" must have a positive integer typeid pointer'),de.hasOwnProperty(e)){if(r.ignoreDuplicateRegistrations)return;Te("Cannot register type '"+n+"' twice")}if(de[e]=t,delete he[e],fe.hasOwnProperty(e)){var a=fe[e];delete fe[e],a.forEach((e=>e()))}}function Ie(e){if(!(this instanceof Ke))return!1;if(!(e instanceof Ke))return!1;for(var t=this.$$.ptrType.registeredClass,r=this.$$.ptr,n=e.$$.ptrType.registeredClass,a=e.$$.ptr;t.baseClass;)r=t.upcast(r),t=t.baseClass;for(;n.baseClass;)a=n.upcast(a),n=n.baseClass;return t===n&&r===a}function ke(e){Te(e.$$.ptrType.registeredClass.name+" instance already deleted")}var Se=!1;function Fe(e){}function De(e){e.count.value-=1,0===e.count.value&&function(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}(e)}function Le(e,t,r){if(t===r)return e;if(void 0===r.baseClass)return null;var n=Le(e,t,r.baseClass);return null===n?null:r.downcast(n)}var Me={};function Re(){return Object.keys(Ne).length}function Ge(){var e=[];for(var t in Ne)Ne.hasOwnProperty(t)&&e.push(Ne[t]);return e}var Be=[];function Oe(){for(;Be.length;){var e=Be.pop();e.$$.deleteScheduled=!1,e.delete()}}var je=void 0;function Ve(e){je=e,Be.length&&je&&je(Oe)}var Ne={};function $e(e,t){return t=function(e,t){for(void 0===t&&Te("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}(e,t),Ne[t]}function Ue(e,t){return t.ptrType&&t.ptr||_e("makeClassHandle requires ptr and ptrType"),!!t.smartPtrType!=!!t.smartPtr&&_e("Both smartPtrType and smartPtr must be specified"),t.count={value:1},ze(Object.create(e,{$$:{value:t}}))}function He(e){var t=this.getPointee(e);if(!t)return this.destructor(e),null;var r=$e(this.registeredClass,t);if(void 0!==r){if(0===r.$$.count.value)return r.$$.ptr=t,r.$$.smartPtr=e,r.clone();var n=r.clone();return this.destructor(e),n}function a(){return this.isSmartPointer?Ue(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:t,smartPtrType:this,smartPtr:e}):Ue(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var i,o=this.registeredClass.getActualType(t),s=Me[o];if(!s)return a.call(this);i=this.isConst?s.constPointerType:s.pointerType;var l=Le(t,this.registeredClass,i.registeredClass);return null===l?a.call(this):this.isSmartPointer?Ue(i.registeredClass.instancePrototype,{ptrType:i,ptr:l,smartPtrType:this,smartPtr:e}):Ue(i.registeredClass.instancePrototype,{ptrType:i,ptr:l})}function ze(e){return"undefined"==typeof FinalizationRegistry?(ze=e=>e,e):(Se=new FinalizationRegistry((e=>{De(e.$$)})),Fe=e=>Se.unregister(e),(ze=e=>{var t=e.$$;if(t.smartPtr){var r={$$:t};Se.register(e,r,e)}return e})(e))}function We(){if(this.$$.ptr||ke(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e,t=ze(Object.create(Object.getPrototypeOf(this),{$$:{value:(e=this.$$,{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType})}}));return t.$$.count.value+=1,t.$$.deleteScheduled=!1,t}function qe(){this.$$.ptr||ke(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Te("Object already scheduled for deletion"),Fe(this),De(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function Xe(){return!this.$$.ptr}function Je(){return this.$$.ptr||ke(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Te("Object already scheduled for deletion"),Be.push(this),1===Be.length&&je&&je(Oe),this.$$.deleteScheduled=!0,this}function Ke(){}function Qe(e,t,r){if(void 0===e[t].overloadTable){var n=e[t];e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||Te("Function '"+r+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[t].overloadTable+")!"),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[n.argCount]=n}}function Ye(e,t,r){n.hasOwnProperty(e)?((void 0===r||void 0!==n[e].overloadTable&&void 0!==n[e].overloadTable[r])&&Te("Cannot register public name '"+e+"' twice"),Qe(n,e,e),n.hasOwnProperty(r)&&Te("Cannot register multiple overloads of a function with the same number of arguments ("+r+")!"),n[e].overloadTable[r]=t):(n[e]=t,void 0!==r&&(n[e].numArguments=r))}function Ze(e,t,r,n,a,i,o,s){this.name=e,this.constructor=t,this.instancePrototype=r,this.rawDestructor=n,this.baseClass=a,this.getActualType=i,this.upcast=o,this.downcast=s,this.pureVirtualFunctions=[]}function et(e,t,r){for(;t!==r;)t.upcast||Te("Expected null or instance of "+r.name+", got an instance of "+t.name),e=t.upcast(e),t=t.baseClass;return e}function tt(e,t){if(null===t)return this.isReference&&Te("null is not a valid "+this.name),0;t.$$||Te('Cannot pass "'+St(t)+'" as a '+this.name),t.$$.ptr||Te("Cannot pass deleted object as a pointer of type "+this.name);var r=t.$$.ptrType.registeredClass;return et(t.$$.ptr,r,this.registeredClass)}function rt(e,t){var r;if(null===t)return this.isReference&&Te("null is not a valid "+this.name),this.isSmartPointer?(r=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,r),r):0;t.$$||Te('Cannot pass "'+St(t)+'" as a '+this.name),t.$$.ptr||Te("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&t.$$.ptrType.isConst&&Te("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);var n=t.$$.ptrType.registeredClass;if(r=et(t.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(void 0===t.$$.smartPtr&&Te("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?r=t.$$.smartPtr:Te("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:r=t.$$.smartPtr;break;case 2:if(t.$$.smartPtrType===this)r=t.$$.smartPtr;else{var a=t.clone();r=this.rawShare(r,At.toHandle((function(){a.delete()}))),null!==e&&e.push(this.rawDestructor,r)}break;default:Te("Unsupporting sharing policy")}return r}function nt(e,t){if(null===t)return this.isReference&&Te("null is not a valid "+this.name),0;t.$$||Te('Cannot pass "'+St(t)+'" as a '+this.name),t.$$.ptr||Te("Cannot pass deleted object as a pointer of type "+this.name),t.$$.ptrType.isConst&&Te("Cannot convert argument of type "+t.$$.ptrType.name+" to parameter type "+this.name);var r=t.$$.ptrType.registeredClass;return et(t.$$.ptr,r,this.registeredClass)}function at(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function it(e){this.rawDestructor&&this.rawDestructor(e)}function ot(e){null!==e&&e.delete()}function st(e,t,r,n,a,i,o,s,l,u,c){this.name=e,this.registeredClass=t,this.isReference=r,this.isConst=n,this.isSmartPointer=a,this.pointeeType=i,this.sharingPolicy=o,this.rawGetPointee=s,this.rawConstructor=l,this.rawShare=u,this.rawDestructor=c,a||void 0!==t.baseClass?this.toWireType=rt:n?(this.toWireType=tt,this.destructorFunction=null):(this.toWireType=nt,this.destructorFunction=null)}function lt(e,t,r){n.hasOwnProperty(e)||_e("Replacing nonexistant public symbol"),void 0!==n[e].overloadTable&&void 0!==r?n[e].overloadTable[r]=t:(n[e]=t,n[e].argCount=r)}function ut(e,t,r){return function(e,t,r){var a=n["dynCall_"+e];return r&&r.length?a.apply(null,[t].concat(r)):a.call(null,t)}(e,t,r)}function ct(e,t){e=xe(e);var r,n,a,i=(r=e,n=t,a=[],function(){return a.length=0,Object.assign(a,arguments),ut(r,n,a)});return"function"!=typeof i&&Te("unknown function pointer with signature "+e+": "+t),i}var ft=void 0;function dt(e){var t=Cr(e),r=xe(t);return br(t),r}function ht(e,t){var r=[],n={};throw t.forEach((function e(t){n[t]||de[t]||(he[t]?he[t].forEach(e):(r.push(t),n[t]=!0))})),new ft(e+": "+r.map(dt).join([", "]))}function mt(e,t){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var r=ve(e.name||"unknownFunctionName",(function(){}));r.prototype=e.prototype;var n=new r,a=e.apply(n,t);return a instanceof Object?a:n}function pt(e){try{return e()}catch(e){U(e)}}function yt(e){if(!g)try{e()}catch(e){!function(e){if(e instanceof Q||"unwind"==e)return v;i(1,e)}(e)}}var vt={State:{Normal:0,Unwinding:1,Rewinding:2,Disabled:3},state:0,StackSize:4096,currData:null,handleSleepReturnValue:0,exportCallStack:[],callStackNameToId:{},callStackIdToName:{},callStackId:0,asyncPromiseHandlers:null,sleepCallbacks:[],getCallStackId:function(e){var t=vt.callStackNameToId[e];return void 0===t&&(t=vt.callStackId++,vt.callStackNameToId[e]=t,vt.callStackIdToName[t]=e),t},instrumentWasmImports:function(e){var t,r,n=["env.invoke_*","env.emscripten_sleep","env.emscripten_wget","env.emscripten_wget_data","env.emscripten_idb_load","env.emscripten_idb_store","env.emscripten_idb_delete","env.emscripten_idb_exists","env.emscripten_idb_load_blob","env.emscripten_idb_store_blob","env.SDL_Delay","env.emscripten_scan_registers","env.emscripten_lazy_load_code","env.emscripten_fiber_swap","wasi_snapshot_preview1.fd_sync","env.__wasi_fd_sync","env._emval_await","env._dlopen_js","env.__asyncjs__*"].map((e=>e.split(".")[1]));for(var a in e)r=void 0,(r=e[t=a]).sig,"function"==typeof r&&(n.indexOf(t)>=0||t.startsWith("__asyncjs__"))},instrumentWasmExports:function(e){var t={};for(var r in e)!function(r){var n=e[r];t[r]="function"==typeof n?function(){vt.exportCallStack.push(r);try{return n.apply(null,arguments)}finally{g||(w(vt.exportCallStack.pop()===r),vt.maybeStopUnwind())}}:n}(r);return t},maybeStopUnwind:function(){vt.currData&&vt.state===vt.State.Unwinding&&0===vt.exportCallStack.length&&(vt.state=vt.State.Normal,pt(Ir),"undefined"!=typeof Fibers&&Fibers.trampoline())},whenDone:function(){return new Promise(((e,t)=>{vt.asyncPromiseHandlers={resolve:e,reject:t}}))},allocateData:function(){var e=Er(12+vt.StackSize);return vt.setDataHeader(e,e+12,vt.StackSize),vt.setDataRewindFunc(e),e},setDataHeader:function(e,t,r){P[e>>2]=t,P[e+4>>2]=t+r},setDataRewindFunc:function(e){var t=vt.exportCallStack[0],r=vt.getCallStackId(t);P[e+8>>2]=r},getDataRewindFunc:function(e){var t=P[e+8>>2],r=vt.callStackIdToName[t];return n.asm[r]},doRewind:function(e){return vt.getDataRewindFunc(e)()},handleSleep:function(e){if(!g){if(vt.state===vt.State.Normal){var t=!1,r=!1;e((e=>{if(!g&&(vt.handleSleepReturnValue=e||0,t=!0,r)){vt.state=vt.State.Rewinding,pt((()=>kr(vt.currData))),"undefined"!=typeof Browser&&Browser.mainLoop.func&&Browser.mainLoop.resume();var n,a=!1;try{n=vt.doRewind(vt.currData)}catch(e){n=e,a=!0}var i=!1;if(!vt.currData){var o=vt.asyncPromiseHandlers;o&&(vt.asyncPromiseHandlers=null,(a?o.reject:o.resolve)(n),i=!0)}if(a&&!i)throw n}})),r=!0,t||(vt.state=vt.State.Unwinding,vt.currData=vt.allocateData(),"undefined"!=typeof Browser&&Browser.mainLoop.func&&Browser.mainLoop.pause(),pt((()=>Ar(vt.currData))))}else vt.state===vt.State.Rewinding?(vt.state=vt.State.Normal,pt(Sr),br(vt.currData),vt.currData=null,vt.sleepCallbacks.forEach((e=>yt(e)))):U("invalid state: "+vt.state);return vt.handleSleepReturnValue}},handleAsync:function(e){return vt.handleSleep((t=>{e().then(t)}))}};function gt(e,t,r,n,a){var i=t.length;i<2&&Te("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var o=null!==t[1]&&null!==r,s=!1,l=1;l<t.length;++l)if(null!==t[l]&&void 0===t[l].destructorFunction){s=!0;break}var u="void"!==t[0].name,c="",f="";for(l=0;l<i-2;++l)c+=(0!==l?", ":"")+"arg"+l,f+=(0!==l?", ":"")+"arg"+l+"Wired";var d="return function "+ye(e)+"("+c+") {\nif (arguments.length !== "+(i-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(i-2)+" args!');\n}\n";s&&(d+="var destructors = [];\n");var h=s?"destructors":"null",m=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],p=[Te,n,a,ue,t[0],t[1]];for(o&&(d+="var thisWired = classParam.toWireType("+h+", this);\n"),l=0;l<i-2;++l)d+="var arg"+l+"Wired = argType"+l+".toWireType("+h+", arg"+l+"); // "+t[l+2].name+"\n",m.push("argType"+l),p.push(t[l+2]);if(o&&(f="thisWired"+(f.length>0?", ":"")+f),d+=(u?"var rv = ":"")+"invoker(fn"+(f.length>0?", ":"")+f+");\n",m.push("Asyncify"),p.push(vt),d+="function onDone("+(u?"rv":"")+") {\n",s)d+="runDestructors(destructors);\n";else for(l=o?1:2;l<t.length;++l){var y=1===l?"thisWired":"arg"+(l-2)+"Wired";null!==t[l].destructorFunction&&(d+=y+"_dtor("+y+"); // "+t[l].name+"\n",m.push(y+"_dtor"),p.push(t[l].destructorFunction))}return u&&(d+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),d+="}\n",d+="return Asyncify.currData ? Asyncify.whenDone().then(onDone) : onDone("+(u?"rv":"")+");\n",d+="}\n",m.push(d),mt(Function,m).apply(null,p)}function wt(e,t){for(var r=[],n=0;n<e;n++)r.push(T[t+4*n>>2]);return r}function _t(e,t,r){return e instanceof Object||Te(r+' with invalid "this": '+e),e instanceof t.registeredClass.constructor||Te(r+' incompatible with "this" of type '+e.constructor.name),e.$$.ptr||Te("cannot call emscripten binding method "+r+" on deleted object"),et(e.$$.ptr,e.$$.ptrType.registeredClass,t.registeredClass)}var Et=[],Ct=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function xt(e){e>4&&0==--Ct[e].refcount&&(Ct[e]=void 0,Et.push(e))}function Pt(){for(var e=0,t=5;t<Ct.length;++t)void 0!==Ct[t]&&++e;return e}function Tt(){for(var e=5;e<Ct.length;++e)if(void 0!==Ct[e])return Ct[e];return null}var At={toValue:e=>(e||Te("Cannot use deleted val. handle = "+e),Ct[e].value),toHandle:e=>{switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var t=Et.length?Et.pop():Ct.length;return Ct[t]={refcount:1,value:e},t}}};function It(e,t,r){switch(t){case 0:return function(e){var t=r?b:E;return this.fromWireType(t[e])};case 1:return function(e){var t=r?C:x;return this.fromWireType(t[e>>1])};case 2:return function(e){var t=r?P:T;return this.fromWireType(t[e>>2])};default:throw new TypeError("Unknown integer type: "+e)}}function kt(e,t){var r=de[e];return void 0===r&&Te(t+" has unknown type "+dt(e)),r}function St(e){if(null===e)return"null";var t=typeof e;return"object"===t||"array"===t||"function"===t?e.toString():""+e}function Ft(e,t){switch(t){case 2:return function(e){return this.fromWireType(A[e>>2])};case 3:return function(e){return this.fromWireType(I[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function Dt(e,t,r){switch(t){case 0:return r?function(e){return b[e]}:function(e){return E[e]};case 1:return r?function(e){return C[e>>1]}:function(e){return x[e>>1]};case 2:return r?function(e){return P[e>>2]}:function(e){return T[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}var Lt="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function Mt(e,t){for(var r=e,n=r>>1,a=n+t/2;!(n>=a)&&x[n];)++n;if((r=n<<1)-e>32&&Lt)return Lt.decode(E.subarray(e,r));for(var i="",o=0;!(o>=t/2);++o){var s=C[e+2*o>>1];if(0==s)break;i+=String.fromCharCode(s)}return i}function Rt(e,t,r){if(void 0===r&&(r=2147483647),r<2)return 0;for(var n=t,a=(r-=2)<2*e.length?r/2:e.length,i=0;i<a;++i){var o=e.charCodeAt(i);C[t>>1]=o,t+=2}return C[t>>1]=0,t-n}function Gt(e){return 2*e.length}function Bt(e,t){for(var r=0,n="";!(r>=t/4);){var a=P[e+4*r>>2];if(0==a)break;if(++r,a>=65536){var i=a-65536;n+=String.fromCharCode(55296|i>>10,56320|1023&i)}else n+=String.fromCharCode(a)}return n}function Ot(e,t,r){if(void 0===r&&(r=2147483647),r<4)return 0;for(var n=t,a=n+r-4,i=0;i<e.length;++i){var o=e.charCodeAt(i);if(o>=55296&&o<=57343&&(o=65536+((1023&o)<<10)|1023&e.charCodeAt(++i)),P[t>>2]=o,(t+=4)+4>a)break}return P[t>>2]=0,t-n}function jt(e){for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n>=55296&&n<=57343&&++r,t+=4}return t}function Vt(e,t){for(var r=new Array(e),n=0;n<e;++n)r[n]=kt(T[t+n*y>>2],"parameter "+n);return r}var Nt={};function $t(e){var t=Nt[e];return void 0===t?xe(e):t}var Ut=[];function Ht(){return"object"==typeof globalThis?globalThis:Function("return this")()}var zt,Wt=[],qt={},Xt={inEventHandler:0,removeAllEventListeners:function(){for(var e=Xt.eventHandlers.length-1;e>=0;--e)Xt._removeHandler(e);Xt.eventHandlers=[],Xt.deferredCalls=[]},registerRemoveEventListeners:function(){Xt.removeEventListenersRegistered||(Xt.removeEventListenersRegistered=!0)},deferredCalls:[],deferCall:function(e,t,r){function n(e,t){if(e.length!=t.length)return!1;for(var r in e)if(e[r]!=t[r])return!1;return!0}for(var a in Xt.deferredCalls){var i=Xt.deferredCalls[a];if(i.targetFunction==e&&n(i.argsList,r))return}Xt.deferredCalls.push({targetFunction:e,precedence:t,argsList:r}),Xt.deferredCalls.sort((function(e,t){return e.precedence<t.precedence}))},removeDeferredCalls:function(e){for(var t=0;t<Xt.deferredCalls.length;++t)Xt.deferredCalls[t].targetFunction==e&&(Xt.deferredCalls.splice(t,1),--t)},canPerformEventHandlerRequests:function(){return Xt.inEventHandler&&Xt.currentEventHandler.allowsDeferredCalls},runDeferredCalls:function(){if(Xt.canPerformEventHandlerRequests())for(var e=0;e<Xt.deferredCalls.length;++e){var t=Xt.deferredCalls[e];Xt.deferredCalls.splice(e,1),--e,t.targetFunction.apply(null,t.argsList)}},eventHandlers:[],removeAllHandlersOnTarget:function(e,t){for(var r=0;r<Xt.eventHandlers.length;++r)Xt.eventHandlers[r].target!=e||t&&t!=Xt.eventHandlers[r].eventTypeString||Xt._removeHandler(r--)},_removeHandler:function(e){var t=Xt.eventHandlers[e];t.target.removeEventListener(t.eventTypeString,t.eventListenerFunc,t.useCapture),Xt.eventHandlers.splice(e,1)},registerOrRemoveHandler:function(e){var t=function(t){++Xt.inEventHandler,Xt.currentEventHandler=e,Xt.runDeferredCalls(),e.handlerFunc(t),Xt.runDeferredCalls(),--Xt.inEventHandler};if(e.callbackfunc)e.eventListenerFunc=t,e.target.addEventListener(e.eventTypeString,t,e.useCapture),Xt.eventHandlers.push(e),Xt.registerRemoveEventListeners();else for(var r=0;r<Xt.eventHandlers.length;++r)Xt.eventHandlers[r].target==e.target&&Xt.eventHandlers[r].eventTypeString==e.eventTypeString&&Xt._removeHandler(r--)},getNodeNameForTarget:function(e){return e?e==window?"#window":e==screen?"#screen":e&&e.nodeName?e.nodeName:"":""},fullscreenEnabled:function(){return document.fullscreenEnabled||document.webkitFullscreenEnabled}},Jt=[0,"undefined"!=typeof document?document:0,"undefined"!=typeof window?window:0];function Kt(e){var t;return e=(t=e)>2?F(t):t,Jt[e]||("undefined"!=typeof document?document.querySelector(e):void 0)}function Qt(e){return Kt(e)}zt=()=>performance.now();var Yt={counter:1,buffers:[],programs:[],framebuffers:[],renderbuffers:[],textures:[],shaders:[],vaos:[],contexts:[],offscreenCanvases:{},queries:[],samplers:[],transformFeedbacks:[],syncs:[],stringCache:{},stringiCache:{},unpackAlignment:4,recordError:function(e){Yt.lastError||(Yt.lastError=e)},getNewId:function(e){for(var t=Yt.counter++,r=e.length;r<t;r++)e[r]=null;return t},getSource:function(e,t,r,n){for(var a="",i=0;i<t;++i){var o=n?P[n+4*i>>2]:-1;a+=F(P[r+4*i>>2],o<0?void 0:o)}return a},createContext:function(e,t){if(!e.getContextSafariWebGL2Fixed){let t=function(t,r){var n=e.getContextSafariWebGL2Fixed(t,r);return"webgl"==t==n instanceof WebGLRenderingContext?n:null};e.getContextSafariWebGL2Fixed=e.getContext,e.getContext=t}var r=t.majorVersion>1?e.getContext("webgl2",t):e.getContext("webgl",t);return r?Yt.registerContext(r,t):0},registerContext:function(e,t){var r=Yt.getNewId(Yt.contexts),n={handle:r,attributes:t,version:t.majorVersion,GLctx:e};return e.canvas&&(e.canvas.GLctxObject=n),Yt.contexts[r]=n,(void 0===t.enableExtensionsByDefault||t.enableExtensionsByDefault)&&Yt.initExtensions(n),r},makeContextCurrent:function(e){return Yt.currentContext=Yt.contexts[e],n.ctx=cr=Yt.currentContext&&Yt.currentContext.GLctx,!(e&&!cr)},getContext:function(e){return Yt.contexts[e]},deleteContext:function(e){Yt.currentContext===Yt.contexts[e]&&(Yt.currentContext=null),"object"==typeof Xt&&Xt.removeAllHandlersOnTarget(Yt.contexts[e].GLctx.canvas),Yt.contexts[e]&&Yt.contexts[e].GLctx.canvas&&(Yt.contexts[e].GLctx.canvas.GLctxObject=void 0),Yt.contexts[e]=null},initExtensions:function(e){if(e||(e=Yt.currentContext),!e.initExtensionsDone){e.initExtensionsDone=!0;var t,r=e.GLctx;!function(e){var t=e.getExtension("ANGLE_instanced_arrays");t&&(e.vertexAttribDivisor=function(e,r){t.vertexAttribDivisorANGLE(e,r)},e.drawArraysInstanced=function(e,r,n,a){t.drawArraysInstancedANGLE(e,r,n,a)},e.drawElementsInstanced=function(e,r,n,a,i){t.drawElementsInstancedANGLE(e,r,n,a,i)})}(r),function(e){var t=e.getExtension("OES_vertex_array_object");t&&(e.createVertexArray=function(){return t.createVertexArrayOES()},e.deleteVertexArray=function(e){t.deleteVertexArrayOES(e)},e.bindVertexArray=function(e){t.bindVertexArrayOES(e)},e.isVertexArray=function(e){return t.isVertexArrayOES(e)})}(r),function(e){var t=e.getExtension("WEBGL_draw_buffers");t&&(e.drawBuffers=function(e,r){t.drawBuffersWEBGL(e,r)})}(r),(t=r).dibvbi=t.getExtension("WEBGL_draw_instanced_base_vertex_base_instance"),function(e){e.mdibvbi=e.getExtension("WEBGL_multi_draw_instanced_base_vertex_base_instance")}(r),e.version>=2&&(r.disjointTimerQueryExt=r.getExtension("EXT_disjoint_timer_query_webgl2")),(e.version<2||!r.disjointTimerQueryExt)&&(r.disjointTimerQueryExt=r.getExtension("EXT_disjoint_timer_query")),function(e){e.multiDrawWebgl=e.getExtension("WEBGL_multi_draw")}(r),(r.getSupportedExtensions()||[]).forEach((function(e){e.includes("lose_context")||e.includes("debug")||r.getExtension(e)}))}}};function Zt(e,t,r,n){for(var a=0;a<e;a++){var i=cr[r](),o=i&&Yt.getNewId(n);i?(i.name=o,n[o]=i):Yt.recordError(1282),P[t+4*a>>2]=o}}function er(e,t,r){if(t){var n,a,i=void 0;switch(e){case 36346:i=1;break;case 36344:return void(0!=r&&1!=r&&Yt.recordError(1280));case 34814:case 36345:i=0;break;case 34466:var o=cr.getParameter(34467);i=o?o.length:0;break;case 33309:if(Yt.currentContext.version<2)return void Yt.recordError(1282);i=2*(cr.getSupportedExtensions()||[]).length;break;case 33307:case 33308:if(Yt.currentContext.version<2)return void Yt.recordError(1280);i=33307==e?3:0}if(void 0===i){var s=cr.getParameter(e);switch(typeof s){case"number":i=s;break;case"boolean":i=s?1:0;break;case"string":return void Yt.recordError(1280);case"object":if(null===s)switch(e){case 34964:case 35725:case 34965:case 36006:case 36007:case 32873:case 34229:case 36662:case 36663:case 35053:case 35055:case 36010:case 35097:case 35869:case 32874:case 36389:case 35983:case 35368:case 34068:i=0;break;default:return void Yt.recordError(1280)}else{if(s instanceof Float32Array||s instanceof Uint32Array||s instanceof Int32Array||s instanceof Array){for(var l=0;l<s.length;++l)switch(r){case 0:P[t+4*l>>2]=s[l];break;case 2:A[t+4*l>>2]=s[l];break;case 4:b[t+l>>0]=s[l]?1:0}return}try{i=0|s.name}catch(t){return Yt.recordError(1280),void h("GL_INVALID_ENUM in glGet"+r+"v: Unknown object returned from WebGL getParameter("+e+")! (error: "+t+")")}}break;default:return Yt.recordError(1280),void h("GL_INVALID_ENUM in glGet"+r+"v: Native code calling glGet"+r+"v("+e+") and it returns "+s+" of type "+typeof s+"!")}}switch(r){case 1:a=i,T[(n=t)>>2]=a,T[n+4>>2]=(a-T[n>>2])/4294967296;break;case 0:P[t>>2]=i;break;case 2:A[t>>2]=i;break;case 4:b[t>>0]=i?1:0}}else Yt.recordError(1281)}function tr(e){var t=M(e)+1,r=Er(t);return L(e,r,t),r}function rr(e){return"]"==e.slice(-1)&&e.lastIndexOf("[")}function nr(e){return 0==(e-=5120)?b:1==e?E:2==e?C:4==e?P:6==e?A:5==e||28922==e||28520==e||30779==e||30782==e?T:x}function ar(e){return 31-Math.clz32(e.BYTES_PER_ELEMENT)}function ir(e,t,r,n,a,i){var o=nr(e),s=ar(o),l=1<<s,u=function(e){return{5:3,6:4,8:2,29502:3,29504:4,26917:2,26918:2,29846:3,29847:4}[e-6402]||1}(t)*l,c=function(e,t,r,n){var a;return t*(e*r+(a=n)-1&-a)}(r,n,u,Yt.unpackAlignment);return o.subarray(a>>s,a+c>>s)}function or(e){var t=cr.currentProgram;if(t){var r=t.uniformLocsById[e];return"number"==typeof r&&(t.uniformLocsById[e]=r=cr.getUniformLocation(t,t.uniformArrayNamesById[e]+(r>0?"["+r+"]":""))),r}Yt.recordError(1282)}var sr=[],lr=[];function ur(e){try{return p.grow(e-_.byteLength+65535>>>16),R(p.buffer),1}catch(e){}}var cr,fr=["default","low-power","high-performance"],dr=function(e,t){var r=t>>2,n=P[r+6],a={alpha:!!P[r+0],depth:!!P[r+1],stencil:!!P[r+2],antialias:!!P[r+3],premultipliedAlpha:!!P[r+4],preserveDrawingBuffer:!!P[r+5],powerPreference:fr[n],failIfMajorPerformanceCaveat:!!P[r+7],majorVersion:P[r+8],minorVersion:P[r+9],enableExtensionsByDefault:P[r+10],explicitSwapControl:P[r+11],proxyContextToMainThread:P[r+12],renderViaOffscreenBackBuffer:P[r+13]},i=Qt(e);return i?a.explicitSwapControl?0:Yt.createContext(i,a):0},hr=function(){return Yt.currentContext?Yt.currentContext.handle:0},mr=function(e,t,r,n){e||(e=this),this.parent=e,this.mount=e.mount,this.mounted=null,this.id=oe.nextInode++,this.name=t,this.mode=r,this.node_ops={},this.stream_ops={},this.rdev=n},pr=365,yr=146;Object.defineProperties(mr.prototype,{read:{get:function(){return(this.mode&pr)===pr},set:function(e){e?this.mode|=pr:this.mode&=-366}},write:{get:function(){return(this.mode&yr)===yr},set:function(e){e?this.mode|=yr:this.mode&=-147}},isFolder:{get:function(){return oe.isDir(this.mode)}},isDevice:{get:function(){return oe.isChrdev(this.mode)}}}),oe.FSNode=mr,oe.staticInit(),we=n.InternalError=ge(Error,"InternalError"),function(){for(var e=new Array(256),t=0;t<256;++t)e[t]=String.fromCharCode(t);Ce=e}(),Pe=n.BindingError=ge(Error,"BindingError"),Ke.prototype.isAliasOf=Ie,Ke.prototype.clone=We,Ke.prototype.delete=qe,Ke.prototype.isDeleted=Xe,Ke.prototype.deleteLater=Je,n.getInheritedInstanceCount=Re,n.getLiveInheritedInstances=Ge,n.flushPendingDeletes=Oe,n.setDelayFunction=Ve,st.prototype.getPointee=at,st.prototype.destructor=it,st.prototype.argPackAdvance=8,st.prototype.readValueFromPointer=ce,st.prototype.deleteObject=ot,st.prototype.fromWireType=He,ft=n.UnboundTypeError=ge(Error,"UnboundTypeError"),n.count_emval_handles=Pt,n.get_first_emval=Tt;for(var vr=new Float32Array(288),gr=0;gr<288;++gr)sr[gr]=vr.subarray(0,gr+1);var wr=new Int32Array(288);for(gr=0;gr<288;++gr)lr[gr]=wr.subarray(0,gr+1);var _r={d:function(e,t,r,n){U("Assertion failed: "+F(e)+", at: "+[t?F(t):"unknown filename",r,n?F(n):"unknown function"])},A:function(e){return Er(e+24)+24},z:function(e,t,r){throw new Z(e).init(t,r),e},G:function(e,t,r){se.varargs=r;try{var n=se.getStreamFromFD(e);switch(t){case 0:return(a=se.get())<0?-28:oe.createStream(n,a).fd;case 1:case 2:case 6:case 7:return 0;case 3:return n.flags;case 4:var a=se.get();return n.flags|=a,0;case 5:return a=se.get(),C[a+0>>1]=2,0;case 16:case 8:default:return-28;case 9:return i=28,P[xr()>>2]=i,-1}}catch(e){if(void 0===oe||!(e instanceof oe.ErrnoError))throw e;return-e.errno}var i},fb:function(e,t,r){se.varargs=r;try{var n=se.getStreamFromFD(e);switch(t){case 21509:case 21505:case 21510:case 21511:case 21512:case 21506:case 21507:case 21508:case 21523:case 21524:return n.tty?0:-59;case 21519:if(!n.tty)return-59;var a=se.get();return P[a>>2]=0,0;case 21520:return n.tty?-28:-59;case 21531:return a=se.get(),oe.ioctl(n,t,a);default:return-28}}catch(e){if(void 0===oe||!(e instanceof oe.ErrnoError))throw e;return-e.errno}},hb:function(e,t,r,n){se.varargs=n;try{t=se.getStr(t),t=se.calculateAt(e,t);var a=n?se.get():0;return oe.open(t,r,a).fd}catch(e){if(void 0===oe||!(e instanceof oe.ErrnoError))throw e;return-e.errno}},o:function(e){var t=le[e];delete le[e];var r=t.rawConstructor,n=t.rawDestructor,a=t.fields;be([e],a.map((e=>e.getterReturnType)).concat(a.map((e=>e.setterArgumentType))),(e=>{var i={};return a.forEach(((t,r)=>{var n=t.fieldName,o=e[r],s=t.getter,l=t.getterContext,u=e[r+a.length],c=t.setter,f=t.setterContext;i[n]={read:e=>o.fromWireType(s(l,e)),write:(e,t)=>{var r=[];c(f,e,u.toWireType(r,t)),ue(r)}}})),[{name:t.name,fromWireType:function(e){var t={};for(var r in i)t[r]=i[r].read(e);return n(e),t},toWireType:function(e,t){for(var a in i)if(!(a in t))throw new TypeError('Missing field:  "'+a+'"');var o=r();for(a in i)i[a].write(o,t[a]);return null!==e&&e.push(n,o),o},argPackAdvance:8,readValueFromPointer:ce,destructorFunction:n}]}))},ab:function(e,t,r,n,a){},jb:function(e,t,r,n,a){var i=Ee(r);Ae(e,{name:t=xe(t),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?n:a},argPackAdvance:8,readValueFromPointer:function(e){var n;if(1===r)n=b;else if(2===r)n=C;else{if(4!==r)throw new TypeError("Unknown boolean type size: "+t);n=P}return this.fromWireType(n[e>>i])},destructorFunction:null})},f:function(e,t,r,n,a,i,o,s,l,u,c,f,d){c=xe(c),i=ct(a,i),s&&(s=ct(o,s)),u&&(u=ct(l,u)),d=ct(f,d);var h=ye(c);Ye(h,(function(){ht("Cannot construct "+c+" due to unbound types",[n])})),be([e,t,r],n?[n]:[],(function(t){var r,a;t=t[0],a=n?(r=t.registeredClass).instancePrototype:Ke.prototype;var o=ve(h,(function(){if(Object.getPrototypeOf(this)!==l)throw new Pe("Use 'new' to construct "+c);if(void 0===f.constructor_body)throw new Pe(c+" has no accessible constructor");var e=f.constructor_body[arguments.length];if(void 0===e)throw new Pe("Tried to invoke ctor of "+c+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(f.constructor_body).toString()+") parameters instead!");return e.apply(this,arguments)})),l=Object.create(a,{constructor:{value:o}});o.prototype=l;var f=new Ze(c,o,l,d,r,i,s,u),m=new st(c,f,!0,!1,!1),p=new st(c+"*",f,!1,!1,!1),y=new st(c+" const*",f,!1,!0,!1);return Me[e]={pointerType:p,constPointerType:y},lt(h,o),[m,p,y]}))},e:function(e,t,r,n,a,i,o){var s=wt(r,n);t=xe(t),i=ct(a,i),be([],[e],(function(e){var n=(e=e[0]).name+"."+t;function a(){ht("Cannot call "+n+" due to unbound types",s)}t.startsWith("@@")&&(t=Symbol[t.substring(2)]);var l=e.registeredClass.constructor;return void 0===l[t]?(a.argCount=r-1,l[t]=a):(Qe(l,t,n),l[t].overloadTable[r-1]=a),be([],s,(function(e){var a=[e[0],null].concat(e.slice(1)),s=gt(n,a,null,i,o);return void 0===l[t].overloadTable?(s.argCount=r-1,l[t]=s):l[t].overloadTable[r-1]=s,[]})),[]}))},r:function(e,t,r,n,a,i){w(t>0);var o=wt(t,r);a=ct(n,a),be([],[e],(function(e){var r="constructor "+(e=e[0]).name;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[t-1])throw new Pe("Cannot register multiple constructors with identical number of parameters ("+(t-1)+") for class '"+e.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return e.registeredClass.constructor_body[t-1]=()=>{ht("Cannot construct "+e.name+" due to unbound types",o)},be([],o,(function(n){return n.splice(1,0,null),e.registeredClass.constructor_body[t-1]=gt(r,n,null,a,i),[]})),[]}))},a:function(e,t,r,n,a,i,o,s){var l=wt(r,n);t=xe(t),i=ct(a,i),be([],[e],(function(e){var n=(e=e[0]).name+"."+t;function a(){ht("Cannot call "+n+" due to unbound types",l)}t.startsWith("@@")&&(t=Symbol[t.substring(2)]),s&&e.registeredClass.pureVirtualFunctions.push(t);var u=e.registeredClass.instancePrototype,c=u[t];return void 0===c||void 0===c.overloadTable&&c.className!==e.name&&c.argCount===r-2?(a.argCount=r-2,a.className=e.name,u[t]=a):(Qe(u,t,n),u[t].overloadTable[r-2]=a),be([],l,(function(a){var s=gt(n,a,e,i,o);return void 0===u[t].overloadTable?(s.argCount=r-2,u[t]=s):u[t].overloadTable[r-2]=s,[]})),[]}))},b:function(e,t,r,n,a,i,o,s,l,u){t=xe(t),a=ct(n,a),be([],[e],(function(e){var n=(e=e[0]).name+"."+t,c={get:function(){ht("Cannot access "+n+" due to unbound types",[r,o])},enumerable:!0,configurable:!0};return c.set=l?()=>{ht("Cannot access "+n+" due to unbound types",[r,o])}:e=>{Te(n+" is a read-only property")},Object.defineProperty(e.registeredClass.instancePrototype,t,c),be([],l?[r,o]:[r],(function(r){var o=r[0],c={get:function(){var t=_t(this,e,n+" getter");return o.fromWireType(a(i,t))},enumerable:!0};if(l){l=ct(s,l);var f=r[1];c.set=function(t){var r=_t(this,e,n+" setter"),a=[];l(u,r,f.toWireType(a,t)),ue(a)}}return Object.defineProperty(e.registeredClass.instancePrototype,t,c),[]})),[]}))},ib:function(e,t){Ae(e,{name:t=xe(t),fromWireType:function(e){var t=At.toValue(e);return xt(e),t},toWireType:function(e,t){return At.toHandle(t)},argPackAdvance:8,readValueFromPointer:ce,destructorFunction:null})},u:function(e,t,r,n){var a=Ee(r);function i(){}t=xe(t),i.values={},Ae(e,{name:t,constructor:i,fromWireType:function(e){return this.constructor.values[e]},toWireType:function(e,t){return t.value},argPackAdvance:8,readValueFromPointer:It(t,a,n),destructorFunction:null}),Ye(t,i)},Za:function(e,t,r){var n=kt(e,"enum");t=xe(t);var a=n.constructor,i=Object.create(n.constructor.prototype,{value:{value:r},constructor:{value:ve(n.name+"_"+t,(function(){}))}});a.values[r]=i,a[t]=i},I:function(e,t,r){var n=Ee(r);Ae(e,{name:t=xe(t),fromWireType:function(e){return e},toWireType:function(e,t){return t},argPackAdvance:8,readValueFromPointer:Ft(t,n),destructorFunction:null})},D:function(e,t,r,n,a,i){var o=wt(t,r);e=xe(e),a=ct(n,a),Ye(e,(function(){ht("Cannot call "+e+" due to unbound types",o)}),t-1),be([],o,(function(r){var n=[r[0],null].concat(r.slice(1));return lt(e,gt(e,n,null,a,i),t-1),[]}))},m:function(e,t,r,n,a){t=xe(t);var i=Ee(r),o=e=>e;if(0===n){var s=32-8*r;o=e=>e<<s>>>s}var l=t.includes("unsigned");Ae(e,{name:t,fromWireType:o,toWireType:l?function(e,t){return this.name,t>>>0}:function(e,t){return this.name,t},argPackAdvance:8,readValueFromPointer:Dt(t,i,0!==n),destructorFunction:null})},h:function(e,t,r){var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];function a(e){var t=T,r=t[e>>=2],a=t[e+1];return new n(_,a,r)}Ae(e,{name:r=xe(r),fromWireType:a,argPackAdvance:8,readValueFromPointer:a},{ignoreDuplicateRegistrations:!0})},j:function(e,t,r,n,a,i,o,s,l,u,c,f){r=xe(r),i=ct(a,i),s=ct(o,s),u=ct(l,u),f=ct(c,f),be([e],[t],(function(e){return e=e[0],[new st(r,e.registeredClass,!1,!1,!0,e,n,i,s,u,f)]}))},H:function(e,t){var r="std::string"===(t=xe(t));Ae(e,{name:t,fromWireType:function(e){var t,n=T[e>>2],a=e+4;if(r)for(var i=a,o=0;o<=n;++o){var s=a+o;if(o==n||0==E[s]){var l=F(i,s-i);void 0===t?t=l:(t+=String.fromCharCode(0),t+=l),i=s+1}}else{var u=new Array(n);for(o=0;o<n;++o)u[o]=String.fromCharCode(E[a+o]);t=u.join("")}return br(e),t},toWireType:function(e,t){var n;t instanceof ArrayBuffer&&(t=new Uint8Array(t));var a="string"==typeof t;a||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||Te("Cannot pass non-string to std::string"),n=r&&a?M(t):t.length;var i=Er(4+n+1),o=i+4;if(T[i>>2]=n,r&&a)L(t,o,n+1);else if(a)for(var s=0;s<n;++s){var l=t.charCodeAt(s);l>255&&(br(o),Te("String has UTF-16 code units that do not fit in 8 bits")),E[o+s]=l}else for(s=0;s<n;++s)E[o+s]=t[s];return null!==e&&e.push(br,i),i},argPackAdvance:8,readValueFromPointer:ce,destructorFunction:function(e){br(e)}})},w:function(e,t,r){var n,a,i,o,s;r=xe(r),2===t?(n=Mt,a=Rt,o=Gt,i=()=>x,s=1):4===t&&(n=Bt,a=Ot,o=jt,i=()=>T,s=2),Ae(e,{name:r,fromWireType:function(e){for(var r,a=T[e>>2],o=i(),l=e+4,u=0;u<=a;++u){var c=e+4+u*t;if(u==a||0==o[c>>s]){var f=n(l,c-l);void 0===r?r=f:(r+=String.fromCharCode(0),r+=f),l=c+t}}return br(e),r},toWireType:function(e,n){"string"!=typeof n&&Te("Cannot pass non-string to C++ string type "+r);var i=o(n),l=Er(4+i+t);return T[l>>2]=i>>s,a(n,l+4,i+t),null!==e&&e.push(br,l),l},argPackAdvance:8,readValueFromPointer:ce,destructorFunction:function(e){br(e)}})},p:function(e,t,r,n,a,i){le[e]={name:xe(t),rawConstructor:ct(r,n),rawDestructor:ct(a,i),fields:[]}},v:function(e,t,r,n,a,i,o,s,l,u){le[e].fields.push({fieldName:xe(t),getterReturnType:r,getter:ct(n,a),getterContext:i,setterArgumentType:o,setter:ct(s,l),setterContext:u})},kb:function(e,t){Ae(e,{isVoid:!0,name:t=xe(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,t){}})},bb:function(){return!0},x:function(e,t,r){e=At.toValue(e),t=kt(t,"emval::as");var n=[],a=At.toHandle(n);return T[r>>2]=a,t.toWireType(n,e)},U:function(e){return vt.handleAsync((()=>(e=At.toValue(e)).then(At.toHandle)))},gb:function(e,t,r,n){e=At.toValue(e);for(var a=Vt(t,r),i=new Array(t),o=0;o<t;++o){var s=a[o];i[o]=s.readValueFromPointer(n),n+=s.argPackAdvance}var l=e.apply(void 0,i);return At.toHandle(l)},g:function(e,t,r,n,a){return(e=Ut[e])(t=At.toValue(t),r=$t(r),function(e){var t=[];return T[e>>2]=At.toHandle(t),t}(n),a)},k:function(e,t,r,n){(e=Ut[e])(t=At.toValue(t),r=$t(r),null,n)},t:xt,Wa:function(e,t){return(e=At.toValue(e))==(t=At.toValue(t))},ob:function(e){return 0===e?At.toHandle(Ht()):(e=$t(e),At.toHandle(Ht()[e]))},c:function(e,t){var r=Vt(e,t),n=r[0],a=n.name+"_$"+r.slice(1).map((function(e){return e.name})).join("_")+"$",i=Wt[a];if(void 0!==i)return i;for(var o=["retType"],s=[n],l="",u=0;u<e-1;++u)l+=(0!==u?", ":"")+"arg"+u,o.push("argType"+u),s.push(r[1+u]);var c="return function "+ye("methodCaller_"+a)+"(handle, name, destructors, args) {\n",f=0;for(u=0;u<e-1;++u)c+="    var arg"+u+" = argType"+u+".readValueFromPointer(args"+(f?"+"+f:"")+");\n",f+=r[u+1].argPackAdvance;for(c+="    var rv = handle[name]("+l+");\n",u=0;u<e-1;++u)r[u+1].deleteObject&&(c+="    argType"+u+".deleteObject(arg"+u+");\n");n.isVoid||(c+="    return retType.toWireType(destructors, rv);\n"),c+="};\n",o.push(c);var d,h,m=mt(Function,o).apply(null,s);return d=m,h=Ut.length,Ut.push(d),i=h,Wt[a]=i,i},_a:function(e){return e=$t(e),At.toHandle(n[e])},J:function(e,t){return e=At.toValue(e),t=At.toValue(t),At.toHandle(e[t])},y:function(e){e>4&&(Ct[e].refcount+=1)},mb:function(e,t){return(e=At.toValue(e))instanceof(t=At.toValue(t))},nb:function(e,t,r,a){e=At.toValue(e);var i=qt[t];return i||(i=function(e){for(var t="",r=0;r<e;++r)t+=(0!==r?", ":"")+"arg"+r;var a="return function emval_allocator_"+e+"(constructor, argTypes, args) {\n  var HEAPU32 = getMemory();\n";for(r=0;r<e;++r)a+="var argType"+r+" = requireRegisteredType(HEAPU32[((argTypes)>>2)], 'parameter "+r+"');\nvar arg"+r+" = argType"+r+".readValueFromPointer(args);\nargs += argType"+r+"['argPackAdvance'];\nargTypes += 4;\n";return a+="var obj = new constructor("+t+");\nreturn valueToHandle(obj);\n}\n",new Function("requireRegisteredType","Module","valueToHandle","getMemory",a)(kt,n,At.toHandle,(()=>T))}(t),qt[t]=i),i(e,r,a)},Ya:function(){return At.toHandle([])},lb:function(e){return At.toHandle($t(e))},Xa:function(){return At.toHandle({})},xa:function(e){ue(At.toValue(e)),xt(e)},s:function(e,t,r){e=At.toValue(e),t=At.toValue(t),r=At.toValue(r),e[t]=r},l:function(e,t){var r=(e=kt(e,"_emval_take_value")).readValueFromPointer(t);return At.toHandle(r)},q:function(){U("")},B:function(e,t,r){var n=Qt(e);if(!n)return-4;P[t>>2]=n.width,P[r>>2]=n.height},db:zt,Ta:function(e){cr.activeTexture(e)},Sa:function(e,t){cr.attachShader(Yt.programs[e],Yt.shaders[t])},Qa:function(e,t,r){cr.bindAttribLocation(Yt.programs[e],t,F(r))},Pa:function(e,t){35051==e?cr.currentPixelPackBufferBinding=t:35052==e&&(cr.currentPixelUnpackBufferBinding=t),cr.bindBuffer(e,Yt.buffers[t])},Oa:function(e,t){cr.bindFramebuffer(e,Yt.framebuffers[t])},Na:function(e,t){cr.bindRenderbuffer(e,Yt.renderbuffers[t])},Ma:function(e,t){cr.bindTexture(e,Yt.textures[t])},zb:function(e){cr.bindVertexArray(Yt.vaos[e])},wb:function(e){cr.bindVertexArray(Yt.vaos[e])},La:function(e,t,r,n){cr.blendColor(e,t,r,n)},Ka:function(e){cr.blendEquation(e)},Ab:function(e,t){cr.blendEquationSeparate(e,t)},Ja:function(e,t){cr.blendFunc(e,t)},rb:function(e,t,r,n,a,i,o,s,l,u){cr.blitFramebuffer(e,t,r,n,a,i,o,s,l,u)},Ia:function(e,t,r,n){Yt.currentContext.version>=2?r&&t?cr.bufferData(e,E,n,r,t):cr.bufferData(e,t,n):cr.bufferData(e,r?E.subarray(r,r+t):t,n)},Ha:function(e){return cr.checkFramebufferStatus(e)},Ga:function(e){cr.clear(e)},Fa:function(e,t,r,n){cr.clearColor(e,t,r,n)},Ea:function(e){cr.clearStencil(e)},Da:function(e,t,r,n){cr.colorMask(!!e,!!t,!!r,!!n)},Ca:function(e){cr.compileShader(Yt.shaders[e])},Ba:function(e,t,r,n,a,i,o,s){cr.copyTexSubImage2D(e,t,r,n,a,i,o,s)},Aa:function(){var e=Yt.getNewId(Yt.programs),t=cr.createProgram();return t.name=e,t.maxUniformLength=t.maxAttributeLength=t.maxUniformBlockNameLength=0,t.uniformIdCounter=1,Yt.programs[e]=t,e},za:function(e){var t=Yt.getNewId(Yt.shaders);return Yt.shaders[t]=cr.createShader(e),t},ya:function(e,t){for(var r=0;r<e;r++){var n=P[t+4*r>>2],a=Yt.buffers[n];a&&(cr.deleteBuffer(a),a.name=0,Yt.buffers[n]=null,n==cr.currentPixelPackBufferBinding&&(cr.currentPixelPackBufferBinding=0),n==cr.currentPixelUnpackBufferBinding&&(cr.currentPixelUnpackBufferBinding=0))}},wa:function(e,t){for(var r=0;r<e;++r){var n=P[t+4*r>>2],a=Yt.framebuffers[n];a&&(cr.deleteFramebuffer(a),a.name=0,Yt.framebuffers[n]=null)}},va:function(e){if(e){var t=Yt.programs[e];t?(cr.deleteProgram(t),t.name=0,Yt.programs[e]=null):Yt.recordError(1281)}},ua:function(e,t){for(var r=0;r<e;r++){var n=P[t+4*r>>2],a=Yt.renderbuffers[n];a&&(cr.deleteRenderbuffer(a),a.name=0,Yt.renderbuffers[n]=null)}},ta:function(e){if(e){var t=Yt.shaders[e];t?(cr.deleteShader(t),Yt.shaders[e]=null):Yt.recordError(1281)}},sb:function(e){if(e){var t=Yt.syncs[e];t?(cr.deleteSync(t),t.name=0,Yt.syncs[e]=null):Yt.recordError(1281)}},sa:function(e,t){for(var r=0;r<e;r++){var n=P[t+4*r>>2],a=Yt.textures[n];a&&(cr.deleteTexture(a),a.name=0,Yt.textures[n]=null)}},yb:function(e,t){for(var r=0;r<e;r++){var n=P[t+4*r>>2];cr.deleteVertexArray(Yt.vaos[n]),Yt.vaos[n]=null}},vb:function(e,t){for(var r=0;r<e;r++){var n=P[t+4*r>>2];cr.deleteVertexArray(Yt.vaos[n]),Yt.vaos[n]=null}},ra:function(e){cr.depthMask(!!e)},qa:function(e){cr.disable(e)},pa:function(e){cr.disableVertexAttribArray(e)},oa:function(e,t,r){cr.drawArrays(e,t,r)},na:function(e,t,r,n){cr.drawElements(e,t,r,n)},ma:function(e){cr.enable(e)},la:function(e){cr.enableVertexAttribArray(e)},tb:function(e,t){var r=cr.fenceSync(e,t);if(r){var n=Yt.getNewId(Yt.syncs);return r.name=n,Yt.syncs[n]=r,n}return 0},ka:function(){cr.finish()},ja:function(){cr.flush()},ia:function(e,t,r,n){cr.framebufferRenderbuffer(e,t,r,Yt.renderbuffers[n])},ha:function(e,t,r,n,a){cr.framebufferTexture2D(e,t,r,Yt.textures[n],a)},ga:function(e,t){Zt(e,t,"createBuffer",Yt.buffers)},fa:function(e,t){Zt(e,t,"createFramebuffer",Yt.framebuffers)},ea:function(e,t){Zt(e,t,"createRenderbuffer",Yt.renderbuffers)},da:function(e,t){Zt(e,t,"createTexture",Yt.textures)},xb:function(e,t){Zt(e,t,"createVertexArray",Yt.vaos)},ub:function(e,t){Zt(e,t,"createVertexArray",Yt.vaos)},Bb:function(e,t){return cr.getAttribLocation(Yt.programs[e],F(t))},ca:function(e,t,r){r?P[r>>2]=cr.getBufferParameter(e,t):Yt.recordError(1281)},ba:function(){var e=cr.getError()||Yt.lastError;return Yt.lastError=0,e},aa:function(e,t,r,n){var a=cr.getFramebufferAttachmentParameter(e,t,r);(a instanceof WebGLRenderbuffer||a instanceof WebGLTexture)&&(a=0|a.name),P[n>>2]=a},$:function(e,t){er(e,t,0)},_:function(e,t,r,n){var a=cr.getProgramInfoLog(Yt.programs[e]);null===a&&(a="(unknown error)");var i=t>0&&n?L(a,n,t):0;r&&(P[r>>2]=i)},Z:function(e,t,r){if(r)if(e>=Yt.counter)Yt.recordError(1281);else if(e=Yt.programs[e],35716==t){var n=cr.getProgramInfoLog(e);null===n&&(n="(unknown error)"),P[r>>2]=n.length+1}else if(35719==t){if(!e.maxUniformLength)for(var a=0;a<cr.getProgramParameter(e,35718);++a)e.maxUniformLength=Math.max(e.maxUniformLength,cr.getActiveUniform(e,a).name.length+1);P[r>>2]=e.maxUniformLength}else if(35722==t){if(!e.maxAttributeLength)for(a=0;a<cr.getProgramParameter(e,35721);++a)e.maxAttributeLength=Math.max(e.maxAttributeLength,cr.getActiveAttrib(e,a).name.length+1);P[r>>2]=e.maxAttributeLength}else if(35381==t){if(!e.maxUniformBlockNameLength)for(a=0;a<cr.getProgramParameter(e,35382);++a)e.maxUniformBlockNameLength=Math.max(e.maxUniformBlockNameLength,cr.getActiveUniformBlockName(e,a).length+1);P[r>>2]=e.maxUniformBlockNameLength}else P[r>>2]=cr.getProgramParameter(e,t);else Yt.recordError(1281)},Y:function(e,t,r){r?P[r>>2]=cr.getRenderbufferParameter(e,t):Yt.recordError(1281)},X:function(e,t,r,n){var a=cr.getShaderInfoLog(Yt.shaders[e]);null===a&&(a="(unknown error)");var i=t>0&&n?L(a,n,t):0;r&&(P[r>>2]=i)},W:function(e,t,r,n){var a=cr.getShaderPrecisionFormat(e,t);P[r>>2]=a.rangeMin,P[r+4>>2]=a.rangeMax,P[n>>2]=a.precision},V:function(e,t,r){if(r)if(35716==t){var n=cr.getShaderInfoLog(Yt.shaders[e]);null===n&&(n="(unknown error)");var a=n?n.length+1:0;P[r>>2]=a}else if(35720==t){var i=cr.getShaderSource(Yt.shaders[e]),o=i?i.length+1:0;P[r>>2]=o}else P[r>>2]=cr.getShaderParameter(Yt.shaders[e],t);else Yt.recordError(1281)},T:function(e){var t=Yt.stringCache[e];if(!t){switch(e){case 7939:var r=cr.getSupportedExtensions()||[];t=tr((r=r.concat(r.map((function(e){return"GL_"+e})))).join(" "));break;case 7936:case 7937:case 37445:case 37446:var n=cr.getParameter(e);n||Yt.recordError(1280),t=n&&tr(n);break;case 7938:var a=cr.getParameter(7938);t=tr(a=Yt.currentContext.version>=2?"OpenGL ES 3.0 ("+a+")":"OpenGL ES 2.0 ("+a+")");break;case 35724:var i=cr.getParameter(35724),o=i.match(/^WebGL GLSL ES ([0-9]\.[0-9][0-9]?)(?:$| .*)/);null!==o&&(3==o[1].length&&(o[1]=o[1]+"0"),i="OpenGL ES GLSL ES "+o[1]+" ("+i+")"),t=tr(i);break;default:Yt.recordError(1280)}Yt.stringCache[e]=t}return t},S:function(e,t){if(Yt.currentContext.version<2)return Yt.recordError(1282),0;var r=Yt.stringiCache[e];if(r)return t<0||t>=r.length?(Yt.recordError(1281),0):r[t];if(7939===e){var n=cr.getSupportedExtensions()||[];return n=(n=n.concat(n.map((function(e){return"GL_"+e})))).map((function(e){return tr(e)})),r=Yt.stringiCache[e]=n,t<0||t>=r.length?(Yt.recordError(1281),0):r[t]}return Yt.recordError(1280),0},R:function(e,t){if(t=F(t),e=Yt.programs[e]){!function(e){var t,r,n=e.uniformLocsById,a=e.uniformSizeAndIdsByName;if(!n)for(e.uniformLocsById=n={},e.uniformArrayNamesById={},t=0;t<cr.getProgramParameter(e,35718);++t){var i=cr.getActiveUniform(e,t),o=i.name,s=i.size,l=rr(o),u=l>0?o.slice(0,l):o,c=e.uniformIdCounter;for(e.uniformIdCounter+=s,a[u]=[s,c],r=0;r<s;++r)n[c]=r,e.uniformArrayNamesById[c++]=u}}(e);var r=e.uniformLocsById,n=0,a=t,i=rr(t);i>0&&(s=t.slice(i+1),n=parseInt(s)>>>0,a=t.slice(0,i));var o=e.uniformSizeAndIdsByName[a];if(o&&n<o[0]&&(r[n+=o[1]]=r[n]||cr.getUniformLocation(e,t)))return n}else Yt.recordError(1281);var s;return-1},Ra:function(e){return cr.isEnabled(e)},Q:function(e){var t=Yt.textures[e];return t?cr.isTexture(t):0},P:function(e){cr.lineWidth(e)},O:function(e){e=Yt.programs[e],cr.linkProgram(e),e.uniformLocsById=0,e.uniformSizeAndIdsByName={}},N:function(e,t){3317==e&&(Yt.unpackAlignment=t),cr.pixelStorei(e,t)},M:function(e,t,r,n,a,i,o){if(Yt.currentContext.version>=2)if(cr.currentPixelPackBufferBinding)cr.readPixels(e,t,r,n,a,i,o);else{var s=nr(i);cr.readPixels(e,t,r,n,a,i,s,o>>ar(s))}else{var l=ir(i,a,r,n,o);l?cr.readPixels(e,t,r,n,a,i,l):Yt.recordError(1280)}},L:function(e,t,r,n){cr.renderbufferStorage(e,t,r,n)},qb:function(e,t,r,n,a){cr.renderbufferStorageMultisample(e,t,r,n,a)},K:function(e,t,r,n){cr.scissor(e,t,r,n)},gc:function(e,t,r,n){var a=Yt.getSource(e,t,r,n);cr.shaderSource(Yt.shaders[e],a)},fc:function(e,t,r,n,a,i,o,s,l){if(Yt.currentContext.version>=2)if(cr.currentPixelUnpackBufferBinding)cr.texImage2D(e,t,r,n,a,i,o,s,l);else if(l){var u=nr(s);cr.texImage2D(e,t,r,n,a,i,o,s,u,l>>ar(u))}else cr.texImage2D(e,t,r,n,a,i,o,s,null);else cr.texImage2D(e,t,r,n,a,i,o,s,l?ir(s,o,n,a,l):null)},ec:function(e,t,r){cr.texParameterf(e,t,r)},dc:function(e,t,r){var n=A[r>>2];cr.texParameterf(e,t,n)},cc:function(e,t,r){cr.texParameteri(e,t,r)},bc:function(e,t,r){var n=P[r>>2];cr.texParameteri(e,t,n)},ac:function(e,t,r,n,a,i,o,s,l){if(Yt.currentContext.version>=2)if(cr.currentPixelUnpackBufferBinding)cr.texSubImage2D(e,t,r,n,a,i,o,s,l);else if(l){var u=nr(s);cr.texSubImage2D(e,t,r,n,a,i,o,s,u,l>>ar(u))}else cr.texSubImage2D(e,t,r,n,a,i,o,s,null);else{var c=null;l&&(c=ir(s,o,a,i,l)),cr.texSubImage2D(e,t,r,n,a,i,o,s,c)}},$b:function(e,t){cr.uniform1f(or(e),t)},_b:function(e,t,r){if(Yt.currentContext.version>=2)t&&cr.uniform1fv(or(e),A,r>>2,t);else{if(t<=288)for(var n=sr[t-1],a=0;a<t;++a)n[a]=A[r+4*a>>2];else n=A.subarray(r>>2,r+4*t>>2);cr.uniform1fv(or(e),n)}},Zb:function(e,t){cr.uniform1i(or(e),t)},Yb:function(e,t,r){if(Yt.currentContext.version>=2)t&&cr.uniform1iv(or(e),P,r>>2,t);else{if(t<=288)for(var n=lr[t-1],a=0;a<t;++a)n[a]=P[r+4*a>>2];else n=P.subarray(r>>2,r+4*t>>2);cr.uniform1iv(or(e),n)}},Xb:function(e,t,r){cr.uniform2f(or(e),t,r)},Wb:function(e,t,r){if(Yt.currentContext.version>=2)t&&cr.uniform2fv(or(e),A,r>>2,2*t);else{if(t<=144)for(var n=sr[2*t-1],a=0;a<2*t;a+=2)n[a]=A[r+4*a>>2],n[a+1]=A[r+(4*a+4)>>2];else n=A.subarray(r>>2,r+8*t>>2);cr.uniform2fv(or(e),n)}},Vb:function(e,t,r){cr.uniform2i(or(e),t,r)},Ub:function(e,t,r){if(Yt.currentContext.version>=2)t&&cr.uniform2iv(or(e),P,r>>2,2*t);else{if(t<=144)for(var n=lr[2*t-1],a=0;a<2*t;a+=2)n[a]=P[r+4*a>>2],n[a+1]=P[r+(4*a+4)>>2];else n=P.subarray(r>>2,r+8*t>>2);cr.uniform2iv(or(e),n)}},Tb:function(e,t,r,n){cr.uniform3f(or(e),t,r,n)},Sb:function(e,t,r){if(Yt.currentContext.version>=2)t&&cr.uniform3fv(or(e),A,r>>2,3*t);else{if(t<=96)for(var n=sr[3*t-1],a=0;a<3*t;a+=3)n[a]=A[r+4*a>>2],n[a+1]=A[r+(4*a+4)>>2],n[a+2]=A[r+(4*a+8)>>2];else n=A.subarray(r>>2,r+12*t>>2);cr.uniform3fv(or(e),n)}},Rb:function(e,t,r,n){cr.uniform3i(or(e),t,r,n)},Qb:function(e,t,r){if(Yt.currentContext.version>=2)t&&cr.uniform3iv(or(e),P,r>>2,3*t);else{if(t<=96)for(var n=lr[3*t-1],a=0;a<3*t;a+=3)n[a]=P[r+4*a>>2],n[a+1]=P[r+(4*a+4)>>2],n[a+2]=P[r+(4*a+8)>>2];else n=P.subarray(r>>2,r+12*t>>2);cr.uniform3iv(or(e),n)}},Pb:function(e,t,r,n,a){cr.uniform4f(or(e),t,r,n,a)},Ob:function(e,t,r){if(Yt.currentContext.version>=2)t&&cr.uniform4fv(or(e),A,r>>2,4*t);else{if(t<=72){var n=sr[4*t-1],a=A;r>>=2;for(var i=0;i<4*t;i+=4){var o=r+i;n[i]=a[o],n[i+1]=a[o+1],n[i+2]=a[o+2],n[i+3]=a[o+3]}}else n=A.subarray(r>>2,r+16*t>>2);cr.uniform4fv(or(e),n)}},Nb:function(e,t,r,n,a){cr.uniform4i(or(e),t,r,n,a)},Mb:function(e,t,r){if(Yt.currentContext.version>=2)t&&cr.uniform4iv(or(e),P,r>>2,4*t);else{if(t<=72)for(var n=lr[4*t-1],a=0;a<4*t;a+=4)n[a]=P[r+4*a>>2],n[a+1]=P[r+(4*a+4)>>2],n[a+2]=P[r+(4*a+8)>>2],n[a+3]=P[r+(4*a+12)>>2];else n=P.subarray(r>>2,r+16*t>>2);cr.uniform4iv(or(e),n)}},Lb:function(e,t,r,n){if(Yt.currentContext.version>=2)t&&cr.uniformMatrix2fv(or(e),!!r,A,n>>2,4*t);else{if(t<=72)for(var a=sr[4*t-1],i=0;i<4*t;i+=4)a[i]=A[n+4*i>>2],a[i+1]=A[n+(4*i+4)>>2],a[i+2]=A[n+(4*i+8)>>2],a[i+3]=A[n+(4*i+12)>>2];else a=A.subarray(n>>2,n+16*t>>2);cr.uniformMatrix2fv(or(e),!!r,a)}},Kb:function(e,t,r,n){if(Yt.currentContext.version>=2)t&&cr.uniformMatrix3fv(or(e),!!r,A,n>>2,9*t);else{if(t<=32)for(var a=sr[9*t-1],i=0;i<9*t;i+=9)a[i]=A[n+4*i>>2],a[i+1]=A[n+(4*i+4)>>2],a[i+2]=A[n+(4*i+8)>>2],a[i+3]=A[n+(4*i+12)>>2],a[i+4]=A[n+(4*i+16)>>2],a[i+5]=A[n+(4*i+20)>>2],a[i+6]=A[n+(4*i+24)>>2],a[i+7]=A[n+(4*i+28)>>2],a[i+8]=A[n+(4*i+32)>>2];else a=A.subarray(n>>2,n+36*t>>2);cr.uniformMatrix3fv(or(e),!!r,a)}},Jb:function(e,t,r,n){if(Yt.currentContext.version>=2)t&&cr.uniformMatrix4fv(or(e),!!r,A,n>>2,16*t);else{if(t<=18){var a=sr[16*t-1],i=A;n>>=2;for(var o=0;o<16*t;o+=16){var s=n+o;a[o]=i[s],a[o+1]=i[s+1],a[o+2]=i[s+2],a[o+3]=i[s+3],a[o+4]=i[s+4],a[o+5]=i[s+5],a[o+6]=i[s+6],a[o+7]=i[s+7],a[o+8]=i[s+8],a[o+9]=i[s+9],a[o+10]=i[s+10],a[o+11]=i[s+11],a[o+12]=i[s+12],a[o+13]=i[s+13],a[o+14]=i[s+14],a[o+15]=i[s+15]}}else a=A.subarray(n>>2,n+64*t>>2);cr.uniformMatrix4fv(or(e),!!r,a)}},Ib:function(e){e=Yt.programs[e],cr.useProgram(e),cr.currentProgram=e},Hb:function(e,t){cr.vertexAttrib1f(e,t)},Gb:function(e,t){cr.vertexAttrib2f(e,A[t>>2],A[t+4>>2])},Fb:function(e,t){cr.vertexAttrib3f(e,A[t>>2],A[t+4>>2],A[t+8>>2])},Eb:function(e,t){cr.vertexAttrib4f(e,A[t>>2],A[t+4>>2],A[t+8>>2],A[t+12>>2])},Db:function(e,t,r,n,a,i){cr.vertexAttribPointer(e,t,r,!!n,a,i)},Cb:function(e,t,r,n){cr.viewport(e,t,r,n)},pb:function(e,t,r,n){cr.waitSync(Yt.syncs[e],t,(r>>>0)+4294967296*n)},cb:function(e){var t,r,n=E.length,a=2147483648;if((e>>>=0)>a)return!1;for(var i=1;i<=4;i*=2){var o=n*(1+.2/i);if(o=Math.min(o,e+100663296),ur(Math.min(a,(t=Math.max(e,o))+((r=65536)-t%r)%r)))return!0}return!1},Ua:dr,C:function(e){Yt.currentContext==e&&(Yt.currentContext=0),Yt.deleteContext(e)},n:hr,Va:function(e){for(var t=e>>2,r=0;r<14;++r)P[t+r]=0;P[t+0]=P[t+1]=P[t+3]=P[t+4]=P[t+8]=P[t+10]=1},i:function(e){return Yt.makeContextCurrent(e)?0:-5},E:function(e){try{var t=se.getStreamFromFD(e);return oe.close(t),0}catch(e){if(void 0===oe||!(e instanceof oe.ErrnoError))throw e;return e.errno}},eb:function(e,t,r,n){try{var a=function(e,t,r,n){for(var a=0,i=0;i<r;i++){var o=T[t>>2],s=T[t+4>>2];t+=8;var l=oe.read(e,b,o,s,n);if(l<0)return-1;if(a+=l,l<s)break}return a}(se.getStreamFromFD(e),t,r);return T[n>>2]=a,0}catch(e){if(void 0===oe||!(e instanceof oe.ErrnoError))throw e;return e.errno}},$a:function(e,t,r,n,a){try{var i=(l=r)+2097152>>>0<4194305-!!(s=t)?(s>>>0)+4294967296*l:NaN;if(isNaN(i))return 61;var o=se.getStreamFromFD(e);return oe.llseek(o,i,n),q=[o.position>>>0,(W=o.position,+Math.abs(W)>=1?W>0?(0|Math.min(+Math.floor(W/4294967296),4294967295))>>>0:~~+Math.ceil((W-+(~~W>>>0))/4294967296)>>>0:0)],P[a>>2]=q[0],P[a+4>>2]=q[1],o.getdents&&0===i&&0===n&&(o.getdents=null),0}catch(e){if(void 0===oe||!(e instanceof oe.ErrnoError))throw e;return e.errno}var s,l},F:function(e,t,r,n){try{var a=function(e,t,r,n){for(var a=0,i=0;i<r;i++){var o=T[t>>2],s=T[t+4>>2];t+=8;var l=oe.write(e,b,o,s,n);if(l<0)return-1;a+=l}return a}(se.getStreamFromFD(e),t,r);return T[n>>2]=a,0}catch(e){if(void 0===oe||!(e instanceof oe.ErrnoError))throw e;return e.errno}}};!function(){var e={a:_r};function t(e,t){var r,a=e.exports;a=vt.instrumentWasmExports(a),n.asm=a,R((p=n.asm.hc).buffer),n.asm.jc,r=n.asm.ic,B.unshift(r),$()}function a(e){t(e.instance)}function i(t){return(m||!o&&!s||"function"!=typeof fetch?Promise.resolve().then((function(){return K(H)})):fetch(H,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+H+"'";return e.arrayBuffer()})).catch((function(){return K(H)}))).then((function(t){return WebAssembly.instantiate(t,e)})).then((function(e){return e})).then(t,(function(e){h("failed to asynchronously prepare wasm: "+e),U(e)}))}if(N(),n.instantiateWasm)try{var l=n.instantiateWasm(e,t);return l=vt.instrumentWasmExports(l)}catch(e){return h("Module.instantiateWasm callback failed with error: "+e),!1}(m||"function"!=typeof WebAssembly.instantiateStreaming||J(H)||"function"!=typeof fetch?i(a):fetch(H,{credentials:"same-origin"}).then((function(t){return WebAssembly.instantiateStreaming(t,e).then(a,(function(e){return h("wasm streaming compile failed: "+e),h("falling back to ArrayBuffer instantiation"),i(a)}))}))).catch(r)}(),n.___wasm_call_ctors=function(){return(n.___wasm_call_ctors=n.asm.ic).apply(null,arguments)};var br=n._free=function(){return(br=n._free=n.asm.kc).apply(null,arguments)},Er=n._malloc=function(){return(Er=n._malloc=n.asm.lc).apply(null,arguments)},Cr=n.___getTypeName=function(){return(Cr=n.___getTypeName=n.asm.mc).apply(null,arguments)};n.__embind_initialize_bindings=function(){return(n.__embind_initialize_bindings=n.asm.nc).apply(null,arguments)};var xr=n.___errno_location=function(){return(xr=n.___errno_location=n.asm.oc).apply(null,arguments)},Pr=n.___cxa_is_pointer_type=function(){return(Pr=n.___cxa_is_pointer_type=n.asm.pc).apply(null,arguments)};n.dynCall_ii=function(){return(n.dynCall_ii=n.asm.qc).apply(null,arguments)},n.dynCall_vi=function(){return(n.dynCall_vi=n.asm.rc).apply(null,arguments)},n.dynCall_ji=function(){return(n.dynCall_ji=n.asm.sc).apply(null,arguments)},n.dynCall_iii=function(){return(n.dynCall_iii=n.asm.tc).apply(null,arguments)},n.dynCall_iij=function(){return(n.dynCall_iij=n.asm.uc).apply(null,arguments)},n.dynCall_viiij=function(){return(n.dynCall_viiij=n.asm.vc).apply(null,arguments)},n.dynCall_viii=function(){return(n.dynCall_viii=n.asm.wc).apply(null,arguments)},n.dynCall_vii=function(){return(n.dynCall_vii=n.asm.xc).apply(null,arguments)},n.dynCall_v=function(){return(n.dynCall_v=n.asm.yc).apply(null,arguments)},n.dynCall_viij=function(){return(n.dynCall_viij=n.asm.zc).apply(null,arguments)},n.dynCall_fij=function(){return(n.dynCall_fij=n.asm.Ac).apply(null,arguments)},n.dynCall_viiii=function(){return(n.dynCall_viiii=n.asm.Bc).apply(null,arguments)},n.dynCall_fii=function(){return(n.dynCall_fii=n.asm.Cc).apply(null,arguments)},n.dynCall_fif=function(){return(n.dynCall_fif=n.asm.Dc).apply(null,arguments)},n.dynCall_iiiff=function(){return(n.dynCall_iiiff=n.asm.Ec).apply(null,arguments)},n.dynCall_vijiii=function(){return(n.dynCall_vijiii=n.asm.Fc).apply(null,arguments)},n.dynCall_viiiii=function(){return(n.dynCall_viiiii=n.asm.Gc).apply(null,arguments)},n.dynCall_iiiiii=function(){return(n.dynCall_iiiiii=n.asm.Hc).apply(null,arguments)},n.dynCall_iiii=function(){return(n.dynCall_iiii=n.asm.Ic).apply(null,arguments)},n.dynCall_vij=function(){return(n.dynCall_vij=n.asm.Jc).apply(null,arguments)},n.dynCall_iiiiiii=function(){return(n.dynCall_iiiiiii=n.asm.Kc).apply(null,arguments)},n.dynCall_viiff=function(){return(n.dynCall_viiff=n.asm.Lc).apply(null,arguments)},n.dynCall_iiij=function(){return(n.dynCall_iiij=n.asm.Mc).apply(null,arguments)},n.dynCall_iiiii=function(){return(n.dynCall_iiiii=n.asm.Nc).apply(null,arguments)},n.dynCall_viif=function(){return(n.dynCall_viif=n.asm.Oc).apply(null,arguments)},n.dynCall_viiffff=function(){return(n.dynCall_viiffff=n.asm.Pc).apply(null,arguments)},n.dynCall_iiifii=function(){return(n.dynCall_iiifii=n.asm.Qc).apply(null,arguments)},n.dynCall_iiiffii=function(){return(n.dynCall_iiiffii=n.asm.Rc).apply(null,arguments)},n.dynCall_iiiij=function(){return(n.dynCall_iiiij=n.asm.Sc).apply(null,arguments)},n.dynCall_viiiiii=function(){return(n.dynCall_viiiiii=n.asm.Tc).apply(null,arguments)},n.dynCall_iiiiiiii=function(){return(n.dynCall_iiiiiiii=n.asm.Uc).apply(null,arguments)},n.dynCall_jii=function(){return(n.dynCall_jii=n.asm.Vc).apply(null,arguments)},n.dynCall_jij=function(){return(n.dynCall_jij=n.asm.Wc).apply(null,arguments)},n.dynCall_fi=function(){return(n.dynCall_fi=n.asm.Xc).apply(null,arguments)},n.dynCall_jijf=function(){return(n.dynCall_jijf=n.asm.Yc).apply(null,arguments)},n.dynCall_i=function(){return(n.dynCall_i=n.asm.Zc).apply(null,arguments)},n.dynCall_vif=function(){return(n.dynCall_vif=n.asm._c).apply(null,arguments)},n.dynCall_di=function(){return(n.dynCall_di=n.asm.$c).apply(null,arguments)},n.dynCall_vid=function(){return(n.dynCall_vid=n.asm.ad).apply(null,arguments)},n.dynCall_iiiifii=function(){return(n.dynCall_iiiifii=n.asm.bd).apply(null,arguments)},n.dynCall_iiiffi=function(){return(n.dynCall_iiiffi=n.asm.cd).apply(null,arguments)},n.dynCall_vifffffffff=function(){return(n.dynCall_vifffffffff=n.asm.dd).apply(null,arguments)},n.dynCall_iifffffffff=function(){return(n.dynCall_iifffffffff=n.asm.ed).apply(null,arguments)},n.dynCall_iiff=function(){return(n.dynCall_iiff=n.asm.fd).apply(null,arguments)},n.dynCall_iif=function(){return(n.dynCall_iif=n.asm.gd).apply(null,arguments)},n.dynCall_viff=function(){return(n.dynCall_viff=n.asm.hd).apply(null,arguments)},n.dynCall_viffffff=function(){return(n.dynCall_viffffff=n.asm.id).apply(null,arguments)},n.dynCall_dii=function(){return(n.dynCall_dii=n.asm.jd).apply(null,arguments)},n.dynCall_viid=function(){return(n.dynCall_viid=n.asm.kd).apply(null,arguments)},n.dynCall_iiiiffi=function(){return(n.dynCall_iiiiffi=n.asm.ld).apply(null,arguments)},n.dynCall_fiii=function(){return(n.dynCall_fiii=n.asm.md).apply(null,arguments)},n.dynCall_viiif=function(){return(n.dynCall_viiif=n.asm.nd).apply(null,arguments)},n.dynCall_viifffffffff=function(){return(n.dynCall_viifffffffff=n.asm.od).apply(null,arguments)},n.dynCall_viiffffff=function(){return(n.dynCall_viiffffff=n.asm.pd).apply(null,arguments)},n.dynCall_viifff=function(){return(n.dynCall_viifff=n.asm.qd).apply(null,arguments)},n.dynCall_viiifii=function(){return(n.dynCall_viiifii=n.asm.rd).apply(null,arguments)},n.dynCall_viiiiiii=function(){return(n.dynCall_viiiiiii=n.asm.sd).apply(null,arguments)},n.dynCall_viffff=function(){return(n.dynCall_viffff=n.asm.td).apply(null,arguments)},n.dynCall_vifff=function(){return(n.dynCall_vifff=n.asm.ud).apply(null,arguments)},n.dynCall_iiffi=function(){return(n.dynCall_iiffi=n.asm.vd).apply(null,arguments)},n.dynCall_iifii=function(){return(n.dynCall_iifii=n.asm.wd).apply(null,arguments)},n.dynCall_iiifi=function(){return(n.dynCall_iiifi=n.asm.xd).apply(null,arguments)},n.dynCall_iiiiij=function(){return(n.dynCall_iiiiij=n.asm.yd).apply(null,arguments)},n.dynCall_iijj=function(){return(n.dynCall_iijj=n.asm.zd).apply(null,arguments)},n.dynCall_vffff=function(){return(n.dynCall_vffff=n.asm.Ad).apply(null,arguments)},n.dynCall_viiiiiiii=function(){return(n.dynCall_viiiiiiii=n.asm.Bd).apply(null,arguments)},n.dynCall_vf=function(){return(n.dynCall_vf=n.asm.Cd).apply(null,arguments)},n.dynCall_viiiiiiiii=function(){return(n.dynCall_viiiiiiiii=n.asm.Dd).apply(null,arguments)},n.dynCall_viiiiiiiiii=function(){return(n.dynCall_viiiiiiiiii=n.asm.Ed).apply(null,arguments)},n.dynCall_fiifiii=function(){return(n.dynCall_fiifiii=n.asm.Fd).apply(null,arguments)},n.dynCall_iiifiii=function(){return(n.dynCall_iiifiii=n.asm.Gd).apply(null,arguments)},n.dynCall_viiifiii=function(){return(n.dynCall_viiifiii=n.asm.Hd).apply(null,arguments)},n.dynCall_vifii=function(){return(n.dynCall_vifii=n.asm.Id).apply(null,arguments)},n.dynCall_viifd=function(){return(n.dynCall_viifd=n.asm.Jd).apply(null,arguments)},n.dynCall_viddi=function(){return(n.dynCall_viddi=n.asm.Kd).apply(null,arguments)},n.dynCall_viiiiiffii=function(){return(n.dynCall_viiiiiffii=n.asm.Ld).apply(null,arguments)},n.dynCall_jiiii=function(){return(n.dynCall_jiiii=n.asm.Md).apply(null,arguments)},n.dynCall_jiji=function(){return(n.dynCall_jiji=n.asm.Nd).apply(null,arguments)},n.dynCall_iidiiii=function(){return(n.dynCall_iidiiii=n.asm.Od).apply(null,arguments)};var Tr,Ar=n._asyncify_start_unwind=function(){return(Ar=n._asyncify_start_unwind=n.asm.Pd).apply(null,arguments)},Ir=n._asyncify_stop_unwind=function(){return(Ir=n._asyncify_stop_unwind=n.asm.Qd).apply(null,arguments)},kr=n._asyncify_start_rewind=function(){return(kr=n._asyncify_start_rewind=n.asm.Rd).apply(null,arguments)},Sr=n._asyncify_stop_rewind=function(){return(Sr=n._asyncify_stop_rewind=n.asm.Sd).apply(null,arguments)};function Fr(e){function r(){Tr||(Tr=!0,n.calledRun=!0,g||(n.noFSInit||oe.init.initialized||oe.init(),oe.ignorePermissions=!1,Y(B),t(n),n.onRuntimeInitialized&&n.onRuntimeInitialized(),function(){if(n.postRun)for("function"==typeof n.postRun&&(n.postRun=[n.postRun]);n.postRun.length;)e=n.postRun.shift(),O.unshift(e);var e;Y(O)}()))}j>0||(function(){if(n.preRun)for("function"==typeof n.preRun&&(n.preRun=[n.preRun]);n.preRun.length;)e=n.preRun.shift(),G.unshift(e);var e;Y(G)}(),j>0||(n.setStatus?(n.setStatus("Running..."),setTimeout((function(){setTimeout((function(){n.setStatus("")}),1),r()}),1)):r()))}if(n.GL=Yt,n.Asyncify=vt,V=function e(){Tr||Fr(),Tr||(V=e)},n.preInit)for("function"==typeof n.preInit&&(n.preInit=[n.preInit]);n.preInit.length>0;)n.preInit.pop()();return Fr(),e.ready});class Ct{constructor(){this.executing=!1,this.queue=[]}exec(e,t,...r){return new Promise(((n,a)=>{this.queue.push({fn:async()=>{if(e)try{const a=await e.call(t,...r);n(a)}catch(e){a(e)}else a(new Error("Function is null!"))}}),this.executing||this.execNextTask()}))}execNextTask(){if(this.queue.length<1)return void(this.executing=!1);this.executing=!0;this.queue.shift().fn().then((()=>{this.execNextTask()}))}}const xt=[],Pt=[],Tt=[],At=[],It=(e={})=>Et(e).then((e=>(_t(e),e.webAssemblyQueue=new Ct,e.globalCanvas=new e.GlobalCanvas,e.PAGFont.registerFallbackFontNames(),e))).catch((e=>{throw console.error(e),new Error("PAGInit fail! Please check .wasm file path valid.")}));ne&&(e=>{onmessage=async t=>{const r={[$e.PAGInit]:t=>{const r={};t.data.args[0]&&(r.locateFile=()=>t.data.args[0].fileUrl),e(r).then((e=>{self.PAG=e,self.postMessage({name:t.data.name,args:[]})}))},[$e.PAGFile_load]:async e=>{const t=(r=await self.PAG.PAGFile.load(e.data.args[0]),xt.push(r),xt.length-1);var r;self.postMessage({name:e.data.name,args:[t]})},[$e.PAGView_init]:async e=>{const[t,r,n]=e.data.args,a=(i=await self.PAG.PAGView.init(xt[t],r,n),Pt.push(i),Pt.length-1);var i;self.postMessage({name:e.data.name,args:[a]})},[$e.PAGView_destroy]:async e=>{const[t]=e.data.args;Pt[t].destroy(),(e=>{Pt[e]=null})(t),self.postMessage({name:e.data.name,args:[]})},[$e.PAGFile_getTextData]:async e=>{const[t,r]=e.data.args,n=xt[t].getTextData(r);let a={};for(const e in n)"function"!=typeof n[e]&&(a[e]=n[e]);a.key=(e=>(At.push(e),At.length-1))(n),self.postMessage({name:e.data.name,args:[a]})},[$e.PAGFile_replaceText]:async e=>{const[t,r,n]=e.data.args,a=At[n.key];for(const e in n)"key"!==e&&(a[e]=n[e]);xt[t].replaceText(r,a),self.postMessage({name:e.data.name,args:[]})},[$e.PAGFile_replaceImage]:async e=>{const[t,r,n]=e.data.args,a=Tt[n];xt[t].replaceImage(r,a),self.postMessage({name:e.data.name,args:[]})},[$e.PAGFile_destroy]:async e=>{const[t]=e.data.args;xt[t].destroy(),(e=>{xt[e]=null})(t),self.postMessage({name:e.data.name,args:[]})},[$e.PAGImage_fromSource]:async e=>{const[t]=e.data.args,r=(n=self.PAG.PAGImage.fromSource(t),Tt.push(n),Tt.length-1);var n;self.postMessage({name:e.data.name,args:[r]})},[$e.PAGImage_destroy]:async e=>{const[t]=e.data.args;Tt[t].destroy(),(e=>{Tt[e]=null})(t),self.postMessage({name:e.data.name,args:[]})},[$e.TextDocument_delete]:async e=>{const[t]=e.data.args;At[t].delete(),(e=>{At[e]=null})(t),self.postMessage({name:e.data.name,args:[]})}},n=t.data.name.split("_")[0];if(r[n])return void r[n](t);const[a,i]=n.split("."),o=t.data.args[0];if("PAGFile"!==a)if("PAGView"!==a);else{const e=Pt[o];if(!e)throw new Error("pagView doesn't exist");const r=e[i];if(!r)throw new Error(`PAGView.${i} doesn't exist`);const n=await r.call(e,...t.data.args.slice(1));self.postMessage({name:t.data.name,args:[n]})}else{const e=xt[o];if(!e)throw new Error("pagFile doesn't exist");const r=e[i];if(!r)throw new Error(`PAGFile.${i} doesn't exist`);const n=await r.call(e,...t.data.args.slice(1));self.postMessage({name:t.data.name,args:[n]})}}})(It),e.PAGInit=It,e.types=y,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=libpag.min.js.map