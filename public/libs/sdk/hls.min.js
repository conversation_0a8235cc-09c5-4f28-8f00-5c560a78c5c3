"undefined"!=typeof window&&function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.Hls=e():t.Hls=e()}(this,function(){return function(t){var e={};function r(i){if(e[i])return e[i].exports;var a=e[i]={i:i,l:!1,exports:{}};return t[i].call(a.exports,a,a.exports,r),a.l=!0,a.exports}return r.m=t,r.c=e,r.d=function(t,e,i){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(r.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var a in t)r.d(i,a,function(e){return t[e]}.bind(null,a));return i},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="/dist/",r(r.s=13)}([function(t,e,r){"use strict";r.d(e,"a",function(){return d}),r.d(e,"b",function(){return u});var i=r(5);function a(){}var n={trace:a,debug:a,log:a,warn:a,info:a,error:a},s=n;var o=Object(i.a)();function l(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];r.forEach(function(e){s[e]=t[e]?t[e].bind(t):function(t){var e=o.console[t];return e?function(){for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];i[0]&&(i[0]=function(t,e){return e="["+t+"] > "+e}(t,i[0])),e.apply(o.console,i)}:a}(e)})}var d=function(t){if(o.console&&!0===t||"object"==typeof t){l(t,"debug","log","info","warn","error");try{s.log()}catch(t){s=n}}else s=n},u=s},function(t,e,r){"use strict";e.a={MEDIA_ATTACHING:"hlsMediaAttaching",MEDIA_ATTACHED:"hlsMediaAttached",MEDIA_DETACHING:"hlsMediaDetaching",MEDIA_DETACHED:"hlsMediaDetached",BUFFER_RESET:"hlsBufferReset",BUFFER_CODECS:"hlsBufferCodecs",BUFFER_CREATED:"hlsBufferCreated",BUFFER_APPENDING:"hlsBufferAppending",BUFFER_APPENDED:"hlsBufferAppended",BUFFER_EOS:"hlsBufferEos",BUFFER_FLUSHING:"hlsBufferFlushing",BUFFER_FLUSHED:"hlsBufferFlushed",MANIFEST_LOADING:"hlsManifestLoading",MANIFEST_LOADED:"hlsManifestLoaded",MANIFEST_PARSED:"hlsManifestParsed",LEVEL_SWITCHING:"hlsLevelSwitching",LEVEL_SWITCHED:"hlsLevelSwitched",LEVEL_LOADING:"hlsLevelLoading",LEVEL_LOADED:"hlsLevelLoaded",LEVEL_UPDATED:"hlsLevelUpdated",LEVEL_PTS_UPDATED:"hlsLevelPtsUpdated",AUDIO_TRACKS_UPDATED:"hlsAudioTracksUpdated",AUDIO_TRACK_SWITCHING:"hlsAudioTrackSwitching",AUDIO_TRACK_SWITCHED:"hlsAudioTrackSwitched",AUDIO_TRACK_LOADING:"hlsAudioTrackLoading",AUDIO_TRACK_LOADED:"hlsAudioTrackLoaded",SUBTITLE_TRACKS_UPDATED:"hlsSubtitleTracksUpdated",SUBTITLE_TRACK_SWITCH:"hlsSubtitleTrackSwitch",SUBTITLE_TRACK_LOADING:"hlsSubtitleTrackLoading",SUBTITLE_TRACK_LOADED:"hlsSubtitleTrackLoaded",SUBTITLE_FRAG_PROCESSED:"hlsSubtitleFragProcessed",INIT_PTS_FOUND:"hlsInitPtsFound",FRAG_LOADING:"hlsFragLoading",FRAG_LOAD_PROGRESS:"hlsFragLoadProgress",FRAG_LOAD_EMERGENCY_ABORTED:"hlsFragLoadEmergencyAborted",FRAG_LOADED:"hlsFragLoaded",FRAG_DECRYPTED:"hlsFragDecrypted",FRAG_PARSING_INIT_SEGMENT:"hlsFragParsingInitSegment",FRAG_PARSING_USERDATA:"hlsFragParsingUserdata",FRAG_PARSING_METADATA:"hlsFragParsingMetadata",FRAG_PARSING_DATA:"hlsFragParsingData",FRAG_PARSED:"hlsFragParsed",FRAG_BUFFERED:"hlsFragBuffered",FRAG_CHANGED:"hlsFragChanged",FPS_DROP:"hlsFpsDrop",FPS_DROP_LEVEL_CAPPING:"hlsFpsDropLevelCapping",ERROR:"hlsError",DESTROYING:"hlsDestroying",KEY_LOADING:"hlsKeyLoading",KEY_LOADED:"hlsKeyLoaded",STREAM_STATE_TRANSITION:"hlsStreamStateTransition"}},function(t,e,r){"use strict";var i,a;r.d(e,"b",function(){return i}),r.d(e,"a",function(){return a}),function(t){t.NETWORK_ERROR="networkError",t.MEDIA_ERROR="mediaError",t.KEY_SYSTEM_ERROR="keySystemError",t.MUX_ERROR="muxError",t.OTHER_ERROR="otherError"}(i||(i={})),function(t){t.KEY_SYSTEM_NO_KEYS="keySystemNoKeys",t.KEY_SYSTEM_NO_ACCESS="keySystemNoAccess",t.KEY_SYSTEM_NO_SESSION="keySystemNoSession",t.KEY_SYSTEM_LICENSE_REQUEST_FAILED="keySystemLicenseRequestFailed",t.KEY_SYSTEM_NO_INIT_DATA="keySystemNoInitData",t.MANIFEST_LOAD_ERROR="manifestLoadError",t.MANIFEST_LOAD_TIMEOUT="manifestLoadTimeOut",t.MANIFEST_PARSING_ERROR="manifestParsingError",t.MANIFEST_INCOMPATIBLE_CODECS_ERROR="manifestIncompatibleCodecsError",t.LEVEL_LOAD_ERROR="levelLoadError",t.LEVEL_LOAD_TIMEOUT="levelLoadTimeOut",t.LEVEL_SWITCH_ERROR="levelSwitchError",t.AUDIO_TRACK_LOAD_ERROR="audioTrackLoadError",t.AUDIO_TRACK_LOAD_TIMEOUT="audioTrackLoadTimeOut",t.FRAG_LOAD_ERROR="fragLoadError",t.FRAG_LOAD_TIMEOUT="fragLoadTimeOut",t.FRAG_DECRYPT_ERROR="fragDecryptError",t.FRAG_PARSING_ERROR="fragParsingError",t.REMUX_ALLOC_ERROR="remuxAllocError",t.KEY_LOAD_ERROR="keyLoadError",t.KEY_LOAD_TIMEOUT="keyLoadTimeOut",t.BUFFER_ADD_CODEC_ERROR="bufferAddCodecError",t.BUFFER_APPEND_ERROR="bufferAppendError",t.BUFFER_APPENDING_ERROR="bufferAppendingError",t.BUFFER_STALLED_ERROR="bufferStalledError",t.BUFFER_FULL_ERROR="bufferFullError",t.BUFFER_SEEK_OVER_HOLE="bufferSeekOverHole",t.BUFFER_NUDGE_ON_STALL="bufferNudgeOnStall",t.INTERNAL_EXCEPTION="internalException"}(a||(a={}))},function(t,e,r){"use strict";r.d(e,"a",function(){return i});var i=Number.isFinite||function(t){return"number"==typeof t&&isFinite(t)}},function(t,e,r){"use strict";r.d(e,"b",function(){return o});var i,a=r(5),n=function(){function t(){}return t.isHeader=function(t,e){return e+10<=t.length&&73===t[e]&&68===t[e+1]&&51===t[e+2]&&t[e+3]<255&&t[e+4]<255&&t[e+6]<128&&t[e+7]<128&&t[e+8]<128&&t[e+9]<128},t.isFooter=function(t,e){return e+10<=t.length&&51===t[e]&&68===t[e+1]&&73===t[e+2]&&t[e+3]<255&&t[e+4]<255&&t[e+6]<128&&t[e+7]<128&&t[e+8]<128&&t[e+9]<128},t.getID3Data=function(e,r){for(var i=r,a=0;t.isHeader(e,r);){a+=10,a+=t._readSize(e,r+6),t.isFooter(e,r+10)&&(a+=10),r+=a}if(a>0)return e.subarray(i,i+a)},t._readSize=function(t,e){var r=0;return r=(127&t[e])<<21,r|=(127&t[e+1])<<14,r|=(127&t[e+2])<<7,r|=127&t[e+3]},t.getTimeStamp=function(e){for(var r=t.getID3Frames(e),i=0;i<r.length;i++){var a=r[i];if(t.isTimeStampFrame(a))return t._readTimeStamp(a)}},t.isTimeStampFrame=function(t){return t&&"PRIV"===t.key&&"com.apple.streaming.transportStreamTimestamp"===t.info},t._getFrameData=function(e){var r=String.fromCharCode(e[0],e[1],e[2],e[3]),i=t._readSize(e,4);return{type:r,size:i,data:e.subarray(10,10+i)}},t.getID3Frames=function(e){for(var r=0,i=[];t.isHeader(e,r);){for(var a=t._readSize(e,r+6),n=(r+=10)+a;r+8<n;){var s=t._getFrameData(e.subarray(r)),o=t._decodeFrame(s);o&&i.push(o),r+=s.size+10}t.isFooter(e,r)&&(r+=10)}return i},t._decodeFrame=function(e){return"PRIV"===e.type?t._decodePrivFrame(e):"T"===e.type[0]?t._decodeTextFrame(e):"W"===e.type[0]?t._decodeURLFrame(e):void 0},t._readTimeStamp=function(t){if(8===t.data.byteLength){var e=new Uint8Array(t.data),r=1&e[3],i=(e[4]<<23)+(e[5]<<15)+(e[6]<<7)+e[7];return i/=45,r&&(i+=47721858.84),Math.round(i)}},t._decodePrivFrame=function(e){if(!(e.size<2)){var r=t._utf8ArrayToStr(e.data,!0),i=new Uint8Array(e.data.subarray(r.length+1));return{key:e.type,info:r,data:i.buffer}}},t._decodeTextFrame=function(e){if(!(e.size<2)){if("TXXX"===e.type){var r=1,i=t._utf8ArrayToStr(e.data.subarray(r),!0);r+=i.length+1;var a=t._utf8ArrayToStr(e.data.subarray(r));return{key:e.type,info:i,data:a}}var n=t._utf8ArrayToStr(e.data.subarray(1));return{key:e.type,data:n}}},t._decodeURLFrame=function(e){if("WXXX"===e.type){if(e.size<2)return;var r=1,i=t._utf8ArrayToStr(e.data.subarray(r));r+=i.length+1;var a=t._utf8ArrayToStr(e.data.subarray(r));return{key:e.type,info:i,data:a}}var n=t._utf8ArrayToStr(e.data);return{key:e.type,data:n}},t._utf8ArrayToStr=function(t,e){void 0===e&&(e=!1);var r=s();if(r){var i=r.decode(t);if(e){var a=i.indexOf("\0");return-1!==a?i.substring(0,a):i}return i.replace(/\0/g,"")}for(var n,o,l,d=t.length,u="",c=0;c<d;){if(0===(n=t[c++])&&e)return u;if(0!==n&&3!==n)switch(n>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:u+=String.fromCharCode(n);break;case 12:case 13:o=t[c++],u+=String.fromCharCode((31&n)<<6|63&o);break;case 14:o=t[c++],l=t[c++],u+=String.fromCharCode((15&n)<<12|(63&o)<<6|(63&l)<<0)}}return u},t}();function s(){var t=Object(a.a)();return i||void 0===t.TextDecoder||(i=new t.TextDecoder("utf-8")),i}var o=n._utf8ArrayToStr;e.a=n},function(t,e,r){"use strict";function i(){return"undefined"==typeof window?self:window}r.d(e,"a",function(){return i})},function(t,e,r){var i,a,n,s,o;i=/^((?:[a-zA-Z0-9+\-.]+:)?)(\/\/[^\/?#]*)?((?:[^\/\?#]*\/)*.*?)??(;.*?)?(\?.*?)?(#.*?)?$/,a=/^([^\/?#]*)(.*)$/,n=/(?:\/|^)\.(?=\/)/g,s=/(?:\/|^)\.\.\/(?!\.\.\/).*?(?=\/)/g,o={buildAbsoluteURL:function(t,e,r){if(r=r||{},t=t.trim(),!(e=e.trim())){if(!r.alwaysNormalize)return t;var i=o.parseURL(t);if(!i)throw new Error("Error trying to parse base URL.");return i.path=o.normalizePath(i.path),o.buildURLFromParts(i)}var n=o.parseURL(e);if(!n)throw new Error("Error trying to parse relative URL.");if(n.scheme)return r.alwaysNormalize?(n.path=o.normalizePath(n.path),o.buildURLFromParts(n)):e;var s=o.parseURL(t);if(!s)throw new Error("Error trying to parse base URL.");if(!s.netLoc&&s.path&&"/"!==s.path[0]){var l=a.exec(s.path);s.netLoc=l[1],s.path=l[2]}s.netLoc&&!s.path&&(s.path="/");var d={scheme:s.scheme,netLoc:n.netLoc,path:null,params:n.params,query:n.query,fragment:n.fragment};if(!n.netLoc&&(d.netLoc=s.netLoc,"/"!==n.path[0]))if(n.path){var u=s.path,c=u.substring(0,u.lastIndexOf("/")+1)+n.path;d.path=o.normalizePath(c)}else d.path=s.path,n.params||(d.params=s.params,n.query||(d.query=s.query));return null===d.path&&(d.path=r.alwaysNormalize?o.normalizePath(n.path):n.path),o.buildURLFromParts(d)},parseURL:function(t){var e=i.exec(t);return e?{scheme:e[1]||"",netLoc:e[2]||"",path:e[3]||"",params:e[4]||"",query:e[5]||"",fragment:e[6]||""}:null},normalizePath:function(t){for(t=t.split("").reverse().join("").replace(n,"");t.length!==(t=t.replace(s,"")).length;);return t.split("").reverse().join("")},buildURLFromParts:function(t){return t.scheme+t.netLoc+t.path+t.params+t.query+t.fragment}},t.exports=o},function(t,e,r){"use strict";var i=function(){function t(t,e){this.subtle=t,this.aesIV=e}return t.prototype.decrypt=function(t,e){return this.subtle.decrypt({name:"AES-CBC",iv:this.aesIV},e,t)},t}(),a=function(){function t(t,e){this.subtle=t,this.key=e}return t.prototype.expandKey=function(){return this.subtle.importKey("raw",this.key,{name:"AES-CBC"},!1,["encrypt","decrypt"])},t}();var n=function(){function t(){this.rcon=[0,1,2,4,8,16,32,64,128,27,54],this.subMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.invSubMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.sBox=new Uint32Array(256),this.invSBox=new Uint32Array(256),this.key=new Uint32Array(0),this.initTable()}var e=t.prototype;return e.uint8ArrayToUint32Array_=function(t){for(var e=new DataView(t),r=new Uint32Array(4),i=0;i<4;i++)r[i]=e.getUint32(4*i);return r},e.initTable=function(){var t=this.sBox,e=this.invSBox,r=this.subMix,i=r[0],a=r[1],n=r[2],s=r[3],o=this.invSubMix,l=o[0],d=o[1],u=o[2],c=o[3],h=new Uint32Array(256),f=0,g=0,p=0;for(p=0;p<256;p++)h[p]=p<128?p<<1:p<<1^283;for(p=0;p<256;p++){var v=g^g<<1^g<<2^g<<3^g<<4;v=v>>>8^255&v^99,t[f]=v,e[v]=f;var m=h[f],y=h[m],b=h[y],E=257*h[v]^16843008*v;i[f]=E<<24|E>>>8,a[f]=E<<16|E>>>16,n[f]=E<<8|E>>>24,s[f]=E,E=16843009*b^65537*y^257*m^16843008*f,l[v]=E<<24|E>>>8,d[v]=E<<16|E>>>16,u[v]=E<<8|E>>>24,c[v]=E,f?(f=m^h[h[h[b^m]]],g^=h[h[g]]):f=g=1}},e.expandKey=function(t){for(var e=this.uint8ArrayToUint32Array_(t),r=!0,i=0;i<e.length&&r;)r=e[i]===this.key[i],i++;if(!r){this.key=e;var a=this.keySize=e.length;if(4!==a&&6!==a&&8!==a)throw new Error("Invalid aes key size="+a);var n,s,o,l,d=this.ksRows=4*(a+6+1),u=this.keySchedule=new Uint32Array(d),c=this.invKeySchedule=new Uint32Array(d),h=this.sBox,f=this.rcon,g=this.invSubMix,p=g[0],v=g[1],m=g[2],y=g[3];for(n=0;n<d;n++)n<a?o=u[n]=e[n]:(l=o,n%a==0?(l=h[(l=l<<8|l>>>24)>>>24]<<24|h[l>>>16&255]<<16|h[l>>>8&255]<<8|h[255&l],l^=f[n/a|0]<<24):a>6&&n%a==4&&(l=h[l>>>24]<<24|h[l>>>16&255]<<16|h[l>>>8&255]<<8|h[255&l]),u[n]=o=(u[n-a]^l)>>>0);for(s=0;s<d;s++)n=d-s,l=3&s?u[n]:u[n-4],c[s]=s<4||n<=4?l:p[h[l>>>24]]^v[h[l>>>16&255]]^m[h[l>>>8&255]]^y[h[255&l]],c[s]=c[s]>>>0}},e.networkToHostOrderSwap=function(t){return t<<24|(65280&t)<<8|(16711680&t)>>8|t>>>24},e.decrypt=function(t,e,r,i){for(var a,n,s,o,l,d,u,c,h,f,g,p,v,m,y,b,E,T=this.keySize+6,S=this.invKeySchedule,_=this.invSBox,A=this.invSubMix,R=A[0],L=A[1],D=A[2],w=A[3],k=this.uint8ArrayToUint32Array_(r),I=k[0],O=k[1],C=k[2],P=k[3],x=new Int32Array(t),F=new Int32Array(x.length),M=this.networkToHostOrderSwap;e<x.length;){for(h=M(x[e]),f=M(x[e+1]),g=M(x[e+2]),p=M(x[e+3]),l=h^S[0],d=p^S[1],u=g^S[2],c=f^S[3],v=4,m=1;m<T;m++)a=R[l>>>24]^L[d>>16&255]^D[u>>8&255]^w[255&c]^S[v],n=R[d>>>24]^L[u>>16&255]^D[c>>8&255]^w[255&l]^S[v+1],s=R[u>>>24]^L[c>>16&255]^D[l>>8&255]^w[255&d]^S[v+2],o=R[c>>>24]^L[l>>16&255]^D[d>>8&255]^w[255&u]^S[v+3],l=a,d=n,u=s,c=o,v+=4;a=_[l>>>24]<<24^_[d>>16&255]<<16^_[u>>8&255]<<8^_[255&c]^S[v],n=_[d>>>24]<<24^_[u>>16&255]<<16^_[c>>8&255]<<8^_[255&l]^S[v+1],s=_[u>>>24]<<24^_[c>>16&255]<<16^_[l>>8&255]<<8^_[255&d]^S[v+2],o=_[c>>>24]<<24^_[l>>16&255]<<16^_[d>>8&255]<<8^_[255&u]^S[v+3],v+=3,F[e]=M(a^I),F[e+1]=M(o^O),F[e+2]=M(s^C),F[e+3]=M(n^P),I=h,O=f,C=g,P=p,e+=4}return i?(y=F.buffer,b=y.byteLength,(E=b&&new DataView(y).getUint8(b-1))?y.slice(0,b-E):y):F.buffer},e.destroy=function(){this.key=void 0,this.keySize=void 0,this.ksRows=void 0,this.sBox=void 0,this.invSBox=void 0,this.subMix=void 0,this.invSubMix=void 0,this.keySchedule=void 0,this.invKeySchedule=void 0,this.rcon=void 0},t}(),s=r(2),o=r(0),l=r(1),d=r(5),u=Object(d.a)(),c=function(){function t(t,e,r){var i=(void 0===r?{}:r).removePKCS7Padding,a=void 0===i||i;if(this.logEnabled=!0,this.observer=t,this.config=e,this.removePKCS7Padding=a,a)try{var n=u.crypto;n&&(this.subtle=n.subtle||n.webkitSubtle)}catch(t){}this.disableWebCrypto=!this.subtle}var e=t.prototype;return e.isSync=function(){return this.disableWebCrypto&&this.config.enableSoftwareAES},e.decrypt=function(t,e,r,s){var l=this;if(this.disableWebCrypto&&this.config.enableSoftwareAES){this.logEnabled&&(o.b.log("JS AES decrypt"),this.logEnabled=!1);var d=this.decryptor;d||(this.decryptor=d=new n),d.expandKey(e),s(d.decrypt(t,0,r,this.removePKCS7Padding))}else{this.logEnabled&&(o.b.log("WebCrypto AES decrypt"),this.logEnabled=!1);var u=this.subtle;this.key!==e&&(this.key=e,this.fastAesKey=new a(u,e)),this.fastAesKey.expandKey().then(function(a){new i(u,r).decrypt(t,a).catch(function(i){l.onWebCryptoError(i,t,e,r,s)}).then(function(t){s(t)})}).catch(function(i){l.onWebCryptoError(i,t,e,r,s)})}},e.onWebCryptoError=function(t,e,r,i,a){this.config.enableSoftwareAES?(o.b.log("WebCrypto Error, disable WebCrypto API"),this.disableWebCrypto=!0,this.logEnabled=!0,this.decrypt(e,r,i,a)):(o.b.error("decrypting error : "+t.message),this.observer.trigger(l.a.ERROR,{type:s.b.MEDIA_ERROR,details:s.a.FRAG_DECRYPT_ERROR,fatal:!0,reason:t.message}))},e.destroy=function(){var t=this.decryptor;t&&(t.destroy(),this.decryptor=void 0)},t}();e.a=c},function(t,e,r){"use strict";var i=Object.prototype.hasOwnProperty,a="~";function n(){}function s(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function o(t,e,r,i,n){if("function"!=typeof r)throw new TypeError("The listener must be a function");var o=new s(r,i||t,n),l=a?a+e:e;return t._events[l]?t._events[l].fn?t._events[l]=[t._events[l],o]:t._events[l].push(o):(t._events[l]=o,t._eventsCount++),t}function l(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function d(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(a=!1)),d.prototype.eventNames=function(){var t,e,r=[];if(0===this._eventsCount)return r;for(e in t=this._events)i.call(t,e)&&r.push(a?e.slice(1):e);return Object.getOwnPropertySymbols?r.concat(Object.getOwnPropertySymbols(t)):r},d.prototype.listeners=function(t){var e=a?a+t:t,r=this._events[e];if(!r)return[];if(r.fn)return[r.fn];for(var i=0,n=r.length,s=new Array(n);i<n;i++)s[i]=r[i].fn;return s},d.prototype.listenerCount=function(t){var e=a?a+t:t,r=this._events[e];return r?r.fn?1:r.length:0},d.prototype.emit=function(t,e,r,i,n,s){var o=a?a+t:t;if(!this._events[o])return!1;var l,d,u=this._events[o],c=arguments.length;if(u.fn){switch(u.once&&this.removeListener(t,u.fn,void 0,!0),c){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,e),!0;case 3:return u.fn.call(u.context,e,r),!0;case 4:return u.fn.call(u.context,e,r,i),!0;case 5:return u.fn.call(u.context,e,r,i,n),!0;case 6:return u.fn.call(u.context,e,r,i,n,s),!0}for(d=1,l=new Array(c-1);d<c;d++)l[d-1]=arguments[d];u.fn.apply(u.context,l)}else{var h,f=u.length;for(d=0;d<f;d++)switch(u[d].once&&this.removeListener(t,u[d].fn,void 0,!0),c){case 1:u[d].fn.call(u[d].context);break;case 2:u[d].fn.call(u[d].context,e);break;case 3:u[d].fn.call(u[d].context,e,r);break;case 4:u[d].fn.call(u[d].context,e,r,i);break;default:if(!l)for(h=1,l=new Array(c-1);h<c;h++)l[h-1]=arguments[h];u[d].fn.apply(u[d].context,l)}}return!0},d.prototype.on=function(t,e,r){return o(this,t,e,r,!1)},d.prototype.once=function(t,e,r){return o(this,t,e,r,!0)},d.prototype.removeListener=function(t,e,r,i){var n=a?a+t:t;if(!this._events[n])return this;if(!e)return l(this,n),this;var s=this._events[n];if(s.fn)s.fn!==e||i&&!s.once||r&&s.context!==r||l(this,n);else{for(var o=0,d=[],u=s.length;o<u;o++)(s[o].fn!==e||i&&!s[o].once||r&&s[o].context!==r)&&d.push(s[o]);d.length?this._events[n]=1===d.length?d[0]:d:l(this,n)}return this},d.prototype.removeAllListeners=function(t){var e;return t?(e=a?a+t:t,this._events[e]&&l(this,e)):(this._events=new n,this._eventsCount=0),this},d.prototype.off=d.prototype.removeListener,d.prototype.addListener=d.prototype.on,d.prefixed=a,d.EventEmitter=d,t.exports=d},function(t,e,r){"use strict";var i=r(1),a=r(2),n=r(7),s=r(3),o=r(0),l=r(5);function d(t,e){return 255===t[e]&&240==(246&t[e+1])}function u(t,e){return 1&t[e+1]?7:9}function c(t,e){return(3&t[e+3])<<11|t[e+4]<<3|(224&t[e+5])>>>5}function h(t,e){return!!(e+1<t.length&&d(t,e))}function f(t,e){if(h(t,e)){var r=u(t,e);e+5<t.length&&(r=c(t,e));var i=e+r;if(i===t.length||i+1<t.length&&d(t,i))return!0}return!1}function g(t,e,r,n,s){if(!t.samplerate){var l=function(t,e,r,n){var s,l,d,u,c,h=navigator.userAgent.toLowerCase(),f=n,g=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];if(s=1+((192&e[r+2])>>>6),!((l=(60&e[r+2])>>>2)>g.length-1))return u=(1&e[r+2])<<2,u|=(192&e[r+3])>>>6,o.b.log("manifest codec:"+n+",ADTS data:type:"+s+",sampleingIndex:"+l+"["+g[l]+"Hz],channelConfig:"+u),/firefox/i.test(h)?l>=6?(s=5,c=new Array(4),d=l-3):(s=2,c=new Array(2),d=l):-1!==h.indexOf("android")?(s=2,c=new Array(2),d=l):(s=5,c=new Array(4),n&&(-1!==n.indexOf("mp4a.40.29")||-1!==n.indexOf("mp4a.40.5"))||!n&&l>=6?d=l-3:((n&&-1!==n.indexOf("mp4a.40.2")&&(l>=6&&1===u||/vivaldi/i.test(h))||!n&&1===u)&&(s=2,c=new Array(2)),d=l)),c[0]=s<<3,c[0]|=(14&l)>>1,c[1]|=(1&l)<<7,c[1]|=u<<3,5===s&&(c[1]|=(14&d)>>1,c[2]=(1&d)<<7,c[2]|=8,c[3]=0),{config:c,samplerate:g[l],channelCount:u,codec:"mp4a.40."+s,manifestCodec:f};t.trigger(i.a.ERROR,{type:a.b.MEDIA_ERROR,details:a.a.FRAG_PARSING_ERROR,fatal:!0,reason:"invalid ADTS sampling index:"+l})}(e,r,n,s);t.config=l.config,t.samplerate=l.samplerate,t.channelCount=l.channelCount,t.codec=l.codec,t.manifestCodec=l.manifestCodec,o.b.log("parsed codec:"+t.codec+",rate:"+l.samplerate+",nb channel:"+l.channelCount)}}function p(t){return 9216e4/t}function v(t,e,r,i,a){var n=function(t,e,r,i,a){var n,s,o=t.length;if(n=u(t,e),s=c(t,e),(s-=n)>0&&e+n+s<=o)return{headerLength:n,frameLength:s,stamp:r+i*a}}(e,r,i,a,p(t.samplerate));if(n){var s=n.stamp,o=n.headerLength,l=n.frameLength,d={unit:e.subarray(r+o,r+o+l),pts:s,dts:s};return t.samples.push(d),{sample:d,length:l+o}}}var m=r(4),y=function(){function t(t,e,r){this.observer=t,this.config=r,this.remuxer=e}var e=t.prototype;return e.resetInitSegment=function(t,e,r,i){this._audioTrack={container:"audio/adts",type:"audio",id:0,sequenceNumber:0,isAAC:!0,samples:[],len:0,manifestCodec:e,duration:i,inputTimeScale:9e4}},e.resetTimeStamp=function(){},t.probe=function(t){if(!t)return!1;for(var e=(m.a.getID3Data(t,0)||[]).length,r=t.length;e<r;e++)if(f(t,e))return o.b.log("ADTS sync word found !"),!0;return!1},e.append=function(t,e,r,i){for(var a=this._audioTrack,n=m.a.getID3Data(t,0)||[],l=m.a.getTimeStamp(n),d=Object(s.a)(l)?90*l:9e4*e,u=0,c=d,f=t.length,p=n.length,y=[{pts:c,dts:c,data:n}];p<f-1;)if(h(t,p)&&p+5<f){g(a,this.observer,t,p,a.manifestCodec);var b=v(a,t,p,d,u);if(!b){o.b.log("Unable to parse AAC frame");break}p+=b.length,c=b.sample.pts,u++}else m.a.isHeader(t,p)?(n=m.a.getID3Data(t,p),y.push({pts:c,dts:c,data:n}),p+=n.length):p++;this.remuxer.remux(a,{samples:[]},{samples:y,inputTimeScale:9e4},{samples:[]},e,r,i)},e.destroy=function(){},t}(),b=r(10),E={BitratesMap:[32,64,96,128,160,192,224,256,288,320,352,384,416,448,32,48,56,64,80,96,112,128,160,192,224,256,320,384,32,40,48,56,64,80,96,112,128,160,192,224,256,320,32,48,56,64,80,96,112,128,144,160,176,192,224,256,8,16,24,32,40,48,56,64,80,96,112,128,144,160],SamplingRateMap:[44100,48e3,32e3,22050,24e3,16e3,11025,12e3,8e3],SamplesCoefficients:[[0,72,144,12],[0,0,0,0],[0,72,144,12],[0,144,144,12]],BytesInSlot:[0,1,1,4],appendFrame:function(t,e,r,i,a){if(!(r+24>e.length)){var n=this.parseHeader(e,r);if(n&&r+n.frameLength<=e.length){var s=i+a*(9e4*n.samplesPerFrame/n.sampleRate),o={unit:e.subarray(r,r+n.frameLength),pts:s,dts:s};return t.config=[],t.channelCount=n.channelCount,t.samplerate=n.sampleRate,t.samples.push(o),{sample:o,length:n.frameLength}}}},parseHeader:function(t,e){var r=t[e+1]>>3&3,i=t[e+1]>>1&3,a=t[e+2]>>4&15,n=t[e+2]>>2&3,s=t[e+2]>>1&1;if(1!==r&&0!==a&&15!==a&&3!==n){var o=3===r?3-i:3===i?3:4,l=1e3*E.BitratesMap[14*o+a-1],d=3===r?0:2===r?1:2,u=E.SamplingRateMap[3*d+n],c=t[e+3]>>6==3?1:2,h=E.SamplesCoefficients[r][i],f=E.BytesInSlot[i],g=8*h*f;return{sampleRate:u,channelCount:c,frameLength:parseInt(h*l/u+s,10)*f,samplesPerFrame:g}}},isHeaderPattern:function(t,e){return 255===t[e]&&224==(224&t[e+1])&&0!=(6&t[e+1])},isHeader:function(t,e){return!!(e+1<t.length&&this.isHeaderPattern(t,e))},probe:function(t,e){if(e+1<t.length&&this.isHeaderPattern(t,e)){var r=this.parseHeader(t,e),i=4;r&&r.frameLength&&(i=r.frameLength);var a=e+i;if(a===t.length||a+1<t.length&&this.isHeaderPattern(t,a))return!0}return!1}},T=E,S=function(){function t(t){this.data=t,this.bytesAvailable=t.byteLength,this.word=0,this.bitsAvailable=0}var e=t.prototype;return e.loadWord=function(){var t=this.data,e=this.bytesAvailable,r=t.byteLength-e,i=new Uint8Array(4),a=Math.min(4,e);if(0===a)throw new Error("no bytes available");i.set(t.subarray(r,r+a)),this.word=new DataView(i.buffer).getUint32(0),this.bitsAvailable=8*a,this.bytesAvailable-=a},e.skipBits=function(t){var e;this.bitsAvailable>t?(this.word<<=t,this.bitsAvailable-=t):(t-=this.bitsAvailable,t-=(e=t>>3)>>3,this.bytesAvailable-=e,this.loadWord(),this.word<<=t,this.bitsAvailable-=t)},e.readBits=function(t){var e=Math.min(this.bitsAvailable,t),r=this.word>>>32-e;return t>32&&o.b.error("Cannot read more than 32 bits at a time"),this.bitsAvailable-=e,this.bitsAvailable>0?this.word<<=e:this.bytesAvailable>0&&this.loadWord(),(e=t-e)>0&&this.bitsAvailable?r<<e|this.readBits(e):r},e.skipLZ=function(){var t;for(t=0;t<this.bitsAvailable;++t)if(0!=(this.word&2147483648>>>t))return this.word<<=t,this.bitsAvailable-=t,t;return this.loadWord(),t+this.skipLZ()},e.skipUEG=function(){this.skipBits(1+this.skipLZ())},e.skipEG=function(){this.skipBits(1+this.skipLZ())},e.readUEG=function(){var t=this.skipLZ();return this.readBits(t+1)-1},e.readEG=function(){var t=this.readUEG();return 1&t?1+t>>>1:-1*(t>>>1)},e.readBoolean=function(){return 1===this.readBits(1)},e.readUByte=function(){return this.readBits(8)},e.readUShort=function(){return this.readBits(16)},e.readUInt=function(){return this.readBits(32)},e.skipScalingList=function(t){var e,r=8,i=8;for(e=0;e<t;e++)0!==i&&(i=(r+this.readEG()+256)%256),r=0===i?r:i},e.readSPS=function(){var t,e,r,i,a,n,s,o=0,l=0,d=0,u=0,c=this.readUByte.bind(this),h=this.readBits.bind(this),f=this.readUEG.bind(this),g=this.readBoolean.bind(this),p=this.skipBits.bind(this),v=this.skipEG.bind(this),m=this.skipUEG.bind(this),y=this.skipScalingList.bind(this);if(c(),t=c(),h(5),p(3),c(),m(),100===t||110===t||122===t||244===t||44===t||83===t||86===t||118===t||128===t){var b=f();if(3===b&&p(1),m(),m(),p(1),g())for(n=3!==b?8:12,s=0;s<n;s++)g()&&y(s<6?16:64)}m();var E=f();if(0===E)f();else if(1===E)for(p(1),v(),v(),e=f(),s=0;s<e;s++)v();m(),p(1),r=f(),i=f(),0===(a=h(1))&&p(1),p(1),g()&&(o=f(),l=f(),d=f(),u=f());var T=[1,1];if(g()&&g())switch(c()){case 1:T=[1,1];break;case 2:T=[12,11];break;case 3:T=[10,11];break;case 4:T=[16,11];break;case 5:T=[40,33];break;case 6:T=[24,11];break;case 7:T=[20,11];break;case 8:T=[32,11];break;case 9:T=[80,33];break;case 10:T=[18,11];break;case 11:T=[15,11];break;case 12:T=[64,33];break;case 13:T=[160,99];break;case 14:T=[4,3];break;case 15:T=[3,2];break;case 16:T=[2,1];break;case 255:T=[c()<<8|c(),c()<<8|c()]}return{width:Math.ceil(16*(r+1)-2*o-2*l),height:(2-a)*(i+1)*16-(a?2:4)*(d+u),pixelRatio:T}},e.readSliceType=function(){return this.readUByte(),this.readUEG(),this.readUEG()},t}(),_=function(){function t(t,e,r,i){this.decryptdata=r,this.discardEPB=i,this.decrypter=new n.a(t,e,{removePKCS7Padding:!1})}var e=t.prototype;return e.decryptBuffer=function(t,e){this.decrypter.decrypt(t,this.decryptdata.key.buffer,this.decryptdata.iv.buffer,e)},e.decryptAacSample=function(t,e,r,i){var a=t[e].unit,n=a.subarray(16,a.length-a.length%16),s=n.buffer.slice(n.byteOffset,n.byteOffset+n.length),o=this;this.decryptBuffer(s,function(n){n=new Uint8Array(n),a.set(n,16),i||o.decryptAacSamples(t,e+1,r)})},e.decryptAacSamples=function(t,e,r){for(;;e++){if(e>=t.length)return void r();if(!(t[e].unit.length<32)){var i=this.decrypter.isSync();if(this.decryptAacSample(t,e,r,i),!i)return}}},e.getAvcEncryptedData=function(t){for(var e=16*Math.floor((t.length-48)/160)+16,r=new Int8Array(e),i=0,a=32;a<=t.length-16;a+=160,i+=16)r.set(t.subarray(a,a+16),i);return r},e.getAvcDecryptedUnit=function(t,e){e=new Uint8Array(e);for(var r=0,i=32;i<=t.length-16;i+=160,r+=16)t.set(e.subarray(r,r+16),i);return t},e.decryptAvcSample=function(t,e,r,i,a,n){var s=this.discardEPB(a.data),o=this.getAvcEncryptedData(s),l=this;this.decryptBuffer(o.buffer,function(o){a.data=l.getAvcDecryptedUnit(s,o),n||l.decryptAvcSamples(t,e,r+1,i)})},e.decryptAvcSamples=function(t,e,r,i){for(;;e++,r=0){if(e>=t.length)return void i();for(var a=t[e].units;!(r>=a.length);r++){var n=a[r];if(!(n.length<=48||1!==n.type&&5!==n.type)){var s=this.decrypter.isSync();if(this.decryptAvcSample(t,e,r,i,n,s),!s)return}}}},t}(),A={video:1,audio:2,id3:3,text:4},R=function(){function t(t,e,r,i){this.observer=t,this.config=r,this.typeSupported=i,this.remuxer=e,this.sampleAes=null}var e=t.prototype;return e.setDecryptData=function(t){null!=t&&null!=t.key&&"SAMPLE-AES"===t.method?this.sampleAes=new _(this.observer,this.config,t,this.discardEPB):this.sampleAes=null},t.probe=function(e){var r=t._syncOffset(e);return!(r<0)&&(r&&o.b.warn("MPEG2-TS detected but first sync word found @ offset "+r+", junk ahead ?"),!0)},t._syncOffset=function(t){for(var e=Math.min(1e3,t.length-564),r=0;r<e;){if(71===t[r]&&71===t[r+188]&&71===t[r+376])return r;r++}return-1},t.createTrack=function(t,e){return{container:"video"===t||"audio"===t?"video/mp2t":void 0,type:t,id:A[t],pid:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],dropped:"video"===t?0:void 0,isAAC:"audio"===t||void 0,duration:"audio"===t?e:void 0}},e.resetInitSegment=function(e,r,i,a){this.pmtParsed=!1,this._pmtId=-1,this._avcTrack=t.createTrack("video",a),this._audioTrack=t.createTrack("audio",a),this._id3Track=t.createTrack("id3",a),this._txtTrack=t.createTrack("text",a),this.aacOverFlow=null,this.aacLastPTS=null,this.avcSample=null,this.audioCodec=r,this.videoCodec=i,this._duration=a},e.resetTimeStamp=function(){},e.append=function(e,r,n,s){var l,d,u,c,h,f=e.length,g=!1;this.contiguous=n;var p=this.pmtParsed,v=this._avcTrack,m=this._audioTrack,y=this._id3Track,b=v.pid,E=m.pid,T=y.pid,S=this._pmtId,_=v.pesData,A=m.pesData,R=y.pesData,L=this._parsePAT,D=this._parsePMT,w=this._parsePES,k=this._parseAVCPES.bind(this),I=this._parseAACPES.bind(this),O=this._parseMPEGPES.bind(this),C=this._parseID3PES.bind(this),P=t._syncOffset(e);for(f-=(f+P)%188,l=P;l<f;l+=188)if(71===e[l]){if(d=!!(64&e[l+1]),u=((31&e[l+1])<<8)+e[l+2],(48&e[l+3])>>4>1){if((c=l+5+e[l+4])===l+188)continue}else c=l+4;switch(u){case b:d&&(_&&(h=w(_))&&void 0!==h.pts&&k(h,!1),_={data:[],size:0}),_&&(_.data.push(e.subarray(c,l+188)),_.size+=l+188-c);break;case E:d&&(A&&(h=w(A))&&void 0!==h.pts&&(m.isAAC?I(h):O(h)),A={data:[],size:0}),A&&(A.data.push(e.subarray(c,l+188)),A.size+=l+188-c);break;case T:d&&(R&&(h=w(R))&&void 0!==h.pts&&C(h),R={data:[],size:0}),R&&(R.data.push(e.subarray(c,l+188)),R.size+=l+188-c);break;case 0:d&&(c+=e[c]+1),S=this._pmtId=L(e,c);break;case S:d&&(c+=e[c]+1);var x=D(e,c,!0===this.typeSupported.mpeg||!0===this.typeSupported.mp3,null!=this.sampleAes);(b=x.avc)>0&&(v.pid=b),(E=x.audio)>0&&(m.pid=E,m.isAAC=x.isAAC),(T=x.id3)>0&&(y.pid=T),g&&!p&&(o.b.log("reparse from beginning"),g=!1,l=P-188),p=this.pmtParsed=!0;break;case 17:case 8191:break;default:g=!0}}else this.observer.trigger(i.a.ERROR,{type:a.b.MEDIA_ERROR,details:a.a.FRAG_PARSING_ERROR,fatal:!1,reason:"TS packet did not start with 0x47"});_&&(h=w(_))&&void 0!==h.pts?(k(h,!0),v.pesData=null):v.pesData=_,A&&(h=w(A))&&void 0!==h.pts?(m.isAAC?I(h):O(h),m.pesData=null):(A&&A.size&&o.b.log("last AAC PES packet truncated,might overlap between fragments"),m.pesData=A),R&&(h=w(R))&&void 0!==h.pts?(C(h),y.pesData=null):y.pesData=R,null==this.sampleAes?this.remuxer.remux(m,v,y,this._txtTrack,r,n,s):this.decryptAndRemux(m,v,y,this._txtTrack,r,n,s)},e.decryptAndRemux=function(t,e,r,i,a,n,s){if(t.samples&&t.isAAC){var o=this;this.sampleAes.decryptAacSamples(t.samples,0,function(){o.decryptAndRemuxAvc(t,e,r,i,a,n,s)})}else this.decryptAndRemuxAvc(t,e,r,i,a,n,s)},e.decryptAndRemuxAvc=function(t,e,r,i,a,n,s){if(e.samples){var o=this;this.sampleAes.decryptAvcSamples(e.samples,0,0,function(){o.remuxer.remux(t,e,r,i,a,n,s)})}else this.remuxer.remux(t,e,r,i,a,n,s)},e.destroy=function(){this._initPTS=this._initDTS=void 0,this._duration=0},e._parsePAT=function(t,e){return(31&t[e+10])<<8|t[e+11]},e._parsePMT=function(t,e,r,i){var a,n,s={audio:-1,avc:-1,id3:-1,isAAC:!0};for(a=e+3+((15&t[e+1])<<8|t[e+2])-4,e+=12+((15&t[e+10])<<8|t[e+11]);e<a;){switch(n=(31&t[e+1])<<8|t[e+2],t[e]){case 207:if(!i){o.b.log("unknown stream type:"+t[e]);break}case 15:-1===s.audio&&(s.audio=n);break;case 21:-1===s.id3&&(s.id3=n);break;case 219:if(!i){o.b.log("unknown stream type:"+t[e]);break}case 27:-1===s.avc&&(s.avc=n);break;case 3:case 4:r?-1===s.audio&&(s.audio=n,s.isAAC=!1):o.b.log("MPEG audio found, not supported in this browser for now");break;case 36:o.b.warn("HEVC stream type found, not supported for now");break;default:o.b.log("unknown stream type:"+t[e])}e+=5+((15&t[e+3])<<8|t[e+4])}return s},e._parsePES=function(t){var e,r,i,a,n,s,l,d,u=0,c=t.data;if(!t||0===t.size)return null;for(;c[0].length<19&&c.length>1;){var h=new Uint8Array(c[0].length+c[1].length);h.set(c[0]),h.set(c[1],c[0].length),c[0]=h,c.splice(1,1)}if(1===((e=c[0])[0]<<16)+(e[1]<<8)+e[2]){if((i=(e[4]<<8)+e[5])&&i>t.size-6)return null;192&(r=e[7])&&((s=536870912*(14&e[9])+4194304*(255&e[10])+16384*(254&e[11])+128*(255&e[12])+(254&e[13])/2)>4294967295&&(s-=8589934592),64&r?((l=536870912*(14&e[14])+4194304*(255&e[15])+16384*(254&e[16])+128*(255&e[17])+(254&e[18])/2)>4294967295&&(l-=8589934592),s-l>54e5&&(o.b.warn(Math.round((s-l)/9e4)+"s delta between PTS and DTS, align them"),s=l)):l=s),d=(a=e[8])+9,t.size-=d,n=new Uint8Array(t.size);for(var f=0,g=c.length;f<g;f++){var p=(e=c[f]).byteLength;if(d){if(d>p){d-=p;continue}e=e.subarray(d),p-=d,d=0}n.set(e,u),u+=p}return i&&(i-=a+3),{data:n,pts:s,dts:l,len:i}}return null},e.pushAccesUnit=function(t,e){if(t.units.length&&t.frame){var r=e.samples,i=r.length;!this.config.forceKeyFrameOnDiscontinuity||!0===t.key||e.sps&&(i||this.contiguous)?(t.id=i,r.push(t)):e.dropped++}t.debug.length&&o.b.log(t.pts+"/"+t.dts+":"+t.debug)},e._parseAVCPES=function(t,e){var r,i,a,n=this,s=this._avcTrack,o=this._parseAVCNALu(t.data),l=this.avcSample,d=!1,u=this.pushAccesUnit.bind(this),c=function(t,e,r,i){return{key:t,pts:e,dts:r,units:[],debug:i}};t.data=null,l&&o.length&&!s.audFound&&(u(l,s),l=this.avcSample=c(!1,t.pts,t.dts,"")),o.forEach(function(e){switch(e.type){case 1:i=!0,l||(l=n.avcSample=c(!0,t.pts,t.dts,"")),l.frame=!0;var o=e.data;if(d&&o.length>4){var h=new S(o).readSliceType();2!==h&&4!==h&&7!==h&&9!==h||(l.key=!0)}break;case 5:i=!0,l||(l=n.avcSample=c(!0,t.pts,t.dts,"")),l.key=!0,l.frame=!0;break;case 6:i=!0,(r=new S(n.discardEPB(e.data))).readUByte();for(var f=0,g=0,p=!1,v=0;!p&&r.bytesAvailable>1;){f=0;do{f+=v=r.readUByte()}while(255===v);g=0;do{g+=v=r.readUByte()}while(255===v);if(4===f&&0!==r.bytesAvailable){if(p=!0,181===r.readUByte())if(49===r.readUShort())if(1195456820===r.readUInt())if(3===r.readUByte()){var y=r.readUByte(),b=31&y,E=[y,r.readUByte()];for(a=0;a<b;a++)E.push(r.readUByte()),E.push(r.readUByte()),E.push(r.readUByte());n._insertSampleInOrder(n._txtTrack.samples,{type:3,pts:t.pts,bytes:E})}}else if(5===f&&0!==r.bytesAvailable){if(p=!0,g>16){var T=[];for(a=0;a<16;a++)T.push(r.readUByte().toString(16)),3!==a&&5!==a&&7!==a&&9!==a||T.push("-");var _=g-16,A=new Uint8Array(_);for(a=0;a<_;a++)A[a]=r.readUByte();n._insertSampleInOrder(n._txtTrack.samples,{pts:t.pts,payloadType:f,uuid:T.join(""),userDataBytes:A,userData:Object(m.b)(A.buffer)})}}else if(g<r.bytesAvailable)for(a=0;a<g;a++)r.readUByte()}break;case 7:if(i=!0,d=!0,!s.sps){var R=(r=new S(e.data)).readSPS();s.width=R.width,s.height=R.height,s.pixelRatio=R.pixelRatio,s.sps=[e.data],s.duration=n._duration;var L=e.data.subarray(1,4),D="avc1.";for(a=0;a<3;a++){var w=L[a].toString(16);w.length<2&&(w="0"+w),D+=w}s.codec=D}break;case 8:i=!0,s.pps||(s.pps=[e.data]);break;case 9:i=!1,s.audFound=!0,l&&u(l,s),l=n.avcSample=c(!1,t.pts,t.dts,"");break;case 12:i=!1;break;default:i=!1,l&&(l.debug+="unknown NAL "+e.type+" ")}l&&i&&l.units.push(e)}),e&&l&&(u(l,s),this.avcSample=null)},e._insertSampleInOrder=function(t,e){var r=t.length;if(r>0){if(e.pts>=t[r-1].pts)t.push(e);else for(var i=r-1;i>=0;i--)if(e.pts<t[i].pts){t.splice(i,0,e);break}}else t.push(e)},e._getLastNalUnit=function(){var t,e=this.avcSample;if(!e||0===e.units.length){var r=this._avcTrack.samples;e=r[r.length-1]}if(e){var i=e.units;t=i[i.length-1]}return t},e._parseAVCNALu=function(t){var e,r,i,a,n=0,s=t.byteLength,o=this._avcTrack,l=o.naluState||0,d=l,u=[],c=-1;for(-1===l&&(c=0,a=31&t[0],l=0,n=1);n<s;)if(e=t[n++],l)if(1!==l)if(e)if(1===e){if(c>=0)i={data:t.subarray(c,n-l-1),type:a},u.push(i);else{var h=this._getLastNalUnit();if(h&&(d&&n<=4-d&&h.state&&(h.data=h.data.subarray(0,h.data.byteLength-d)),(r=n-l-1)>0)){var f=new Uint8Array(h.data.byteLength+r);f.set(h.data,0),f.set(t.subarray(0,r),h.data.byteLength),h.data=f}}n<s?(c=n,a=31&t[n],l=0):l=-1}else l=0;else l=3;else l=e?0:2;else l=e?0:1;if(c>=0&&l>=0&&(i={data:t.subarray(c,s),type:a,state:l},u.push(i)),0===u.length){var g=this._getLastNalUnit();if(g){var p=new Uint8Array(g.data.byteLength+t.byteLength);p.set(g.data,0),p.set(t,g.data.byteLength),g.data=p}}return o.naluState=l,u},e.discardEPB=function(t){for(var e,r,i=t.byteLength,a=[],n=1;n<i-2;)0===t[n]&&0===t[n+1]&&3===t[n+2]?(a.push(n+2),n+=2):n++;if(0===a.length)return t;e=i-a.length,r=new Uint8Array(e);var s=0;for(n=0;n<e;s++,n++)s===a[0]&&(s++,a.shift()),r[n]=t[s];return r},e._parseAACPES=function(t){var e,r,n,s,l,d,u,c=this._audioTrack,f=t.data,m=t.pts,y=this.aacOverFlow,b=this.aacLastPTS;if(y){var E=new Uint8Array(y.byteLength+f.byteLength);E.set(y,0),E.set(f,y.byteLength),f=E}for(n=0,l=f.length;n<l-1&&!h(f,n);n++);if(n&&(n<l-1?(d="AAC PES did not start with ADTS header,offset:"+n,u=!1):(d="no ADTS header found in AAC PES",u=!0),o.b.warn("parsing error:"+d),this.observer.trigger(i.a.ERROR,{type:a.b.MEDIA_ERROR,details:a.a.FRAG_PARSING_ERROR,fatal:u,reason:d}),u))return;if(g(c,this.observer,f,n,this.audioCodec),r=0,e=p(c.samplerate),y&&b){var T=b+e;Math.abs(T-m)>1&&(o.b.log("AAC: align PTS for overlapping frames by "+Math.round((T-m)/90)),m=T)}for(;n<l;)if(h(f,n)&&n+5<l){var S=v(c,f,n,m,r);if(!S)break;n+=S.length,s=S.sample.pts,r++}else n++;y=n<l?f.subarray(n,l):null,this.aacOverFlow=y,this.aacLastPTS=s},e._parseMPEGPES=function(t){for(var e=t.data,r=e.length,i=0,a=0,n=t.pts;a<r;)if(T.isHeader(e,a)){var s=T.appendFrame(this._audioTrack,e,a,n,i);if(!s)break;a+=s.length,i++}else a++},e._parseID3PES=function(t){this._id3Track.samples.push(t)},t}(),L=function(){function t(t,e,r){this.observer=t,this.config=r,this.remuxer=e}var e=t.prototype;return e.resetInitSegment=function(t,e,r,i){this._audioTrack={container:"audio/mpeg",type:"audio",id:-1,sequenceNumber:0,isAAC:!1,samples:[],len:0,manifestCodec:e,duration:i,inputTimeScale:9e4}},e.resetTimeStamp=function(){},t.probe=function(t){var e,r,i=m.a.getID3Data(t,0);if(i&&void 0!==m.a.getTimeStamp(i))for(e=i.length,r=Math.min(t.length-1,e+100);e<r;e++)if(T.probe(t,e))return o.b.log("MPEG Audio sync word found !"),!0;return!1},e.append=function(t,e,r,i){for(var a=m.a.getID3Data(t,0),n=m.a.getTimeStamp(a),s=n?90*n:9e4*e,o=a.length,l=t.length,d=0,u=0,c=this._audioTrack,h=[{pts:s,dts:s,data:a}];o<l;)if(T.isHeader(t,o)){var f=T.appendFrame(c,t,o,s,d);if(!f)break;o+=f.length,u=f.sample.pts,d++}else m.a.isHeader(t,o)?(a=m.a.getID3Data(t,o),h.push({pts:u,dts:u,data:a}),o+=a.length):o++;this.remuxer.remux(c,{samples:[]},{samples:h,inputTimeScale:9e4},{samples:[]},e,r,i)},e.destroy=function(){},t}(),D=function(){function t(){}return t.getSilentFrame=function(t,e){switch(t){case"mp4a.40.2":if(1===e)return new Uint8Array([0,200,0,128,35,128]);if(2===e)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(3===e)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(4===e)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(5===e)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(6===e)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224]);break;default:if(1===e)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(2===e)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(3===e)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94])}return null},t}(),w=Math.pow(2,32)-1,k=function(){function t(){}return t.init=function(){var e;for(e in t.types={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],".mp3":[],mvex:[],mvhd:[],pasp:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[]},t.types)t.types.hasOwnProperty(e)&&(t.types[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);var r=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),i=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]);t.HDLR_TYPES={video:r,audio:i};var a=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),n=new Uint8Array([0,0,0,0,0,0,0,0]);t.STTS=t.STSC=t.STCO=n,t.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),t.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0]),t.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),t.STSD=new Uint8Array([0,0,0,0,0,0,0,1]);var s=new Uint8Array([105,115,111,109]),o=new Uint8Array([97,118,99,49]),l=new Uint8Array([0,0,0,1]);t.FTYP=t.box(t.types.ftyp,s,l,s,o),t.DINF=t.box(t.types.dinf,t.box(t.types.dref,a))},t.box=function(t){for(var e,r=Array.prototype.slice.call(arguments,1),i=8,a=r.length,n=a;a--;)i+=r[a].byteLength;for((e=new Uint8Array(i))[0]=i>>24&255,e[1]=i>>16&255,e[2]=i>>8&255,e[3]=255&i,e.set(t,4),a=0,i=8;a<n;a++)e.set(r[a],i),i+=r[a].byteLength;return e},t.hdlr=function(e){return t.box(t.types.hdlr,t.HDLR_TYPES[e])},t.mdat=function(e){return t.box(t.types.mdat,e)},t.mdhd=function(e,r){r*=e;var i=Math.floor(r/(w+1)),a=Math.floor(r%(w+1));return t.box(t.types.mdhd,new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,e>>24&255,e>>16&255,e>>8&255,255&e,i>>24,i>>16&255,i>>8&255,255&i,a>>24,a>>16&255,a>>8&255,255&a,85,196,0,0]))},t.mdia=function(e){return t.box(t.types.mdia,t.mdhd(e.timescale,e.duration),t.hdlr(e.type),t.minf(e))},t.mfhd=function(e){return t.box(t.types.mfhd,new Uint8Array([0,0,0,0,e>>24,e>>16&255,e>>8&255,255&e]))},t.minf=function(e){return"audio"===e.type?t.box(t.types.minf,t.box(t.types.smhd,t.SMHD),t.DINF,t.stbl(e)):t.box(t.types.minf,t.box(t.types.vmhd,t.VMHD),t.DINF,t.stbl(e))},t.moof=function(e,r,i){return t.box(t.types.moof,t.mfhd(e),t.traf(i,r))},t.moov=function(e){for(var r=e.length,i=[];r--;)i[r]=t.trak(e[r]);return t.box.apply(null,[t.types.moov,t.mvhd(e[0].timescale,e[0].duration)].concat(i).concat(t.mvex(e)))},t.mvex=function(e){for(var r=e.length,i=[];r--;)i[r]=t.trex(e[r]);return t.box.apply(null,[t.types.mvex].concat(i))},t.mvhd=function(e,r){r*=e;var i=Math.floor(r/(w+1)),a=Math.floor(r%(w+1)),n=new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,e>>24&255,e>>16&255,e>>8&255,255&e,i>>24,i>>16&255,i>>8&255,255&i,a>>24,a>>16&255,a>>8&255,255&a,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return t.box(t.types.mvhd,n)},t.sdtp=function(e){var r,i,a=e.samples||[],n=new Uint8Array(4+a.length);for(i=0;i<a.length;i++)r=a[i].flags,n[i+4]=r.dependsOn<<4|r.isDependedOn<<2|r.hasRedundancy;return t.box(t.types.sdtp,n)},t.stbl=function(e){return t.box(t.types.stbl,t.stsd(e),t.box(t.types.stts,t.STTS),t.box(t.types.stsc,t.STSC),t.box(t.types.stsz,t.STSZ),t.box(t.types.stco,t.STCO))},t.avc1=function(e){var r,i,a,n=[],s=[];for(r=0;r<e.sps.length;r++)a=(i=e.sps[r]).byteLength,n.push(a>>>8&255),n.push(255&a),n=n.concat(Array.prototype.slice.call(i));for(r=0;r<e.pps.length;r++)a=(i=e.pps[r]).byteLength,s.push(a>>>8&255),s.push(255&a),s=s.concat(Array.prototype.slice.call(i));var o=t.box(t.types.avcC,new Uint8Array([1,n[3],n[4],n[5],255,224|e.sps.length].concat(n).concat([e.pps.length]).concat(s))),l=e.width,d=e.height,u=e.pixelRatio[0],c=e.pixelRatio[1];return t.box(t.types.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,l>>8&255,255&l,d>>8&255,255&d,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,100,97,105,108,121,109,111,116,105,111,110,47,104,108,115,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),o,t.box(t.types.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])),t.box(t.types.pasp,new Uint8Array([u>>24,u>>16&255,u>>8&255,255&u,c>>24,c>>16&255,c>>8&255,255&c])))},t.esds=function(t){var e=t.config.length;return new Uint8Array([0,0,0,0,3,23+e,0,1,0,4,15+e,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([e]).concat(t.config).concat([6,1,2]))},t.mp4a=function(e){var r=e.samplerate;return t.box(t.types.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,e.channelCount,0,16,0,0,0,0,r>>8&255,255&r,0,0]),t.box(t.types.esds,t.esds(e)))},t.mp3=function(e){var r=e.samplerate;return t.box(t.types[".mp3"],new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,e.channelCount,0,16,0,0,0,0,r>>8&255,255&r,0,0]))},t.stsd=function(e){return"audio"===e.type?e.isAAC||"mp3"!==e.codec?t.box(t.types.stsd,t.STSD,t.mp4a(e)):t.box(t.types.stsd,t.STSD,t.mp3(e)):t.box(t.types.stsd,t.STSD,t.avc1(e))},t.tkhd=function(e){var r=e.id,i=e.duration*e.timescale,a=e.width,n=e.height,s=Math.floor(i/(w+1)),o=Math.floor(i%(w+1));return t.box(t.types.tkhd,new Uint8Array([1,0,0,7,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,r>>24&255,r>>16&255,r>>8&255,255&r,0,0,0,0,s>>24,s>>16&255,s>>8&255,255&s,o>>24,o>>16&255,o>>8&255,255&o,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,a>>8&255,255&a,0,0,n>>8&255,255&n,0,0]))},t.traf=function(e,r){var i=t.sdtp(e),a=e.id,n=Math.floor(r/(w+1)),s=Math.floor(r%(w+1));return t.box(t.types.traf,t.box(t.types.tfhd,new Uint8Array([0,0,0,0,a>>24,a>>16&255,a>>8&255,255&a])),t.box(t.types.tfdt,new Uint8Array([1,0,0,0,n>>24,n>>16&255,n>>8&255,255&n,s>>24,s>>16&255,s>>8&255,255&s])),t.trun(e,i.length+16+20+8+16+8+8),i)},t.trak=function(e){return e.duration=e.duration||4294967295,t.box(t.types.trak,t.tkhd(e),t.mdia(e))},t.trex=function(e){var r=e.id;return t.box(t.types.trex,new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]))},t.trun=function(e,r){var i,a,n,s,o,l,d=e.samples||[],u=d.length,c=12+16*u,h=new Uint8Array(c);for(r+=8+c,h.set([0,0,15,1,u>>>24&255,u>>>16&255,u>>>8&255,255&u,r>>>24&255,r>>>16&255,r>>>8&255,255&r],0),i=0;i<u;i++)n=(a=d[i]).duration,s=a.size,o=a.flags,l=a.cts,h.set([n>>>24&255,n>>>16&255,n>>>8&255,255&n,s>>>24&255,s>>>16&255,s>>>8&255,255&s,o.isLeading<<2|o.dependsOn,o.isDependedOn<<6|o.hasRedundancy<<4|o.paddingValue<<1|o.isNonSync,61440&o.degradPrio,15&o.degradPrio,l>>>24&255,l>>>16&255,l>>>8&255,255&l],12+16*i);return t.box(t.types.trun,h)},t.initSegment=function(e){t.types||t.init();var r,i=t.moov(e);return(r=new Uint8Array(t.FTYP.byteLength+i.byteLength)).set(t.FTYP),r.set(i,t.FTYP.byteLength),r},t}(),I=9e4;function O(t,e,r,i){void 0===r&&(r=1),void 0===i&&(i=!1);var a=t*e*r;return i?Math.round(a):a}function C(t,e){return void 0===e&&(e=!1),O(t,1e3,1/I,e)}function P(t,e){return void 0===e&&(e=1),O(t,I,1/e)}var x,F=P(10),M=P(.2),N=function(){function t(t,e,r,i){this.observer=t,this.config=e,this.typeSupported=r;var a=navigator.userAgent;this.isSafari=i&&i.indexOf("Apple")>-1&&a&&!a.match("CriOS"),this.ISGenerated=!1}var e=t.prototype;return e.destroy=function(){},e.resetTimeStamp=function(t){this._initPTS=this._initDTS=t},e.resetInitSegment=function(){this.ISGenerated=!1},e.remux=function(t,e,r,a,n,s,l){if(this.ISGenerated||this.generateIS(t,e,n),this.ISGenerated){var d=t.samples.length,u=e.samples.length,c=n,h=n;if(d&&u){var f=(t.samples[0].pts-e.samples[0].pts)/e.inputTimeScale;c+=Math.max(0,f),h+=Math.max(0,-f)}if(d){t.timescale||(o.b.warn("regenerate InitSegment as audio detected"),this.generateIS(t,e,n));var g,p=this.remuxAudio(t,c,s,l);if(u)p&&(g=p.endPTS-p.startPTS),e.timescale||(o.b.warn("regenerate InitSegment as video detected"),this.generateIS(t,e,n)),this.remuxVideo(e,h,s,g,l)}else if(u){var v=this.remuxVideo(e,h,s,0,l);v&&t.codec&&this.remuxEmptyAudio(t,c,s,v)}}r.samples.length&&this.remuxID3(r,n),a.samples.length&&this.remuxText(a,n),this.observer.trigger(i.a.FRAG_PARSED)},e.generateIS=function(t,e,r){var n,s,l=this.observer,d=t.samples,u=e.samples,c=this.typeSupported,h="audio/mp4",f={},g={tracks:f},p=void 0===this._initPTS;if(p&&(n=s=1/0),t.config&&d.length&&(t.timescale=t.samplerate,o.b.log("audio sampling rate : "+t.samplerate),t.isAAC||(c.mpeg?(h="audio/mpeg",t.codec=""):c.mp3&&(t.codec="mp3")),f.audio={container:h,codec:t.codec,initSegment:!t.isAAC&&c.mpeg?new Uint8Array:k.initSegment([t]),metadata:{channelCount:t.channelCount}},p&&(n=s=d[0].pts-t.inputTimeScale*r)),e.sps&&e.pps&&u.length){var v=e.inputTimeScale;e.timescale=v,f.video={container:"video/mp4",codec:e.codec,initSegment:k.initSegment([e]),metadata:{width:e.width,height:e.height}},p&&(n=Math.min(n,u[0].pts-v*r),s=Math.min(s,u[0].dts-v*r),this.observer.trigger(i.a.INIT_PTS_FOUND,{initPTS:n}))}Object.keys(f).length?(l.trigger(i.a.FRAG_PARSING_INIT_SEGMENT,g),this.ISGenerated=!0,p&&(this._initPTS=n,this._initDTS=s)):l.trigger(i.a.ERROR,{type:a.b.MEDIA_ERROR,details:a.a.FRAG_PARSING_ERROR,fatal:!1,reason:"no audio/video samples found"})},e.remuxVideo=function(t,e,r,n,s){var l,d,u,c,h,f,g,p=8,v=t.timescale,m=t.samples,y=[],b=m.length,E=this._PTSNormalize,T=this._initPTS,S=this.nextAvcDts,_=this.isSafari;if(0!==b){_&&(r|=m.length&&S&&(s&&Math.abs(e-S/v)<.1||Math.abs(m[0].pts-S-T)<v/5)),r||(S=e*v),m.forEach(function(t){t.pts=E(t.pts-T,S),t.dts=E(t.dts-T,S)}),m.sort(function(t,e){var r=t.dts-e.dts,i=t.pts-e.pts;return r||i||t.id-e.id});var A=m.reduce(function(t,e){return Math.max(Math.min(t,e.pts-e.dts),-1*M)},0);if(A<0){o.b.warn("PTS < DTS detected in video samples, shifting DTS by "+C(A,!0)+" ms to overcome this issue");for(var R=0;R<m.length;R++)m[R].dts+=A}var L=m[0];h=Math.max(L.dts,0),c=Math.max(L.pts,0);var D=h-S;r&&D&&(D>1?o.b.log("AVC: "+C(D,!0)+" ms hole between fragments detected,filling it"):D<-1&&o.b.log("AVC: "+C(-D,!0)+" ms overlapping between fragments detected"),h=S,m[0].dts=h,c=Math.max(c-D,S),m[0].pts=c,o.b.log("Video: PTS/DTS adjusted: "+C(c,!0)+"/"+C(h,!0)+", delta: "+C(D,!0)+" ms")),L=m[m.length-1],g=Math.max(L.dts,0),f=Math.max(L.pts,0,g),_&&(l=Math.round((g-h)/(m.length-1)));for(var w=0,I=0,O=0;O<b;O++){for(var P=m[O],x=P.units,F=x.length,N=0,U=0;U<F;U++)N+=x[U].data.length;I+=N,w+=F,P.length=N,P.dts=_?h+O*l:Math.max(P.dts,h),P.pts=Math.max(P.pts,P.dts)}var B=I+4*w+8;try{d=new Uint8Array(B)}catch(t){return void this.observer.trigger(i.a.ERROR,{type:a.b.MUX_ERROR,details:a.a.REMUX_ALLOC_ERROR,fatal:!1,bytes:B,reason:"fail allocating video mdat "+B})}var G=new DataView(d.buffer);G.setUint32(0,B),d.set(k.types.mdat,4);for(var K=0;K<b;K++){for(var j=m[K],H=j.units,V=0,W=void 0,Y=0,q=H.length;Y<q;Y++){var z=H[Y],X=z.data,Q=z.data.byteLength;G.setUint32(p,Q),p+=4,d.set(X,p),p+=Q,V+=4+Q}if(_)W=Math.max(0,l*Math.round((j.pts-j.dts)/l));else{if(K<b-1)l=m[K+1].dts-j.dts;else{var $=this.config,J=j.dts-m[K>0?K-1:K].dts;if($.stretchShortVideoTrack){var Z=$.maxBufferHole,tt=Math.floor(Z*v),et=(n?c+n*v:this.nextAudioPts)-j.pts;et>tt?((l=et-J)<0&&(l=J),o.b.log("It is approximately "+C(et,!1)+" ms to the next segment; using duration "+C(l,!1)+" ms for the last video frame.")):l=J}else l=J}W=Math.round(j.pts-j.dts)}y.push({size:V,duration:l,cts:W,flags:{isLeading:0,isDependedOn:0,hasRedundancy:0,degradPrio:0,dependsOn:j.key?2:1,isNonSync:j.key?0:1}})}this.nextAvcDts=g+l;var rt=t.dropped;if(t.nbNalu=0,t.dropped=0,y.length&&navigator.userAgent.toLowerCase().indexOf("chrome")>-1){var it=y[0].flags;it.dependsOn=2,it.isNonSync=0}t.samples=y,u=k.moof(t.sequenceNumber++,h,t),t.samples=[];var at={data1:u,data2:d,startPTS:c/v,endPTS:(f+l)/v,startDTS:h/v,endDTS:this.nextAvcDts/v,type:"video",hasAudio:!1,hasVideo:!0,nb:y.length,dropped:rt};return this.observer.trigger(i.a.FRAG_PARSING_DATA,at),at}},e.remuxAudio=function(t,e,r,n){var s,l,d,u,c,h,f=t.inputTimeScale,g=t.timescale,p=f/g,v=(t.isAAC?1024:1152)*p,m=this._PTSNormalize,y=this._initPTS,b=!t.isAAC&&this.typeSupported.mpeg,E=b?0:8,T=t.samples,S=[],_=this.nextAudioPts;if(r|=T.length&&_&&(n&&Math.abs(e-_/f)<.1||Math.abs(T[0].pts-_-y)<20*v),T.forEach(function(t){t.pts=t.dts=m(t.pts-y,e*f)}),0!==(T=T.filter(function(t){return t.pts>=0})).length){if(r||(_=n?e*f:T[0].pts),t.isAAC)for(var A=this.config.maxAudioFramesDrift,R=0,L=_;R<T.length;){var w,I=T[R];if((w=I.pts-L)<=-A*v)o.b.warn("Dropping 1 audio frame @ "+C(L,!0)+" ms due to "+C(w,!0)+" ms overlap."),T.splice(R,1);else if(w>=A*v&&w<F&&L){var O=Math.round(w/v);o.b.warn("Injecting "+O+" audio frames @ "+C(L,!0)+" ms due to "+C(L,!0)+" ms gap.");for(var P=0;P<O;P++){var x=Math.max(L,0);(l=D.getSilentFrame(t.manifestCodec||t.codec,t.channelCount))||(o.b.log("Unable to get silent frame for given audio codec; duplicating last frame instead."),l=I.unit.subarray()),T.splice(R,0,{unit:l,pts:x,dts:x}),L+=v,R++}I.pts=I.dts=L,L+=v,R++}else Math.abs(w),I.pts=I.dts=L,L+=v,R++}for(var M=T.length,N=0;M--;)N+=T[M].unit.byteLength;for(var U=0,B=T.length;U<B;U++){var G=T[U],K=G.unit,j=G.pts;if(void 0!==h)s.duration=Math.round((j-h)/p);else{var H=j-_,V=0;if(r&&t.isAAC&&H){if(H>0&&H<F)V=Math.round((j-_)/v),o.b.log(C(H,!0)+" ms hole between AAC samples detected,filling it"),V>0&&((l=D.getSilentFrame(t.manifestCodec||t.codec,t.channelCount))||(l=K.subarray()),N+=V*l.length);else if(H<-12){o.b.log("drop overlapping AAC sample, expected/parsed/delta: "+C(_,!0)+" ms / "+C(j,!0)+" ms / "+C(-H,!0)+" ms"),N-=K.byteLength;continue}j=_}if(c=j,!(N>0))return;N+=E;try{d=new Uint8Array(N)}catch(t){return void this.observer.trigger(i.a.ERROR,{type:a.b.MUX_ERROR,details:a.a.REMUX_ALLOC_ERROR,fatal:!1,bytes:N,reason:"fail allocating audio mdat "+N})}b||(new DataView(d.buffer).setUint32(0,N),d.set(k.types.mdat,4));for(var W=0;W<V;W++)(l=D.getSilentFrame(t.manifestCodec||t.codec,t.channelCount))||(o.b.log("Unable to get silent frame for given audio codec; duplicating this frame instead."),l=K.subarray()),d.set(l,E),E+=l.byteLength,s={size:l.byteLength,cts:0,duration:1024,flags:{isLeading:0,isDependedOn:0,hasRedundancy:0,degradPrio:0,dependsOn:1}},S.push(s)}d.set(K,E);var Y=K.byteLength;E+=Y,s={size:Y,cts:0,duration:0,flags:{isLeading:0,isDependedOn:0,hasRedundancy:0,degradPrio:0,dependsOn:1}},S.push(s),h=j}var q=0;if((M=S.length)>=2&&(q=S[M-2].duration,s.duration=q),M){this.nextAudioPts=_=h+p*q,t.samples=S,u=b?new Uint8Array:k.moof(t.sequenceNumber++,c/p,t),t.samples=[];var z=c/f,X=_/f,Q={data1:u,data2:d,startPTS:z,endPTS:X,startDTS:z,endDTS:X,type:"audio",hasAudio:!0,hasVideo:!1,nb:M};return this.observer.trigger(i.a.FRAG_PARSING_DATA,Q),Q}return null}},e.remuxEmptyAudio=function(t,e,r,i){var a=t.inputTimeScale,n=a/(t.samplerate?t.samplerate:a),s=this.nextAudioPts,l=(void 0!==s?s:i.startDTS*a)+this._initDTS,d=i.endDTS*a+this._initDTS,u=1024*n,c=Math.ceil((d-l)/u),h=D.getSilentFrame(t.manifestCodec||t.codec,t.channelCount);if(o.b.warn("remux empty Audio"),h){for(var f=[],g=0;g<c;g++){var p=l+g*u;f.push({unit:h,pts:p,dts:p})}t.samples=f,this.remuxAudio(t,e,r)}else o.b.trace("Unable to remuxEmptyAudio since we were unable to get a silent frame for given audio codec!")},e.remuxID3=function(t){var e,r=t.samples.length,a=t.inputTimeScale,n=this._initPTS,s=this._initDTS;if(r){for(var o=0;o<r;o++)(e=t.samples[o]).pts=(e.pts-n)/a,e.dts=(e.dts-s)/a;this.observer.trigger(i.a.FRAG_PARSING_METADATA,{samples:t.samples})}t.samples=[]},e.remuxText=function(t){t.samples.sort(function(t,e){return t.pts-e.pts});var e,r=t.samples.length,a=t.inputTimeScale,n=this._initPTS;if(r){for(var s=0;s<r;s++)(e=t.samples[s]).pts=(e.pts-n)/a;this.observer.trigger(i.a.FRAG_PARSING_USERDATA,{samples:t.samples})}t.samples=[]},e._PTSNormalize=function(t,e){var r;if(void 0===e)return t;for(r=e<t?-8589934592:8589934592;Math.abs(t-e)>4294967296;)t+=r;return t},t}(),U=function(){function t(t){this.observer=t}var e=t.prototype;return e.destroy=function(){},e.resetTimeStamp=function(){},e.resetInitSegment=function(){},e.remux=function(t,e,r,a,n,s,o,l){var d=this.observer,u="";t&&(u+="audio"),e&&(u+="video"),d.trigger(i.a.FRAG_PARSING_DATA,{data1:l,startPTS:n,startDTS:n,type:u,hasAudio:!!t,hasVideo:!!e,nb:1,dropped:0}),d.trigger(i.a.FRAG_PARSED)},t}(),B=Object(l.a)();try{x=B.performance.now.bind(B.performance)}catch(t){o.b.debug("Unable to use Performance API on this environment"),x=B.Date.now}var G=function(){function t(t,e,r,i){this.observer=t,this.typeSupported=e,this.config=r,this.vendor=i}var e=t.prototype;return e.destroy=function(){var t=this.demuxer;t&&t.destroy()},e.push=function(t,e,r,a,s,o,l,d,u,c,h,f){var g=this;if(t.byteLength>0&&null!=e&&null!=e.key&&"AES-128"===e.method){var p=this.decrypter;null==p&&(p=this.decrypter=new n.a(this.observer,this.config));var v=x();p.decrypt(t,e.key.buffer,e.iv.buffer,function(t){var n=x();g.observer.trigger(i.a.FRAG_DECRYPTED,{stats:{tstart:v,tdecrypt:n}}),g.pushDecrypted(new Uint8Array(t),e,new Uint8Array(r),a,s,o,l,d,u,c,h,f)})}else this.pushDecrypted(new Uint8Array(t),e,new Uint8Array(r),a,s,o,l,d,u,c,h,f)},e.pushDecrypted=function(t,e,r,n,s,o,l,d,u,c,h,f){var g=this.demuxer;if(!g||(l||d)&&!this.probe(t)){for(var p=this.observer,v=this.typeSupported,m=this.config,E=[{demux:R,remux:N},{demux:b.a,remux:U},{demux:y,remux:N},{demux:L,remux:N}],T=0,S=E.length;T<S;T++){var _=E[T],A=_.demux.probe;if(A(t)){var D=this.remuxer=new _.remux(p,m,v,this.vendor);g=new _.demux(p,D,m,v),this.probe=A;break}}if(!g)return void p.trigger(i.a.ERROR,{type:a.b.MEDIA_ERROR,details:a.a.FRAG_PARSING_ERROR,fatal:!0,reason:"no demux matching with content found"});this.demuxer=g}var w=this.remuxer;(l||d)&&(g.resetInitSegment(r,n,s,c),w.resetInitSegment()),l&&(g.resetTimeStamp(f),w.resetTimeStamp(f)),"function"==typeof g.setDecryptData&&g.setDecryptData(e),g.append(t,o,u,h)},t}();e.a=G},function(t,e,r){"use strict";var i=r(0),a=r(1),n=Math.pow(2,32)-1,s=function(){function t(t,e){this.observer=t,this.remuxer=e}var e=t.prototype;return e.resetTimeStamp=function(t){this.initPTS=t},e.resetInitSegment=function(e,r,i,n){if(e&&e.byteLength){var s=this.initData=t.parseInitSegment(e);null==r&&(r="mp4a.40.5"),null==i&&(i="avc1.42e01e");var o={};s.audio&&s.video?o.audiovideo={container:"video/mp4",codec:r+","+i,initSegment:n?e:null}:(s.audio&&(o.audio={container:"audio/mp4",codec:r,initSegment:n?e:null}),s.video&&(o.video={container:"video/mp4",codec:i,initSegment:n?e:null})),this.observer.trigger(a.a.FRAG_PARSING_INIT_SEGMENT,{tracks:o})}else r&&(this.audioCodec=r),i&&(this.videoCodec=i)},t.probe=function(e){return t.findBox({data:e,start:0,end:Math.min(e.length,16384)},["moof"]).length>0},t.bin2str=function(t){return String.fromCharCode.apply(null,t)},t.readUint16=function(t,e){t.data&&(e+=t.start,t=t.data);var r=t[e]<<8|t[e+1];return r<0?65536+r:r},t.readUint32=function(t,e){t.data&&(e+=t.start,t=t.data);var r=t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3];return r<0?4294967296+r:r},t.writeUint32=function(t,e,r){t.data&&(e+=t.start,t=t.data),t[e]=r>>24,t[e+1]=r>>16&255,t[e+2]=r>>8&255,t[e+3]=255&r},t.findBox=function(e,r){var i,a,n,s,o,l,d=[];if(e.data?(o=e.start,n=e.end,e=e.data):(o=0,n=e.byteLength),!r.length)return null;for(i=o;i<n;)l=(a=t.readUint32(e,i))>1?i+a:n,t.bin2str(e.subarray(i+4,i+8))===r[0]&&(1===r.length?d.push({data:e,start:i+8,end:l}):(s=t.findBox({data:e,start:i+8,end:l},r.slice(1))).length&&(d=d.concat(s))),i=l;return d},t.parseSegmentIndex=function(e){var r,i=t.findBox(e,["moov"])[0],a=i?i.end:null,n=0,s=t.findBox(e,["sidx"]);if(!s||!s[0])return null;r=[];var o=(s=s[0]).data[0];n=0===o?8:16;var l=t.readUint32(s,n);n+=4;n+=0===o?8:16,n+=2;var d=s.end+0,u=t.readUint16(s,n);n+=2;for(var c=0;c<u;c++){var h=n,f=t.readUint32(s,h);h+=4;var g=2147483647&f;if(1===(2147483648&f)>>>31)return void console.warn("SIDX has hierarchical references (not supported)");var p=t.readUint32(s,h);h+=4,r.push({referenceSize:g,subsegmentDuration:p,info:{duration:p/l,start:d,end:d+g-1}}),d+=g,n=h+=4}return{earliestPresentationTime:0,timescale:l,version:o,referencesCount:u,references:r,moovEndOffset:a}},t.parseInitSegment=function(e){var r=[];return t.findBox(e,["moov","trak"]).forEach(function(e){var a=t.findBox(e,["tkhd"])[0];if(a){var n=a.data[a.start],s=0===n?12:20,o=t.readUint32(a,s),l=t.findBox(e,["mdia","mdhd"])[0];if(l){s=0===(n=l.data[l.start])?12:20;var d=t.readUint32(l,s),u=t.findBox(e,["mdia","hdlr"])[0];if(u){var c={soun:"audio",vide:"video"}[t.bin2str(u.data.subarray(u.start+8,u.start+12))];if(c){var h=t.findBox(e,["mdia","minf","stbl","stsd"]);if(h.length){h=h[0];var f=t.bin2str(h.data.subarray(h.start+12,h.start+16));i.b.log("MP4Demuxer:"+c+":"+f+" found")}r[o]={timescale:d,type:c},r[c]={timescale:d,id:o}}}}}}),r},t.getStartDTS=function(e,r){var i,a,n;return i=t.findBox(r,["moof","traf"]),a=[].concat.apply([],i.map(function(r){return t.findBox(r,["tfhd"]).map(function(i){var a,n;return a=t.readUint32(i,4),n=e[a].timescale||9e4,t.findBox(r,["tfdt"]).map(function(e){var r,i;return r=e.data[e.start],i=t.readUint32(e,4),1===r&&(i*=Math.pow(2,32),i+=t.readUint32(e,8)),i})[0]/n})})),n=Math.min.apply(null,a),isFinite(n)?n:0},t.offsetStartDTS=function(e,r,i){t.findBox(r,["moof","traf"]).map(function(r){return t.findBox(r,["tfhd"]).map(function(a){var s=t.readUint32(a,4),o=e[s].timescale||9e4;t.findBox(r,["tfdt"]).map(function(e){var r=e.data[e.start],a=t.readUint32(e,4);if(0===r)t.writeUint32(e,4,a-i*o);else{a*=Math.pow(2,32),a+=t.readUint32(e,8),a-=i*o,a=Math.max(a,0);var s=Math.floor(a/(n+1)),l=Math.floor(a%(n+1));t.writeUint32(e,4,s),t.writeUint32(e,8,l)}})})})},e.append=function(e,r,i,n){var s=this.initData;s||(this.resetInitSegment(e,this.audioCodec,this.videoCodec,!1),s=this.initData);var o,l=this.initPTS;if(void 0===l){var d=t.getStartDTS(s,e);this.initPTS=l=d-r,this.observer.trigger(a.a.INIT_PTS_FOUND,{initPTS:l})}t.offsetStartDTS(s,e,l),o=t.getStartDTS(s,e),this.remuxer.remux(s.audio,s.video,null,null,o,i,n,e)},e.destroy=function(){},t}();e.a=s},function(t,e,r){function i(t){var e={};function r(i){if(e[i])return e[i].exports;var a=e[i]={i:i,l:!1,exports:{}};return t[i].call(a.exports,a,a.exports,r),a.l=!0,a.exports}r.m=t,r.c=e,r.i=function(t){return t},r.d=function(t,e,i){r.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:i})},r.r=function(t){Object.defineProperty(t,"__esModule",{value:!0})},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="/",r.oe=function(t){throw console.error(t),t};var i=r(r.s=ENTRY_MODULE);return i.default||i}var a="[\\.|\\-|\\+|\\w|/|@]+",n="\\(\\s*(/\\*.*?\\*/)?\\s*.*?("+a+").*?\\)";function s(t){return(t+"").replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}function o(t,e,i){var o={};o[i]=[];var l=e.toString(),d=l.match(/^function\s?\w*\(\w+,\s*\w+,\s*(\w+)\)/);if(!d)return o;for(var u,c=d[1],h=new RegExp("(\\\\n|\\W)"+s(c)+n,"g");u=h.exec(l);)"dll-reference"!==u[3]&&o[i].push(u[3]);for(h=new RegExp("\\("+s(c)+'\\("(dll-reference\\s('+a+'))"\\)\\)'+n,"g");u=h.exec(l);)t[u[2]]||(o[i].push(u[1]),t[u[2]]=r(u[1]).m),o[u[2]]=o[u[2]]||[],o[u[2]].push(u[4]);for(var f,g=Object.keys(o),p=0;p<g.length;p++)for(var v=0;v<o[g[p]].length;v++)f=o[g[p]][v],isNaN(1*f)||(o[g[p]][v]=1*o[g[p]][v]);return o}function l(t){return Object.keys(t).reduce(function(e,r){return e||t[r].length>0},!1)}t.exports=function(t,e){e=e||{};var a={main:r.m},n=e.all?{main:Object.keys(a.main)}:function(t,e){for(var r={main:[e]},i={main:[]},a={main:{}};l(r);)for(var n=Object.keys(r),s=0;s<n.length;s++){var d=n[s],u=r[d].pop();if(a[d]=a[d]||{},!a[d][u]&&t[d][u]){a[d][u]=!0,i[d]=i[d]||[],i[d].push(u);for(var c=o(t,t[d][u],d),h=Object.keys(c),f=0;f<h.length;f++)r[h[f]]=r[h[f]]||[],r[h[f]]=r[h[f]].concat(c[h[f]])}}return i}(a,t),s="";Object.keys(n).filter(function(t){return"main"!==t}).forEach(function(t){for(var e=0;n[t][e];)e++;n[t].push(e),a[t][e]="(function(module, exports, __webpack_require__) { module.exports = __webpack_require__; })",s=s+"var "+t+" = ("+i.toString().replace("ENTRY_MODULE",JSON.stringify(e))+")({"+n[t].map(function(e){return JSON.stringify(e)+": "+a[t][e].toString()}).join(",")+"});\n"}),s=s+"new (("+i.toString().replace("ENTRY_MODULE",JSON.stringify(t))+")({"+n.main.map(function(t){return JSON.stringify(t)+": "+a.main[t].toString()}).join(",")+"}))(self);";var d=new window.Blob([s],{type:"text/javascript"});if(e.bare)return d;var u=(window.URL||window.webkitURL||window.mozURL||window.msURL).createObjectURL(d),c=new window.Worker(u);return c.objectURL=u,c}},function(t,e,r){"use strict";r.r(e);var i=r(9),a=r(1),n=r(0),s=r(8);e.default=function(t){var e=new s.EventEmitter;e.trigger=function(t){for(var r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];e.emit.apply(e,[t,t].concat(i))},e.off=function(t){for(var r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];e.removeListener.apply(e,[t].concat(i))};var r=function(e,r){t.postMessage({event:e,data:r})};t.addEventListener("message",function(a){var s=a.data;switch(s.cmd){case"init":var o=JSON.parse(s.config);t.demuxer=new i.a(e,s.typeSupported,o,s.vendor),Object(n.a)(o.debug),r("init",null);break;case"demux":t.demuxer.push(s.data,s.decryptdata,s.initSegment,s.audioCodec,s.videoCodec,s.timeOffset,s.discontinuity,s.trackSwitch,s.contiguous,s.duration,s.accurateTimeOffset,s.defaultInitPTS)}}),e.on(a.a.FRAG_DECRYPTED,r),e.on(a.a.FRAG_PARSING_INIT_SEGMENT,r),e.on(a.a.FRAG_PARSED,r),e.on(a.a.ERROR,r),e.on(a.a.FRAG_PARSING_METADATA,r),e.on(a.a.FRAG_PARSING_USERDATA,r),e.on(a.a.INIT_PTS_FOUND,r),e.on(a.a.FRAG_PARSING_DATA,function(e,r){var i=[],a={event:e,data:r};r.data1&&(a.data1=r.data1.buffer,i.push(r.data1.buffer),delete r.data1),r.data2&&(a.data2=r.data2.buffer,i.push(r.data2.buffer),delete r.data2),t.postMessage(a,i)})}},function(t,e,r){"use strict";r.r(e);var i={};r.r(i),r.d(i,"newCue",function(){return ee});var a,n,s=r(6),o=r(2),l=r(3),d=r(1),u=r(0),c={hlsEventGeneric:!0,hlsHandlerDestroying:!0,hlsHandlerDestroyed:!0},h=function(){function t(t){this.hls=void 0,this.handledEvents=void 0,this.useGenericHandler=void 0,this.hls=t,this.onEvent=this.onEvent.bind(this);for(var e=arguments.length,r=new Array(e>1?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];this.handledEvents=r,this.useGenericHandler=!0,this.registerListeners()}var e=t.prototype;return e.destroy=function(){this.onHandlerDestroying(),this.unregisterListeners(),this.onHandlerDestroyed()},e.onHandlerDestroying=function(){},e.onHandlerDestroyed=function(){},e.isEventHandler=function(){return"object"==typeof this.handledEvents&&this.handledEvents.length&&"function"==typeof this.onEvent},e.registerListeners=function(){this.isEventHandler()&&this.handledEvents.forEach(function(t){if(c[t])throw new Error("Forbidden event-name: "+t);this.hls.on(t,this.onEvent)},this)},e.unregisterListeners=function(){this.isEventHandler()&&this.handledEvents.forEach(function(t){this.hls.off(t,this.onEvent)},this)},e.onEvent=function(t,e){this.onEventGeneric(t,e)},e.onEventGeneric=function(t,e){try{(function(t,e){var r="on"+t.replace("hls","");if("function"!=typeof this[r])throw new Error("Event "+t+" has no generic handler in this "+this.constructor.name+" class (tried "+r+")");return this[r].bind(this,e)}).call(this,t,e).call()}catch(e){u.b.error("An internal error happened while handling event "+t+'. Error message: "'+e.message+'". Here is a stacktrace:',e),this.hls.trigger(d.a.ERROR,{type:o.b.OTHER_ERROR,details:o.a.INTERNAL_EXCEPTION,fatal:!1,event:t,err:e})}},t}();!function(t){t.MANIFEST="manifest",t.LEVEL="level",t.AUDIO_TRACK="audioTrack",t.SUBTITLE_TRACK="subtitleTrack"}(a||(a={})),function(t){t.MAIN="main",t.AUDIO="audio",t.SUBTITLE="subtitle"}(n||(n={}));var f=r(10);function g(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var p,v=function(){function t(t,e){this._uri=null,this.baseuri=void 0,this.reluri=void 0,this.method=null,this.key=null,this.iv=null,this.baseuri=t,this.reluri=e}var e,r,i;return e=t,(r=[{key:"uri",get:function(){return!this._uri&&this.reluri&&(this._uri=Object(s.buildAbsoluteURL)(this.baseuri,this.reluri,{alwaysNormalize:!0})),this._uri}}])&&g(e.prototype,r),i&&g(e,i),t}();function m(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}!function(t){t.AUDIO="audio",t.VIDEO="video"}(p||(p={}));var y=function(){function t(){var t;this._url=null,this._byteRange=null,this._decryptdata=null,this._elementaryStreams=((t={})[p.AUDIO]=!1,t[p.VIDEO]=!1,t),this.deltaPTS=0,this.rawProgramDateTime=null,this.programDateTime=null,this.title=null,this.tagList=[],this.cc=void 0,this.type=void 0,this.relurl=void 0,this.baseurl=void 0,this.duration=void 0,this.start=void 0,this.sn=0,this.urlId=0,this.level=0,this.levelkey=void 0,this.loader=void 0}var e,r,i,a=t.prototype;return a.setByteRange=function(t,e){var r=t.split("@",2),i=[];1===r.length?i[0]=e?e.byteRangeEndOffset:0:i[0]=parseInt(r[1]),i[1]=parseInt(r[0])+i[0],this._byteRange=i},a.addElementaryStream=function(t){this._elementaryStreams[t]=!0},a.hasElementaryStream=function(t){return!0===this._elementaryStreams[t]},a.createInitializationVector=function(t){for(var e=new Uint8Array(16),r=12;r<16;r++)e[r]=t>>8*(15-r)&255;return e},a.setDecryptDataFromLevelKey=function(t,e){var r=t;return t&&t.method&&t.uri&&!t.iv&&((r=new v(t.baseuri,t.reluri)).method=t.method,r.iv=this.createInitializationVector(e)),r},e=t,(r=[{key:"url",get:function(){return!this._url&&this.relurl&&(this._url=Object(s.buildAbsoluteURL)(this.baseurl,this.relurl,{alwaysNormalize:!0})),this._url},set:function(t){this._url=t}},{key:"byteRange",get:function(){return this._byteRange?this._byteRange:[]}},{key:"byteRangeStartOffset",get:function(){return this.byteRange[0]}},{key:"byteRangeEndOffset",get:function(){return this.byteRange[1]}},{key:"decryptdata",get:function(){if(!this.levelkey&&!this._decryptdata)return null;if(!this._decryptdata&&this.levelkey){var t=this.sn;"number"!=typeof t&&(this.levelkey&&"AES-128"===this.levelkey.method&&!this.levelkey.iv&&u.b.warn('missing IV for initialization segment with method="'+this.levelkey.method+'" - compliance issue'),t=0),this._decryptdata=this.setDecryptDataFromLevelKey(this.levelkey,t)}return this._decryptdata}},{key:"endProgramDateTime",get:function(){if(null===this.programDateTime)return null;if(!Object(l.a)(this.programDateTime))return null;var t=Object(l.a)(this.duration)?this.duration:0;return this.programDateTime+1e3*t}},{key:"encrypted",get:function(){return!(!this.decryptdata||null===this.decryptdata.uri||null!==this.decryptdata.key)}}])&&m(e.prototype,r),i&&m(e,i),t}();function b(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var E=function(){function t(t){this.endCC=0,this.endSN=0,this.fragments=[],this.initSegment=null,this.live=!0,this.needSidxRanges=!1,this.startCC=0,this.startSN=0,this.startTimeOffset=null,this.targetduration=0,this.totalduration=0,this.type=null,this.url=t,this.version=null}var e,r,i;return e=t,(r=[{key:"hasProgramDateTime",get:function(){return!(!this.fragments[0]||!Object(l.a)(this.fragments[0].programDateTime))}}])&&b(e.prototype,r),i&&b(e,i),t}(),T=/^(\d+)x(\d+)$/,S=/\s*(.+?)\s*=((?:\".*?\")|.*?)(?:,|$)/g,_=function(){function t(e){for(var r in"string"==typeof e&&(e=t.parseAttrList(e)),e)e.hasOwnProperty(r)&&(this[r]=e[r])}var e=t.prototype;return e.decimalInteger=function(t){var e=parseInt(this[t],10);return e>Number.MAX_SAFE_INTEGER?1/0:e},e.hexadecimalInteger=function(t){if(this[t]){var e=(this[t]||"0x").slice(2);e=(1&e.length?"0":"")+e;for(var r=new Uint8Array(e.length/2),i=0;i<e.length/2;i++)r[i]=parseInt(e.slice(2*i,2*i+2),16);return r}return null},e.hexadecimalIntegerAsNumber=function(t){var e=parseInt(this[t],16);return e>Number.MAX_SAFE_INTEGER?1/0:e},e.decimalFloatingPoint=function(t){return parseFloat(this[t])},e.enumeratedString=function(t){return this[t]},e.decimalResolution=function(t){var e=T.exec(this[t]);if(null!==e)return{width:parseInt(e[1],10),height:parseInt(e[2],10)}},t.parseAttrList=function(t){var e,r={};for(S.lastIndex=0;null!==(e=S.exec(t));){var i=e[2];0===i.indexOf('"')&&i.lastIndexOf('"')===i.length-1&&(i=i.slice(1,-1)),r[e[1]]=i}return r},t}(),A={audio:{a3ds:!0,"ac-3":!0,"ac-4":!0,alac:!0,alaw:!0,dra1:!0,"dts+":!0,"dts-":!0,dtsc:!0,dtse:!0,dtsh:!0,"ec-3":!0,enca:!0,g719:!0,g726:!0,m4ae:!0,mha1:!0,mha2:!0,mhm1:!0,mhm2:!0,mlpa:!0,mp4a:!0,"raw ":!0,Opus:!0,samr:!0,sawb:!0,sawp:!0,sevc:!0,sqcp:!0,ssmv:!0,twos:!0,ulaw:!0},video:{avc1:!0,avc2:!0,avc3:!0,avc4:!0,avcp:!0,drac:!0,dvav:!0,dvhe:!0,encv:!0,hev1:!0,hvc1:!0,mjp2:!0,mp4v:!0,mvc1:!0,mvc2:!0,mvc3:!0,mvc4:!0,resv:!0,rv60:!0,s263:!0,svc1:!0,svc2:!0,"vc-1":!0,vp08:!0,vp09:!0}};function R(t,e){return MediaSource.isTypeSupported((e||"video")+'/mp4;codecs="'+t+'"')}var L=/#EXT-X-STREAM-INF:([^\n\r]*)[\r\n]+([^\r\n]+)/g,D=/#EXT-X-MEDIA:(.*)/g,w=new RegExp([/#EXTINF:\s*(\d*(?:\.\d+)?)(?:,(.*)\s+)?/.source,/|(?!#)([\S+ ?]+)/.source,/|#EXT-X-BYTERANGE:*(.+)/.source,/|#EXT-X-PROGRAM-DATE-TIME:(.+)/.source,/|#.*/.source].join(""),"g"),k=/(?:(?:#(EXTM3U))|(?:#EXT-X-(PLAYLIST-TYPE):(.+))|(?:#EXT-X-(MEDIA-SEQUENCE): *(\d+))|(?:#EXT-X-(TARGETDURATION): *(\d+))|(?:#EXT-X-(KEY):(.+))|(?:#EXT-X-(START):(.+))|(?:#EXT-X-(ENDLIST))|(?:#EXT-X-(DISCONTINUITY-SEQ)UENCE:(\d+))|(?:#EXT-X-(DIS)CONTINUITY))|(?:#EXT-X-(VERSION):(\d+))|(?:#EXT-X-(MAP):(.+))|(?:(#)([^:]*):(.*))|(?:(#)(.*))(?:.*)\r?\n?/,I=/\.(mp4|m4s|m4v|m4a)$/i,O=function(){function t(){}return t.findGroup=function(t,e){for(var r=0;r<t.length;r++){var i=t[r];if(i.id===e)return i}},t.convertAVC1ToAVCOTI=function(t){var e,r=t.split(".");return r.length>2?(e=r.shift()+".",e+=parseInt(r.shift()).toString(16),e+=("000"+parseInt(r.shift()).toString(16)).substr(-4)):e=t,e},t.resolve=function(t,e){return s.buildAbsoluteURL(e,t,{alwaysNormalize:!0})},t.parseMasterPlaylist=function(e,r){var i,a=[];function n(t,e){["video","audio"].forEach(function(r){var i=t.filter(function(t){return function(t,e){var r=A[e];return!!r&&!0===r[t.slice(0,4)]}(t,r)});if(i.length){var a=i.filter(function(t){return 0===t.lastIndexOf("avc1",0)||0===t.lastIndexOf("mp4a",0)});e[r+"Codec"]=a.length>0?a[0]:i[0],t=t.filter(function(t){return-1===i.indexOf(t)})}}),e.unknownCodecs=t}for(L.lastIndex=0;null!=(i=L.exec(e));){var s={},o=s.attrs=new _(i[1]);s.url=t.resolve(i[2],r);var l=o.decimalResolution("RESOLUTION");l&&(s.width=l.width,s.height=l.height),s.bitrate=o.decimalInteger("AVERAGE-BANDWIDTH")||o.decimalInteger("BANDWIDTH"),s.name=o.NAME,n([].concat((o.CODECS||"").split(/[ ,]+/)),s),s.videoCodec&&-1!==s.videoCodec.indexOf("avc1")&&(s.videoCodec=t.convertAVC1ToAVCOTI(s.videoCodec)),a.push(s)}return a},t.parseMasterPlaylistMedia=function(e,r,i,a){var n;void 0===a&&(a=[]);var s=[],o=0;for(D.lastIndex=0;null!==(n=D.exec(e));){var l=new _(n[1]);if(l.TYPE===i){var d={id:o++,groupId:l["GROUP-ID"],name:l.NAME||l.LANGUAGE,type:i,default:"YES"===l.DEFAULT,autoselect:"YES"===l.AUTOSELECT,forced:"YES"===l.FORCED,lang:l.LANGUAGE};if(l.URI&&(d.url=t.resolve(l.URI,r)),a.length){var u=t.findGroup(a,d.groupId);d.audioCodec=u?u.codec:a[0].codec}s.push(d)}}return s},t.parseLevelPlaylist=function(t,e,r,i,a){var n,s,o,d=0,c=0,h=new E(e),f=0,g=null,p=new y,m=null;for(w.lastIndex=0;null!==(n=w.exec(t));){var b=n[1];if(b){p.duration=parseFloat(b);var T=(" "+n[2]).slice(1);p.title=T||null,p.tagList.push(T?["INF",b,T]:["INF",b])}else if(n[3]){if(Object(l.a)(p.duration)){var S=d++;p.type=i,p.start=c,o&&(p.levelkey=o),p.sn=S,p.level=r,p.cc=f,p.urlId=a,p.baseurl=e,p.relurl=(" "+n[3]).slice(1),C(p,g),h.fragments.push(p),g=p,c+=p.duration,p=new y}}else if(n[4]){var A=(" "+n[4]).slice(1);g?p.setByteRange(A,g):p.setByteRange(A)}else if(n[5])p.rawProgramDateTime=(" "+n[5]).slice(1),p.tagList.push(["PROGRAM-DATE-TIME",p.rawProgramDateTime]),null===m&&(m=h.fragments.length);else{if(!(n=n[0].match(k))){u.b.warn("No matches on slow regex match for level playlist!");continue}for(s=1;s<n.length&&void 0===n[s];s++);var R=(" "+n[s+1]).slice(1),L=(" "+n[s+2]).slice(1);switch(n[s]){case"#":p.tagList.push(L?[R,L]:[R]);break;case"PLAYLIST-TYPE":h.type=R.toUpperCase();break;case"MEDIA-SEQUENCE":d=h.startSN=parseInt(R);break;case"TARGETDURATION":h.targetduration=parseFloat(R);break;case"VERSION":h.version=parseInt(R);break;case"EXTM3U":break;case"ENDLIST":h.live=!1;break;case"DIS":f++,p.tagList.push(["DIS"]);break;case"DISCONTINUITY-SEQ":f=parseInt(R);break;case"KEY":var D=new _(R),O=D.enumeratedString("METHOD"),P=D.URI,x=D.hexadecimalInteger("IV");O&&(o=new v(e,P),P&&["AES-128","SAMPLE-AES","SAMPLE-AES-CENC"].indexOf(O)>=0&&(o.method=O,o.key=null,o.iv=x));break;case"START":var F=new _(R).decimalFloatingPoint("TIME-OFFSET");Object(l.a)(F)&&(h.startTimeOffset=F);break;case"MAP":var M=new _(R);p.relurl=M.URI,M.BYTERANGE&&p.setByteRange(M.BYTERANGE),p.baseurl=e,p.level=r,p.type=i,p.sn="initSegment",h.initSegment=p,(p=new y).rawProgramDateTime=h.initSegment.rawProgramDateTime;break;default:u.b.warn("line parsed but not handled: "+n)}}}return(p=g)&&!p.relurl&&(h.fragments.pop(),c-=p.duration),h.totalduration=c,h.averagetargetduration=c/h.fragments.length,h.endSN=d-1,h.startCC=h.fragments[0]?h.fragments[0].cc:0,h.endCC=f,!h.initSegment&&h.fragments.length&&h.fragments.every(function(t){return I.test(t.relurl)})&&(u.b.warn("MP4 fragments found but no init segment (probably no MAP, incomplete M3U8), trying to fetch SIDX"),(p=new y).relurl=h.fragments[0].relurl,p.baseurl=e,p.level=r,p.type=i,p.sn="initSegment",h.initSegment=p,h.needSidxRanges=!0),m&&function(t,e){for(var r=t[e],i=e-1;i>=0;i--){var a=t[i];a.programDateTime=r.programDateTime-1e3*a.duration,r=a}}(h.fragments,m),h},t}();function C(t,e){t.rawProgramDateTime?t.programDateTime=Date.parse(t.rawProgramDateTime):e&&e.programDateTime&&(t.programDateTime=e.endProgramDateTime),Object(l.a)(t.programDateTime)||(t.programDateTime=null,t.rawProgramDateTime=null)}var P=window.performance,x=function(t){var e,r;function i(e){var r;return(r=t.call(this,e,d.a.MANIFEST_LOADING,d.a.LEVEL_LOADING,d.a.AUDIO_TRACK_LOADING,d.a.SUBTITLE_TRACK_LOADING)||this).loaders={},r}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r,i.canHaveQualityLevels=function(t){return t!==a.AUDIO_TRACK&&t!==a.SUBTITLE_TRACK},i.mapContextToLevelType=function(t){switch(t.type){case a.AUDIO_TRACK:return n.AUDIO;case a.SUBTITLE_TRACK:return n.SUBTITLE;default:return n.MAIN}},i.getResponseUrl=function(t,e){var r=t.url;return void 0!==r&&0!==r.indexOf("data:")||(r=e.url),r};var s=i.prototype;return s.createInternalLoader=function(t){var e=this.hls.config,r=e.pLoader,i=e.loader,a=new(r||i)(e);return t.loader=a,this.loaders[t.type]=a,a},s.getInternalLoader=function(t){return this.loaders[t.type]},s.resetInternalLoader=function(t){this.loaders[t]&&delete this.loaders[t]},s.destroyInternalLoaders=function(){for(var t in this.loaders){var e=this.loaders[t];e&&e.destroy(),this.resetInternalLoader(t)}},s.destroy=function(){this.destroyInternalLoaders(),t.prototype.destroy.call(this)},s.onManifestLoading=function(t){this.load({url:t.url,type:a.MANIFEST,level:0,id:null,responseType:"text"})},s.onLevelLoading=function(t){this.load({url:t.url,type:a.LEVEL,level:t.level,id:t.id,responseType:"text"})},s.onAudioTrackLoading=function(t){this.load({url:t.url,type:a.AUDIO_TRACK,level:null,id:t.id,responseType:"text"})},s.onSubtitleTrackLoading=function(t){this.load({url:t.url,type:a.SUBTITLE_TRACK,level:null,id:t.id,responseType:"text"})},s.load=function(t){var e=this.hls.config;u.b.debug("Loading playlist of type "+t.type+", level: "+t.level+", id: "+t.id);var r,i,n,s,o=this.getInternalLoader(t);if(o){var l=o.context;if(l&&l.url===t.url)return u.b.trace("playlist request ongoing"),!1;u.b.warn("aborting previous loader for type: "+t.type),o.abort()}switch(t.type){case a.MANIFEST:r=e.manifestLoadingMaxRetry,i=e.manifestLoadingTimeOut,n=e.manifestLoadingRetryDelay,s=e.manifestLoadingMaxRetryTimeout;break;case a.LEVEL:r=0,s=0,n=0,i=e.levelLoadingTimeOut;break;default:r=e.levelLoadingMaxRetry,i=e.levelLoadingTimeOut,n=e.levelLoadingRetryDelay,s=e.levelLoadingMaxRetryTimeout}o=this.createInternalLoader(t);var d={timeout:i,maxRetry:r,retryDelay:n,maxRetryDelay:s},c={onSuccess:this.loadsuccess.bind(this),onError:this.loaderror.bind(this),onTimeout:this.loadtimeout.bind(this)};return u.b.debug("Calling internal loader delegate for URL: "+t.url),o.load(t,d,c),!0},s.loadsuccess=function(t,e,r,i){if(void 0===i&&(i=null),r.isSidxRequest)return this._handleSidxRequest(t,r),void this._handlePlaylistLoaded(t,e,r,i);if(this.resetInternalLoader(r.type),"string"!=typeof t.data)throw new Error('expected responseType of "text" for PlaylistLoader');var a=t.data;e.tload=P.now(),0===a.indexOf("#EXTM3U")?a.indexOf("#EXTINF:")>0||a.indexOf("#EXT-X-TARGETDURATION:")>0?this._handleTrackOrLevelPlaylist(t,e,r,i):this._handleMasterPlaylist(t,e,r,i):this._handleManifestParsingError(t,r,"no EXTM3U delimiter",i)},s.loaderror=function(t,e,r){void 0===r&&(r=null),this._handleNetworkError(e,r,!1,t)},s.loadtimeout=function(t,e,r){void 0===r&&(r=null),this._handleNetworkError(e,r,!0)},s._handleMasterPlaylist=function(t,e,r,a){var n=this.hls,s=t.data,o=i.getResponseUrl(t,r),l=O.parseMasterPlaylist(s,o);if(l.length){var c=l.map(function(t){return{id:t.attrs.AUDIO,codec:t.audioCodec}}),h=O.parseMasterPlaylistMedia(s,o,"AUDIO",c),f=O.parseMasterPlaylistMedia(s,o,"SUBTITLES");if(h.length){var g=!1;h.forEach(function(t){t.url||(g=!0)}),!1===g&&l[0].audioCodec&&!l[0].attrs.AUDIO&&(u.b.log("audio codec signaled in quality level, but no embedded audio track signaled, create one"),h.unshift({type:"main",name:"main",default:!1,autoselect:!1,forced:!1,id:-1}))}n.trigger(d.a.MANIFEST_LOADED,{levels:l,audioTracks:h,subtitles:f,url:o,stats:e,networkDetails:a})}else this._handleManifestParsingError(t,r,"no level found in manifest",a)},s._handleTrackOrLevelPlaylist=function(t,e,r,n){var s=this.hls,o=r.id,u=r.level,c=r.type,h=i.getResponseUrl(t,r),f=Object(l.a)(o)?o:0,g=Object(l.a)(u)?u:f,p=i.mapContextToLevelType(r),v=O.parseLevelPlaylist(t.data,h,g,p,f);if(v.tload=e.tload,c===a.MANIFEST){var m={url:h,details:v};s.trigger(d.a.MANIFEST_LOADED,{levels:[m],audioTracks:[],url:h,stats:e,networkDetails:n})}if(e.tparsed=P.now(),v.needSidxRanges){var y=v.initSegment.url;this.load({url:y,isSidxRequest:!0,type:c,level:u,levelDetails:v,id:o,rangeStart:0,rangeEnd:2048,responseType:"arraybuffer"})}else r.levelDetails=v,this._handlePlaylistLoaded(t,e,r,n)},s._handleSidxRequest=function(t,e){if("string"==typeof t.data)throw new Error("sidx request must be made with responseType of array buffer");var r=f.a.parseSegmentIndex(new Uint8Array(t.data));if(r){var i=r.references,a=e.levelDetails;i.forEach(function(t,e){var r=t.info;if(a){var i=a.fragments[e];0===i.byteRange.length&&i.setByteRange(String(1+r.end-r.start)+"@"+String(r.start))}}),a&&a.initSegment.setByteRange(String(r.moovEndOffset)+"@0")}},s._handleManifestParsingError=function(t,e,r,i){this.hls.trigger(d.a.ERROR,{type:o.b.NETWORK_ERROR,details:o.a.MANIFEST_PARSING_ERROR,fatal:!0,url:t.url,reason:r,networkDetails:i})},s._handleNetworkError=function(t,e,r,i){var n,s;void 0===r&&(r=!1),void 0===i&&(i=null),u.b.info("A network error occured while loading a "+t.type+"-type playlist");var l=this.getInternalLoader(t);switch(t.type){case a.MANIFEST:n=r?o.a.MANIFEST_LOAD_TIMEOUT:o.a.MANIFEST_LOAD_ERROR,s=!0;break;case a.LEVEL:n=r?o.a.LEVEL_LOAD_TIMEOUT:o.a.LEVEL_LOAD_ERROR,s=!1;break;case a.AUDIO_TRACK:n=r?o.a.AUDIO_TRACK_LOAD_TIMEOUT:o.a.AUDIO_TRACK_LOAD_ERROR,s=!1;break;default:s=!1}l&&(l.abort(),this.resetInternalLoader(t.type));var c={type:o.b.NETWORK_ERROR,details:n,fatal:s,url:t.url,loader:l,context:t,networkDetails:e};i&&(c.response=i),this.hls.trigger(d.a.ERROR,c)},s._handlePlaylistLoaded=function(t,e,r,n){var s=r.type,o=r.level,l=r.id,u=r.levelDetails;if(u&&u.targetduration)if(i.canHaveQualityLevels(r.type))this.hls.trigger(d.a.LEVEL_LOADED,{details:u,level:o||0,id:l||0,stats:e,networkDetails:n});else switch(s){case a.AUDIO_TRACK:this.hls.trigger(d.a.AUDIO_TRACK_LOADED,{details:u,id:l,stats:e,networkDetails:n});break;case a.SUBTITLE_TRACK:this.hls.trigger(d.a.SUBTITLE_TRACK_LOADED,{details:u,id:l,stats:e,networkDetails:n})}else this._handleManifestParsingError(t,r,"invalid target duration",n)},i}(h);var F=function(t){var e,r;function i(e){var r;return(r=t.call(this,e,d.a.FRAG_LOADING)||this).loaders={},r}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a=i.prototype;return a.destroy=function(){var e=this.loaders;for(var r in e){var i=e[r];i&&i.destroy()}this.loaders={},t.prototype.destroy.call(this)},a.onFragLoading=function(t){var e=t.frag,r=e.type,i=this.loaders,a=this.hls.config,n=a.fLoader,s=a.loader;e.loaded=0;var o,d,c,h=i[r];h&&(u.b.warn("abort previous fragment loader for type: "+r),h.abort()),h=i[r]=e.loader=a.fLoader?new n(a):new s(a),o={url:e.url,frag:e,responseType:"arraybuffer",progressData:!1};var f=e.byteRangeStartOffset,g=e.byteRangeEndOffset;Object(l.a)(f)&&Object(l.a)(g)&&(o.rangeStart=f,o.rangeEnd=g),d={timeout:a.fragLoadingTimeOut,maxRetry:0,retryDelay:0,maxRetryDelay:a.fragLoadingMaxRetryTimeout},c={onSuccess:this.loadsuccess.bind(this),onError:this.loaderror.bind(this),onTimeout:this.loadtimeout.bind(this),onProgress:this.loadprogress.bind(this)},h.load(o,d,c)},a.loadsuccess=function(t,e,r,i){void 0===i&&(i=null);var a=t.data,n=r.frag;n.loader=void 0,this.loaders[n.type]=void 0,this.hls.trigger(d.a.FRAG_LOADED,{payload:a,frag:n,stats:e,networkDetails:i})},a.loaderror=function(t,e,r){void 0===r&&(r=null);var i=e.frag,a=i.loader;a&&a.abort(),this.loaders[i.type]=void 0,this.hls.trigger(d.a.ERROR,{type:o.b.NETWORK_ERROR,details:o.a.FRAG_LOAD_ERROR,fatal:!1,frag:e.frag,response:t,networkDetails:r})},a.loadtimeout=function(t,e,r){void 0===r&&(r=null);var i=e.frag,a=i.loader;a&&a.abort(),this.loaders[i.type]=void 0,this.hls.trigger(d.a.ERROR,{type:o.b.NETWORK_ERROR,details:o.a.FRAG_LOAD_TIMEOUT,fatal:!1,frag:e.frag,networkDetails:r})},a.loadprogress=function(t,e,r,i){void 0===i&&(i=null);var a=e.frag;a.loaded=t.loaded,this.hls.trigger(d.a.FRAG_LOAD_PROGRESS,{frag:a,stats:t,networkDetails:i})},i}(h);var M=function(t){var e,r;function i(e){var r;return(r=t.call(this,e,d.a.KEY_LOADING)||this).loaders={},r.decryptkey=null,r.decrypturl=null,r}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a=i.prototype;return a.destroy=function(){for(var e in this.loaders){var r=this.loaders[e];r&&r.destroy()}this.loaders={},t.prototype.destroy.call(this)},a.onKeyLoading=function(t){var e=t.frag,r=e.type,i=this.loaders[r];if(e.decryptdata){var a=e.decryptdata.uri;if(a!==this.decrypturl||null===this.decryptkey){var n=this.hls.config;if(i&&(u.b.warn("abort previous key loader for type:"+r),i.abort()),!a)return void u.b.warn("key uri is falsy");e.loader=this.loaders[r]=new n.loader(n),this.decrypturl=a,this.decryptkey=null;var s={url:a,frag:e,responseType:"arraybuffer"},o={timeout:n.fragLoadingTimeOut,maxRetry:0,retryDelay:n.fragLoadingRetryDelay,maxRetryDelay:n.fragLoadingMaxRetryTimeout},l={onSuccess:this.loadsuccess.bind(this),onError:this.loaderror.bind(this),onTimeout:this.loadtimeout.bind(this)};e.loader.load(s,o,l)}else this.decryptkey&&(e.decryptdata.key=this.decryptkey,this.hls.trigger(d.a.KEY_LOADED,{frag:e}))}else u.b.warn("Missing decryption data on fragment in onKeyLoading")},a.loadsuccess=function(t,e,r){var i=r.frag;i.decryptdata?(this.decryptkey=i.decryptdata.key=new Uint8Array(t.data),i.loader=void 0,delete this.loaders[i.type],this.hls.trigger(d.a.KEY_LOADED,{frag:i})):u.b.error("after key load, decryptdata unset")},a.loaderror=function(t,e){var r=e.frag,i=r.loader;i&&i.abort(),delete this.loaders[r.type],this.hls.trigger(d.a.ERROR,{type:o.b.NETWORK_ERROR,details:o.a.KEY_LOAD_ERROR,fatal:!1,frag:r,response:t})},a.loadtimeout=function(t,e){var r=e.frag,i=r.loader;i&&i.abort(),delete this.loaders[r.type],this.hls.trigger(d.a.ERROR,{type:o.b.NETWORK_ERROR,details:o.a.KEY_LOAD_TIMEOUT,fatal:!1,frag:r})},i}(h);var N="NOT_LOADED",U="APPENDING",B="PARTIAL",G="OK",K=function(t){var e,r;function i(e){var r;return(r=t.call(this,e,d.a.BUFFER_APPENDED,d.a.FRAG_BUFFERED,d.a.FRAG_LOADED)||this).bufferPadding=.2,r.fragments=Object.create(null),r.timeRanges=Object.create(null),r.config=e.config,r}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a=i.prototype;return a.destroy=function(){this.fragments=Object.create(null),this.timeRanges=Object.create(null),this.config=null,h.prototype.destroy.call(this),t.prototype.destroy.call(this)},a.getBufferedFrag=function(t,e){var r=this.fragments,i=Object.keys(r).filter(function(i){var a=r[i];if(a.body.type!==e)return!1;if(!a.buffered)return!1;var n=a.body;return n.startPTS<=t&&t<=n.endPTS});if(0===i.length)return null;var a=i.pop();return r[a].body},a.detectEvictedFragments=function(t,e){var r,i,a=this;Object.keys(this.fragments).forEach(function(n){var s=a.fragments[n];if(!0===s.buffered){var o=s.range[t];if(o){r=o.time;for(var l=0;l<r.length;l++)if(i=r[l],!1===a.isTimeBuffered(i.startPTS,i.endPTS,e)){a.removeFragment(s.body);break}}}})},a.detectPartialFragments=function(t){var e=this,r=this.getFragmentKey(t),i=this.fragments[r];i&&(i.buffered=!0,Object.keys(this.timeRanges).forEach(function(r){if(t.hasElementaryStream(r)){var a=e.timeRanges[r];i.range[r]=e.getBufferedTimes(t.startPTS,t.endPTS,a)}}))},a.getBufferedTimes=function(t,e,r){for(var i,a,n=[],s=!1,o=0;o<r.length;o++){if(i=r.start(o)-this.bufferPadding,a=r.end(o)+this.bufferPadding,t>=i&&e<=a){n.push({startPTS:Math.max(t,r.start(o)),endPTS:Math.min(e,r.end(o))});break}if(t<a&&e>i)n.push({startPTS:Math.max(t,r.start(o)),endPTS:Math.min(e,r.end(o))}),s=!0;else if(e<=i)break}return{time:n,partial:s}},a.getFragmentKey=function(t){return t.type+"_"+t.level+"_"+t.urlId+"_"+t.sn},a.getPartialFragment=function(t){var e,r,i,a=this,n=null,s=0;return Object.keys(this.fragments).forEach(function(o){var l=a.fragments[o];a.isPartial(l)&&(r=l.body.startPTS-a.bufferPadding,i=l.body.endPTS+a.bufferPadding,t>=r&&t<=i&&(e=Math.min(t-r,i-t),s<=e&&(n=l.body,s=e)))}),n},a.getState=function(t){var e=this.getFragmentKey(t),r=this.fragments[e],i=N;return void 0!==r&&(i=r.buffered?!0===this.isPartial(r)?B:G:U),i},a.isPartial=function(t){return!0===t.buffered&&(void 0!==t.range.video&&!0===t.range.video.partial||void 0!==t.range.audio&&!0===t.range.audio.partial)},a.isTimeBuffered=function(t,e,r){for(var i,a,n=0;n<r.length;n++){if(i=r.start(n)-this.bufferPadding,a=r.end(n)+this.bufferPadding,t>=i&&e<=a)return!0;if(e<=i)return!1}return!1},a.onFragLoaded=function(t){var e=t.frag;Object(l.a)(e.sn)&&!e.bitrateTest&&(this.fragments[this.getFragmentKey(e)]={body:e,range:Object.create(null),buffered:!1})},a.onBufferAppended=function(t){var e=this;this.timeRanges=t.timeRanges,Object.keys(this.timeRanges).forEach(function(t){var r=e.timeRanges[t];e.detectEvictedFragments(t,r)})},a.onFragBuffered=function(t){this.detectPartialFragments(t.frag)},a.hasFragment=function(t){var e=this.getFragmentKey(t);return void 0!==this.fragments[e]},a.removeFragment=function(t){var e=this.getFragmentKey(t);delete this.fragments[e]},a.removeAllFragments=function(){this.fragments=Object.create(null)},i}(h),j={search:function(t,e){for(var r=0,i=t.length-1,a=null,n=null;r<=i;){var s=e(n=t[a=(r+i)/2|0]);if(s>0)r=a+1;else{if(!(s<0))return n;i=a-1}}return null}},H=function(){function t(){}return t.isBuffered=function(t,e){try{if(t)for(var r=t.buffered,i=0;i<r.length;i++)if(e>=r.start(i)&&e<=r.end(i))return!0}catch(t){}return!1},t.bufferInfo=function(t,e,r){try{if(t){var i,a=t.buffered,n=[];for(i=0;i<a.length;i++)n.push({start:a.start(i),end:a.end(i)});return this.bufferedInfo(n,e,r)}}catch(t){}return{len:0,start:e,end:e,nextStart:void 0}},t.bufferedInfo=function(t,e,r){t.sort(function(t,e){var r=t.start-e.start;return r||e.end-t.end});for(var i=[],a=0;a<t.length;a++){var n=i.length;if(n){var s=i[n-1].end;t[a].start-s<r?t[a].end>s&&(i[n-1].end=t[a].end):i.push(t[a])}else i.push(t[a])}for(var o,l=0,d=e,u=e,c=0;c<i.length;c++){var h=i[c].start,f=i[c].end;if(e+r>=h&&e<f)d=h,l=(u=f)-e;else if(e+r<h){o=h;break}}return{len:l,start:d,end:u,nextStart:o}},t}(),V=r(8),W=r(11),Y=r(9);function q(){return window.MediaSource||window.WebKitMediaSource}var z=r(5);var X=function(t){var e,r;function i(){return t.apply(this,arguments)||this}return r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r,i.prototype.trigger=function(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];this.emit.apply(this,[t,t].concat(r))},i}(V.EventEmitter),Q=Object(z.a)(),$=q()||{isTypeSupported:function(){return!1}},J=function(){function t(t,e){var r=this;this.hls=t,this.id=e;var i=this.observer=new X,a=t.config,n=function(e,i){(i=i||{}).frag=r.frag,i.id=r.id,t.trigger(e,i)};i.on(d.a.FRAG_DECRYPTED,n),i.on(d.a.FRAG_PARSING_INIT_SEGMENT,n),i.on(d.a.FRAG_PARSING_DATA,n),i.on(d.a.FRAG_PARSED,n),i.on(d.a.ERROR,n),i.on(d.a.FRAG_PARSING_METADATA,n),i.on(d.a.FRAG_PARSING_USERDATA,n),i.on(d.a.INIT_PTS_FOUND,n);var s={mp4:$.isTypeSupported("video/mp4"),mpeg:$.isTypeSupported("audio/mpeg"),mp3:$.isTypeSupported('audio/mp4; codecs="mp3"')},l=navigator.vendor;if(a.enableWorker&&"undefined"!=typeof Worker){var c;u.b.log("demuxing in webworker");try{c=this.w=W(12),this.onwmsg=this.onWorkerMessage.bind(this),c.addEventListener("message",this.onwmsg),c.onerror=function(e){t.trigger(d.a.ERROR,{type:o.b.OTHER_ERROR,details:o.a.INTERNAL_EXCEPTION,fatal:!0,event:"demuxerWorker",err:{message:e.message+" ("+e.filename+":"+e.lineno+")"}})},c.postMessage({cmd:"init",typeSupported:s,vendor:l,id:e,config:JSON.stringify(a)})}catch(t){u.b.warn("Error in worker:",t),u.b.error("Error while initializing DemuxerWorker, fallback on DemuxerInline"),c&&Q.URL.revokeObjectURL(c.objectURL),this.demuxer=new Y.a(i,s,a,l),this.w=void 0}}else this.demuxer=new Y.a(i,s,a,l)}var e=t.prototype;return e.destroy=function(){var t=this.w;if(t)t.removeEventListener("message",this.onwmsg),t.terminate(),this.w=null;else{var e=this.demuxer;e&&(e.destroy(),this.demuxer=null)}var r=this.observer;r&&(r.removeAllListeners(),this.observer=null)},e.push=function(t,e,r,i,a,n,s,o){var d=this.w,c=Object(l.a)(a.startPTS)?a.startPTS:a.start,h=a.decryptdata,f=this.frag,g=!(f&&a.cc===f.cc),p=!(f&&a.level===f.level),v=f&&a.sn===f.sn+1,m=!p&&v;if(g&&u.b.log(this.id+":discontinuity detected"),p&&u.b.log(this.id+":switch detected"),this.frag=a,d)d.postMessage({cmd:"demux",data:t,decryptdata:h,initSegment:e,audioCodec:r,videoCodec:i,timeOffset:c,discontinuity:g,trackSwitch:p,contiguous:m,duration:n,accurateTimeOffset:s,defaultInitPTS:o},t instanceof ArrayBuffer?[t]:[]);else{var y=this.demuxer;y&&y.push(t,h,e,r,i,c,g,p,m,n,s,o)}},e.onWorkerMessage=function(t){var e=t.data,r=this.hls;switch(e.event){case"init":Q.URL.revokeObjectURL(this.w.objectURL);break;case d.a.FRAG_PARSING_DATA:e.data.data1=new Uint8Array(e.data1),e.data2&&(e.data.data2=new Uint8Array(e.data2));default:e.data=e.data||{},e.data.frag=this.frag,e.data.id=this.id,r.trigger(e.event,e.data)}},t}();function Z(t,e,r){switch(e){case"audio":t.audioGroupIds||(t.audioGroupIds=[]),t.audioGroupIds.push(r);break;case"text":t.textGroupIds||(t.textGroupIds=[]),t.textGroupIds.push(r)}}function tt(t,e,r){var i=t[e],a=t[r],n=a.startPTS;Object(l.a)(n)?r>e?(i.duration=n-i.start,i.duration<0&&u.b.warn("negative duration computed for frag "+i.sn+",level "+i.level+", there should be some duration drift between playlist and fragment!")):(a.duration=i.start-n,a.duration<0&&u.b.warn("negative duration computed for frag "+a.sn+",level "+a.level+", there should be some duration drift between playlist and fragment!")):a.start=r>e?i.start+i.duration:Math.max(i.start-a.duration,0)}function et(t,e,r,i,a,n){var s=r;if(Object(l.a)(e.startPTS)){var o=Math.abs(e.startPTS-r);Object(l.a)(e.deltaPTS)?e.deltaPTS=Math.max(o,e.deltaPTS):e.deltaPTS=o,s=Math.max(r,e.startPTS),r=Math.min(r,e.startPTS),i=Math.max(i,e.endPTS),a=Math.min(a,e.startDTS),n=Math.max(n,e.endDTS)}var d=r-e.start;e.start=e.startPTS=r,e.maxStartPTS=s,e.endPTS=i,e.startDTS=a,e.endDTS=n,e.duration=i-r;var u,c,h,f=e.sn;if(!t||f<t.startSN||f>t.endSN)return 0;for(u=f-t.startSN,(c=t.fragments)[u]=e,h=u;h>0;h--)tt(c,h,h-1);for(h=u;h<c.length-1;h++)tt(c,h,h+1);return t.PTSKnown=!0,d}function rt(t,e){e.initSegment&&t.initSegment&&(e.initSegment=t.initSegment);var r,i=0;if(it(t,e,function(t,a){i=t.cc-a.cc,Object(l.a)(t.startPTS)&&(a.start=a.startPTS=t.startPTS,a.endPTS=t.endPTS,a.duration=t.duration,a.backtracked=t.backtracked,a.dropped=t.dropped,r=a),e.PTSKnown=!0}),e.PTSKnown){if(i){u.b.log("discontinuity sliding from playlist, take drift into account");for(var a=e.fragments,n=0;n<a.length;n++)a[n].cc+=i}r?et(e,r,r.startPTS,r.endPTS,r.startDTS,r.endDTS):function(t,e){var r=e.startSN-t.startSN,i=t.fragments,a=e.fragments;if(r<0||r>i.length)return;for(var n=0;n<a.length;n++)a[n].start+=i[r].start}(t,e),e.PTSKnown=t.PTSKnown}}function it(t,e,r){if(t&&e)for(var i=Math.max(t.startSN,e.startSN)-e.startSN,a=Math.min(t.endSN,e.endSN)-e.startSN,n=e.startSN-t.startSN,s=i;s<=a;s++){var o=t.fragments[n+s],l=e.fragments[s];if(!o||!l)break;r(o,l,s)}}function at(t,e,r){var i=1e3*(e.averagetargetduration?e.averagetargetduration:e.targetduration),a=i/2;return t&&e.endSN===t.endSN&&(i=a),r&&(i=Math.max(a,i-(window.performance.now()-r))),Math.round(i)}var nt={toString:function(t){for(var e="",r=t.length,i=0;i<r;i++)e+="["+t.start(i).toFixed(3)+","+t.end(i).toFixed(3)+"]";return e}};function st(t,e){e.fragments.forEach(function(e){if(e){var r=e.start+t;e.start=e.startPTS=r,e.endPTS=r+e.duration}}),e.PTSKnown=!0}function ot(t,e,r){!function(t,e,r){if(function(t,e,r){var i=!1;return e&&e.details&&r&&(r.endCC>r.startCC||t&&t.cc<r.startCC)&&(i=!0),i}(t,r,e)){var i=function(t,e){var r=t.fragments,i=e.fragments;if(i.length&&r.length){var a=function(t,e){for(var r=null,i=0;i<t.length;i+=1){var a=t[i];if(a&&a.cc===e){r=a;break}}return r}(r,i[0].cc);if(a&&(!a||a.startPTS))return a;u.b.log("No frag in previous level to align on")}else u.b.log("No fragments to align")}(r.details,e);i&&(u.b.log("Adjusting PTS using last level due to CC increase within current level"),st(i.start,e))}}(t,r,e),!r.PTSKnown&&e&&function(t,e){if(e&&e.fragments.length){if(!t.hasProgramDateTime||!e.hasProgramDateTime)return;var r=e.fragments[0].programDateTime,i=t.fragments[0].programDateTime,a=(i-r)/1e3+e.fragments[0].start;Object(l.a)(a)&&(u.b.log("adjusting PTS using programDateTime delta, sliding:"+a.toFixed(3)),st(a,t))}}(r,e.details)}function lt(t,e,r){if(null===e||!Array.isArray(t)||!t.length||!Object(l.a)(e))return null;if(e<(t[0].programDateTime||0))return null;if(e>=(t[t.length-1].endProgramDateTime||0))return null;r=r||0;for(var i=0;i<t.length;++i){var a=t[i];if(ct(e,r,a))return a}return null}function dt(t,e,r,i){void 0===r&&(r=0),void 0===i&&(i=0);var a=t?e[t.sn-e[0].sn+1]:null;return a&&!ut(r,i,a)?a:j.search(e,ut.bind(null,r,i))}function ut(t,e,r){void 0===t&&(t=0),void 0===e&&(e=0);var i=Math.min(e,r.duration+(r.deltaPTS?r.deltaPTS:0));return r.start+r.duration-i<=t?1:r.start-i>t&&r.start?-1:0}function ct(t,e,r){var i=1e3*Math.min(e,r.duration+(r.deltaPTS?r.deltaPTS:0));return(r.endProgramDateTime||0)-i>t}var ht=function(){function t(t,e,r,i){this.config=t,this.media=e,this.fragmentTracker=r,this.hls=i,this.stallReported=!1}var e=t.prototype;return e.poll=function(t,e){var r=this.config,i=this.media,a=i.currentTime,n=window.performance.now();if(a!==t)return this.stallReported&&(u.b.warn("playback not stuck anymore @"+a+", after "+Math.round(n-this.stalled)+"ms"),this.stallReported=!1),this.stalled=null,void(this.nudgeRetry=0);if(!(i.ended||!i.buffered.length||i.readyState>2||i.seeking&&H.isBuffered(i,a))){var s=n-this.stalled,o=H.bufferInfo(i,a,r.maxBufferHole);this.stalled?(s>=1e3&&this._reportStall(o.len),this._tryFixBufferStall(o,s)):this.stalled=n}},e._tryFixBufferStall=function(t,e){var r=this.config,i=this.fragmentTracker,a=this.media.currentTime,n=i.getPartialFragment(a);n&&this._trySkipBufferHole(n),t.len>.5&&e>1e3*r.highBufferWatchdogPeriod&&(this.stalled=null,this._tryNudgeBuffer())},e._reportStall=function(t){var e=this.hls,r=this.media;this.stallReported||(this.stallReported=!0,u.b.warn("Playback stalling at @"+r.currentTime+" due to low buffer"),e.trigger(d.a.ERROR,{type:o.b.MEDIA_ERROR,details:o.a.BUFFER_STALLED_ERROR,fatal:!1,buffer:t}))},e._trySkipBufferHole=function(t){for(var e=this.hls,r=this.media,i=r.currentTime,a=0,n=0;n<r.buffered.length;n++){var s=r.buffered.start(n);if(i>=a&&i<s)return r.currentTime=Math.max(s,r.currentTime+.1),u.b.warn("skipping hole, adjusting currentTime from "+i+" to "+r.currentTime),this.stalled=null,void e.trigger(d.a.ERROR,{type:o.b.MEDIA_ERROR,details:o.a.BUFFER_SEEK_OVER_HOLE,fatal:!1,reason:"fragment loaded with buffer holes, seeking from "+i+" to "+r.currentTime,frag:t});a=r.buffered.end(n)}},e._tryNudgeBuffer=function(){var t=this.config,e=this.hls,r=this.media,i=r.currentTime,a=(this.nudgeRetry||0)+1;if(this.nudgeRetry=a,a<t.nudgeMaxRetry){var n=i+a*t.nudgeOffset;u.b.log("adjust currentTime from "+i+" to "+n),r.currentTime=n,e.trigger(d.a.ERROR,{type:o.b.MEDIA_ERROR,details:o.a.BUFFER_NUDGE_ON_STALL,fatal:!1})}else u.b.error("still stuck in high buffer @"+i+" after "+t.nudgeMaxRetry+", raise fatal error"),e.trigger(d.a.ERROR,{type:o.b.MEDIA_ERROR,details:o.a.BUFFER_STALLED_ERROR,fatal:!0})},t}();var ft=function(t){var e,r;function i(e){for(var r,i=arguments.length,a=new Array(i>1?i-1:0),n=1;n<i;n++)a[n-1]=arguments[n];return(r=t.call.apply(t,[this,e].concat(a))||this)._boundTick=void 0,r._tickTimer=null,r._tickInterval=null,r._tickCallCount=0,r._boundTick=r.tick.bind(function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(r)),r}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a=i.prototype;return a.onHandlerDestroying=function(){this.clearNextTick(),this.clearInterval()},a.hasInterval=function(){return!!this._tickInterval},a.hasNextTick=function(){return!!this._tickTimer},a.setInterval=function(t){return!this._tickInterval&&(this._tickInterval=self.setInterval(this._boundTick,t),!0)},a.clearInterval=function(){return!!this._tickInterval&&(self.clearInterval(this._tickInterval),this._tickInterval=null,!0)},a.clearNextTick=function(){return!!this._tickTimer&&(self.clearTimeout(this._tickTimer),this._tickTimer=null,!0)},a.tick=function(){this._tickCallCount++,1===this._tickCallCount&&(this.doTick(),this._tickCallCount>1&&(this.clearNextTick(),this._tickTimer=self.setTimeout(this._boundTick,0)),this._tickCallCount=0)},a.doTick=function(){},i}(h);var gt={STOPPED:"STOPPED",STARTING:"STARTING",IDLE:"IDLE",PAUSED:"PAUSED",KEY_LOADING:"KEY_LOADING",FRAG_LOADING:"FRAG_LOADING",FRAG_LOADING_WAITING_RETRY:"FRAG_LOADING_WAITING_RETRY",WAITING_TRACK:"WAITING_TRACK",PARSING:"PARSING",PARSED:"PARSED",BUFFER_FLUSHING:"BUFFER_FLUSHING",ENDED:"ENDED",ERROR:"ERROR",WAITING_INIT_PTS:"WAITING_INIT_PTS",WAITING_LEVEL:"WAITING_LEVEL"},pt=function(t){var e,r;function i(){return t.apply(this,arguments)||this}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a=i.prototype;return a.doTick=function(){},a.startLoad=function(){},a.stopLoad=function(){var t=this.fragCurrent;t&&(t.loader&&t.loader.abort(),this.fragmentTracker.removeFragment(t)),this.demuxer&&(this.demuxer.destroy(),this.demuxer=null),this.fragCurrent=null,this.fragPrevious=null,this.clearInterval(),this.clearNextTick(),this.state=gt.STOPPED},a._streamEnded=function(t,e){var r=this.fragCurrent,i=this.fragmentTracker;if(!e.live&&r&&!r.backtracked&&r.sn===e.endSN&&!t.nextStart){var a=i.getState(r);return a===B||a===G}return!1},a.onMediaSeeking=function(){var t=this.config,e=this.media,r=this.mediaBuffer,i=this.state,a=e?e.currentTime:null,n=H.bufferInfo(r||e,a,this.config.maxBufferHole);if(Object(l.a)(a)&&u.b.log("media seeking to "+a.toFixed(3)),i===gt.FRAG_LOADING){var s=this.fragCurrent;if(0===n.len&&s){var o=t.maxFragLookUpTolerance,d=s.start-o,c=s.start+s.duration+o;a<d||a>c?(s.loader&&(u.b.log("seeking outside of buffer while fragment load in progress, cancel fragment load"),s.loader.abort()),this.fragCurrent=null,this.fragPrevious=null,this.state=gt.IDLE):u.b.log("seeking outside of buffer but within currently loaded fragment range")}}else i===gt.ENDED&&(0===n.len&&(this.fragPrevious=null,this.fragCurrent=null),this.state=gt.IDLE);e&&(this.lastCurrentTime=a),this.loadedmetadata||(this.nextLoadPosition=this.startPosition=a),this.tick()},a.onMediaEnded=function(){this.startPosition=this.lastCurrentTime=0},a.onHandlerDestroying=function(){this.stopLoad(),t.prototype.onHandlerDestroying.call(this)},a.onHandlerDestroyed=function(){this.state=gt.STOPPED,this.fragmentTracker=null},a.computeLivePosition=function(t,e){var r=void 0!==this.config.liveSyncDuration?this.config.liveSyncDuration:this.config.liveSyncDurationCount*e.targetduration;return t+Math.max(0,e.totalduration-r)},i}(ft);function vt(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var mt=function(t){var e,r;function i(e,r){var i;return(i=t.call(this,e,d.a.MEDIA_ATTACHED,d.a.MEDIA_DETACHING,d.a.MANIFEST_LOADING,d.a.MANIFEST_PARSED,d.a.LEVEL_LOADED,d.a.KEY_LOADED,d.a.FRAG_LOADED,d.a.FRAG_LOAD_EMERGENCY_ABORTED,d.a.FRAG_PARSING_INIT_SEGMENT,d.a.FRAG_PARSING_DATA,d.a.FRAG_PARSED,d.a.ERROR,d.a.AUDIO_TRACK_SWITCHING,d.a.AUDIO_TRACK_SWITCHED,d.a.BUFFER_CREATED,d.a.BUFFER_APPENDED,d.a.BUFFER_FLUSHED)||this).fragmentTracker=r,i.config=e.config,i.audioCodecSwap=!1,i._state=gt.STOPPED,i.stallReported=!1,i.gapController=null,i.altAudio=!1,i}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a,s,c,h=i.prototype;return h.startLoad=function(t){if(this.levels){var e=this.lastCurrentTime,r=this.hls;if(this.stopLoad(),this.setInterval(100),this.level=-1,this.fragLoadError=0,!this.startFragRequested){var i=r.startLevel;-1===i&&(i=0,this.bitrateTest=!0),this.level=r.nextLoadLevel=i,this.loadedmetadata=!1}e>0&&-1===t&&(u.b.log("override startPosition with lastCurrentTime @"+e.toFixed(3)),t=e),this.state=gt.IDLE,this.nextLoadPosition=this.startPosition=this.lastCurrentTime=t,this.tick()}else this.forceStartLoad=!0,this.state=gt.STOPPED},h.stopLoad=function(){this.forceStartLoad=!1,t.prototype.stopLoad.call(this)},h.doTick=function(){switch(this.state){case gt.BUFFER_FLUSHING:this.fragLoadError=0;break;case gt.IDLE:this._doTickIdle();break;case gt.WAITING_LEVEL:var t=this.levels[this.level];t&&t.details&&(this.state=gt.IDLE);break;case gt.FRAG_LOADING_WAITING_RETRY:var e=window.performance.now(),r=this.retryDate;(!r||e>=r||this.media&&this.media.seeking)&&(u.b.log("mediaController: retryDate reached, switch back to IDLE state"),this.state=gt.IDLE);break;case gt.ERROR:case gt.STOPPED:case gt.FRAG_LOADING:case gt.PARSING:case gt.PARSED:case gt.ENDED:}this._checkBuffer(),this._checkFragmentChanged()},h._doTickIdle=function(){var t=this.hls,e=t.config,r=this.media;if(void 0!==this.levelLastLoaded&&(r||!this.startFragRequested&&e.startFragPrefetch)){var i;i=this.loadedmetadata?r.currentTime:this.nextLoadPosition;var a=t.nextLoadLevel,n=this.levels[a];if(n){var s,o=n.bitrate;s=o?Math.max(8*e.maxBufferSize/o,e.maxBufferLength):e.maxBufferLength,s=Math.min(s,e.maxMaxBufferLength);var l=H.bufferInfo(this.mediaBuffer?this.mediaBuffer:r,i,e.maxBufferHole),c=l.len;if(!(c>=s)){u.b.trace("buffer length of "+c.toFixed(3)+" is below max of "+s.toFixed(3)+". checking for more payload ..."),this.level=t.nextLoadLevel=a;var h=n.details;if(!h||h.live&&this.levelLastLoaded!==a)this.state=gt.WAITING_LEVEL;else{if(this._streamEnded(l,h)){var f={};return this.altAudio&&(f.type="video"),this.hls.trigger(d.a.BUFFER_EOS,f),void(this.state=gt.ENDED)}this._fetchPayloadOrEos(i,l,h)}}}}},h._fetchPayloadOrEos=function(t,e,r){var i=this.fragPrevious,a=this.level,n=r.fragments,s=n.length;if(0!==s){var o,l=n[0].start,d=n[s-1].start+n[s-1].duration,c=e.end;if(r.initSegment&&!r.initSegment.data)o=r.initSegment;else if(r.live){var h=this.config.initialLiveManifestSize;if(s<h)return void u.b.warn("Can not start playback of a level, reason: not enough fragments "+s+" < "+h);if(null===(o=this._ensureFragmentAtLivePoint(r,c,l,d,i,n,s)))return}else c<l&&(o=n[0]);o||(o=this._findFragment(l,i,s,n,c,d,r)),o&&(o.encrypted?(u.b.log("Loading key for "+o.sn+" of ["+r.startSN+" ,"+r.endSN+"],level "+a),this._loadKey(o)):(u.b.log("Loading "+o.sn+" of ["+r.startSN+" ,"+r.endSN+"],level "+a+", currentTime:"+t.toFixed(3)+",bufferEnd:"+c.toFixed(3)),this._loadFragment(o)))}},h._ensureFragmentAtLivePoint=function(t,e,r,i,a,n,s){var o,l=this.hls.config,d=this.media,c=void 0!==l.liveMaxLatencyDuration?l.liveMaxLatencyDuration:l.liveMaxLatencyDurationCount*t.targetduration;if(e<Math.max(r-l.maxFragLookUpTolerance,i-c)){var h=this.liveSyncPosition=this.computeLivePosition(r,t);u.b.log("buffer end: "+e.toFixed(3)+" is located too far from the end of live sliding playlist, reset currentTime to : "+h.toFixed(3)),e=h,d&&d.readyState&&d.duration>h&&(d.currentTime=h),this.nextLoadPosition=h}if(t.PTSKnown&&e>i&&d&&d.readyState)return null;if(this.startFragRequested&&!t.PTSKnown){if(a)if(t.hasProgramDateTime)u.b.log("live playlist, switching playlist, load frag with same PDT: "+a.programDateTime),o=lt(n,a.endProgramDateTime,l.maxFragLookUpTolerance);else{var f=a.sn+1;if(f>=t.startSN&&f<=t.endSN){var g=n[f-t.startSN];a.cc===g.cc&&(o=g,u.b.log("live playlist, switching playlist, load frag with next SN: "+o.sn))}o||(o=j.search(n,function(t){return a.cc-t.cc}))&&u.b.log("live playlist, switching playlist, load frag with same CC: "+o.sn)}o||(o=n[Math.min(s-1,Math.round(s/2))],u.b.log("live playlist, switching playlist, unknown, load middle frag : "+o.sn))}return o},h._findFragment=function(t,e,r,i,a,n,s){var o,l=this.hls.config;a<n?o=dt(e,i,a,a>n-l.maxFragLookUpTolerance?0:l.maxFragLookUpTolerance):o=i[r-1];if(o){var d=o.sn-s.startSN,c=e&&o.level===e.level,h=i[d-1],f=i[d+1];if(e&&o.sn===e.sn)if(c&&!o.backtracked)if(o.sn<s.endSN){var g=e.deltaPTS;g&&g>l.maxBufferHole&&e.dropped&&d?(o=h,u.b.warn("SN just loaded, with large PTS gap between audio and video, maybe frag is not starting with a keyframe ? load previous one to try to overcome this")):(o=f,u.b.log("SN just loaded, load next one: "+o.sn,o))}else o=null;else o.backtracked&&(f&&f.backtracked?(u.b.warn("Already backtracked from fragment "+f.sn+", will not backtrack to fragment "+o.sn+". Loading fragment "+f.sn),o=f):(u.b.warn("Loaded fragment with dropped frames, backtracking 1 segment to find a keyframe"),o.dropped=0,h?(o=h).backtracked=!0:d&&(o=null)))}return o},h._loadKey=function(t){this.state=gt.KEY_LOADING,this.hls.trigger(d.a.KEY_LOADING,{frag:t})},h._loadFragment=function(t){var e=this.fragmentTracker.getState(t);this.fragCurrent=t,"initSegment"!==t.sn&&(this.startFragRequested=!0),Object(l.a)(t.sn)&&!t.bitrateTest&&(this.nextLoadPosition=t.start+t.duration),t.backtracked||e===N||e===B?(t.autoLevel=this.hls.autoLevelEnabled,t.bitrateTest=this.bitrateTest,this.hls.trigger(d.a.FRAG_LOADING,{frag:t}),this.demuxer||(this.demuxer=new J(this.hls,"main")),this.state=gt.FRAG_LOADING):e===U&&this._reduceMaxBufferLength(t.duration)&&this.fragmentTracker.removeFragment(t)},h.getBufferedFrag=function(t){return this.fragmentTracker.getBufferedFrag(t,n.MAIN)},h.followingBufferedFrag=function(t){return t?this.getBufferedFrag(t.endPTS+.5):null},h._checkFragmentChanged=function(){var t,e,r=this.media;if(r&&r.readyState&&!1===r.seeking&&((e=r.currentTime)>this.lastCurrentTime&&(this.lastCurrentTime=e),H.isBuffered(r,e)?t=this.getBufferedFrag(e):H.isBuffered(r,e+.1)&&(t=this.getBufferedFrag(e+.1)),t)){var i=t;if(i!==this.fragPlaying){this.hls.trigger(d.a.FRAG_CHANGED,{frag:i});var a=i.level;this.fragPlaying&&this.fragPlaying.level===a||this.hls.trigger(d.a.LEVEL_SWITCHED,{level:a}),this.fragPlaying=i}}},h.immediateLevelSwitch=function(){if(u.b.log("immediateLevelSwitch"),!this.immediateSwitch){this.immediateSwitch=!0;var t,e=this.media;e?(t=e.paused,e.pause()):t=!0,this.previouslyPaused=t}var r=this.fragCurrent;r&&r.loader&&r.loader.abort(),this.fragCurrent=null,this.flushMainBuffer(0,Number.POSITIVE_INFINITY)},h.immediateLevelSwitchEnd=function(){var t=this.media;t&&t.buffered.length&&(this.immediateSwitch=!1,H.isBuffered(t,t.currentTime)&&(t.currentTime-=1e-4),this.previouslyPaused||t.play())},h.nextLevelSwitch=function(){var t=this.media;if(t&&t.readyState){var e,r,i;if((r=this.getBufferedFrag(t.currentTime))&&r.startPTS>1&&this.flushMainBuffer(0,r.startPTS-1),t.paused)e=0;else{var a=this.hls.nextLoadLevel,n=this.levels[a],s=this.fragLastKbps;e=s&&this.fragCurrent?this.fragCurrent.duration*n.bitrate/(1e3*s)+1:0}if((i=this.getBufferedFrag(t.currentTime+e))&&(i=this.followingBufferedFrag(i))){var o=this.fragCurrent;o&&o.loader&&o.loader.abort(),this.fragCurrent=null,this.flushMainBuffer(i.maxStartPTS,Number.POSITIVE_INFINITY)}}},h.flushMainBuffer=function(t,e){this.state=gt.BUFFER_FLUSHING;var r={startOffset:t,endOffset:e};this.altAudio&&(r.type="video"),this.hls.trigger(d.a.BUFFER_FLUSHING,r)},h.onMediaAttached=function(t){var e=this.media=this.mediaBuffer=t.media;this.onvseeking=this.onMediaSeeking.bind(this),this.onvseeked=this.onMediaSeeked.bind(this),this.onvended=this.onMediaEnded.bind(this),e.addEventListener("seeking",this.onvseeking),e.addEventListener("seeked",this.onvseeked),e.addEventListener("ended",this.onvended);var r=this.config;this.levels&&r.autoStartLoad&&this.hls.startLoad(r.startPosition),this.gapController=new ht(r,e,this.fragmentTracker,this.hls)},h.onMediaDetaching=function(){var t=this.media;t&&t.ended&&(u.b.log("MSE detaching and video ended, reset startPosition"),this.startPosition=this.lastCurrentTime=0);var e=this.levels;e&&e.forEach(function(t){t.details&&t.details.fragments.forEach(function(t){t.backtracked=void 0})}),t&&(t.removeEventListener("seeking",this.onvseeking),t.removeEventListener("seeked",this.onvseeked),t.removeEventListener("ended",this.onvended),this.onvseeking=this.onvseeked=this.onvended=null),this.fragmentTracker.removeAllFragments(),this.media=this.mediaBuffer=null,this.loadedmetadata=!1,this.stopLoad()},h.onMediaSeeked=function(){var t=this.media,e=t?t.currentTime:void 0;Object(l.a)(e)&&u.b.log("media seeked to "+e.toFixed(3)),this.tick()},h.onManifestLoading=function(){u.b.log("trigger BUFFER_RESET"),this.hls.trigger(d.a.BUFFER_RESET),this.fragmentTracker.removeAllFragments(),this.stalled=!1,this.startPosition=this.lastCurrentTime=0},h.onManifestParsed=function(t){var e,r=!1,i=!1;t.levels.forEach(function(t){(e=t.audioCodec)&&(-1!==e.indexOf("mp4a.40.2")&&(r=!0),-1!==e.indexOf("mp4a.40.5")&&(i=!0))}),this.audioCodecSwitch=r&&i,this.audioCodecSwitch&&u.b.log("both AAC/HE-AAC audio found in levels; declaring level codec as HE-AAC"),this.altAudio=t.altAudio,this.levels=t.levels,this.startFragRequested=!1;var a=this.config;(a.autoStartLoad||this.forceStartLoad)&&this.hls.startLoad(a.startPosition)},h.onLevelLoaded=function(t){var e=t.details,r=t.level,i=this.levels[this.levelLastLoaded],a=this.levels[r],n=e.totalduration,s=0;if(u.b.log("level "+r+" loaded ["+e.startSN+","+e.endSN+"],duration:"+n),e.live){var o=a.details;o&&e.fragments.length>0?(rt(o,e),s=e.fragments[0].start,this.liveSyncPosition=this.computeLivePosition(s,o),e.PTSKnown&&Object(l.a)(s)?u.b.log("live playlist sliding:"+s.toFixed(3)):(u.b.log("live playlist - outdated PTS, unknown sliding"),ot(this.fragPrevious,i,e))):(u.b.log("live playlist - first load, unknown sliding"),e.PTSKnown=!1,ot(this.fragPrevious,i,e))}else e.PTSKnown=!1;if(a.details=e,this.levelLastLoaded=r,this.hls.trigger(d.a.LEVEL_UPDATED,{details:e,level:r}),!1===this.startFragRequested){if(-1===this.startPosition||-1===this.lastCurrentTime){var c=e.startTimeOffset;Object(l.a)(c)?(c<0&&(u.b.log("negative start time offset "+c+", count from end of last fragment"),c=s+n+c),u.b.log("start time offset found in playlist, adjust startPosition to "+c),this.startPosition=c):e.live?(this.startPosition=this.computeLivePosition(s,e),u.b.log("configure startPosition to "+this.startPosition)):this.startPosition=0,this.lastCurrentTime=this.startPosition}this.nextLoadPosition=this.startPosition}this.state===gt.WAITING_LEVEL&&(this.state=gt.IDLE),this.tick()},h.onKeyLoaded=function(){this.state===gt.KEY_LOADING&&(this.state=gt.IDLE,this.tick())},h.onFragLoaded=function(t){var e=this.fragCurrent,r=this.hls,i=this.levels,a=this.media,n=t.frag;if(this.state===gt.FRAG_LOADING&&e&&"main"===n.type&&n.level===e.level&&n.sn===e.sn){var s=t.stats,o=i[e.level],l=o.details;if(this.bitrateTest=!1,this.stats=s,u.b.log("Loaded "+e.sn+" of ["+l.startSN+" ,"+l.endSN+"],level "+e.level),n.bitrateTest&&r.nextLoadLevel)this.state=gt.IDLE,this.startFragRequested=!1,s.tparsed=s.tbuffered=window.performance.now(),r.trigger(d.a.FRAG_BUFFERED,{stats:s,frag:e,id:"main"}),this.tick();else if("initSegment"===n.sn)this.state=gt.IDLE,s.tparsed=s.tbuffered=window.performance.now(),l.initSegment.data=t.payload,r.trigger(d.a.FRAG_BUFFERED,{stats:s,frag:e,id:"main"}),this.tick();else{u.b.log("Parsing "+e.sn+" of ["+l.startSN+" ,"+l.endSN+"],level "+e.level+", cc "+e.cc),this.state=gt.PARSING,this.pendingBuffering=!0,this.appended=!1,n.bitrateTest&&(n.bitrateTest=!1,this.fragmentTracker.onFragLoaded({frag:n}));var c=!(a&&a.seeking)&&(l.PTSKnown||!l.live),h=l.initSegment?l.initSegment.data:[],f=this._getAudioCodec(o);(this.demuxer=this.demuxer||new J(this.hls,"main")).push(t.payload,h,f,o.videoCodec,e,l.totalduration,c)}}this.fragLoadError=0},h.onFragParsingInitSegment=function(t){var e=this.fragCurrent,r=t.frag;if(e&&"main"===t.id&&r.sn===e.sn&&r.level===e.level&&this.state===gt.PARSING){var i,a,n=t.tracks;if(n.audio&&this.altAudio&&delete n.audio,a=n.audio){var s=this.levels[this.level].audioCodec,o=navigator.userAgent.toLowerCase();s&&this.audioCodecSwap&&(u.b.log("swapping playlist audio codec"),s=-1!==s.indexOf("mp4a.40.5")?"mp4a.40.2":"mp4a.40.5"),this.audioCodecSwitch&&1!==a.metadata.channelCount&&-1===o.indexOf("firefox")&&(s="mp4a.40.5"),-1!==o.indexOf("android")&&"audio/mpeg"!==a.container&&(s="mp4a.40.2",u.b.log("Android: force audio codec to "+s)),a.levelCodec=s,a.id=t.id}for(i in(a=n.video)&&(a.levelCodec=this.levels[this.level].videoCodec,a.id=t.id),this.hls.trigger(d.a.BUFFER_CODECS,n),n){a=n[i],u.b.log("main track:"+i+",container:"+a.container+",codecs[level/parsed]=["+a.levelCodec+"/"+a.codec+"]");var l=a.initSegment;l&&(this.appended=!0,this.pendingBuffering=!0,this.hls.trigger(d.a.BUFFER_APPENDING,{type:i,data:l,parent:"main",content:"initSegment"}))}this.tick()}},h.onFragParsingData=function(t){var e=this,r=this.fragCurrent,i=t.frag;if(r&&"main"===t.id&&i.sn===r.sn&&i.level===r.level&&("audio"!==t.type||!this.altAudio)&&this.state===gt.PARSING){var a=this.levels[this.level],n=r;if(Object(l.a)(t.endPTS)||(t.endPTS=t.startPTS+r.duration,t.endDTS=t.startDTS+r.duration),!0===t.hasAudio&&n.addElementaryStream(p.AUDIO),!0===t.hasVideo&&n.addElementaryStream(p.VIDEO),u.b.log("Parsed "+t.type+",PTS:["+t.startPTS.toFixed(3)+","+t.endPTS.toFixed(3)+"],DTS:["+t.startDTS.toFixed(3)+"/"+t.endDTS.toFixed(3)+"],nb:"+t.nb+",dropped:"+(t.dropped||0)),"video"===t.type)if(n.dropped=t.dropped,n.dropped)if(n.backtracked)u.b.warn("Already backtracked on this fragment, appending with the gap",n.sn);else{var s=a.details;if(!s||n.sn!==s.startSN)return u.b.warn("missing video frame(s), backtracking fragment",n.sn),this.fragmentTracker.removeFragment(n),n.backtracked=!0,this.nextLoadPosition=t.startPTS,this.state=gt.IDLE,this.fragPrevious=n,void this.tick();u.b.warn("missing video frame(s) on first frag, appending with gap",n.sn)}else n.backtracked=!1;var o=et(a.details,n,t.startPTS,t.endPTS,t.startDTS,t.endDTS),c=this.hls;c.trigger(d.a.LEVEL_PTS_UPDATED,{details:a.details,level:this.level,drift:o,type:t.type,start:t.startPTS,end:t.endPTS}),[t.data1,t.data2].forEach(function(r){r&&r.length&&e.state===gt.PARSING&&(e.appended=!0,e.pendingBuffering=!0,c.trigger(d.a.BUFFER_APPENDING,{type:t.type,data:r,parent:"main",content:"data"}))}),this.tick()}},h.onFragParsed=function(t){var e=this.fragCurrent,r=t.frag;e&&"main"===t.id&&r.sn===e.sn&&r.level===e.level&&this.state===gt.PARSING&&(this.stats.tparsed=window.performance.now(),this.state=gt.PARSED,this._checkAppendedParsed())},h.onAudioTrackSwitching=function(t){var e=!!t.url,r=t.id;if(!e){if(this.mediaBuffer!==this.media){u.b.log("switching on main audio, use media.buffered to schedule main fragment loading"),this.mediaBuffer=this.media;var i=this.fragCurrent;i.loader&&(u.b.log("switching to main audio track, cancel main fragment load"),i.loader.abort()),this.fragCurrent=null,this.fragPrevious=null,this.demuxer&&(this.demuxer.destroy(),this.demuxer=null),this.state=gt.IDLE}var a=this.hls;a.trigger(d.a.BUFFER_FLUSHING,{startOffset:0,endOffset:Number.POSITIVE_INFINITY,type:"audio"}),a.trigger(d.a.AUDIO_TRACK_SWITCHED,{id:r}),this.altAudio=!1}},h.onAudioTrackSwitched=function(t){var e=t.id,r=!!this.hls.audioTracks[e].url;if(r){var i=this.videoBuffer;i&&this.mediaBuffer!==i&&(u.b.log("switching on alternate audio, use video.buffered to schedule main fragment loading"),this.mediaBuffer=i)}this.altAudio=r,this.tick()},h.onBufferCreated=function(t){var e,r,i=t.tracks,a=!1;for(var n in i){var s=i[n];"main"===s.id?(r=n,e=s,"video"===n&&(this.videoBuffer=i[n].buffer)):a=!0}a&&e?(u.b.log("alternate track found, use "+r+".buffered to schedule main fragment loading"),this.mediaBuffer=e.buffer):this.mediaBuffer=this.media},h.onBufferAppended=function(t){if("main"===t.parent){var e=this.state;e!==gt.PARSING&&e!==gt.PARSED||(this.pendingBuffering=t.pending>0,this._checkAppendedParsed())}},h._checkAppendedParsed=function(){if(!(this.state!==gt.PARSED||this.appended&&this.pendingBuffering)){var t=this.fragCurrent;if(t){var e=this.mediaBuffer?this.mediaBuffer:this.media;u.b.log("main buffered : "+nt.toString(e.buffered)),this.fragPrevious=t;var r=this.stats;r.tbuffered=window.performance.now(),this.fragLastKbps=Math.round(8*r.total/(r.tbuffered-r.tfirst)),this.hls.trigger(d.a.FRAG_BUFFERED,{stats:r,frag:t,id:"main"}),this.state=gt.IDLE}this.tick()}},h.onError=function(t){var e=t.frag||this.fragCurrent;if(!e||"main"===e.type){var r=!!this.media&&H.isBuffered(this.media,this.media.currentTime)&&H.isBuffered(this.media,this.media.currentTime+.5);switch(t.details){case o.a.FRAG_LOAD_ERROR:case o.a.FRAG_LOAD_TIMEOUT:case o.a.KEY_LOAD_ERROR:case o.a.KEY_LOAD_TIMEOUT:if(!t.fatal)if(this.fragLoadError+1<=this.config.fragLoadingMaxRetry){var i=Math.min(Math.pow(2,this.fragLoadError)*this.config.fragLoadingRetryDelay,this.config.fragLoadingMaxRetryTimeout);u.b.warn("mediaController: frag loading failed, retry in "+i+" ms"),this.retryDate=window.performance.now()+i,this.loadedmetadata||(this.startFragRequested=!1,this.nextLoadPosition=this.startPosition),this.fragLoadError++,this.state=gt.FRAG_LOADING_WAITING_RETRY}else u.b.error("mediaController: "+t.details+" reaches max retry, redispatch as fatal ..."),t.fatal=!0,this.state=gt.ERROR;break;case o.a.LEVEL_LOAD_ERROR:case o.a.LEVEL_LOAD_TIMEOUT:this.state!==gt.ERROR&&(t.fatal?(this.state=gt.ERROR,u.b.warn("streamController: "+t.details+",switch to "+this.state+" state ...")):t.levelRetry||this.state!==gt.WAITING_LEVEL||(this.state=gt.IDLE));break;case o.a.BUFFER_FULL_ERROR:"main"!==t.parent||this.state!==gt.PARSING&&this.state!==gt.PARSED||(r?(this._reduceMaxBufferLength(this.config.maxBufferLength),this.state=gt.IDLE):(u.b.warn("buffer full error also media.currentTime is not buffered, flush everything"),this.fragCurrent=null,this.flushMainBuffer(0,Number.POSITIVE_INFINITY)))}}},h._reduceMaxBufferLength=function(t){var e=this.config;return e.maxMaxBufferLength>=t&&(e.maxMaxBufferLength/=2,u.b.warn("main:reduce max buffer length to "+e.maxMaxBufferLength+"s"),!0)},h._checkBuffer=function(){var t=this.media;if(t&&0!==t.readyState){var e=(this.mediaBuffer?this.mediaBuffer:t).buffered;!this.loadedmetadata&&e.length?(this.loadedmetadata=!0,this._seekToStartPos()):this.immediateSwitch?this.immediateLevelSwitchEnd():this.gapController.poll(this.lastCurrentTime,e)}},h.onFragLoadEmergencyAborted=function(){this.state=gt.IDLE,this.loadedmetadata||(this.startFragRequested=!1,this.nextLoadPosition=this.startPosition),this.tick()},h.onBufferFlushed=function(){var t=this.mediaBuffer?this.mediaBuffer:this.media;t&&this.fragmentTracker.detectEvictedFragments(p.VIDEO,t.buffered),this.state=gt.IDLE,this.fragPrevious=null},h.swapAudioCodec=function(){this.audioCodecSwap=!this.audioCodecSwap},h._seekToStartPos=function(){var t=this.media,e=t.currentTime,r=t.seeking?e:this.startPosition;e!==r&&(u.b.log("target start position not buffered, seek to buffered.start(0) "+r+" from current time "+e+" "),t.currentTime=r)},h._getAudioCodec=function(t){var e=this.config.defaultAudioCodec||t.audioCodec;return this.audioCodecSwap&&(u.b.log("swapping playlist audio codec"),e&&(e=-1!==e.indexOf("mp4a.40.5")?"mp4a.40.2":"mp4a.40.5")),e},a=i,(s=[{key:"state",set:function(t){if(this.state!==t){var e=this.state;this._state=t,u.b.log("main stream:"+e+"->"+t),this.hls.trigger(d.a.STREAM_STATE_TRANSITION,{previousState:e,nextState:t})}},get:function(){return this._state}},{key:"currentLevel",get:function(){var t=this.media;if(t){var e=this.getBufferedFrag(t.currentTime);if(e)return e.level}return-1}},{key:"nextBufferedFrag",get:function(){var t=this.media;return t?this.followingBufferedFrag(this.getBufferedFrag(t.currentTime)):null}},{key:"nextLevel",get:function(){var t=this.nextBufferedFrag;return t?t.level:-1}},{key:"liveSyncPosition",get:function(){return this._liveSyncPosition},set:function(t){this._liveSyncPosition=t}}])&&vt(a.prototype,s),c&&vt(a,c),i}(pt);function yt(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}window.performance;var bt,Et=function(t){var e,r;function i(e){var r;return(r=t.call(this,e,d.a.MANIFEST_LOADED,d.a.LEVEL_LOADED,d.a.AUDIO_TRACK_SWITCHED,d.a.FRAG_LOADED,d.a.ERROR)||this).canload=!1,r.currentLevelIndex=null,r.manualLevelIndex=-1,r.timer=null,bt=/chrome|firefox/.test(navigator.userAgent.toLowerCase()),r}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a,n,s,l=i.prototype;return l.onHandlerDestroying=function(){this.clearTimer(),this.manualLevelIndex=-1},l.clearTimer=function(){null!==this.timer&&(clearTimeout(this.timer),this.timer=null)},l.startLoad=function(){var t=this._levels;this.canload=!0,this.levelRetryCount=0,t&&t.forEach(function(t){t.loadError=0;var e=t.details;e&&e.live&&(t.details=void 0)}),null!==this.timer&&this.loadLevel()},l.stopLoad=function(){this.canload=!1},l.onManifestLoaded=function(t){var e,r=[],i=[],a={},n=null,s=!1,l=!1;if(t.levels.forEach(function(t){var e=t.attrs;t.loadError=0,t.fragmentError=!1,s=s||!!t.videoCodec,l=l||!!t.audioCodec,bt&&t.audioCodec&&-1!==t.audioCodec.indexOf("mp4a.40.34")&&(t.audioCodec=void 0),(n=a[t.bitrate])?n.url.push(t.url):(t.url=[t.url],t.urlId=0,a[t.bitrate]=t,r.push(t)),e&&(e.AUDIO&&(l=!0,Z(n||t,"audio",e.AUDIO)),e.SUBTITLES&&Z(n||t,"text",e.SUBTITLES))}),s&&l&&(r=r.filter(function(t){return!!t.videoCodec})),r=r.filter(function(t){var e=t.audioCodec,r=t.videoCodec;return(!e||R(e,"audio"))&&(!r||R(r,"video"))}),t.audioTracks&&(i=t.audioTracks.filter(function(t){return!t.audioCodec||R(t.audioCodec,"audio")})).forEach(function(t,e){t.id=e}),r.length>0){e=r[0].bitrate,r.sort(function(t,e){return t.bitrate-e.bitrate}),this._levels=r;for(var c=0;c<r.length;c++)if(r[c].bitrate===e){this._firstLevel=c,u.b.log("manifest loaded,"+r.length+" level(s) found, first bitrate:"+e);break}this.hls.trigger(d.a.MANIFEST_PARSED,{levels:r,audioTracks:i,firstLevel:this._firstLevel,stats:t.stats,audio:l,video:s,altAudio:i.some(function(t){return!!t.url})})}else this.hls.trigger(d.a.ERROR,{type:o.b.MEDIA_ERROR,details:o.a.MANIFEST_INCOMPATIBLE_CODECS_ERROR,fatal:!0,url:this.hls.url,reason:"no level with compatible codecs found in manifest"})},l.setLevelInternal=function(t){var e=this._levels,r=this.hls;if(t>=0&&t<e.length){if(this.clearTimer(),this.currentLevelIndex!==t){u.b.log("switching to level "+t),this.currentLevelIndex=t;var i=e[t];i.level=t,r.trigger(d.a.LEVEL_SWITCHING,i)}var a=e[t],n=a.details;if(!n||n.live){var s=a.urlId;r.trigger(d.a.LEVEL_LOADING,{url:a.url[s],level:t,id:s})}}else r.trigger(d.a.ERROR,{type:o.b.OTHER_ERROR,details:o.a.LEVEL_SWITCH_ERROR,level:t,fatal:!1,reason:"invalid level idx"})},l.onError=function(t){if(t.fatal)t.type===o.b.NETWORK_ERROR&&this.clearTimer();else{var e,r=!1,i=!1;switch(t.details){case o.a.FRAG_LOAD_ERROR:case o.a.FRAG_LOAD_TIMEOUT:case o.a.KEY_LOAD_ERROR:case o.a.KEY_LOAD_TIMEOUT:e=t.frag.level,i=!0;break;case o.a.LEVEL_LOAD_ERROR:case o.a.LEVEL_LOAD_TIMEOUT:e=t.context.level,r=!0;break;case o.a.REMUX_ALLOC_ERROR:e=t.level,r=!0}void 0!==e&&this.recoverLevel(t,e,r,i)}},l.recoverLevel=function(t,e,r,i){var a,n,s,o=this,l=this.hls.config,d=t.details,c=this._levels[e];if(c.loadError++,c.fragmentError=i,r){if(!(this.levelRetryCount+1<=l.levelLoadingMaxRetry))return u.b.error("level controller, cannot recover from "+d+" error"),this.currentLevelIndex=null,this.clearTimer(),void(t.fatal=!0);n=Math.min(Math.pow(2,this.levelRetryCount)*l.levelLoadingRetryDelay,l.levelLoadingMaxRetryTimeout),this.timer=setTimeout(function(){return o.loadLevel()},n),t.levelRetry=!0,this.levelRetryCount++,u.b.warn("level controller, "+d+", retry in "+n+" ms, current retry count is "+this.levelRetryCount)}(r||i)&&((a=c.url.length)>1&&c.loadError<a?(c.urlId=(c.urlId+1)%a,c.details=void 0,u.b.warn("level controller, "+d+" for level "+e+": switching to redundant URL-id "+c.urlId)):-1===this.manualLevelIndex?(s=0===e?this._levels.length-1:e-1,u.b.warn("level controller, "+d+": switch to "+s),this.hls.nextAutoLevel=this.currentLevelIndex=s):i&&(u.b.warn("level controller, "+d+": reload a fragment"),this.currentLevelIndex=null))},l.onFragLoaded=function(t){var e=t.frag;if(void 0!==e&&"main"===e.type){var r=this._levels[e.level];void 0!==r&&(r.fragmentError=!1,r.loadError=0,this.levelRetryCount=0)}},l.onLevelLoaded=function(t){var e=this,r=t.level,i=t.details;if(r===this.currentLevelIndex){var a=this._levels[r];if(a.fragmentError||(a.loadError=0,this.levelRetryCount=0),i.live){var n=at(a.details,i,t.stats.trequest);u.b.log("live playlist, reload in "+Math.round(n)+" ms"),this.timer=setTimeout(function(){return e.loadLevel()},n)}else this.clearTimer()}},l.onAudioTrackSwitched=function(t){var e=this.hls.audioTracks[t.id].groupId,r=this.hls.levels[this.currentLevelIndex];if(r&&r.audioGroupIds){for(var i=-1,a=0;a<r.audioGroupIds.length;a++)if(r.audioGroupIds[a]===e){i=a;break}i!==r.urlId&&(r.urlId=i,this.startLoad())}},l.loadLevel=function(){if(u.b.debug("call to loadLevel"),null!==this.currentLevelIndex&&this.canload){var t=this._levels[this.currentLevelIndex];if("object"==typeof t&&t.url.length>0){var e=this.currentLevelIndex,r=t.urlId,i=t.url[r];u.b.log("Attempt loading level index "+e+" with URL-id "+r),this.hls.trigger(d.a.LEVEL_LOADING,{url:i,level:e,id:r})}}},a=i,(n=[{key:"levels",get:function(){return this._levels}},{key:"level",get:function(){return this.currentLevelIndex},set:function(t){var e=this._levels;e&&(t=Math.min(t,e.length-1),this.currentLevelIndex===t&&e[t].details||this.setLevelInternal(t))}},{key:"manualLevel",get:function(){return this.manualLevelIndex},set:function(t){this.manualLevelIndex=t,void 0===this._startLevel&&(this._startLevel=t),-1!==t&&(this.level=t)}},{key:"firstLevel",get:function(){return this._firstLevel},set:function(t){this._firstLevel=t}},{key:"startLevel",get:function(){if(void 0===this._startLevel){var t=this.hls.config.startLevel;return void 0!==t?t:this._firstLevel}return this._startLevel},set:function(t){this._startLevel=t}},{key:"nextLoadLevel",get:function(){return-1!==this.manualLevelIndex?this.manualLevelIndex:this.hls.nextAutoLevel},set:function(t){this.level=t,-1===this.manualLevelIndex&&(this.hls.nextAutoLevel=t)}}])&&yt(a.prototype,n),s&&yt(a,s),i}(h),Tt=r(4);function St(t,e){var r;try{r=new Event("addtrack")}catch(t){(r=document.createEvent("Event")).initEvent("addtrack",!1,!1)}r.track=t,e.dispatchEvent(r)}function _t(t){if(t&&t.cues)for(;t.cues.length>0;)t.removeCue(t.cues[0])}var At=function(t){var e,r;function i(e){var r;return(r=t.call(this,e,d.a.MEDIA_ATTACHED,d.a.MEDIA_DETACHING,d.a.FRAG_PARSING_METADATA)||this).id3Track=void 0,r.media=void 0,r}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a=i.prototype;return a.destroy=function(){h.prototype.destroy.call(this)},a.onMediaAttached=function(t){this.media=t.media,this.media},a.onMediaDetaching=function(){_t(this.id3Track),this.id3Track=void 0,this.media=void 0},a.getID3Track=function(t){for(var e=0;e<t.length;e++){var r=t[e];if("metadata"===r.kind&&"id3"===r.label)return St(r,this.media),r}return this.media.addTextTrack("metadata","id3")},a.onFragParsingMetadata=function(t){var e=t.frag,r=t.samples;this.id3Track||(this.id3Track=this.getID3Track(this.media.textTracks),this.id3Track.mode="hidden");for(var i=window.WebKitDataCue||window.VTTCue||window.TextTrackCue,a=0;a<r.length;a++){var n=Tt.a.getID3Frames(r[a].data);if(n){var s=r[a].pts,o=a<r.length-1?r[a+1].pts:e.endPTS;s===o?o+=1e-4:s>o&&(u.b.warn("detected an id3 sample with endTime < startTime, adjusting endTime to (startTime + 0.25)"),o=s+.25);for(var l=0;l<n.length;l++){var d=n[l];if(!Tt.a.isTimeStampFrame(d)){var c=new i(s,o,"");c.value=d,this.id3Track.addCue(c)}}}}},i}(h);var Rt=function(){function t(t){this.alpha_=void 0,this.estimate_=void 0,this.totalWeight_=void 0,this.alpha_=t?Math.exp(Math.log(.5)/t):0,this.estimate_=0,this.totalWeight_=0}var e=t.prototype;return e.sample=function(t,e){var r=Math.pow(this.alpha_,t);this.estimate_=e*(1-r)+r*this.estimate_,this.totalWeight_+=t},e.getTotalWeight=function(){return this.totalWeight_},e.getEstimate=function(){if(this.alpha_){var t=1-Math.pow(this.alpha_,this.totalWeight_);return this.estimate_/t}return this.estimate_},t}(),Lt=function(){function t(t,e,r,i){this.hls=void 0,this.defaultEstimate_=void 0,this.minWeight_=void 0,this.minDelayMs_=void 0,this.slow_=void 0,this.fast_=void 0,this.hls=t,this.defaultEstimate_=i,this.minWeight_=.001,this.minDelayMs_=50,this.slow_=new Rt(e),this.fast_=new Rt(r)}var e=t.prototype;return e.sample=function(t,e){var r=(t=Math.max(t,this.minDelayMs_))/1e3,i=8*e/r;this.fast_.sample(r,i),this.slow_.sample(r,i)},e.canEstimate=function(){var t=this.fast_;return t&&t.getTotalWeight()>=this.minWeight_},e.getEstimate=function(){return this.canEstimate()?Math.min(this.fast_.getEstimate(),this.slow_.getEstimate()):this.defaultEstimate_},e.destroy=function(){},t}();function Dt(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var wt=window.performance,kt=function(t){var e,r;function i(e){var r;return(r=t.call(this,e,d.a.FRAG_LOADING,d.a.FRAG_LOADED,d.a.FRAG_BUFFERED,d.a.ERROR)||this).lastLoadedFragLevel=0,r._nextAutoLevel=-1,r.hls=e,r.timer=null,r._bwEstimator=null,r.onCheck=r._abandonRulesCheck.bind(function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(r)),r}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a,n,s,c=i.prototype;return c.destroy=function(){this.clearTimer(),h.prototype.destroy.call(this)},c.onFragLoading=function(t){var e=t.frag;if("main"===e.type&&(this.timer||(this.fragCurrent=e,this.timer=setInterval(this.onCheck,100)),!this._bwEstimator)){var r,i,a=this.hls,n=a.config,s=e.level;a.levels[s].details.live?(r=n.abrEwmaFastLive,i=n.abrEwmaSlowLive):(r=n.abrEwmaFastVoD,i=n.abrEwmaSlowVoD),this._bwEstimator=new Lt(a,i,r,n.abrEwmaDefaultEstimate)}},c._abandonRulesCheck=function(){var t=this.hls,e=t.media,r=this.fragCurrent;if(r){var i=r.loader,a=t.minAutoLevel;if(!i||i.stats&&i.stats.aborted)return u.b.warn("frag loader destroy or aborted, disarm abandonRules"),this.clearTimer(),void(this._nextAutoLevel=-1);var n=i.stats;if(e&&n&&(!e.paused&&0!==e.playbackRate||!e.readyState)&&r.autoLevel&&r.level){var s=wt.now()-n.trequest,o=Math.abs(e.playbackRate);if(s>500*r.duration/o){var l=t.levels,c=Math.max(1,n.bw?n.bw/8:1e3*n.loaded/s),h=l[r.level],f=h.realBitrate?Math.max(h.realBitrate,h.bitrate):h.bitrate,g=n.total?n.total:Math.max(n.loaded,Math.round(r.duration*f/8)),p=e.currentTime,v=(g-n.loaded)/c,m=(H.bufferInfo(e,p,t.config.maxBufferHole).end-p)/o;if(m<2*r.duration/o&&v>m){var y;for(y=r.level-1;y>a;y--){var b=l[y].realBitrate?Math.max(l[y].realBitrate,l[y].bitrate):l[y].bitrate;if(r.duration*b/(6.4*c)<m)break}void 0<v&&(u.b.warn("loading too slow, abort fragment loading and switch to level "+y+":fragLoadedDelay["+y+"]<fragLoadedDelay["+(r.level-1)+"];bufferStarvationDelay:"+(void 0).toFixed(1)+"<"+v.toFixed(1)+":"+m.toFixed(1)),t.nextLoadLevel=y,this._bwEstimator.sample(s,n.loaded),i.abort(),this.clearTimer(),t.trigger(d.a.FRAG_LOAD_EMERGENCY_ABORTED,{frag:r,stats:n}))}}}}},c.onFragLoaded=function(t){var e=t.frag;if("main"===e.type&&Object(l.a)(e.sn)){if(this.clearTimer(),this.lastLoadedFragLevel=e.level,this._nextAutoLevel=-1,this.hls.config.abrMaxWithRealBitrate){var r=this.hls.levels[e.level],i=(r.loaded?r.loaded.bytes:0)+t.stats.loaded,a=(r.loaded?r.loaded.duration:0)+t.frag.duration;r.loaded={bytes:i,duration:a},r.realBitrate=Math.round(8*i/a)}if(t.frag.bitrateTest){var n=t.stats;n.tparsed=n.tbuffered=n.tload,this.onFragBuffered(t)}}},c.onFragBuffered=function(t){var e=t.stats,r=t.frag;if(!0!==e.aborted&&"main"===r.type&&Object(l.a)(r.sn)&&(!r.bitrateTest||e.tload===e.tbuffered)){var i=e.tparsed-e.trequest;u.b.log("latency/loading/parsing/append/kbps:"+Math.round(e.tfirst-e.trequest)+"/"+Math.round(e.tload-e.tfirst)+"/"+Math.round(e.tparsed-e.tload)+"/"+Math.round(e.tbuffered-e.tparsed)+"/"+Math.round(8*e.loaded/(e.tbuffered-e.trequest))),this._bwEstimator.sample(i,e.loaded),e.bwEstimate=this._bwEstimator.getEstimate(),r.bitrateTest?this.bitrateTestDelay=i/1e3:this.bitrateTestDelay=0}},c.onError=function(t){switch(t.details){case o.a.FRAG_LOAD_ERROR:case o.a.FRAG_LOAD_TIMEOUT:this.clearTimer()}},c.clearTimer=function(){clearInterval(this.timer),this.timer=null},c._findBestLevel=function(t,e,r,i,a,n,s,o,l){for(var d=a;d>=i;d--){var c=l[d];if(c){var h=c.details,f=h?h.totalduration/h.fragments.length:e,g=!!h&&h.live,p=void 0;p=d<=t?s*r:o*r;var v=l[d].realBitrate?Math.max(l[d].realBitrate,l[d].bitrate):l[d].bitrate,m=v*f/p;if(u.b.trace("level/adjustedbw/bitrate/avgDuration/maxFetchDuration/fetchDuration: "+d+"/"+Math.round(p)+"/"+v+"/"+f+"/"+n+"/"+m),p>v&&(!m||g&&!this.bitrateTestDelay||m<n))return d}}return-1},a=i,(n=[{key:"nextAutoLevel",get:function(){var t=this._nextAutoLevel,e=this._bwEstimator;if(!(-1===t||e&&e.canEstimate()))return t;var r=this._nextABRAutoLevel;return-1!==t&&(r=Math.min(t,r)),r},set:function(t){this._nextAutoLevel=t}},{key:"_nextABRAutoLevel",get:function(){var t=this.hls,e=t.maxAutoLevel,r=t.levels,i=t.config,a=t.minAutoLevel,n=t.media,s=this.lastLoadedFragLevel,o=this.fragCurrent?this.fragCurrent.duration:0,l=n?n.currentTime:0,d=n&&0!==n.playbackRate?Math.abs(n.playbackRate):1,c=this._bwEstimator?this._bwEstimator.getEstimate():i.abrEwmaDefaultEstimate,h=(H.bufferInfo(n,l,i.maxBufferHole).end-l)/d,f=this._findBestLevel(s,o,c,a,e,h,i.abrBandWidthFactor,i.abrBandWidthUpFactor,r);if(f>=0)return f;u.b.trace("rebuffering expected to happen, lets try to find a quality level minimizing the rebuffering");var g=o?Math.min(o,i.maxStarvationDelay):i.maxStarvationDelay,p=i.abrBandWidthFactor,v=i.abrBandWidthUpFactor;if(0===h){var m=this.bitrateTestDelay;if(m)g=(o?Math.min(o,i.maxLoadingDelay):i.maxLoadingDelay)-m,u.b.trace("bitrate test took "+Math.round(1e3*m)+"ms, set first fragment max fetchDuration to "+Math.round(1e3*g)+" ms"),p=v=1}return f=this._findBestLevel(s,o,c,a,e,h+g,p,v,r),Math.max(f,0)}}])&&Dt(a.prototype,n),s&&Dt(a,s),i}(h);var It=q(),Ot=function(t){var e,r;function i(e){var r;return(r=t.call(this,e,d.a.MEDIA_ATTACHING,d.a.MEDIA_DETACHING,d.a.MANIFEST_PARSED,d.a.BUFFER_RESET,d.a.BUFFER_APPENDING,d.a.BUFFER_CODECS,d.a.BUFFER_EOS,d.a.BUFFER_FLUSHING,d.a.LEVEL_PTS_UPDATED,d.a.LEVEL_UPDATED)||this)._msDuration=null,r._levelDuration=null,r._levelTargetDuration=10,r._live=null,r._objectUrl=null,r._needsFlush=!1,r._needsEos=!1,r.config=void 0,r.audioTimestampOffset=void 0,r.bufferCodecEventsExpected=0,r._bufferCodecEventsTotal=0,r.media=null,r.mediaSource=null,r.segments=[],r.parent=void 0,r.appending=!1,r.appended=0,r.appendError=0,r.flushBufferCounter=0,r.tracks={},r.pendingTracks={},r.sourceBuffer={},r.flushRange=[],r._onMediaSourceOpen=function(){u.b.log("media source opened"),r.hls.trigger(d.a.MEDIA_ATTACHED,{media:r.media});var t=r.mediaSource;t&&t.removeEventListener("sourceopen",r._onMediaSourceOpen),r.checkPendingTracks()},r._onMediaSourceClose=function(){u.b.log("media source closed")},r._onMediaSourceEnded=function(){u.b.log("media source ended")},r._onSBUpdateEnd=function(){if(r.audioTimestampOffset&&r.sourceBuffer.audio){var t=r.sourceBuffer.audio;u.b.warn("change mpeg audio timestamp offset from "+t.timestampOffset+" to "+r.audioTimestampOffset),t.timestampOffset=r.audioTimestampOffset,delete r.audioTimestampOffset}r._needsFlush&&r.doFlush(),r._needsEos&&r.checkEos(),r.appending=!1;var e=r.parent,i=r.segments.reduce(function(t,r){return r.parent===e?t+1:t},0),a={},n=r.sourceBuffer;for(var s in n){var o=n[s];if(!o)throw Error("handling source buffer update end error: source buffer for "+s+" uninitilized and unable to update buffered TimeRanges.");a[s]=o.buffered}r.hls.trigger(d.a.BUFFER_APPENDED,{parent:e,pending:i,timeRanges:a}),r._needsFlush||r.doAppending(),r.updateMediaElementDuration(),0===i&&r.flushLiveBackBuffer()},r._onSBUpdateError=function(t){u.b.error("sourceBuffer error:",t),r.hls.trigger(d.a.ERROR,{type:o.b.MEDIA_ERROR,details:o.a.BUFFER_APPENDING_ERROR,fatal:!1})},r.config=e.config,r}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a=i.prototype;return a.destroy=function(){h.prototype.destroy.call(this)},a.onLevelPtsUpdated=function(t){var e=t.type,r=this.tracks.audio;if("audio"===e&&r&&"audio/mpeg"===r.container){var i=this.sourceBuffer.audio;if(!i)throw Error("Level PTS Updated and source buffer for audio uninitalized");if(Math.abs(i.timestampOffset-t.start)>.1){var a=i.updating;try{i.abort()}catch(t){u.b.warn("can not abort audio buffer: "+t)}a?this.audioTimestampOffset=t.start:(u.b.warn("change mpeg audio timestamp offset from "+i.timestampOffset+" to "+t.start),i.timestampOffset=t.start)}}},a.onManifestParsed=function(t){this.bufferCodecEventsExpected=this._bufferCodecEventsTotal=t.altAudio?2:1,u.b.log(this.bufferCodecEventsExpected+" bufferCodec event(s) expected")},a.onMediaAttaching=function(t){var e=this.media=t.media;if(e&&It){var r=this.mediaSource=new It;r.addEventListener("sourceopen",this._onMediaSourceOpen),r.addEventListener("sourceended",this._onMediaSourceEnded),r.addEventListener("sourceclose",this._onMediaSourceClose),e.src=window.URL.createObjectURL(r),this._objectUrl=e.src}},a.onMediaDetaching=function(){u.b.log("media source detaching");var t=this.mediaSource;if(t){if("open"===t.readyState)try{t.endOfStream()}catch(t){u.b.warn("onMediaDetaching:"+t.message+" while calling endOfStream")}t.removeEventListener("sourceopen",this._onMediaSourceOpen),t.removeEventListener("sourceended",this._onMediaSourceEnded),t.removeEventListener("sourceclose",this._onMediaSourceClose),this.media&&(this._objectUrl&&window.URL.revokeObjectURL(this._objectUrl),this.media.src===this._objectUrl?(this.media.removeAttribute("src"),this.media.load()):u.b.warn("media.src was changed by a third party - skip cleanup")),this.mediaSource=null,this.media=null,this._objectUrl=null,this.bufferCodecEventsExpected=this._bufferCodecEventsTotal,this.pendingTracks={},this.tracks={},this.sourceBuffer={},this.flushRange=[],this.segments=[],this.appended=0}this.hls.trigger(d.a.MEDIA_DETACHED)},a.checkPendingTracks=function(){var t=this.bufferCodecEventsExpected,e=this.pendingTracks,r=Object.keys(e).length;(r&&!t||2===r)&&(this.createSourceBuffers(e),this.pendingTracks={},this.doAppending())},a.onBufferReset=function(){var t=this.sourceBuffer;for(var e in t){var r=t[e];try{r&&(this.mediaSource&&this.mediaSource.removeSourceBuffer(r),r.removeEventListener("updateend",this._onSBUpdateEnd),r.removeEventListener("error",this._onSBUpdateError))}catch(t){}}this.sourceBuffer={},this.flushRange=[],this.segments=[],this.appended=0},a.onBufferCodecs=function(t){var e=this;Object.keys(this.sourceBuffer).length||(Object.keys(t).forEach(function(r){e.pendingTracks[r]=t[r]}),this.bufferCodecEventsExpected=Math.max(this.bufferCodecEventsExpected-1,0),this.mediaSource&&"open"===this.mediaSource.readyState&&this.checkPendingTracks())},a.createSourceBuffers=function(t){var e=this.sourceBuffer,r=this.mediaSource;if(!r)throw Error("createSourceBuffers called when mediaSource was null");for(var i in t)if(!e[i]){var a=t[i];if(!a)throw Error("source buffer exists for track "+i+", however track does not");var n=a.levelCodec||a.codec,s=a.container+";codecs="+n;u.b.log("creating sourceBuffer("+s+")");try{var l=e[i]=r.addSourceBuffer(s);l.addEventListener("updateend",this._onSBUpdateEnd),l.addEventListener("error",this._onSBUpdateError),this.tracks[i]={buffer:l,codec:n,id:a.id,container:a.container,levelCodec:a.levelCodec}}catch(t){u.b.error("error while trying to add sourceBuffer:"+t.message),this.hls.trigger(d.a.ERROR,{type:o.b.MEDIA_ERROR,details:o.a.BUFFER_ADD_CODEC_ERROR,fatal:!1,err:t,mimeType:s})}}this.hls.trigger(d.a.BUFFER_CREATED,{tracks:this.tracks})},a.onBufferAppending=function(t){this._needsFlush||(this.segments?this.segments.push(t):this.segments=[t],this.doAppending())},a.onBufferEos=function(t){for(var e in this.sourceBuffer)if(!t.type||t.type===e){var r=this.sourceBuffer[e];r&&!r.ended&&(r.ended=!0,u.b.log(e+" sourceBuffer now EOS"))}this.checkEos()},a.checkEos=function(){var t=this.sourceBuffer,e=this.mediaSource;if(e&&"open"===e.readyState){for(var r in t){var i=t[r];if(i){if(!i.ended)return;if(i.updating)return void(this._needsEos=!0)}}u.b.log("all media data are available, signal endOfStream() to MediaSource and stop loading fragment");try{e.endOfStream()}catch(t){u.b.warn("exception while calling mediaSource.endOfStream()")}this._needsEos=!1}else this._needsEos=!1},a.onBufferFlushing=function(t){t.type?this.flushRange.push({start:t.startOffset,end:t.endOffset,type:t.type}):(this.flushRange.push({start:t.startOffset,end:t.endOffset,type:"video"}),this.flushRange.push({start:t.startOffset,end:t.endOffset,type:"audio"})),this.flushBufferCounter=0,this.doFlush()},a.flushLiveBackBuffer=function(){if(this._live){var t=this.config.liveBackBufferLength;if(isFinite(t)&&!(t<0))if(this.media)for(var e=this.media.currentTime,r=this.sourceBuffer,i=Object.keys(r),a=e-Math.max(t,this._levelTargetDuration),n=i.length-1;n>=0;n--){var s=i[n],o=r[s];if(o){var l=o.buffered;l.length>0&&a>l.start(0)&&this.removeBufferRange(s,o,0,a)}}else u.b.error("flushLiveBackBuffer called without attaching media")}},a.onLevelUpdated=function(t){var e=t.details;e.fragments.length>0&&(this._levelDuration=e.totalduration+e.fragments[0].start,this._levelTargetDuration=e.averagetargetduration||e.targetduration||10,this._live=e.live,this.updateMediaElementDuration())},a.updateMediaElementDuration=function(){var t,e=this.config;if(null!==this._levelDuration&&this.media&&this.mediaSource&&this.sourceBuffer&&0!==this.media.readyState&&"open"===this.mediaSource.readyState){for(var r in this.sourceBuffer){var i=this.sourceBuffer[r];if(i&&!0===i.updating)return}t=this.media.duration,null===this._msDuration&&(this._msDuration=this.mediaSource.duration),!0===this._live&&!0===e.liveDurationInfinity?(u.b.log("Media Source duration is set to Infinity"),this._msDuration=this.mediaSource.duration=1/0):(this._levelDuration>this._msDuration&&this._levelDuration>t||!Object(l.a)(t))&&(u.b.log("Updating Media Source duration to "+this._levelDuration.toFixed(3)),this._msDuration=this.mediaSource.duration=this._levelDuration)}},a.doFlush=function(){for(;this.flushRange.length;){var t=this.flushRange[0];if(!this.flushBuffer(t.start,t.end,t.type))return void(this._needsFlush=!0);this.flushRange.shift(),this.flushBufferCounter=0}if(0===this.flushRange.length){this._needsFlush=!1;var e=0,r=this.sourceBuffer;try{for(var i in r){var a=r[i];a&&(e+=a.buffered.length)}}catch(t){u.b.error("error while accessing sourceBuffer.buffered")}this.appended=e,this.hls.trigger(d.a.BUFFER_FLUSHED)}},a.doAppending=function(){var t=this.config,e=this.hls,r=this.segments,i=this.sourceBuffer;if(Object.keys(i).length){if(!this.media||this.media.error)return this.segments=[],void u.b.error("trying to append although a media error occured, flush segment and abort");if(!this.appending){var a=r.shift();if(a)try{var n=i[a.type];if(!n)return void this._onSBUpdateEnd();if(n.updating)return void r.unshift(a);n.ended=!1,this.parent=a.parent,n.appendBuffer(a.data),this.appendError=0,this.appended++,this.appending=!0}catch(i){u.b.error("error while trying to append buffer:"+i.message),r.unshift(a);var s={type:o.b.MEDIA_ERROR,parent:a.parent,details:"",fatal:!1};22===i.code?(this.segments=[],s.details=o.a.BUFFER_FULL_ERROR):(this.appendError++,s.details=o.a.BUFFER_APPEND_ERROR,this.appendError>t.appendErrorMaxRetry&&(u.b.log("fail "+t.appendErrorMaxRetry+" times to append segment in sourceBuffer"),this.segments=[],s.fatal=!0)),e.trigger(d.a.ERROR,s)}}}},a.flushBuffer=function(t,e,r){var i=this.sourceBuffer;if(!Object.keys(i).length)return!0;var a="null";if(this.media&&(a=this.media.currentTime.toFixed(3)),u.b.log("flushBuffer,pos/start/end: "+a+"/"+t+"/"+e),this.flushBufferCounter>=this.appended)return u.b.warn("abort flushing too many retries"),!0;var n=i[r];if(n){if(n.ended=!1,n.updating)return u.b.warn("cannot flush, sb updating in progress"),!1;if(this.removeBufferRange(r,n,t,e))return this.flushBufferCounter++,!1}return u.b.log("buffer flushed"),!0},a.removeBufferRange=function(t,e,r,i){try{for(var a=0;a<e.buffered.length;a++){var n=e.buffered.start(a),s=e.buffered.end(a),o=Math.max(n,r),l=Math.min(s,i);if(Math.min(l,s)-o>.5){var d="null";return this.media&&(d=this.media.currentTime.toString()),u.b.log("sb remove "+t+" ["+o+","+l+"], of ["+n+","+s+"], pos:"+d),e.remove(o,l),!0}}}catch(t){u.b.warn("removeBufferRange failed",t)}return!1},i}(h);function Ct(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var Pt=function(t){var e,r;function i(e){var r;return(r=t.call(this,e,d.a.FPS_DROP_LEVEL_CAPPING,d.a.MEDIA_ATTACHING,d.a.MANIFEST_PARSED,d.a.BUFFER_CODECS,d.a.MEDIA_DETACHING)||this).autoLevelCapping=Number.POSITIVE_INFINITY,r.firstLevel=null,r.levels=[],r.media=null,r.restrictedLevels=[],r.timer=null,r}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a,n,s,o=i.prototype;return o.destroy=function(){this.hls.config.capLevelToPlayerSize&&(this.media=null,this.stopCapping())},o.onFpsDropLevelCapping=function(t){i.isLevelAllowed(t.droppedLevel,this.restrictedLevels)&&this.restrictedLevels.push(t.droppedLevel)},o.onMediaAttaching=function(t){this.media=t.media instanceof window.HTMLVideoElement?t.media:null},o.onManifestParsed=function(t){var e=this.hls;this.restrictedLevels=[],this.levels=t.levels,this.firstLevel=t.firstLevel,e.config.capLevelToPlayerSize&&t.video&&this.startCapping()},o.onBufferCodecs=function(t){this.hls.config.capLevelToPlayerSize&&t.video&&this.startCapping()},o.onLevelsUpdated=function(t){this.levels=t.levels},o.onMediaDetaching=function(){this.stopCapping()},o.detectPlayerSize=function(){if(this.media){var t=this.levels?this.levels.length:0;if(t){var e=this.hls;e.autoLevelCapping=this.getMaxLevel(t-1),e.autoLevelCapping>this.autoLevelCapping&&e.streamController.nextLevelSwitch(),this.autoLevelCapping=e.autoLevelCapping}}},o.getMaxLevel=function(t){var e=this;if(!this.levels)return-1;var r=this.levels.filter(function(r,a){return i.isLevelAllowed(a,e.restrictedLevels)&&a<=t});return i.getMaxLevelByMediaSize(r,this.mediaWidth,this.mediaHeight)},o.startCapping=function(){this.timer||(this.autoLevelCapping=Number.POSITIVE_INFINITY,this.hls.firstLevel=this.getMaxLevel(this.firstLevel),clearInterval(this.timer),this.timer=setInterval(this.detectPlayerSize.bind(this),1e3),this.detectPlayerSize())},o.stopCapping=function(){this.restrictedLevels=[],this.firstLevel=null,this.autoLevelCapping=Number.POSITIVE_INFINITY,this.timer&&(this.timer=clearInterval(this.timer),this.timer=null)},i.isLevelAllowed=function(t,e){return void 0===e&&(e=[]),-1===e.indexOf(t)},i.getMaxLevelByMediaSize=function(t,e,r){if(!t||t&&!t.length)return-1;for(var i,a,n=t.length-1,s=0;s<t.length;s+=1){var o=t[s];if((o.width>=e||o.height>=r)&&(i=o,!(a=t[s+1])||i.width!==a.width||i.height!==a.height)){n=s;break}}return n},a=i,s=[{key:"contentScaleFactor",get:function(){var t=1;try{t=window.devicePixelRatio}catch(t){}return t}}],(n=[{key:"mediaWidth",get:function(){var t,e=this.media;return e&&(t=e.width||e.clientWidth||e.offsetWidth,t*=i.contentScaleFactor),t}},{key:"mediaHeight",get:function(){var t,e=this.media;return e&&(t=e.height||e.clientHeight||e.offsetHeight,t*=i.contentScaleFactor),t}}])&&Ct(a.prototype,n),s&&Ct(a,s),i}(h);var xt=window.performance,Ft=function(t){var e,r;function i(e){return t.call(this,e,d.a.MEDIA_ATTACHING)||this}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a=i.prototype;return a.destroy=function(){this.timer&&clearInterval(this.timer),this.isVideoPlaybackQualityAvailable=!1},a.onMediaAttaching=function(t){var e=this.hls.config;e.capLevelOnFPSDrop&&("function"==typeof(this.video=t.media instanceof window.HTMLVideoElement?t.media:null).getVideoPlaybackQuality&&(this.isVideoPlaybackQualityAvailable=!0),clearInterval(this.timer),this.timer=setInterval(this.checkFPSInterval.bind(this),e.fpsDroppedMonitoringPeriod))},a.checkFPS=function(t,e,r){var i=xt.now();if(e){if(this.lastTime){var a=i-this.lastTime,n=r-this.lastDroppedFrames,s=e-this.lastDecodedFrames,o=1e3*n/a,l=this.hls;if(l.trigger(d.a.FPS_DROP,{currentDropped:n,currentDecoded:s,totalDroppedFrames:r}),o>0&&n>l.config.fpsDroppedMonitoringThreshold*s){var c=l.currentLevel;u.b.warn("drop FPS ratio greater than max allowed value for currentLevel: "+c),c>0&&(-1===l.autoLevelCapping||l.autoLevelCapping>=c)&&(c-=1,l.trigger(d.a.FPS_DROP_LEVEL_CAPPING,{level:c,droppedLevel:l.currentLevel}),l.autoLevelCapping=c,l.streamController.nextLevelSwitch())}}this.lastTime=i,this.lastDroppedFrames=r,this.lastDecodedFrames=e}},a.checkFPSInterval=function(){var t=this.video;if(t)if(this.isVideoPlaybackQualityAvailable){var e=t.getVideoPlaybackQuality();this.checkFPS(t,e.totalVideoFrames,e.droppedVideoFrames)}else this.checkFPS(t,t.webkitDecodedFrameCount,t.webkitDroppedFrameCount)},i}(h),Mt=window,Nt=Mt.performance,Ut=Mt.XMLHttpRequest,Bt=function(){function t(t){t&&t.xhrSetup&&(this.xhrSetup=t.xhrSetup)}var e=t.prototype;return e.destroy=function(){this.abort(),this.loader=null},e.abort=function(){var t=this.loader;t&&4!==t.readyState&&(this.stats.aborted=!0,t.abort()),window.clearTimeout(this.requestTimeout),this.requestTimeout=null,window.clearTimeout(this.retryTimeout),this.retryTimeout=null},e.load=function(t,e,r){this.context=t,this.config=e,this.callbacks=r,this.stats={trequest:Nt.now(),retry:0},this.retryDelay=e.retryDelay,this.loadInternal()},e.loadInternal=function(){var t,e=this.context;t=this.loader=new Ut;var r=this.stats;r.tfirst=0,r.loaded=0;var i=this.xhrSetup;try{if(i)try{i(t,e.url)}catch(r){t.open("GET",e.url,!0),i(t,e.url)}t.readyState||t.open("GET",e.url,!0)}catch(r){return void this.callbacks.onError({code:t.status,text:r.message},e,t)}e.rangeEnd&&t.setRequestHeader("Range","bytes="+e.rangeStart+"-"+(e.rangeEnd-1)),t.onreadystatechange=this.readystatechange.bind(this),t.onprogress=this.loadprogress.bind(this),t.responseType=e.responseType,this.requestTimeout=window.setTimeout(this.loadtimeout.bind(this),this.config.timeout),t.send()},e.readystatechange=function(t){var e=t.currentTarget,r=e.readyState,i=this.stats,a=this.context,n=this.config;if(!i.aborted&&r>=2)if(window.clearTimeout(this.requestTimeout),0===i.tfirst&&(i.tfirst=Math.max(Nt.now(),i.trequest)),4===r){var s=e.status;if(s>=200&&s<300){var o,l;i.tload=Math.max(i.tfirst,Nt.now()),l="arraybuffer"===a.responseType?(o=e.response).byteLength:(o=e.responseText).length,i.loaded=i.total=l;var d={url:e.responseURL,data:o};this.callbacks.onSuccess(d,i,a,e)}else i.retry>=n.maxRetry||s>=400&&s<499?(u.b.error(s+" while loading "+a.url),this.callbacks.onError({code:s,text:e.statusText},a,e)):(u.b.warn(s+" while loading "+a.url+", retrying in "+this.retryDelay+"..."),this.destroy(),this.retryTimeout=window.setTimeout(this.loadInternal.bind(this),this.retryDelay),this.retryDelay=Math.min(2*this.retryDelay,n.maxRetryDelay),i.retry++)}else this.requestTimeout=window.setTimeout(this.loadtimeout.bind(this),n.timeout)},e.loadtimeout=function(){u.b.warn("timeout while loading "+this.context.url),this.callbacks.onTimeout(this.stats,this.context,null)},e.loadprogress=function(t){var e=t.currentTarget,r=this.stats;r.loaded=t.loaded,t.lengthComputable&&(r.total=t.total);var i=this.callbacks.onProgress;i&&i(r,this.context,null,e)},t}();function Gt(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var Kt=function(t){var e,r;function i(e){var r;return(r=t.call(this,e,d.a.MANIFEST_LOADING,d.a.MANIFEST_PARSED,d.a.AUDIO_TRACK_LOADED,d.a.AUDIO_TRACK_SWITCHED,d.a.LEVEL_LOADED,d.a.ERROR)||this)._trackId=-1,r._selectDefaultTrack=!0,r.tracks=[],r.trackIdBlacklist=Object.create(null),r.audioGroupId=null,r}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a,n,s,l=i.prototype;return l.onManifestLoading=function(){this.tracks=[],this._trackId=-1,this._selectDefaultTrack=!0},l.onManifestParsed=function(t){var e=this.tracks=t.audioTracks||[];this.hls.trigger(d.a.AUDIO_TRACKS_UPDATED,{audioTracks:e})},l.onAudioTrackLoaded=function(t){if(t.id>=this.tracks.length)u.b.warn("Invalid audio track id:",t.id);else{if(u.b.log("audioTrack "+t.id+" loaded"),this.tracks[t.id].details=t.details,t.details.live&&!this.hasInterval()){var e=1e3*t.details.targetduration;this.setInterval(e)}!t.details.live&&this.hasInterval()&&this.clearInterval()}},l.onAudioTrackSwitched=function(t){var e=this.tracks[t.id].groupId;e&&this.audioGroupId!==e&&(this.audioGroupId=e)},l.onLevelLoaded=function(t){var e=this.hls.levels[t.level];if(e.audioGroupIds){var r=e.audioGroupIds[e.urlId];this.audioGroupId!==r&&(this.audioGroupId=r,this._selectInitialAudioTrack())}},l.onError=function(t){t.type===o.b.NETWORK_ERROR&&(t.fatal&&this.clearInterval(),t.details===o.a.AUDIO_TRACK_LOAD_ERROR&&(u.b.warn("Network failure on audio-track id:",t.context.id),this._handleLoadError()))},l._setAudioTrack=function(t){if(this._trackId===t&&this.tracks[this._trackId].details)u.b.debug("Same id as current audio-track passed, and track details available -> no-op");else if(t<0||t>=this.tracks.length)u.b.warn("Invalid id passed to audio-track controller");else{var e=this.tracks[t];u.b.log("Now switching to audio-track index "+t),this.clearInterval(),this._trackId=t;var r=e.url,i=e.type,a=e.id;this.hls.trigger(d.a.AUDIO_TRACK_SWITCHING,{id:a,type:i,url:r}),this._loadTrackDetailsIfNeeded(e)}},l.doTick=function(){this._updateTrack(this._trackId)},l._selectInitialAudioTrack=function(){var t=this,e=this.tracks;if(e.length){var r=this.tracks[this._trackId],i=null;if(r&&(i=r.name),this._selectDefaultTrack){var a=e.filter(function(t){return t.default});a.length?e=a:u.b.warn("No default audio tracks defined")}var n=!1,s=function(){e.forEach(function(e){n||t.audioGroupId&&e.groupId!==t.audioGroupId||i&&i!==e.name||(t._setAudioTrack(e.id),n=!0)})};s(),n||(i=null,s()),n||(u.b.error("No track found for running audio group-ID: "+this.audioGroupId),this.hls.trigger(d.a.ERROR,{type:o.b.MEDIA_ERROR,details:o.a.AUDIO_TRACK_LOAD_ERROR,fatal:!0}))}},l._needsTrackLoading=function(t){var e=t.details,r=t.url;return!(e&&!e.live)&&!!r},l._loadTrackDetailsIfNeeded=function(t){if(this._needsTrackLoading(t)){var e=t.url,r=t.id;u.b.log("loading audio-track playlist for id: "+r),this.hls.trigger(d.a.AUDIO_TRACK_LOADING,{url:e,id:r})}},l._updateTrack=function(t){if(!(t<0||t>=this.tracks.length)){this.clearInterval(),this._trackId=t,u.b.log("trying to update audio-track "+t);var e=this.tracks[t];this._loadTrackDetailsIfNeeded(e)}},l._handleLoadError=function(){this.trackIdBlacklist[this._trackId]=!0;var t=this._trackId,e=this.tracks[t],r=e.name,i=e.language,a=e.groupId;u.b.warn("Loading failed on audio track id: "+t+", group-id: "+a+', name/language: "'+r+'" / "'+i+'"');for(var n=t,s=0;s<this.tracks.length;s++){if(!this.trackIdBlacklist[s])if(this.tracks[s].name===r){n=s;break}}n!==t?(u.b.log("Attempting audio-track fallback id:",n,"group-id:",this.tracks[n].groupId),this._setAudioTrack(n)):u.b.warn('No fallback audio-track found for name/language: "'+r+'" / "'+i+'"')},a=i,(n=[{key:"audioTracks",get:function(){return this.tracks}},{key:"audioTrack",get:function(){return this._trackId},set:function(t){this._setAudioTrack(t),this._selectDefaultTrack=!1}}])&&Gt(a.prototype,n),s&&Gt(a,s),i}(ft);function jt(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var Ht=window.performance,Vt=function(t){var e,r;function i(e,r){var i;return(i=t.call(this,e,d.a.MEDIA_ATTACHED,d.a.MEDIA_DETACHING,d.a.AUDIO_TRACKS_UPDATED,d.a.AUDIO_TRACK_SWITCHING,d.a.AUDIO_TRACK_LOADED,d.a.KEY_LOADED,d.a.FRAG_LOADED,d.a.FRAG_PARSING_INIT_SEGMENT,d.a.FRAG_PARSING_DATA,d.a.FRAG_PARSED,d.a.ERROR,d.a.BUFFER_RESET,d.a.BUFFER_CREATED,d.a.BUFFER_APPENDED,d.a.BUFFER_FLUSHED,d.a.INIT_PTS_FOUND)||this).fragmentTracker=r,i.config=e.config,i.audioCodecSwap=!1,i._state=gt.STOPPED,i.initPTS=[],i.waitingFragment=null,i.videoTrackCC=null,i}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a,n,s,c=i.prototype;return c.onInitPtsFound=function(t){var e=t.id,r=t.frag.cc,i=t.initPTS;"main"===e&&(this.initPTS[r]=i,this.videoTrackCC=r,u.b.log("InitPTS for cc: "+r+" found from video track: "+i),this.state===gt.WAITING_INIT_PTS&&this.tick())},c.startLoad=function(t){if(this.tracks){var e=this.lastCurrentTime;this.stopLoad(),this.setInterval(100),this.fragLoadError=0,e>0&&-1===t?(u.b.log("audio:override startPosition with lastCurrentTime @"+e.toFixed(3)),this.state=gt.IDLE):(this.lastCurrentTime=this.startPosition?this.startPosition:t,this.state=gt.STARTING),this.nextLoadPosition=this.startPosition=this.lastCurrentTime,this.tick()}else this.startPosition=t,this.state=gt.STOPPED},c.doTick=function(){var t,e,r,i=this.hls,a=i.config;switch(this.state){case gt.ERROR:case gt.PAUSED:case gt.BUFFER_FLUSHING:break;case gt.STARTING:this.state=gt.WAITING_TRACK,this.loadedmetadata=!1;break;case gt.IDLE:var n=this.tracks;if(!n)break;if(!this.media&&(this.startFragRequested||!a.startFragPrefetch))break;if(this.loadedmetadata)t=this.media.currentTime;else if(void 0===(t=this.nextLoadPosition))break;var s=this.mediaBuffer?this.mediaBuffer:this.media,o=this.videoBuffer?this.videoBuffer:this.media,c=H.bufferInfo(s,t,a.maxBufferHole),h=H.bufferInfo(o,t,a.maxBufferHole),f=c.len,g=c.end,p=this.fragPrevious,v=Math.min(a.maxBufferLength,a.maxMaxBufferLength),m=Math.max(v,h.len),y=this.audioSwitch,b=this.trackId;if((f<m||y)&&b<n.length){if(void 0===(r=n[b].details)){this.state=gt.WAITING_TRACK;break}if(!y&&this._streamEnded(c,r))return this.hls.trigger(d.a.BUFFER_EOS,{type:"audio"}),void(this.state=gt.ENDED);var E,T=r.fragments,S=T.length,_=T[0].start,A=T[S-1].start+T[S-1].duration;if(y)if(r.live&&!r.PTSKnown)u.b.log("switching audiotrack, live stream, unknown PTS,load first fragment"),g=0;else if(g=t,r.PTSKnown&&t<_){if(!(c.end>_||c.nextStart))return;u.b.log("alt audio track ahead of main track, seek to start of alt audio track"),this.media.currentTime=_+.05}if(r.initSegment&&!r.initSegment.data)E=r.initSegment;else if(g<=_){if(E=T[0],null!==this.videoTrackCC&&E.cc!==this.videoTrackCC&&(E=function(t,e){return j.search(t,function(t){return t.cc<e?1:t.cc>e?-1:0})}(T,this.videoTrackCC)),r.live&&E.loadIdx&&E.loadIdx===this.fragLoadIdx){var R=c.nextStart?c.nextStart:_;return u.b.log("no alt audio available @currentTime:"+this.media.currentTime+", seeking @"+(R+.05)),void(this.media.currentTime=R+.05)}}else{var L,D=a.maxFragLookUpTolerance,w=p?T[p.sn-T[0].sn+1]:void 0,k=function(t){var e=Math.min(D,t.duration);return t.start+t.duration-e<=g?1:t.start-e>g&&t.start?-1:0};g<A?(g>A-D&&(D=0),L=w&&!k(w)?w:j.search(T,k)):L=T[S-1],L&&(E=L,_=L.start,p&&E.level===p.level&&E.sn===p.sn&&(E.sn<r.endSN?(E=T[E.sn+1-r.startSN],u.b.log("SN just loaded, load next one: "+E.sn)):E=null))}E&&(E.encrypted?(u.b.log("Loading key for "+E.sn+" of ["+r.startSN+" ,"+r.endSN+"],track "+b),this.state=gt.KEY_LOADING,i.trigger(d.a.KEY_LOADING,{frag:E})):(u.b.log("Loading "+E.sn+", cc: "+E.cc+" of ["+r.startSN+" ,"+r.endSN+"],track "+b+", currentTime:"+t+",bufferEnd:"+g.toFixed(3)),this.fragCurrent=E,(y||this.fragmentTracker.getState(E)===N)&&("initSegment"!==E.sn&&(this.startFragRequested=!0),Object(l.a)(E.sn)&&(this.nextLoadPosition=E.start+E.duration),i.trigger(d.a.FRAG_LOADING,{frag:E}),this.state=gt.FRAG_LOADING)))}break;case gt.WAITING_TRACK:(e=this.tracks[this.trackId])&&e.details&&(this.state=gt.IDLE);break;case gt.FRAG_LOADING_WAITING_RETRY:var I=Ht.now(),O=this.retryDate,C=(s=this.media)&&s.seeking;(!O||I>=O||C)&&(u.b.log("audioStreamController: retryDate reached, switch back to IDLE state"),this.state=gt.IDLE);break;case gt.WAITING_INIT_PTS:var P=this.videoTrackCC;if(void 0===this.initPTS[P])break;var x=this.waitingFragment;if(x){var F=x.frag.cc;P!==F?(e=this.tracks[this.trackId]).details&&e.details.live&&(u.b.warn("Waiting fragment CC ("+F+") does not match video track CC ("+P+")"),this.waitingFragment=null,this.state=gt.IDLE):(this.state=gt.FRAG_LOADING,this.onFragLoaded(this.waitingFragment),this.waitingFragment=null)}else this.state=gt.IDLE;break;case gt.STOPPED:case gt.FRAG_LOADING:case gt.PARSING:case gt.PARSED:case gt.ENDED:}},c.onMediaAttached=function(t){var e=this.media=this.mediaBuffer=t.media;this.onvseeking=this.onMediaSeeking.bind(this),this.onvended=this.onMediaEnded.bind(this),e.addEventListener("seeking",this.onvseeking),e.addEventListener("ended",this.onvended);var r=this.config;this.tracks&&r.autoStartLoad&&this.startLoad(r.startPosition)},c.onMediaDetaching=function(){var t=this.media;t&&t.ended&&(u.b.log("MSE detaching and video ended, reset startPosition"),this.startPosition=this.lastCurrentTime=0),t&&(t.removeEventListener("seeking",this.onvseeking),t.removeEventListener("ended",this.onvended),this.onvseeking=this.onvseeked=this.onvended=null),this.media=this.mediaBuffer=this.videoBuffer=null,this.loadedmetadata=!1,this.fragmentTracker.removeAllFragments(),this.stopLoad()},c.onAudioTracksUpdated=function(t){u.b.log("audio tracks updated"),this.tracks=t.audioTracks},c.onAudioTrackSwitching=function(t){var e=!!t.url;this.trackId=t.id,this.fragCurrent=null,this.state=gt.PAUSED,this.waitingFragment=null,e?this.setInterval(100):this.demuxer&&(this.demuxer.destroy(),this.demuxer=null),e&&(this.audioSwitch=!0,this.state=gt.IDLE),this.tick()},c.onAudioTrackLoaded=function(t){var e=t.details,r=t.id,i=this.tracks[r],a=e.totalduration,n=0;if(u.b.log("track "+r+" loaded ["+e.startSN+","+e.endSN+"],duration:"+a),e.live){var s=i.details;s&&e.fragments.length>0?(rt(s,e),n=e.fragments[0].start,e.PTSKnown?u.b.log("live audio playlist sliding:"+n.toFixed(3)):u.b.log("live audio playlist - outdated PTS, unknown sliding")):(e.PTSKnown=!1,u.b.log("live audio playlist - first load, unknown sliding"))}else e.PTSKnown=!1;if(i.details=e,!this.startFragRequested){if(-1===this.startPosition){var o=e.startTimeOffset;Object(l.a)(o)?(u.b.log("start time offset found in playlist, adjust startPosition to "+o),this.startPosition=o):e.live?(this.startPosition=this.computeLivePosition(n,e),u.b.log("compute startPosition for audio-track to "+this.startPosition)):this.startPosition=0}this.nextLoadPosition=this.startPosition}this.state===gt.WAITING_TRACK&&(this.state=gt.IDLE),this.tick()},c.onKeyLoaded=function(){this.state===gt.KEY_LOADING&&(this.state=gt.IDLE,this.tick())},c.onFragLoaded=function(t){var e=this.fragCurrent,r=t.frag;if(this.state===gt.FRAG_LOADING&&e&&"audio"===r.type&&r.level===e.level&&r.sn===e.sn){var i=this.tracks[this.trackId],a=i.details,n=a.totalduration,s=e.level,o=e.sn,l=e.cc,c=this.config.defaultAudioCodec||i.audioCodec||"mp4a.40.2",h=this.stats=t.stats;if("initSegment"===o)this.state=gt.IDLE,h.tparsed=h.tbuffered=Ht.now(),a.initSegment.data=t.payload,this.hls.trigger(d.a.FRAG_BUFFERED,{stats:h,frag:e,id:"audio"}),this.tick();else{this.state=gt.PARSING,this.appended=!1,this.demuxer||(this.demuxer=new J(this.hls,"audio"));var f=this.initPTS[l],g=a.initSegment?a.initSegment.data:[];if(a.initSegment||void 0!==f){this.pendingBuffering=!0,u.b.log("Demuxing "+o+" of ["+a.startSN+" ,"+a.endSN+"],track "+s);this.demuxer.push(t.payload,g,c,null,e,n,!1,f)}else u.b.log("unknown video PTS for continuity counter "+l+", waiting for video PTS before demuxing audio frag "+o+" of ["+a.startSN+" ,"+a.endSN+"],track "+s),this.waitingFragment=t,this.state=gt.WAITING_INIT_PTS}}this.fragLoadError=0},c.onFragParsingInitSegment=function(t){var e=this.fragCurrent,r=t.frag;if(e&&"audio"===t.id&&r.sn===e.sn&&r.level===e.level&&this.state===gt.PARSING){var i,a=t.tracks;if(a.video&&delete a.video,i=a.audio){i.levelCodec=i.codec,i.id=t.id,this.hls.trigger(d.a.BUFFER_CODECS,a),u.b.log("audio track:audio,container:"+i.container+",codecs[level/parsed]=["+i.levelCodec+"/"+i.codec+"]");var n=i.initSegment;if(n){var s={type:"audio",data:n,parent:"audio",content:"initSegment"};this.audioSwitch?this.pendingData=[s]:(this.appended=!0,this.pendingBuffering=!0,this.hls.trigger(d.a.BUFFER_APPENDING,s))}this.tick()}}},c.onFragParsingData=function(t){var e=this,r=this.fragCurrent,i=t.frag;if(r&&"audio"===t.id&&"audio"===t.type&&i.sn===r.sn&&i.level===r.level&&this.state===gt.PARSING){var a=this.trackId,n=this.tracks[a],s=this.hls;Object(l.a)(t.endPTS)||(t.endPTS=t.startPTS+r.duration,t.endDTS=t.startDTS+r.duration),r.addElementaryStream(p.AUDIO),u.b.log("parsed "+t.type+",PTS:["+t.startPTS.toFixed(3)+","+t.endPTS.toFixed(3)+"],DTS:["+t.startDTS.toFixed(3)+"/"+t.endDTS.toFixed(3)+"],nb:"+t.nb),et(n.details,r,t.startPTS,t.endPTS);var c=this.audioSwitch,h=this.media,f=!1;if(c)if(h&&h.readyState){var g=h.currentTime;u.b.log("switching audio track : currentTime:"+g),g>=t.startPTS&&(u.b.log("switching audio track : flushing all audio"),this.state=gt.BUFFER_FLUSHING,s.trigger(d.a.BUFFER_FLUSHING,{startOffset:0,endOffset:Number.POSITIVE_INFINITY,type:"audio"}),f=!0,this.audioSwitch=!1,s.trigger(d.a.AUDIO_TRACK_SWITCHED,{id:a}))}else this.audioSwitch=!1,s.trigger(d.a.AUDIO_TRACK_SWITCHED,{id:a});var v=this.pendingData;if(!v)return u.b.warn("Apparently attempt to enqueue media payload without codec initialization data upfront"),void s.trigger(d.a.ERROR,{type:o.b.MEDIA_ERROR,details:null,fatal:!0});this.audioSwitch||([t.data1,t.data2].forEach(function(e){e&&e.length&&v.push({type:t.type,data:e,parent:"audio",content:"data"})}),!f&&v.length&&(v.forEach(function(t){e.state===gt.PARSING&&(e.pendingBuffering=!0,e.hls.trigger(d.a.BUFFER_APPENDING,t))}),this.pendingData=[],this.appended=!0)),this.tick()}},c.onFragParsed=function(t){var e=this.fragCurrent,r=t.frag;e&&"audio"===t.id&&r.sn===e.sn&&r.level===e.level&&this.state===gt.PARSING&&(this.stats.tparsed=Ht.now(),this.state=gt.PARSED,this._checkAppendedParsed())},c.onBufferReset=function(){this.mediaBuffer=this.videoBuffer=null,this.loadedmetadata=!1},c.onBufferCreated=function(t){var e=t.tracks.audio;e&&(this.mediaBuffer=e.buffer,this.loadedmetadata=!0),t.tracks.video&&(this.videoBuffer=t.tracks.video.buffer)},c.onBufferAppended=function(t){if("audio"===t.parent){var e=this.state;e!==gt.PARSING&&e!==gt.PARSED||(this.pendingBuffering=t.pending>0,this._checkAppendedParsed())}},c._checkAppendedParsed=function(){if(!(this.state!==gt.PARSED||this.appended&&this.pendingBuffering)){var t=this.fragCurrent,e=this.stats,r=this.hls;if(t){this.fragPrevious=t,e.tbuffered=Ht.now(),r.trigger(d.a.FRAG_BUFFERED,{stats:e,frag:t,id:"audio"});var i=this.mediaBuffer?this.mediaBuffer:this.media;i&&u.b.log("audio buffered : "+nt.toString(i.buffered)),this.audioSwitch&&this.appended&&(this.audioSwitch=!1,r.trigger(d.a.AUDIO_TRACK_SWITCHED,{id:this.trackId})),this.state=gt.IDLE}this.tick()}},c.onError=function(t){var e=t.frag;if(!e||"audio"===e.type)switch(t.details){case o.a.FRAG_LOAD_ERROR:case o.a.FRAG_LOAD_TIMEOUT:var r=t.frag;if(r&&"audio"!==r.type)break;if(!t.fatal){var i=this.fragLoadError;i?i++:i=1;var a=this.config;if(i<=a.fragLoadingMaxRetry){this.fragLoadError=i;var n=Math.min(Math.pow(2,i-1)*a.fragLoadingRetryDelay,a.fragLoadingMaxRetryTimeout);u.b.warn("AudioStreamController: frag loading failed, retry in "+n+" ms"),this.retryDate=Ht.now()+n,this.state=gt.FRAG_LOADING_WAITING_RETRY}else u.b.error("AudioStreamController: "+t.details+" reaches max retry, redispatch as fatal ..."),t.fatal=!0,this.state=gt.ERROR}break;case o.a.AUDIO_TRACK_LOAD_ERROR:case o.a.AUDIO_TRACK_LOAD_TIMEOUT:case o.a.KEY_LOAD_ERROR:case o.a.KEY_LOAD_TIMEOUT:this.state!==gt.ERROR&&(this.state=t.fatal?gt.ERROR:gt.IDLE,u.b.warn("AudioStreamController: "+t.details+" while loading frag, now switching to "+this.state+" state ..."));break;case o.a.BUFFER_FULL_ERROR:if("audio"===t.parent&&(this.state===gt.PARSING||this.state===gt.PARSED)){var s=this.mediaBuffer,l=this.media.currentTime;if(s&&H.isBuffered(s,l)&&H.isBuffered(s,l+.5)){var c=this.config;c.maxMaxBufferLength>=c.maxBufferLength&&(c.maxMaxBufferLength/=2,u.b.warn("AudioStreamController: reduce max buffer length to "+c.maxMaxBufferLength+"s")),this.state=gt.IDLE}else u.b.warn("AudioStreamController: buffer full error also media.currentTime is not buffered, flush audio buffer"),this.fragCurrent=null,this.state=gt.BUFFER_FLUSHING,this.hls.trigger(d.a.BUFFER_FLUSHING,{startOffset:0,endOffset:Number.POSITIVE_INFINITY,type:"audio"})}}},c.onBufferFlushed=function(){var t=this,e=this.pendingData;e&&e.length?(u.b.log("AudioStreamController: appending pending audio data after buffer flushed"),e.forEach(function(e){t.hls.trigger(d.a.BUFFER_APPENDING,e)}),this.appended=!0,this.pendingData=[],this.state=gt.PARSED):(this.state=gt.IDLE,this.fragPrevious=null,this.tick())},a=i,(n=[{key:"state",set:function(t){if(this.state!==t){var e=this.state;this._state=t,u.b.log("audio stream:"+e+"->"+t)}},get:function(){return this._state}}])&&jt(a.prototype,n),s&&jt(a,s),i}(pt),Wt=function(){if("undefined"!=typeof window&&window.VTTCue)return window.VTTCue;var t="auto",e={"":!0,lr:!0,rl:!0},r={start:!0,middle:!0,end:!0,left:!0,right:!0};function i(t){return"string"==typeof t&&(!!r[t.toLowerCase()]&&t.toLowerCase())}function a(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var i in r)t[i]=r[i]}return t}function n(r,n,s){var o=this,l={enumerable:!0};o.hasBeenReset=!1;var d="",u=!1,c=r,h=n,f=s,g=null,p="",v=!0,m="auto",y="start",b=50,E="middle",T=50,S="middle";Object.defineProperty(o,"id",a({},l,{get:function(){return d},set:function(t){d=""+t}})),Object.defineProperty(o,"pauseOnExit",a({},l,{get:function(){return u},set:function(t){u=!!t}})),Object.defineProperty(o,"startTime",a({},l,{get:function(){return c},set:function(t){if("number"!=typeof t)throw new TypeError("Start time must be set to a number.");c=t,this.hasBeenReset=!0}})),Object.defineProperty(o,"endTime",a({},l,{get:function(){return h},set:function(t){if("number"!=typeof t)throw new TypeError("End time must be set to a number.");h=t,this.hasBeenReset=!0}})),Object.defineProperty(o,"text",a({},l,{get:function(){return f},set:function(t){f=""+t,this.hasBeenReset=!0}})),Object.defineProperty(o,"region",a({},l,{get:function(){return g},set:function(t){g=t,this.hasBeenReset=!0}})),Object.defineProperty(o,"vertical",a({},l,{get:function(){return p},set:function(t){var r=function(t){return"string"==typeof t&&!!e[t.toLowerCase()]&&t.toLowerCase()}(t);if(!1===r)throw new SyntaxError("An invalid or illegal string was specified.");p=r,this.hasBeenReset=!0}})),Object.defineProperty(o,"snapToLines",a({},l,{get:function(){return v},set:function(t){v=!!t,this.hasBeenReset=!0}})),Object.defineProperty(o,"line",a({},l,{get:function(){return m},set:function(e){if("number"!=typeof e&&e!==t)throw new SyntaxError("An invalid number or illegal string was specified.");m=e,this.hasBeenReset=!0}})),Object.defineProperty(o,"lineAlign",a({},l,{get:function(){return y},set:function(t){var e=i(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");y=e,this.hasBeenReset=!0}})),Object.defineProperty(o,"position",a({},l,{get:function(){return b},set:function(t){if(t<0||t>100)throw new Error("Position must be between 0 and 100.");b=t,this.hasBeenReset=!0}})),Object.defineProperty(o,"positionAlign",a({},l,{get:function(){return E},set:function(t){var e=i(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");E=e,this.hasBeenReset=!0}})),Object.defineProperty(o,"size",a({},l,{get:function(){return T},set:function(t){if(t<0||t>100)throw new Error("Size must be between 0 and 100.");T=t,this.hasBeenReset=!0}})),Object.defineProperty(o,"align",a({},l,{get:function(){return S},set:function(t){var e=i(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");S=e,this.hasBeenReset=!0}})),o.displayState=void 0}return n.prototype.getCueAsHTML=function(){return window.WebVTT.convertCueToDOMTree(window,this.text)},n}(),Yt=function(){return{decode:function(t){if(!t)return"";if("string"!=typeof t)throw new Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(t))}}};function qt(){this.window=window,this.state="INITIAL",this.buffer="",this.decoder=new Yt,this.regionList=[]}function zt(){this.values=Object.create(null)}function Xt(t,e,r,i){var a=i?t.split(i):[t];for(var n in a)if("string"==typeof a[n]){var s=a[n].split(r);if(2===s.length)e(s[0],s[1])}}zt.prototype={set:function(t,e){this.get(t)||""===e||(this.values[t]=e)},get:function(t,e,r){return r?this.has(t)?this.values[t]:e[r]:this.has(t)?this.values[t]:e},has:function(t){return t in this.values},alt:function(t,e,r){for(var i=0;i<r.length;++i)if(e===r[i]){this.set(t,e);break}},integer:function(t,e){/^-?\d+$/.test(e)&&this.set(t,parseInt(e,10))},percent:function(t,e){return!!(e.match(/^([\d]{1,3})(\.[\d]*)?%$/)&&(e=parseFloat(e))>=0&&e<=100)&&(this.set(t,e),!0)}};var Qt=new Wt(0,0,0),$t="middle"===Qt.align?"middle":"center";function Jt(t,e,r){var i=t;function a(){var e=function(t){function e(t,e,r,i){return 3600*(0|t)+60*(0|e)+(0|r)+(0|i)/1e3}var r=t.match(/^(\d+):(\d{2})(:\d{2})?\.(\d{3})/);return r?r[3]?e(r[1],r[2],r[3].replace(":",""),r[4]):r[1]>59?e(r[1],r[2],0,r[4]):e(0,r[1],r[2],r[4]):null}(t);if(null===e)throw new Error("Malformed timestamp: "+i);return t=t.replace(/^[^\sa-zA-Z-]+/,""),e}function n(){t=t.replace(/^\s+/,"")}if(n(),e.startTime=a(),n(),"--\x3e"!==t.substr(0,3))throw new Error("Malformed time stamp (time stamps must be separated by '--\x3e'): "+i);t=t.substr(3),n(),e.endTime=a(),n(),function(t,e){var i=new zt;Xt(t,function(t,e){switch(t){case"region":for(var a=r.length-1;a>=0;a--)if(r[a].id===e){i.set(t,r[a].region);break}break;case"vertical":i.alt(t,e,["rl","lr"]);break;case"line":var n=e.split(","),s=n[0];i.integer(t,s),i.percent(t,s)&&i.set("snapToLines",!1),i.alt(t,s,["auto"]),2===n.length&&i.alt("lineAlign",n[1],["start",$t,"end"]);break;case"position":n=e.split(","),i.percent(t,n[0]),2===n.length&&i.alt("positionAlign",n[1],["start",$t,"end","line-left","line-right","auto"]);break;case"size":i.percent(t,e);break;case"align":i.alt(t,e,["start",$t,"end","left","right"])}},/:/,/\s/),e.region=i.get("region",null),e.vertical=i.get("vertical","");var a=i.get("line","auto");"auto"===a&&-1===Qt.line&&(a=-1),e.line=a,e.lineAlign=i.get("lineAlign","start"),e.snapToLines=i.get("snapToLines",!0),e.size=i.get("size",100),e.align=i.get("align",$t);var n=i.get("position","auto");"auto"===n&&50===Qt.position&&(n="start"===e.align||"left"===e.align?0:"end"===e.align||"right"===e.align?100:50),e.position=n}(t,e)}function Zt(t){return t.replace(/<br(?: \/)?>/gi,"\n")}qt.prototype={parse:function(t){var e=this;function r(){var t=e.buffer,r=0;for(t=Zt(t);r<t.length&&"\r"!==t[r]&&"\n"!==t[r];)++r;var i=t.substr(0,r);return"\r"===t[r]&&++r,"\n"===t[r]&&++r,e.buffer=t.substr(r),i}t&&(e.buffer+=e.decoder.decode(t,{stream:!0}));try{var i;if("INITIAL"===e.state){if(!/\r\n|\n/.test(e.buffer))return this;var a=(i=r()).match(/^(ï»¿)?WEBVTT([ \t].*)?$/);if(!a||!a[0])throw new Error("Malformed WebVTT signature.");e.state="HEADER"}for(var n=!1;e.buffer;){if(!/\r\n|\n/.test(e.buffer))return this;switch(n?n=!1:i=r(),e.state){case"HEADER":/:/.test(i)?Xt(i,function(t,e){},/:/):i||(e.state="ID");continue;case"NOTE":i||(e.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(i)){e.state="NOTE";break}if(!i)continue;if(e.cue=new Wt(0,0,""),e.state="CUE",-1===i.indexOf("--\x3e")){e.cue.id=i;continue}case"CUE":try{Jt(i,e.cue,e.regionList)}catch(t){e.cue=null,e.state="BADCUE";continue}e.state="CUETEXT";continue;case"CUETEXT":var s=-1!==i.indexOf("--\x3e");if(!i||s&&(n=!0)){e.oncue&&e.oncue(e.cue),e.cue=null,e.state="ID";continue}e.cue.text&&(e.cue.text+="\n"),e.cue.text+=i;continue;case"BADCUE":i||(e.state="ID");continue}}}catch(t){"CUETEXT"===e.state&&e.cue&&e.oncue&&e.oncue(e.cue),e.cue=null,e.state="INITIAL"===e.state?"BADWEBVTT":"BADCUE"}return this},flush:function(){try{if(this.buffer+=this.decoder.decode(),(this.cue||"HEADER"===this.state)&&(this.buffer+="\n\n",this.parse()),"INITIAL"===this.state)throw new Error("Malformed WebVTT signature.")}catch(t){throw t}return this.onflush&&this.onflush(),this}};var te=qt;function ee(t,e,r,i){for(var a,n,s,o,l,d=window.VTTCue||TextTrackCue,u=0;u<i.rows.length;u++)if(s=!0,o=0,l="",!(a=i.rows[u]).isEmpty()){for(var c=0;c<a.chars.length;c++)a.chars[c].uchar.match(/\s/)&&s?o++:(l+=a.chars[c].uchar,s=!1);a.cueStartTime=e,e===r&&(r+=1e-4),n=new d(e,r,Zt(l.trim())),o>=16?o--:o++,navigator.userAgent.match(/Firefox\//)?n.line=u+1:n.line=u>7?u-2:u+1,n.align="left",n.position=Math.max(0,Math.min(100,o/32*100)),t.addCue(n)}}var re,ie={42:225,92:233,94:237,95:243,96:250,123:231,124:247,125:209,126:241,127:9608,128:174,129:176,130:189,131:191,132:8482,133:162,134:163,135:9834,136:224,137:32,138:232,139:226,140:234,141:238,142:244,143:251,144:193,145:201,146:211,147:218,148:220,149:252,150:8216,151:161,152:42,153:8217,154:9473,155:169,156:8480,157:8226,158:8220,159:8221,160:192,161:194,162:199,163:200,164:202,165:203,166:235,167:206,168:207,169:239,170:212,171:217,172:249,173:219,174:171,175:187,176:195,177:227,178:205,179:204,180:236,181:210,182:242,183:213,184:245,185:123,186:125,187:92,188:94,189:95,190:124,191:8764,192:196,193:228,194:214,195:246,196:223,197:165,198:164,199:9475,200:197,201:229,202:216,203:248,204:9487,205:9491,206:9495,207:9499},ae=function(t){var e=t;return ie.hasOwnProperty(t)&&(e=ie[t]),String.fromCharCode(e)},ne=15,se=100,oe={17:1,18:3,21:5,22:7,23:9,16:11,19:12,20:14},le={17:2,18:4,21:6,22:8,23:10,19:13,20:15},de={25:1,26:3,29:5,30:7,31:9,24:11,27:12,28:14},ue={25:2,26:4,29:6,30:8,31:10,27:13,28:15},ce=["white","green","blue","cyan","red","yellow","magenta","black","transparent"];!function(t){t[t.ERROR=0]="ERROR",t[t.TEXT=1]="TEXT",t[t.WARNING=2]="WARNING",t[t.INFO=2]="INFO",t[t.DEBUG=3]="DEBUG",t[t.DATA=3]="DATA"}(re||(re={}));var he={verboseFilter:{DATA:3,DEBUG:3,INFO:2,WARNING:2,TEXT:1,ERROR:0},time:null,verboseLevel:0,setTime:function(t){this.time=t},log:function(t,e){this.verboseFilter[t];this.verboseLevel}},fe=function(t){for(var e=[],r=0;r<t.length;r++)e.push(t[r].toString(16));return e},ge=function(){function t(t,e,r,i,a){this.foreground=void 0,this.underline=void 0,this.italics=void 0,this.background=void 0,this.flash=void 0,this.foreground=t||"white",this.underline=e||!1,this.italics=r||!1,this.background=i||"black",this.flash=a||!1}var e=t.prototype;return e.reset=function(){this.foreground="white",this.underline=!1,this.italics=!1,this.background="black",this.flash=!1},e.setStyles=function(t){for(var e=["foreground","underline","italics","background","flash"],r=0;r<e.length;r++){var i=e[r];t.hasOwnProperty(i)&&(this[i]=t[i])}},e.isDefault=function(){return"white"===this.foreground&&!this.underline&&!this.italics&&"black"===this.background&&!this.flash},e.equals=function(t){return this.foreground===t.foreground&&this.underline===t.underline&&this.italics===t.italics&&this.background===t.background&&this.flash===t.flash},e.copy=function(t){this.foreground=t.foreground,this.underline=t.underline,this.italics=t.italics,this.background=t.background,this.flash=t.flash},e.toString=function(){return"color="+this.foreground+", underline="+this.underline+", italics="+this.italics+", background="+this.background+", flash="+this.flash},t}(),pe=function(){function t(t,e,r,i,a,n){this.uchar=void 0,this.penState=void 0,this.uchar=t||" ",this.penState=new ge(e,r,i,a,n)}var e=t.prototype;return e.reset=function(){this.uchar=" ",this.penState.reset()},e.setChar=function(t,e){this.uchar=t,this.penState.copy(e)},e.setPenState=function(t){this.penState.copy(t)},e.equals=function(t){return this.uchar===t.uchar&&this.penState.equals(t.penState)},e.copy=function(t){this.uchar=t.uchar,this.penState.copy(t.penState)},e.isEmpty=function(){return" "===this.uchar&&this.penState.isDefault()},t}(),ve=function(){function t(){this.chars=void 0,this.pos=void 0,this.currPenState=void 0,this.cueStartTime=void 0,this.chars=[];for(var t=0;t<se;t++)this.chars.push(new pe);this.pos=0,this.currPenState=new ge}var e=t.prototype;return e.equals=function(t){for(var e=!0,r=0;r<se;r++)if(!this.chars[r].equals(t.chars[r])){e=!1;break}return e},e.copy=function(t){for(var e=0;e<se;e++)this.chars[e].copy(t.chars[e])},e.isEmpty=function(){for(var t=!0,e=0;e<se;e++)if(!this.chars[e].isEmpty()){t=!1;break}return t},e.setCursor=function(t){this.pos!==t&&(this.pos=t),this.pos<0?(he.log("ERROR","Negative cursor position "+this.pos),this.pos=0):this.pos>se&&(he.log("ERROR","Too large cursor position "+this.pos),this.pos=se)},e.moveCursor=function(t){var e=this.pos+t;if(t>1)for(var r=this.pos+1;r<e+1;r++)this.chars[r].setPenState(this.currPenState);this.setCursor(e)},e.backSpace=function(){this.moveCursor(-1),this.chars[this.pos].setChar(" ",this.currPenState)},e.insertChar=function(t){t>=144&&this.backSpace();var e=ae(t);this.pos>=se?he.log("ERROR","Cannot insert "+t.toString(16)+" ("+e+") at position "+this.pos+". Skipping it!"):(this.chars[this.pos].setChar(e,this.currPenState),this.moveCursor(1))},e.clearFromPos=function(t){var e;for(e=t;e<se;e++)this.chars[e].reset()},e.clear=function(){this.clearFromPos(0),this.pos=0,this.currPenState.reset()},e.clearToEndOfRow=function(){this.clearFromPos(this.pos)},e.getTextString=function(){for(var t=[],e=!0,r=0;r<se;r++){var i=this.chars[r].uchar;" "!==i&&(e=!1),t.push(i)}return e?"":t.join("")},e.setPenStyles=function(t){this.currPenState.setStyles(t),this.chars[this.pos].setPenState(this.currPenState)},t}(),me=function(){function t(){this.rows=void 0,this.currRow=void 0,this.nrRollUpRows=void 0,this.lastOutputScreen=void 0,this.rows=[];for(var t=0;t<ne;t++)this.rows.push(new ve);this.currRow=ne-1,this.nrRollUpRows=null,this.reset()}var e=t.prototype;return e.reset=function(){for(var t=0;t<ne;t++)this.rows[t].clear();this.currRow=ne-1},e.equals=function(t){for(var e=!0,r=0;r<ne;r++)if(!this.rows[r].equals(t.rows[r])){e=!1;break}return e},e.copy=function(t){for(var e=0;e<ne;e++)this.rows[e].copy(t.rows[e])},e.isEmpty=function(){for(var t=!0,e=0;e<ne;e++)if(!this.rows[e].isEmpty()){t=!1;break}return t},e.backSpace=function(){this.rows[this.currRow].backSpace()},e.clearToEndOfRow=function(){this.rows[this.currRow].clearToEndOfRow()},e.insertChar=function(t){this.rows[this.currRow].insertChar(t)},e.setPen=function(t){this.rows[this.currRow].setPenStyles(t)},e.moveCursor=function(t){this.rows[this.currRow].moveCursor(t)},e.setCursor=function(t){he.log("INFO","setCursor: "+t),this.rows[this.currRow].setCursor(t)},e.setPAC=function(t){he.log("INFO","pacData = "+JSON.stringify(t));var e=t.row-1;if(this.nrRollUpRows&&e<this.nrRollUpRows-1&&(e=this.nrRollUpRows-1),this.nrRollUpRows&&this.currRow!==e){for(var r=0;r<ne;r++)this.rows[r].clear();var i=this.currRow+1-this.nrRollUpRows,a=this.lastOutputScreen;if(a){var n=a.rows[i].cueStartTime;if(n&&he.time&&n<he.time)for(var s=0;s<this.nrRollUpRows;s++)this.rows[e-this.nrRollUpRows+s+1].copy(a.rows[i+s])}}this.currRow=e;var o=this.rows[this.currRow];if(null!==t.indent){var l=t.indent,d=Math.max(l-1,0);o.setCursor(t.indent),t.color=o.chars[d].penState.foreground}var u={foreground:t.color,underline:t.underline,italics:t.italics,background:"black",flash:!1};this.setPen(u)},e.setBkgData=function(t){he.log("INFO","bkgData = "+JSON.stringify(t)),this.backSpace(),this.setPen(t),this.insertChar(32)},e.setRollUpRows=function(t){this.nrRollUpRows=t},e.rollUp=function(){if(null!==this.nrRollUpRows){he.log("TEXT",this.getDisplayText());var t=this.currRow+1-this.nrRollUpRows,e=this.rows.splice(t,1)[0];e.clear(),this.rows.splice(this.currRow,0,e),he.log("INFO","Rolling up")}else he.log("DEBUG","roll_up but nrRollUpRows not set yet")},e.getDisplayText=function(t){t=t||!1;for(var e=[],r="",i=-1,a=0;a<ne;a++){var n=this.rows[a].getTextString();n&&(i=a+1,t?e.push("Row "+i+": '"+n+"'"):e.push(n.trim()))}return e.length>0&&(r=t?"["+e.join(" | ")+"]":e.join("\n")),r},e.getTextAndFormat=function(){return this.rows},t}(),ye=function(){function t(t,e){this.chNr=void 0,this.outputFilter=void 0,this.mode=void 0,this.verbose=void 0,this.displayedMemory=void 0,this.nonDisplayedMemory=void 0,this.lastOutputScreen=void 0,this.currRollUpRow=void 0,this.writeScreen=void 0,this.cueStartTime=void 0,this.lastCueEndTime=void 0,this.chNr=t,this.outputFilter=e,this.mode=null,this.verbose=0,this.displayedMemory=new me,this.nonDisplayedMemory=new me,this.lastOutputScreen=new me,this.currRollUpRow=this.displayedMemory.rows[ne-1],this.writeScreen=this.displayedMemory,this.mode=null,this.cueStartTime=null}var e=t.prototype;return e.reset=function(){this.mode=null,this.displayedMemory.reset(),this.nonDisplayedMemory.reset(),this.lastOutputScreen.reset(),this.currRollUpRow=this.displayedMemory.rows[ne-1],this.writeScreen=this.displayedMemory,this.mode=null,this.cueStartTime=null},e.getHandler=function(){return this.outputFilter},e.setHandler=function(t){this.outputFilter=t},e.setPAC=function(t){this.writeScreen.setPAC(t)},e.setBkgData=function(t){this.writeScreen.setBkgData(t)},e.setMode=function(t){t!==this.mode&&(this.mode=t,he.log("INFO","MODE="+t),"MODE_POP-ON"===this.mode?this.writeScreen=this.nonDisplayedMemory:(this.writeScreen=this.displayedMemory,this.writeScreen.reset()),"MODE_ROLL-UP"!==this.mode&&(this.displayedMemory.nrRollUpRows=null,this.nonDisplayedMemory.nrRollUpRows=null),this.mode=t)},e.insertChars=function(t){for(var e=0;e<t.length;e++)this.writeScreen.insertChar(t[e]);var r=this.writeScreen===this.displayedMemory?"DISP":"NON_DISP";he.log("INFO",r+": "+this.writeScreen.getDisplayText(!0)),"MODE_PAINT-ON"!==this.mode&&"MODE_ROLL-UP"!==this.mode||(he.log("TEXT","DISPLAYED: "+this.displayedMemory.getDisplayText(!0)),this.outputDataUpdate())},e.ccRCL=function(){he.log("INFO","RCL - Resume Caption Loading"),this.setMode("MODE_POP-ON")},e.ccBS=function(){he.log("INFO","BS - BackSpace"),"MODE_TEXT"!==this.mode&&(this.writeScreen.backSpace(),this.writeScreen===this.displayedMemory&&this.outputDataUpdate())},e.ccAOF=function(){},e.ccAON=function(){},e.ccDER=function(){he.log("INFO","DER- Delete to End of Row"),this.writeScreen.clearToEndOfRow(),this.outputDataUpdate()},e.ccRU=function(t){he.log("INFO","RU("+t+") - Roll Up"),this.writeScreen=this.displayedMemory,this.setMode("MODE_ROLL-UP"),this.writeScreen.setRollUpRows(t)},e.ccFON=function(){he.log("INFO","FON - Flash On"),this.writeScreen.setPen({flash:!0})},e.ccRDC=function(){he.log("INFO","RDC - Resume Direct Captioning"),this.setMode("MODE_PAINT-ON")},e.ccTR=function(){he.log("INFO","TR"),this.setMode("MODE_TEXT")},e.ccRTD=function(){he.log("INFO","RTD"),this.setMode("MODE_TEXT")},e.ccEDM=function(){he.log("INFO","EDM - Erase Displayed Memory"),this.displayedMemory.reset(),this.outputDataUpdate(!0)},e.ccCR=function(){he.log("INFO","CR - Carriage Return"),this.writeScreen.rollUp(),this.outputDataUpdate(!0)},e.ccENM=function(){he.log("INFO","ENM - Erase Non-displayed Memory"),this.nonDisplayedMemory.reset()},e.ccEOC=function(){if(he.log("INFO","EOC - End Of Caption"),"MODE_POP-ON"===this.mode){var t=this.displayedMemory;this.displayedMemory=this.nonDisplayedMemory,this.nonDisplayedMemory=t,this.writeScreen=this.nonDisplayedMemory,he.log("TEXT","DISP: "+this.displayedMemory.getDisplayText())}this.outputDataUpdate(!0)},e.ccTO=function(t){he.log("INFO","TO("+t+") - Tab Offset"),this.writeScreen.moveCursor(t)},e.ccMIDROW=function(t){var e={flash:!1};if(e.underline=t%2==1,e.italics=t>=46,e.italics)e.foreground="white";else{var r=Math.floor(t/2)-16;e.foreground=["white","green","blue","cyan","red","yellow","magenta"][r]}he.log("INFO","MIDROW: "+JSON.stringify(e)),this.writeScreen.setPen(e)},e.outputDataUpdate=function(t){void 0===t&&(t=!1);var e=he.time;null!==e&&this.outputFilter&&(null!==this.cueStartTime||this.displayedMemory.isEmpty()?this.displayedMemory.equals(this.lastOutputScreen)||(this.outputFilter.newCue(this.cueStartTime,e,this.lastOutputScreen),t&&this.outputFilter.dispatchCue&&this.outputFilter.dispatchCue(),this.cueStartTime=this.displayedMemory.isEmpty()?null:e):this.cueStartTime=e,this.lastOutputScreen.copy(this.displayedMemory))},e.cueSplitAtTime=function(t){this.outputFilter&&(this.displayedMemory.isEmpty()||(this.outputFilter.newCue&&this.outputFilter.newCue(this.cueStartTime,t,this.displayedMemory),this.cueStartTime=t))},t}(),be=function(){function t(t,e,r){this.field=void 0,this.outputs=void 0,this.channels=void 0,this.currChNr=void 0,this.lastCmdA=void 0,this.lastCmdB=void 0,this.lastTime=void 0,this.dataCounters=void 0,this.field=t||1,this.outputs=[e,r],this.channels=[new ye(1,e),new ye(2,r)],this.currChNr=-1,this.lastCmdA=null,this.lastCmdB=null,this.lastTime=null,this.dataCounters={padding:0,char:0,cmd:0,other:0}}var e=t.prototype;return e.getHandler=function(t){return this.channels[t].getHandler()},e.setHandler=function(t,e){this.channels[t].setHandler(e)},e.addData=function(t,e){var r,i,a,n=!1;this.lastTime=t,he.setTime(t);for(var s=0;s<e.length;s+=2)if(i=127&e[s],a=127&e[s+1],0!==i||0!==a){if(he.log("DATA","["+fe([e[s],e[s+1]])+"] -> ("+fe([i,a])+")"),(r=this.parseCmd(i,a))||(r=this.parseMidrow(i,a)),r||(r=this.parsePAC(i,a)),r||(r=this.parseBackgroundAttributes(i,a)),!r)if(n=this.parseChars(i,a))if(this.currChNr&&this.currChNr>=0)this.channels[this.currChNr-1].insertChars(n);else he.log("WARNING","No channel found yet. TEXT-MODE?");r?this.dataCounters.cmd+=2:n?this.dataCounters.char+=2:(this.dataCounters.other+=2,he.log("WARNING","Couldn't parse cleaned data "+fe([i,a])+" orig: "+fe([e[s],e[s+1]])))}else this.dataCounters.padding+=2},e.parseCmd=function(t,e){var r=null;if(!((20===t||28===t)&&e>=32&&e<=47)&&!((23===t||31===t)&&e>=33&&e<=35))return!1;if(t===this.lastCmdA&&e===this.lastCmdB)return this.lastCmdA=null,this.lastCmdB=null,he.log("DEBUG","Repeated command ("+fe([t,e])+") is dropped"),!0;r=20===t||23===t?1:2;var i=this.channels[r-1];return 20===t||28===t?32===e?i.ccRCL():33===e?i.ccBS():34===e?i.ccAOF():35===e?i.ccAON():36===e?i.ccDER():37===e?i.ccRU(2):38===e?i.ccRU(3):39===e?i.ccRU(4):40===e?i.ccFON():41===e?i.ccRDC():42===e?i.ccTR():43===e?i.ccRTD():44===e?i.ccEDM():45===e?i.ccCR():46===e?i.ccENM():47===e&&i.ccEOC():i.ccTO(e-32),this.lastCmdA=t,this.lastCmdB=e,this.currChNr=r,!0},e.parseMidrow=function(t,e){var r=null;return(17===t||25===t)&&e>=32&&e<=47&&((r=17===t?1:2)!==this.currChNr?(he.log("ERROR","Mismatch channel in midrow parsing"),!1):(this.channels[r-1].ccMIDROW(e),he.log("DEBUG","MIDROW ("+fe([t,e])+")"),!0))},e.parsePAC=function(t,e){var r,i=null;if(!((t>=17&&t<=23||t>=25&&t<=31)&&e>=64&&e<=127)&&!((16===t||24===t)&&e>=64&&e<=95))return!1;if(t===this.lastCmdA&&e===this.lastCmdB)return this.lastCmdA=null,this.lastCmdB=null,!0;r=t<=23?1:2,i=e>=64&&e<=95?1===r?oe[t]:de[t]:1===r?le[t]:ue[t];var a=this.interpretPAC(i,e);return this.channels[r-1].setPAC(a),this.lastCmdA=t,this.lastCmdB=e,this.currChNr=r,!0},e.interpretPAC=function(t,e){var r=e,i={color:null,italics:!1,indent:null,underline:!1,row:t};return r=e>95?e-96:e-64,i.underline=1==(1&r),r<=13?i.color=["white","green","blue","cyan","red","yellow","magenta","white"][Math.floor(r/2)]:r<=15?(i.italics=!0,i.color="white"):i.indent=4*Math.floor((r-16)/2),i},e.parseChars=function(t,e){var r=null,i=null,a=null;if(t>=25?(r=2,a=t-8):(r=1,a=t),a>=17&&a<=19){var n=e;n=17===a?e+80:18===a?e+112:e+144,he.log("INFO","Special char '"+ae(n)+"' in channel "+r),i=[n]}else t>=32&&t<=127&&(i=0===e?[t]:[t,e]);if(i){var s=fe(i);he.log("DEBUG","Char codes =  "+s.join(",")),this.lastCmdA=null,this.lastCmdB=null}return i},e.parseBackgroundAttributes=function(t,e){var r,i,a;return((16===t||24===t)&&e>=32&&e<=47||(23===t||31===t)&&e>=45&&e<=47)&&(r={},16===t||24===t?(i=Math.floor((e-32)/2),r.background=ce[i],e%2==1&&(r.background=r.background+"_semi")):45===e?r.background="transparent":(r.foreground="black",47===e&&(r.underline=!0)),a=t<24?1:2,this.channels[a-1].setBkgData(r),this.lastCmdA=null,this.lastCmdB=null,!0)},e.reset=function(){for(var t=0;t<this.channels.length;t++)this.channels[t]&&this.channels[t].reset();this.lastCmdA=null,this.lastCmdB=null},e.cueSplitAtTime=function(t){for(var e=0;e<this.channels.length;e++)this.channels[e]&&this.channels[e].cueSplitAtTime(t)},t}(),Ee=function(){function t(t,e){this.timelineController=void 0,this.trackName=void 0,this.startTime=void 0,this.endTime=void 0,this.screen=void 0,this.timelineController=t,this.trackName=e,this.startTime=null,this.endTime=null,this.screen=null}var e=t.prototype;return e.dispatchCue=function(){null!==this.startTime&&(this.timelineController.addCues(this.trackName,this.startTime,this.endTime,this.screen),this.startTime=null)},e.newCue=function(t,e,r){(null===this.startTime||this.startTime>t)&&(this.startTime=t),this.endTime=e,this.screen=r,this.timelineController.createCaptionsTrack(this.trackName)},t}(),Te=function(t,e,r){return t.substr(r||0,e.length)===e},Se=function(t){for(var e=5381,r=t.length;r;)e=33*e^t.charCodeAt(--r);return(e>>>0).toString()},_e={parse:function(t,e,r,i,a,n){var s,o=Object(Tt.b)(new Uint8Array(t)).trim().replace(/\r\n|\n\r|\n|\r/g,"\n").split("\n"),d="00:00.000",u=0,c=0,h=0,f=[],g=!0,p=!1,v=new te;v.oncue=function(t){var e=r[i],a=r.ccOffset;e&&e.new&&(void 0!==c?a=r.ccOffset=e.start:function(t,e,r){var i=t[e],a=t[i.prevCC];if(!a||!a.new&&i.new)return t.ccOffset=t.presentationOffset=i.start,void(i.new=!1);for(;a&&a.new;)t.ccOffset+=i.start-a.start,i.new=!1,a=t[(i=a).prevCC];t.presentationOffset=r}(r,i,h)),h&&(a=h-r.presentationOffset),p&&(t.startTime+=a-c,t.endTime+=a-c),t.id=Se(t.startTime.toString())+Se(t.endTime.toString())+Se(t.text),t.text=decodeURIComponent(encodeURIComponent(t.text)),t.endTime>0&&f.push(t)},v.onparsingerror=function(t){s=t},v.onflush=function(){s&&n?n(s):a(f)},o.forEach(function(t){if(g){if(Te(t,"X-TIMESTAMP-MAP=")){g=!1,p=!0,t.substr(16).split(",").forEach(function(t){Te(t,"LOCAL:")?d=t.substr(6):Te(t,"MPEGTS:")&&(u=parseInt(t.substr(7)))});try{e+(9e4*r[i].start||0)<0&&(e+=8589934592),u-=e,c=function(t){var e=parseInt(t.substr(-3)),r=parseInt(t.substr(-6,2)),i=parseInt(t.substr(-9,2)),a=t.length>9?parseInt(t.substr(0,t.indexOf(":"))):0;if(!(Object(l.a)(e)&&Object(l.a)(r)&&Object(l.a)(i)&&Object(l.a)(a)))throw Error("Malformed X-TIMESTAMP-MAP: Local:"+t);return e+=1e3*r,e+=6e4*i,e+=36e5*a}(d)/1e3,h=u/9e4}catch(t){p=!1,s=t}return}""===t&&(g=!1)}v.parse(t+"\n")}),v.flush()}};function Ae(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Re(t,e){return t&&t.label===e.name&&!(t.textTrack1||t.textTrack2)}var Le=function(t){var e,r;function i(e){var r;if((r=t.call(this,e,d.a.MEDIA_ATTACHING,d.a.MEDIA_DETACHING,d.a.FRAG_PARSING_USERDATA,d.a.FRAG_DECRYPTED,d.a.MANIFEST_LOADING,d.a.MANIFEST_LOADED,d.a.FRAG_LOADED,d.a.INIT_PTS_FOUND)||this).media=null,r.config=void 0,r.enabled=!0,r.Cues=void 0,r.textTracks=[],r.tracks=[],r.initPTS=[],r.unparsedVttFrags=[],r.cueRanges=[],r.captionsTracks={},r.captionsProperties=void 0,r.cea608Parser=void 0,r.lastSn=-1,r.prevCC=-1,r.vttCCs=null,r.hls=e,r.config=e.config,r.Cues=e.config.cueHandler,r.captionsProperties={textTrack1:{label:r.config.captionsTextTrack1Label,languageCode:r.config.captionsTextTrack1LanguageCode},textTrack2:{label:r.config.captionsTextTrack2Label,languageCode:r.config.captionsTextTrack2LanguageCode}},r.config.enableCEA708Captions){var i=new Ee(Ae(r),"textTrack1"),a=new Ee(Ae(r),"textTrack2");r.cea608Parser=new be(0,i,a)}return r}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a=i.prototype;return a.addCues=function(t,e,r,i){for(var a,n,s,o,l=this.cueRanges,d=!1,u=l.length;u--;){var c=l[u],h=(a=c[0],n=c[1],s=e,o=r,Math.min(n,o)-Math.max(a,s));if(h>=0&&(c[0]=Math.min(c[0],e),c[1]=Math.max(c[1],r),d=!0,h/(r-e)>.5))return}d||l.push([e,r]),this.Cues.newCue(this.captionsTracks[t],e,r,i)},a.onInitPtsFound=function(t){var e=this,r=t.frag,i=t.id,a=t.initPTS,n=this.unparsedVttFrags;"main"===i&&(this.initPTS[r.cc]=a),n.length&&(this.unparsedVttFrags=[],n.forEach(function(t){e.onFragLoaded(t)}))},a.getExistingTrack=function(t){var e=this.media;if(e)for(var r=0;r<e.textTracks.length;r++){var i=e.textTracks[r];if(i[t])return i}return null},a.createCaptionsTrack=function(t){var e=this.captionsProperties,r=this.captionsTracks,i=this.media,a=e[t],n=a.label,s=a.languageCode;if(!r[t]){var o=this.getExistingTrack(t);if(o)r[t]=o,_t(r[t]),St(r[t],i);else{var l=this.createTextTrack("captions",n,s);l&&(l[t]=!0,r[t]=l)}}},a.createTextTrack=function(t,e,r){var i=this.media;if(i)return i.addTextTrack(t,e,r)},a.destroy=function(){t.prototype.destroy.call(this)},a.onMediaAttaching=function(t){this.media=t.media,this._cleanTracks()},a.onMediaDetaching=function(){var t=this.captionsTracks;Object.keys(t).forEach(function(e){_t(t[e]),delete t[e]})},a.onManifestLoading=function(){this.lastSn=-1,this.prevCC=-1,this.vttCCs={ccOffset:0,presentationOffset:0,0:{start:0,prevCC:-1,new:!1}},this._cleanTracks()},a._cleanTracks=function(){var t=this.media;if(t){var e=t.textTracks;if(e)for(var r=0;r<e.length;r++)_t(e[r])}},a.onManifestLoaded=function(t){var e=this;if(this.textTracks=[],this.unparsedVttFrags=this.unparsedVttFrags||[],this.initPTS=[],this.cueRanges=[],this.config.enableWebVTT){this.tracks=t.subtitles||[];var r=this.media?this.media.textTracks:[];this.tracks.forEach(function(t,i){var a;if(i<r.length){for(var n=null,s=0;s<r.length;s++)if(Re(r[s],t)){n=r[s];break}n&&(a=n)}a||(a=e.createTextTrack("subtitles",t.name,t.lang)),t.default?a.mode=e.hls.subtitleDisplay?"showing":"hidden":a.mode="disabled",e.textTracks.push(a)})}},a.onFragLoaded=function(t){var e=t.frag,r=t.payload,i=this.cea608Parser,a=this.initPTS,n=this.lastSn,s=this.unparsedVttFrags;if("main"===e.type){var o=e.sn;e.sn!==n+1&&i&&i.reset(),this.lastSn=o}else if("subtitle"===e.type)if(r.byteLength){if(!Object(l.a)(a[e.cc]))return s.push(t),void(a.length&&this.hls.trigger(d.a.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:e}));var u=e.decryptdata;null!=u&&null!=u.key&&"AES-128"===u.method||this._parseVTTs(e,r)}else this.hls.trigger(d.a.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:e})},a._parseVTTs=function(t,e){var r=this.hls,i=this.prevCC,a=this.textTracks,n=this.vttCCs;n[t.cc]||(n[t.cc]={start:t.start,prevCC:i,new:!0},this.prevCC=t.cc),_e.parse(e,this.initPTS[t.cc],n,t.cc,function(e){var i=a[t.level];"disabled"!==i.mode?(e.forEach(function(t){if(!i.cues.getCueById(t.id))try{i.addCue(t)}catch(r){var e=new window.TextTrackCue(t.startTime,t.endTime,t.text);e.id=t.id,i.addCue(e)}}),r.trigger(d.a.SUBTITLE_FRAG_PROCESSED,{success:!0,frag:t})):r.trigger(d.a.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:t})},function(e){u.b.log("Failed to parse VTT cue: "+e),r.trigger(d.a.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:t})})},a.onFragDecrypted=function(t){var e=t.frag,r=t.payload;if("subtitle"===e.type){if(!Object(l.a)(this.initPTS[e.cc]))return void this.unparsedVttFrags.push(t);this._parseVTTs(e,r)}},a.onFragParsingUserdata=function(t){if(this.enabled&&this.cea608Parser)for(var e=0;e<t.samples.length;e++){var r=t.samples[e].bytes;if(r){var i=this.extractCea608Data(r);this.cea608Parser.addData(t.samples[e].pts,i)}}},a.extractCea608Data=function(t){for(var e,r,i,a=31&t[0],n=2,s=[],o=0;o<a;o++)e=t[n++],r=127&t[n++],i=127&t[n++],0===r&&0===i||0!=(4&e)&&0===(3&e)&&(s.push(r),s.push(i));return s},i}(h);function De(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function we(t){for(var e=[],r=0;r<t.length;r++){var i=t[r];"subtitles"===i.kind&&i.label&&e.push(t[r])}return e}var ke=function(t){var e,r;function i(e){var r;return(r=t.call(this,e,d.a.MEDIA_ATTACHED,d.a.MEDIA_DETACHING,d.a.MANIFEST_LOADED,d.a.SUBTITLE_TRACK_LOADED)||this).tracks=[],r.trackId=-1,r.media=null,r.stopped=!0,r.subtitleDisplay=!0,r.queuedDefaultTrack=null,r}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a,n,s,o=i.prototype;return o.destroy=function(){h.prototype.destroy.call(this)},o.onMediaAttached=function(t){var e=this;this.media=t.media,this.media&&(Object(l.a)(this.queuedDefaultTrack)&&(this.subtitleTrack=this.queuedDefaultTrack,this.queuedDefaultTrack=null),this.trackChangeListener=this._onTextTracksChanged.bind(this),this.useTextTrackPolling=!(this.media.textTracks&&"onchange"in this.media.textTracks),this.useTextTrackPolling?this.subtitlePollingInterval=setInterval(function(){e.trackChangeListener()},500):this.media.textTracks.addEventListener("change",this.trackChangeListener))},o.onMediaDetaching=function(){this.media&&(this.useTextTrackPolling?clearInterval(this.subtitlePollingInterval):this.media.textTracks.removeEventListener("change",this.trackChangeListener),Object(l.a)(this.subtitleTrack)&&(this.queuedDefaultTrack=this.subtitleTrack),this.trackId=-1,this.media=null)},o.onManifestLoaded=function(t){var e=this,r=t.subtitles||[];this.tracks=r,this.hls.trigger(d.a.SUBTITLE_TRACKS_UPDATED,{subtitleTracks:r}),r.forEach(function(t){t.default&&(e.media?e.subtitleTrack=t.id:e.queuedDefaultTrack=t.id)})},o.onSubtitleTrackLoaded=function(t){var e=this,r=t.id,i=t.details,a=this.trackId,n=this.tracks,s=n[a];if(r>=n.length||r!==a||!s||this.stopped)this._clearReloadTimer();else if(u.b.log("subtitle track "+r+" loaded"),i.live){var o=at(s.details,i,t.stats.trequest);u.b.log("Reloading live subtitle playlist in "+o+"ms"),this.timer=setTimeout(function(){e._loadCurrentTrack()},o)}else this._clearReloadTimer()},o.startLoad=function(){this.stopped=!1,this._loadCurrentTrack()},o.stopLoad=function(){this.stopped=!0,this._clearReloadTimer()},o._clearReloadTimer=function(){this.timer&&(clearTimeout(this.timer),this.timer=null)},o._loadCurrentTrack=function(){var t=this.trackId,e=this.tracks,r=this.hls,i=e[t];t<0||!i||i.details&&!i.details.live||(u.b.log("Loading subtitle track "+t),r.trigger(d.a.SUBTITLE_TRACK_LOADING,{url:i.url,id:t}))},o._toggleTrackModes=function(t){var e=this.media,r=this.subtitleDisplay,i=this.trackId;if(e){var a=we(e.textTracks);if(-1===t)[].slice.call(a).forEach(function(t){t.mode="disabled"});else{var n=a[i];n&&(n.mode="disabled")}var s=a[t];s&&(s.mode=r?"showing":"hidden")}},o._setSubtitleTrackInternal=function(t){var e=this.hls,r=this.tracks;!Object(l.a)(t)||t<-1||t>=r.length||(this.trackId=t,u.b.log("Switching to subtitle track "+t),e.trigger(d.a.SUBTITLE_TRACK_SWITCH,{id:t}),this._loadCurrentTrack())},o._onTextTracksChanged=function(){if(this.media){for(var t=-1,e=we(this.media.textTracks),r=0;r<e.length;r++)if("hidden"===e[r].mode)t=r;else if("showing"===e[r].mode){t=r;break}this.subtitleTrack=t}},a=i,(n=[{key:"subtitleTracks",get:function(){return this.tracks}},{key:"subtitleTrack",get:function(){return this.trackId},set:function(t){this.trackId!==t&&(this._toggleTrackModes(t),this._setSubtitleTrackInternal(t))}}])&&De(a.prototype,n),s&&De(a,s),i}(h),Ie=r(7);var Oe,Ce=window.performance,Pe=function(t){var e,r;function i(e,r){var i;return(i=t.call(this,e,d.a.MEDIA_ATTACHED,d.a.MEDIA_DETACHING,d.a.ERROR,d.a.KEY_LOADED,d.a.FRAG_LOADED,d.a.SUBTITLE_TRACKS_UPDATED,d.a.SUBTITLE_TRACK_SWITCH,d.a.SUBTITLE_TRACK_LOADED,d.a.SUBTITLE_FRAG_PROCESSED,d.a.LEVEL_UPDATED)||this).fragmentTracker=r,i.config=e.config,i.state=gt.STOPPED,i.tracks=[],i.tracksBuffered=[],i.currentTrackId=-1,i.decrypter=new Ie.a(e,e.config),i.lastAVStart=0,i._onMediaSeeking=i.onMediaSeeking.bind(function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(i)),i}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a=i.prototype;return a.onSubtitleFragProcessed=function(t){var e=t.frag,r=t.success;if(this.fragPrevious=e,this.state=gt.IDLE,r){var i=this.tracksBuffered[this.currentTrackId];if(i){for(var a,n=e.start,s=0;s<i.length;s++)if(n>=i[s].start&&n<=i[s].end){a=i[s];break}var o=e.start+e.duration;a?a.end=o:(a={start:n,end:o},i.push(a))}}},a.onMediaAttached=function(t){var e=t.media;this.media=e,e.addEventListener("seeking",this._onMediaSeeking),this.state=gt.IDLE},a.onMediaDetaching=function(){var t=this;this.media.removeEventListener("seeking",this._onMediaSeeking),this.fragmentTracker.removeAllFragments(),this.currentTrackId=-1,this.tracks.forEach(function(e){t.tracksBuffered[e.id]=[]}),this.media=null,this.state=gt.STOPPED},a.onError=function(t){var e=t.frag;e&&"subtitle"===e.type&&(this.state=gt.IDLE)},a.onSubtitleTracksUpdated=function(t){var e=this;u.b.log("subtitle tracks updated"),this.tracksBuffered=[],this.tracks=t.subtitleTracks,this.tracks.forEach(function(t){e.tracksBuffered[t.id]=[]})},a.onSubtitleTrackSwitch=function(t){if(this.currentTrackId=t.id,this.tracks&&this.tracks.length&&-1!==this.currentTrackId){var e=this.tracks[this.currentTrackId];e&&e.details&&this.setInterval(500)}else this.clearInterval()},a.onSubtitleTrackLoaded=function(t){var e=t.id,r=t.details,i=this.currentTrackId,a=this.tracks,n=a[i];e>=a.length||e!==i||!n||(r.live&&function(t,e,r){void 0===r&&(r=0);var i=-1;it(t,e,function(t,e,r){e.start=t.start,i=r});var a=e.fragments;if(i<0)a.forEach(function(t){t.start+=r});else for(var n=i+1;n<a.length;n++)a[n].start=a[n-1].start+a[n-1].duration}(n.details,r,this.lastAVStart),n.details=r,this.setInterval(500))},a.onKeyLoaded=function(){this.state===gt.KEY_LOADING&&(this.state=gt.IDLE)},a.onFragLoaded=function(t){var e=this.fragCurrent,r=t.frag.decryptdata,i=t.frag,a=this.hls;if(this.state===gt.FRAG_LOADING&&e&&"subtitle"===t.frag.type&&e.sn===t.frag.sn&&t.payload.byteLength>0&&r&&r.key&&"AES-128"===r.method){var n=Ce.now();this.decrypter.decrypt(t.payload,r.key.buffer,r.iv.buffer,function(t){var e=Ce.now();a.trigger(d.a.FRAG_DECRYPTED,{frag:i,payload:t,stats:{tstart:n,tdecrypt:e}})})}},a.onLevelUpdated=function(t){var e=t.details.fragments;this.lastAVStart=e.length?e[0].start:0},a.doTick=function(){if(this.media)switch(this.state){case gt.IDLE:var t=this.config,e=this.currentTrackId,r=this.fragmentTracker,i=this.media,a=this.tracks;if(!a||!a[e]||!a[e].details)break;var n,s=t.maxBufferHole,o=t.maxFragLookUpTolerance,l=Math.min(t.maxBufferLength,t.maxMaxBufferLength),c=H.bufferedInfo(this._getBuffered(),i.currentTime,s),h=c.end,f=c.len,g=a[e].details,p=g.fragments,v=p.length,m=p[v-1].start+p[v-1].duration;if(f>l)return;var y=this.fragPrevious;h<m?(y&&g.hasProgramDateTime&&(n=lt(p,y.endProgramDateTime,o)),n||(n=dt(y,p,h,o))):n=p[v-1],n&&n.encrypted?(u.b.log("Loading key for "+n.sn),this.state=gt.KEY_LOADING,this.hls.trigger(d.a.KEY_LOADING,{frag:n})):n&&r.getState(n)===N&&(this.fragCurrent=n,this.state=gt.FRAG_LOADING,this.hls.trigger(d.a.FRAG_LOADING,{frag:n}))}else this.state=gt.IDLE},a.stopLoad=function(){this.lastAVStart=0,t.prototype.stopLoad.call(this)},a._getBuffered=function(){return this.tracksBuffered[this.currentTrackId]||[]},a.onMediaSeeking=function(){this.fragPrevious=null},i}(pt);!function(t){t.WIDEVINE="com.widevine.alpha",t.PLAYREADY="com.microsoft.playready"}(Oe||(Oe={}));var xe="undefined"!=typeof window&&window.navigator&&window.navigator.requestMediaKeySystemAccess?window.navigator.requestMediaKeySystemAccess.bind(window.navigator):null;function Fe(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var Me=function(t){var e,r;function i(e){var r;return(r=t.call(this,e,d.a.MEDIA_ATTACHED,d.a.MEDIA_DETACHED,d.a.MANIFEST_PARSED)||this)._widevineLicenseUrl=void 0,r._licenseXhrSetup=void 0,r._emeEnabled=void 0,r._requestMediaKeySystemAccess=void 0,r._config=void 0,r._mediaKeysList=[],r._media=null,r._hasSetMediaKeys=!1,r._requestLicenseFailureCount=0,r._onMediaEncrypted=function(t){u.b.log('Media is encrypted using "'+t.initDataType+'" init data type'),r._attemptSetMediaKeys(),r._generateRequestWithPreferredKeySession(t.initDataType,t.initData)},r._config=e.config,r._widevineLicenseUrl=r._config.widevineLicenseUrl,r._licenseXhrSetup=r._config.licenseXhrSetup,r._emeEnabled=r._config.emeEnabled,r._requestMediaKeySystemAccess=r._config.requestMediaKeySystemAccessFunc,r}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a,n,s,l=i.prototype;return l.getLicenseServerUrl=function(t){switch(t){case Oe.WIDEVINE:if(!this._widevineLicenseUrl)break;return this._widevineLicenseUrl}throw new Error('no license server URL configured for key-system "'+t+'"')},l._attemptKeySystemAccess=function(t,e,r){var i=this,a=function(t,e,r){switch(t){case Oe.WIDEVINE:return function(t,e){var r={videoCapabilities:[]};return e.forEach(function(t){r.videoCapabilities.push({contentType:'video/mp4; codecs="'+t+'"'})}),[r]}(0,r);default:throw new Error("Unknown key-system: "+t)}}(t,0,r);u.b.log("Requesting encrypted media key-system access"),this.requestMediaKeySystemAccess(t,a).then(function(e){i._onMediaKeySystemAccessObtained(t,e)}).catch(function(e){u.b.error('Failed to obtain key-system "'+t+'" access:',e)})},l._onMediaKeySystemAccessObtained=function(t,e){var r=this;u.b.log('Access for key-system "'+t+'" obtained');var i={mediaKeysSessionInitialized:!1,mediaKeySystemAccess:e,mediaKeySystemDomain:t};this._mediaKeysList.push(i),e.createMediaKeys().then(function(e){i.mediaKeys=e,u.b.log('Media-keys created for key-system "'+t+'"'),r._onMediaKeysCreated()}).catch(function(t){u.b.error("Failed to create media-keys:",t)})},l._onMediaKeysCreated=function(){var t=this;this._mediaKeysList.forEach(function(e){e.mediaKeysSession||(e.mediaKeysSession=e.mediaKeys.createSession(),t._onNewMediaKeySession(e.mediaKeysSession))})},l._onNewMediaKeySession=function(t){var e=this;u.b.log("New key-system session "+t.sessionId),t.addEventListener("message",function(r){e._onKeySessionMessage(t,r.message)},!1)},l._onKeySessionMessage=function(t,e){u.b.log("Got EME message event, creating license request"),this._requestLicense(e,function(e){u.b.log("Received license data, updating key-session"),t.update(e)})},l._attemptSetMediaKeys=function(){if(!this._media)throw new Error("Attempted to set mediaKeys without first attaching a media element");if(!this._hasSetMediaKeys){var t=this._mediaKeysList[0];if(!t||!t.mediaKeys)return u.b.error("Fatal: Media is encrypted but no CDM access or no keys have been obtained yet"),void this.hls.trigger(d.a.ERROR,{type:o.b.KEY_SYSTEM_ERROR,details:o.a.KEY_SYSTEM_NO_KEYS,fatal:!0});u.b.log("Setting keys for encrypted media"),this._media.setMediaKeys(t.mediaKeys),this._hasSetMediaKeys=!0}},l._generateRequestWithPreferredKeySession=function(t,e){var r=this,i=this._mediaKeysList[0];if(!i)return u.b.error("Fatal: Media is encrypted but not any key-system access has been obtained yet"),void this.hls.trigger(d.a.ERROR,{type:o.b.KEY_SYSTEM_ERROR,details:o.a.KEY_SYSTEM_NO_ACCESS,fatal:!0});if(i.mediaKeysSessionInitialized)u.b.warn("Key-Session already initialized but requested again");else{var a=i.mediaKeysSession;if(!a)return u.b.error("Fatal: Media is encrypted but no key-session existing"),void this.hls.trigger(d.a.ERROR,{type:o.b.KEY_SYSTEM_ERROR,details:o.a.KEY_SYSTEM_NO_SESSION,fatal:!0});if(!e)return u.b.warn("Fatal: initData required for generating a key session is null"),void this.hls.trigger(d.a.ERROR,{type:o.b.KEY_SYSTEM_ERROR,details:o.a.KEY_SYSTEM_NO_INIT_DATA,fatal:!0});u.b.log('Generating key-session request for "'+t+'" init data type'),i.mediaKeysSessionInitialized=!0,a.generateRequest(t,e).then(function(){u.b.debug("Key-session generation succeeded")}).catch(function(t){u.b.error("Error generating key-session request:",t),r.hls.trigger(d.a.ERROR,{type:o.b.KEY_SYSTEM_ERROR,details:o.a.KEY_SYSTEM_NO_SESSION,fatal:!1})})}},l._createLicenseXhr=function(t,e,r){var i=new XMLHttpRequest,a=this._licenseXhrSetup;try{if(a)try{a(i,t)}catch(e){i.open("POST",t,!0),a(i,t)}i.readyState||i.open("POST",t,!0)}catch(t){throw new Error("issue setting up KeySystem license XHR "+t)}return i.responseType="arraybuffer",i.onreadystatechange=this._onLicenseRequestReadyStageChange.bind(this,i,t,e,r),i},l._onLicenseRequestReadyStageChange=function(t,e,r,i){switch(t.readyState){case 4:if(200===t.status)this._requestLicenseFailureCount=0,u.b.log("License request succeeded"),"arraybuffer"!==t.responseType&&u.b.warn("xhr response type was not set to the expected arraybuffer for license request"),i(t.response);else{if(u.b.error("License Request XHR failed ("+e+"). Status: "+t.status+" ("+t.statusText+")"),this._requestLicenseFailureCount++,this._requestLicenseFailureCount>3)return void this.hls.trigger(d.a.ERROR,{type:o.b.KEY_SYSTEM_ERROR,details:o.a.KEY_SYSTEM_LICENSE_REQUEST_FAILED,fatal:!0});var a=3-this._requestLicenseFailureCount+1;u.b.warn("Retrying license request, "+a+" attempts left"),this._requestLicense(r,i)}}},l._generateLicenseRequestChallenge=function(t,e){switch(t.mediaKeySystemDomain){case Oe.WIDEVINE:return e}throw new Error("unsupported key-system: "+t.mediaKeySystemDomain)},l._requestLicense=function(t,e){u.b.log("Requesting content license for key-system");var r=this._mediaKeysList[0];if(!r)return u.b.error("Fatal error: Media is encrypted but no key-system access has been obtained yet"),void this.hls.trigger(d.a.ERROR,{type:o.b.KEY_SYSTEM_ERROR,details:o.a.KEY_SYSTEM_NO_ACCESS,fatal:!0});try{var i=this.getLicenseServerUrl(r.mediaKeySystemDomain),a=this._createLicenseXhr(i,t,e);u.b.log("Sending license request to URL: "+i);var n=this._generateLicenseRequestChallenge(r,t);a.send(n)}catch(t){u.b.error("Failure requesting DRM license: "+t),this.hls.trigger(d.a.ERROR,{type:o.b.KEY_SYSTEM_ERROR,details:o.a.KEY_SYSTEM_LICENSE_REQUEST_FAILED,fatal:!0})}},l.onMediaAttached=function(t){if(this._emeEnabled){var e=t.media;this._media=e,e.addEventListener("encrypted",this._onMediaEncrypted)}},l.onMediaDetached=function(){this._media&&(this._media.removeEventListener("encrypted",this._onMediaEncrypted),this._media=null)},l.onManifestParsed=function(t){if(this._emeEnabled){var e=t.levels.map(function(t){return t.audioCodec}),r=t.levels.map(function(t){return t.videoCodec});this._attemptKeySystemAccess(Oe.WIDEVINE,e,r)}},a=i,(n=[{key:"requestMediaKeySystemAccess",get:function(){if(!this._requestMediaKeySystemAccess)throw new Error("No requestMediaKeySystemAccess function configured");return this._requestMediaKeySystemAccess}}])&&Fe(a.prototype,n),s&&Fe(a,s),i}(h);function Ne(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Ue=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},i=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(r).filter(function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable}))),i.forEach(function(e){Ne(t,e,r[e])})}return t}({autoStartLoad:!0,startPosition:-1,defaultAudioCodec:void 0,debug:!1,capLevelOnFPSDrop:!1,capLevelToPlayerSize:!1,initialLiveManifestSize:1,maxBufferLength:30,maxBufferSize:6e7,maxBufferHole:.5,lowBufferWatchdogPeriod:.5,highBufferWatchdogPeriod:3,nudgeOffset:.1,nudgeMaxRetry:3,maxFragLookUpTolerance:.25,liveSyncDurationCount:3,liveMaxLatencyDurationCount:1/0,liveSyncDuration:void 0,liveMaxLatencyDuration:void 0,liveDurationInfinity:!1,liveBackBufferLength:1/0,maxMaxBufferLength:600,enableWorker:!0,enableSoftwareAES:!0,manifestLoadingTimeOut:1e4,manifestLoadingMaxRetry:1,manifestLoadingRetryDelay:1e3,manifestLoadingMaxRetryTimeout:64e3,startLevel:void 0,levelLoadingTimeOut:1e4,levelLoadingMaxRetry:4,levelLoadingRetryDelay:1e3,levelLoadingMaxRetryTimeout:64e3,fragLoadingTimeOut:2e4,fragLoadingMaxRetry:6,fragLoadingRetryDelay:1e3,fragLoadingMaxRetryTimeout:64e3,startFragPrefetch:!1,fpsDroppedMonitoringPeriod:5e3,fpsDroppedMonitoringThreshold:.2,appendErrorMaxRetry:3,loader:Bt,fLoader:void 0,pLoader:void 0,xhrSetup:void 0,licenseXhrSetup:void 0,abrController:kt,bufferController:Ot,capLevelController:Pt,fpsController:Ft,stretchShortVideoTrack:!1,maxAudioFramesDrift:1,forceKeyFrameOnDiscontinuity:!0,abrEwmaFastLive:3,abrEwmaSlowLive:9,abrEwmaFastVoD:3,abrEwmaSlowVoD:9,abrEwmaDefaultEstimate:5e5,abrBandWidthFactor:.95,abrBandWidthUpFactor:.7,abrMaxWithRealBitrate:!1,maxStarvationDelay:4,maxLoadingDelay:4,minAutoBitrate:0,emeEnabled:!1,widevineLicenseUrl:void 0,requestMediaKeySystemAccessFunc:xe},function(){0;return{cueHandler:i,enableCEA708Captions:!0,enableWebVTT:!0,captionsTextTrack1Label:"English",captionsTextTrack1LanguageCode:"en",captionsTextTrack2Label:"Spanish",captionsTextTrack2LanguageCode:"es"}}(),{subtitleStreamController:Pe,subtitleTrackController:ke,timelineController:Le,audioStreamController:Vt,audioTrackController:Kt,emeController:Me});function Be(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ge(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Ke(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function je(t,e,r){return e&&Ke(t.prototype,e),r&&Ke(t,r),t}r.d(e,"default",function(){return He});var He=function(t){var e,r;function i(e){var r;void 0===e&&(e={}),(r=t.call(this)||this).config=void 0,r._autoLevelCapping=void 0,r.abrController=void 0,r.capLevelController=void 0,r.levelController=void 0,r.streamController=void 0,r.networkControllers=void 0,r.audioTrackController=void 0,r.subtitleTrackController=void 0,r.emeController=void 0,r.coreComponents=void 0,r.media=null,r.url=null;var a=i.DefaultConfig;if((e.liveSyncDurationCount||e.liveMaxLatencyDurationCount)&&(e.liveSyncDuration||e.liveMaxLatencyDuration))throw new Error("Illegal hls.js config: don't mix up liveSyncDurationCount/liveMaxLatencyDurationCount and liveSyncDuration/liveMaxLatencyDuration");r.config=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},i=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(r).filter(function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable}))),i.forEach(function(e){Be(t,e,r[e])})}return t}({},a,e);var n=Ge(r).config;if(void 0!==n.liveMaxLatencyDurationCount&&n.liveMaxLatencyDurationCount<=n.liveSyncDurationCount)throw new Error('Illegal hls.js config: "liveMaxLatencyDurationCount" must be gt "liveSyncDurationCount"');if(void 0!==n.liveMaxLatencyDuration&&(void 0===n.liveSyncDuration||n.liveMaxLatencyDuration<=n.liveSyncDuration))throw new Error('Illegal hls.js config: "liveMaxLatencyDuration" must be gt "liveSyncDuration"');Object(u.a)(n.debug),r._autoLevelCapping=-1;var s=r.abrController=new n.abrController(Ge(r)),o=new n.bufferController(Ge(r)),l=r.capLevelController=new n.capLevelController(Ge(r)),d=new n.fpsController(Ge(r)),c=new x(Ge(r)),h=new F(Ge(r)),f=new M(Ge(r)),g=new At(Ge(r)),p=r.levelController=new Et(Ge(r)),v=new K(Ge(r)),m=[p,r.streamController=new mt(Ge(r),v)],y=n.audioStreamController;y&&m.push(new y(Ge(r),v)),r.networkControllers=m;var b=[c,h,f,s,o,l,d,g,v];if(y=n.audioTrackController){var E=new y(Ge(r));r.audioTrackController=E,b.push(E)}if(y=n.subtitleTrackController){var T=new y(Ge(r));r.subtitleTrackController=T,m.push(T)}if(y=n.emeController){var S=new y(Ge(r));r.emeController=S,b.push(S)}return(y=n.subtitleStreamController)&&m.push(new y(Ge(r),v)),(y=n.timelineController)&&b.push(new y(Ge(r))),r.coreComponents=b,r}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r,i.isSupported=function(){return function(){var t=q();if(!t)return!1;var e=SourceBuffer||window.WebKitSourceBuffer,r=t&&"function"==typeof t.isTypeSupported&&t.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"'),i=!e||e.prototype&&"function"==typeof e.prototype.appendBuffer&&"function"==typeof e.prototype.remove;return!!r&&!!i}()},je(i,null,[{key:"version",get:function(){}},{key:"Events",get:function(){return d.a}},{key:"ErrorTypes",get:function(){return o.b}},{key:"ErrorDetails",get:function(){return o.a}},{key:"DefaultConfig",get:function(){return i.defaultConfig?i.defaultConfig:Ue},set:function(t){i.defaultConfig=t}}]);var a=i.prototype;return a.destroy=function(){u.b.log("destroy"),this.trigger(d.a.DESTROYING),this.detachMedia(),this.coreComponents.concat(this.networkControllers).forEach(function(t){t.destroy()}),this.url=null,this.removeAllListeners(),this._autoLevelCapping=-1},a.attachMedia=function(t){u.b.log("attachMedia"),this.media=t,this.trigger(d.a.MEDIA_ATTACHING,{media:t})},a.detachMedia=function(){u.b.log("detachMedia"),this.trigger(d.a.MEDIA_DETACHING),this.media=null},a.loadSource=function(t){t=s.buildAbsoluteURL(window.location.href,t,{alwaysNormalize:!0}),u.b.log("loadSource:"+t),this.url=t,this.trigger(d.a.MANIFEST_LOADING,{url:t})},a.startLoad=function(t){void 0===t&&(t=-1),u.b.log("startLoad("+t+")"),this.networkControllers.forEach(function(e){e.startLoad(t)})},a.stopLoad=function(){u.b.log("stopLoad"),this.networkControllers.forEach(function(t){t.stopLoad()})},a.swapAudioCodec=function(){u.b.log("swapAudioCodec"),this.streamController.swapAudioCodec()},a.recoverMediaError=function(){u.b.log("recoverMediaError");var t=this.media;this.detachMedia(),t&&this.attachMedia(t)},je(i,[{key:"levels",get:function(){return this.levelController.levels}},{key:"currentLevel",get:function(){return this.streamController.currentLevel},set:function(t){u.b.log("set currentLevel:"+t),this.loadLevel=t,this.streamController.immediateLevelSwitch()}},{key:"nextLevel",get:function(){return this.streamController.nextLevel},set:function(t){u.b.log("set nextLevel:"+t),this.levelController.manualLevel=t,this.streamController.nextLevelSwitch()}},{key:"loadLevel",get:function(){return this.levelController.level},set:function(t){u.b.log("set loadLevel:"+t),this.levelController.manualLevel=t}},{key:"nextLoadLevel",get:function(){return this.levelController.nextLoadLevel},set:function(t){this.levelController.nextLoadLevel=t}},{key:"firstLevel",get:function(){return Math.max(this.levelController.firstLevel,this.minAutoLevel)},set:function(t){u.b.log("set firstLevel:"+t),this.levelController.firstLevel=t}},{key:"startLevel",get:function(){return this.levelController.startLevel},set:function(t){u.b.log("set startLevel:"+t),-1!==t&&(t=Math.max(t,this.minAutoLevel)),this.levelController.startLevel=t}},{key:"capLevelToPlayerSize",set:function(t){var e=!!t;e!==this.config.capLevelToPlayerSize&&(e?this.capLevelController.startCapping():(this.capLevelController.stopCapping(),this.autoLevelCapping=-1,this.streamController.nextLevelSwitch()),this.config.capLevelToPlayerSize=e)}},{key:"autoLevelCapping",get:function(){return this._autoLevelCapping},set:function(t){u.b.log("set autoLevelCapping:"+t),this._autoLevelCapping=t}},{key:"bandwidthEstimate",get:function(){var t=this.abrController._bwEstimator;return t?t.getEstimate():NaN}},{key:"autoLevelEnabled",get:function(){return-1===this.levelController.manualLevel}},{key:"manualLevel",get:function(){return this.levelController.manualLevel}},{key:"minAutoLevel",get:function(){for(var t=this.levels,e=this.config.minAutoBitrate,r=t?t.length:0,i=0;i<r;i++){if((t[i].realBitrate?Math.max(t[i].realBitrate,t[i].bitrate):t[i].bitrate)>e)return i}return 0}},{key:"maxAutoLevel",get:function(){var t=this.levels,e=this.autoLevelCapping;return-1===e&&t&&t.length?t.length-1:e}},{key:"nextAutoLevel",get:function(){return Math.min(Math.max(this.abrController.nextAutoLevel,this.minAutoLevel),this.maxAutoLevel)},set:function(t){this.abrController.nextAutoLevel=Math.max(this.minAutoLevel,t)}},{key:"audioTracks",get:function(){var t=this.audioTrackController;return t?t.audioTracks:[]}},{key:"audioTrack",get:function(){var t=this.audioTrackController;return t?t.audioTrack:-1},set:function(t){var e=this.audioTrackController;e&&(e.audioTrack=t)}},{key:"liveSyncPosition",get:function(){return this.streamController.liveSyncPosition}},{key:"subtitleTracks",get:function(){var t=this.subtitleTrackController;return t?t.subtitleTracks:[]}},{key:"subtitleTrack",get:function(){var t=this.subtitleTrackController;return t?t.subtitleTrack:-1},set:function(t){var e=this.subtitleTrackController;e&&(e.subtitleTrack=t)}},{key:"subtitleDisplay",get:function(){var t=this.subtitleTrackController;return!!t&&t.subtitleDisplay},set:function(t){var e=this.subtitleTrackController;e&&(e.subtitleDisplay=t)}}]),i}(X);He.defaultConfig=void 0}]).default});
//# sourceMappingURL=hls.min.js.map