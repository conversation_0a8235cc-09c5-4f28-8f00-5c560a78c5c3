/*!
 * NeosUI
 * version 1.0.1
 * releaseTime 2024-08-07 19:46
 * (c) 2024 NeosUI
 */
(function(t,e){"object"===typeof exports&&"object"===typeof module?module.exports=e(require("vue")):"function"===typeof define&&define.amd?define("NeosUI",["vue"],e):"object"===typeof exports?exports["NeosUI"]=e(require("vue")):t["NeosUI"]=e(t["Vue"])})("undefined"!==typeof self?self:this,(function(t){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var i=e[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)n.d(r,i,function(e){return t[e]}.bind(null,i));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s="fb15")}({"00b4":function(t,e,n){"use strict";n("ac1f");var r=n("23e7"),i=n("c65b"),o=n("1626"),a=n("825a"),s=n("577e"),c=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),u=/./.test;r({target:"RegExp",proto:!0,forced:!c},{test:function(t){var e=a(this),n=s(t),r=e.exec;if(!o(r))return i(u,e,n);var c=i(r,e,n);return null!==c&&(a(c),!0)}})},"00ee":function(t,e,n){"use strict";var r=n("b622"),i=r("toStringTag"),o={};o[i]="z",t.exports="[object z]"===String(o)},"01b4":function(t,e,n){"use strict";var r=function(){this.head=null,this.tail=null};r.prototype={add:function(t){var e={item:t,next:null},n=this.tail;n?n.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t){var e=this.head=t.next;return null===e&&(this.tail=null),t.item}}},t.exports=r},"0366":function(t,e,n){"use strict";var r=n("4625"),i=n("59ed"),o=n("40d5"),a=r(r.bind);t.exports=function(t,e){return i(t),void 0===e?t:o?a(t,e):function(){return t.apply(e,arguments)}}},"04f8":function(t,e,n){"use strict";var r=n("1212"),i=n("d039"),o=n("cfe9"),a=o.String;t.exports=!!Object.getOwnPropertySymbols&&!i((function(){var t=Symbol("symbol detection");return!a(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&r&&r<41}))},"0530":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("cb29"),n("c96a");var i=r(n("d4ec")),o=r(n("bee2")),a=r(n("8f33")),s=r(n("262e")),c=n("26a1"),u=n("faa1"),f=function(t){return Array.isArray(t)},d=!c.isServer&&(window.requestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},l=!c.isServer&&(window.cancelAnimationFrame||window.webkitCancelAnimationFrame)||function(t){return clearTimeout(t)};e.default=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,i.default)(this,e),t=(0,a.default)(this,e),t.options=n,t.ctx=null,t.rotate=null,t.stepRing=60,t.animationStep=60,t.animation=null,t.countDownTime=0,t.init(),t}return(0,s.default)(e,t),(0,o.default)(e,[{key:"init",value:function(){var t=this.options,e=t.dom,n=t.size,r=t.background,i=t.borderWidth,o=t.borderColor,a=t.pointerWidth,s=t.pointerColor,c=t.text,u=t.scale,f=t.countDownTime,d=t.ratio,l=void 0===d?2:d;e.setAttribute("width",n*l),e.setAttribute("height",n*l),e.style["width"]="".concat(n,"px"),e.style["height"]="".concat(n,"px"),this.ctx=e.getContext("2d"),this.ctx.scale(l,l),this.stepRing++,this.rotate=this.stepRing*(Math.PI/(f/1e3*this.animationStep/2)),this.countDownTime=f/1e3,this.text=c,this.drawBg(n,r),u&&this.drawScale(n,u,i),this.drawPointer(s,a,n),this.drawBorder(i,o,n),this.emit("remain",f-Math.floor(this.stepRing/60)),this.start()}},{key:"start",value:function(){this.animation=d(this.init.bind(this)),this.stepRing===this.countDownTime*this.animationStep&&(l(this.animation),this.emit("complete"))}},{key:"destroy",value:function(){this.animation&&(l(this.animation),this.ctx=null,this.rotate=null,this.stepRing=60,this.animationStep=60,this.animation=null,this.countDownTime=0)}},{key:"drawBg",value:function(t,e){var n,r=f(e);this.ctx.save(),this.ctx.clearRect(0,0,t,t),this.ctx.translate(t/2,t/2),this.ctx.rotate(-Math.PI/2),r&&e.length>=2?(n=this.ctx.createLinearGradient(t/2,0,-t/2,0),n.addColorStop(0,e[0]),n.addColorStop(1,e[1])):n=e,this.ctx.fillStyle=n,this.ctx.beginPath(),this.ctx.arc(0,0,t/2,0,2*Math.PI,!0),this.ctx.fill()}},{key:"drawScale",value:function(t,e,n){var r=e.color,i=void 0===r?"#ffffff":r,o=e.lineWidth,a=void 0===o?3:o;this.ctx.save();for(var s=0;s<12;s++)this.ctx.beginPath(),this.ctx.strokeStyle=i,this.ctx.lineWidth=a,this.ctx.rotate(Math.PI/6),this.ctx.moveTo(t/2-a+3-n,0),this.ctx.lineTo(t/2-a-6-n,0),this.ctx.lineCap="round",this.ctx.stroke();if(this.ctx.restore(),e.small){this.ctx.save();for(var c=0;c<60;c++)c%5!==0&&(this.ctx.beginPath(),this.ctx.strokeStyle=i,this.ctx.lineWidth=a-2,this.ctx.moveTo(t/2-a+3-n,0),this.ctx.lineTo(t/2-a-15-n,0),this.ctx.stroke()),this.ctx.rotate(Math.PI/30);this.ctx.restore()}}},{key:"drawText",value:function(t,e,n){var r=t.content,i=t.color,o=void 0===i?"#ffffff":i,a=t.font,s=void 0===a?"20px Microsoft yahei":a;if(r.length){var c=e/2-30-n;this.ctx.save(),this.ctx.rotate(Math.PI/2),this.ctx.beginPath(),this.ctx.fillStyle=o,this.ctx.font=s,this.ctx.textAlign="center",this.ctx.textBaseline="middle",this.ctx.fillText(r[0],0,-c),this.ctx.fillText(r[1],c,0),this.ctx.fillText(r[2],0,c),this.ctx.fillText(r[3],-c,0),this.ctx.restore(),this.ctx.save(),this.ctx.rotate(Math.PI/2),this.ctx.beginPath(),this.ctx.fillStyle=o,this.ctx.font=s,this.ctx.textAlign="center",this.ctx.textBaseline="middle",this.ctx.fillText("剩余 "+(this.countDownTime-Math.floor(this.stepRing/60))+" 秒",0,80),this.ctx.restore()}}},{key:"drawPointer",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2?arguments[2]:void 0;this.ctx.save(),this.ctx.rotate(0),this.ctx.lineWidth=e||.0525*n,this.ctx.strokeStyle=t,this.ctx.beginPath(),this.ctx.lineCap="round",this.ctx.moveTo(0,0),this.ctx.lineTo(n/3.5,0),this.ctx.stroke(),this.ctx.save(),this.ctx.rotate(this.rotate),this.ctx.beginPath(),this.ctx.lineWidth=e||.0525*n,this.ctx.strokeStyle=t,this.ctx.lineCap="round",this.ctx.moveTo(0,0),this.ctx.lineTo(n/3.5,0),this.ctx.stroke()}},{key:"drawBorder",value:function(t,e,n){var r,i=f(e);this.ctx.beginPath(),this.ctx.lineWidth=t,i&&e.length>=2?(r=this.ctx.createLinearGradient(n/2,0,-n/2,0),r.addColorStop(0,e[0]),r.addColorStop(1,e[1])):r=e,this.ctx.strokeStyle=r,this.ctx.arc(0,0,n/2-t/2.2,0,2*Math.PI,!0),this.ctx.stroke(),this.ctx.restore()}}])}(u.EventEmitter)},"0538":function(t,e,n){"use strict";var r=n("e330"),i=n("59ed"),o=n("861d"),a=n("1a2d"),s=n("f36a"),c=n("40d5"),u=Function,f=r([].concat),d=r([].join),l={},h=function(t,e,n){if(!a(l,e)){for(var r=[],i=0;i<e;i++)r[i]="a["+i+"]";l[e]=u("C,a","return new C("+d(r,",")+")")}return l[e](t,n)};t.exports=c?u.bind:function(t){var e=i(this),n=e.prototype,r=s(arguments,1),a=function(){var n=f(r,s(arguments));return this instanceof a?h(e,n.length,n):e.apply(t,n)};return o(n)&&(a.prototype=n),a}},"056e":function(t,e,n){"use strict";var r=n("6ae3");n.o(r,"render")&&n.d(e,"render",(function(){return r["render"]})),n.o(r,"staticRenderFns")&&n.d(e,"staticRenderFns",(function(){return r["staticRenderFns"]}))},"057f":function(t,e,n){"use strict";var r=n("c6b6"),i=n("fc6a"),o=n("241c").f,a=n("f36a"),s="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],c=function(t){try{return o(t)}catch(e){return a(s)}};t.exports.f=function(t){return s&&"Window"===r(t)?c(t):o(i(t))}},"05d5":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0");var i=r(n("6025"));i.default.install=function(t){t.component(i.default.name,i.default)};e.default=i.default},"0662":function(t,e,n){"use strict";n.r(e);var r=n("1b85"),i=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=i.a},"06c5":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=o,n("a630"),n("fb6a"),n("b0c0"),n("d3b7"),n("ac1f"),n("00b4"),n("25f0"),n("3ca3");var r=i(n("6b75"));function i(t){return t&&t.__esModule?t:{default:t}}function o(t,e){if(t){if("string"==typeof t)return(0,r.default)(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?(0,r.default)(t,e):void 0}}},"06cf":function(t,e,n){"use strict";var r=n("83ab"),i=n("c65b"),o=n("d1e7"),a=n("5c6c"),s=n("fc6a"),c=n("a04b"),u=n("1a2d"),f=n("0cfb"),d=Object.getOwnPropertyDescriptor;e.f=r?d:function(t,e){if(t=s(t),e=c(e),f)try{return d(t,e)}catch(n){}if(u(t,e))return a(!i(o.f,t,e),t[e])}},"073b":function(t,e){t.exports="data:image/png;base64,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"},"07fa":function(t,e,n){"use strict";var r=n("50c4");t.exports=function(t){return r(t.length)}},"07fc":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0");var i=r(n("ac62"));i.default.install=function(t){t.component(i.default.name,i.default)};e.default=i.default},"083a":function(t,e,n){"use strict";var r=n("0d51"),i=TypeError;t.exports=function(t,e){if(!delete t[e])throw new i("Cannot delete property "+r(e)+" of "+r(t))}},"0b25":function(t,e,n){"use strict";var r=n("5926"),i=n("50c4"),o=RangeError;t.exports=function(t){if(void 0===t)return 0;var e=r(t),n=i(e);if(e!==n)throw new o("Wrong length or index");return n}},"0b42":function(t,e,n){"use strict";var r=n("e8b5"),i=n("68ee"),o=n("861d"),a=n("b622"),s=a("species"),c=Array;t.exports=function(t){var e;return r(t)&&(e=t.constructor,i(e)&&(e===c||r(e.prototype))?e=void 0:o(e)&&(e=e[s],null===e&&(e=void 0))),void 0===e?c:e}},"0b43":function(t,e,n){"use strict";var r=n("04f8");t.exports=r&&!!Symbol["for"]&&!!Symbol.keyFor},"0c47":function(t,e,n){"use strict";var r=n("cfe9"),i=n("d44e");i(r.JSON,"JSON",!0)},"0c69":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0");var i=r(n("786b"));i.default.install=function(t){t.component(i.default.name,i.default)};e.default=i.default},"0c7d":function(t,e,n){"use strict";var r=n("15ec");n.o(r,"render")&&n.d(e,"render",(function(){return r["render"]})),n.o(r,"staticRenderFns")&&n.d(e,"staticRenderFns",(function(){return r["staticRenderFns"]}))},"0cb2":function(t,e,n){"use strict";var r=n("e330"),i=n("7b0b"),o=Math.floor,a=r("".charAt),s=r("".replace),c=r("".slice),u=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,f=/\$([$&'`]|\d{1,2})/g;t.exports=function(t,e,n,r,d,l){var h=n+t.length,p=r.length,v=f;return void 0!==d&&(d=i(d),v=u),s(l,v,(function(i,s){var u;switch(a(s,0)){case"$":return"$";case"&":return t;case"`":return c(e,0,n);case"'":return c(e,h);case"<":u=d[c(s,1,-1)];break;default:var f=+s;if(0===f)return i;if(f>p){var l=o(f/10);return 0===l?i:l<=p?void 0===r[l-1]?a(s,1):r[l-1]+a(s,1):i}u=r[f-1]}return void 0===u?"":u}))}},"0cfb":function(t,e,n){"use strict";var r=n("83ab"),i=n("d039"),o=n("cc12");t.exports=!r&&!i((function(){return 7!==Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a}))},"0d26":function(t,e,n){"use strict";var r=n("e330"),i=Error,o=r("".replace),a=function(t){return String(new i(t).stack)}("zxcasd"),s=/\n\s*at [^:]*:[^\n]*/,c=s.test(a);t.exports=function(t,e){if(c&&"string"==typeof t&&!i.prepareStackTrace)while(e--)t=o(t,s,"");return t}},"0d51":function(t,e,n){"use strict";var r=String;t.exports=function(t){try{return r(t)}catch(e){return"Object"}}},"0dbd":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0");var i=r(n("c59e"));i.default.install=function(t){t.component(i.default.name,i.default)};e.default=i.default},"0e6d":function(t,e,n){"use strict";var r=n("fd1d");n.o(r,"render")&&n.d(e,"render",(function(){return r["render"]})),n.o(r,"staticRenderFns")&&n.d(e,"staticRenderFns",(function(){return r["staticRenderFns"]}))},"0eb6":function(t,e,n){"use strict";var r=n("23e7"),i=n("d066"),o=n("2a07"),a=n("d039"),s=n("7c73"),c=n("5c6c"),u=n("9bf2").f,f=n("cb2d"),d=n("edd0"),l=n("1a2d"),h=n("19aa"),p=n("825a"),v=n("aa1f"),b=n("e391"),m=n("cf98"),g=n("0d26"),y=n("69f3"),x=n("83ab"),w=n("c430"),_="DOMException",A="DATA_CLONE_ERR",O=i("Error"),S=i(_)||function(){try{var t=i("MessageChannel")||o("worker_threads").MessageChannel;(new t).port1.postMessage(new WeakMap)}catch(e){if(e.name===A&&25===e.code)return e.constructor}}(),E=S&&S.prototype,T=O.prototype,C=y.set,R=y.getterFor(_),k="stack"in new O(_),j=function(t){return l(m,t)&&m[t].m?m[t].c:0},I=function(){h(this,M);var t=arguments.length,e=b(t<1?void 0:arguments[0]),n=b(t<2?void 0:arguments[1],"Error"),r=j(n);if(C(this,{type:_,name:n,message:e,code:r}),x||(this.name=n,this.message=e,this.code=r),k){var i=new O(e);i.name=_,u(this,"stack",c(1,g(i.stack,1)))}},M=I.prototype=s(T),L=function(t){return{enumerable:!0,configurable:!0,get:t}},P=function(t){return L((function(){return R(this)[t]}))};x&&(d(M,"code",P("code")),d(M,"message",P("message")),d(M,"name",P("name"))),u(M,"constructor",c(1,I));var F=a((function(){return!(new S instanceof O)})),N=F||a((function(){return T.toString!==v||"2: 1"!==String(new S(1,2))})),D=F||a((function(){return 25!==new S(1,"DataCloneError").code})),B=F||25!==S[A]||25!==E[A],G=w?N||D||B:F;r({global:!0,constructor:!0,forced:G},{DOMException:G?I:S});var z=i(_),U=z.prototype;for(var $ in N&&(w||S===z)&&f(U,"toString",v),D&&x&&S===z&&d(U,"code",L((function(){return j(p(this).name)}))),m)if(l(m,$)){var V=m[$],W=V.s,q=c(6,V.c);l(z,W)||u(z,W,q),l(U,W)||u(U,W,q)}},"107c":function(t,e,n){"use strict";var r=n("d039"),i=n("cfe9"),o=i.RegExp;t.exports=r((function(){var t=o("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")}))},"10ba":function(t,e,n){var r=n("4f1a");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var i=n("499e").default;i("e47efcf2",r,!0,{sourceMap:!1,shadowMode:!1})},"113c":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0");var i=r(n("f9aa"));i.default.install=function(t){t.component(i.default.name,i.default)};e.default=i.default},1212:function(t,e,n){"use strict";var r,i,o=n("cfe9"),a=n("b5db"),s=o.process,c=o.Deno,u=s&&s.versions||c&&c.version,f=u&&u.v8;f&&(r=f.split("."),i=r[0]>0&&r[0]<4?1:+(r[0]+r[1])),!i&&a&&(r=a.match(/Edge\/(\d+)/),(!r||r[1]>=74)&&(r=a.match(/Chrome\/(\d+)/),r&&(i=+r[1]))),t.exports=i},"129f":function(t,e,n){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},"131a":function(t,e,n){"use strict";var r=n("23e7"),i=n("d2bb");r({target:"Object",stat:!0},{setPrototypeOf:i})},"13a6":function(t,e,n){"use strict";var r=Math.round;t.exports=function(t){var e=r(t);return e<0?0:e>255?255:255&e}},"13d2":function(t,e,n){"use strict";var r=n("e330"),i=n("d039"),o=n("1626"),a=n("1a2d"),s=n("83ab"),c=n("5e77").CONFIGURABLE,u=n("8925"),f=n("69f3"),d=f.enforce,l=f.get,h=String,p=Object.defineProperty,v=r("".slice),b=r("".replace),m=r([].join),g=s&&!i((function(){return 8!==p((function(){}),"length",{value:8}).length})),y=String(String).split("String"),x=t.exports=function(t,e,n){"Symbol("===v(h(e),0,7)&&(e="["+b(h(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),n&&n.getter&&(e="get "+e),n&&n.setter&&(e="set "+e),(!a(t,"name")||c&&t.name!==e)&&(s?p(t,"name",{value:e,configurable:!0}):t.name=e),g&&n&&a(n,"arity")&&t.length!==n.arity&&p(t,"length",{value:n.arity});try{n&&a(n,"constructor")&&n.constructor?s&&p(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(i){}var r=d(t);return a(r,"source")||(r.source=m(y,"string"==typeof e?e:"")),t};Function.prototype.toString=x((function(){return o(this)&&l(this).source||u(this)}),"toString")},"13d5":function(t,e,n){"use strict";var r=n("23e7"),i=n("d58f").left,o=n("a640"),a=n("1212"),s=n("9adc"),c=!s&&a>79&&a<83,u=c||!o("reduce");r({target:"Array",proto:!0,forced:u},{reduce:function(t){var e=arguments.length;return i(this,t,e,e>1?arguments[1]:void 0)}})},1448:function(t,e,n){"use strict";var r=n("dfb9"),i=n("b6b7");t.exports=function(t,e){return r(i(t),e)}},"145e":function(t,e,n){"use strict";var r=n("7b0b"),i=n("23cb"),o=n("07fa"),a=n("083a"),s=Math.min;t.exports=[].copyWithin||function(t,e){var n=r(this),c=o(n),u=i(t,c),f=i(e,c),d=arguments.length>2?arguments[2]:void 0,l=s((void 0===d?c:i(d,c))-f,c-u),h=1;f<u&&u<f+l&&(h=-1,f+=l-1,u+=l-1);while(l-- >0)f in n?n[u]=n[f]:a(n,u),u+=h,f+=h;return n}},"14c3":function(t,e,n){"use strict";var r=n("c65b"),i=n("825a"),o=n("1626"),a=n("c6b6"),s=n("9263"),c=TypeError;t.exports=function(t,e){var n=t.exec;if(o(n)){var u=r(n,t,e);return null!==u&&i(u),u}if("RegExp"===a(t))return r(s,t,e);throw new c("RegExp#exec called on incompatible receiver")}},"14d9":function(t,e,n){"use strict";var r=n("23e7"),i=n("7b0b"),o=n("07fa"),a=n("3a34"),s=n("3511"),c=n("d039"),u=c((function(){return 4294967297!==[].push.call({length:4294967296},1)})),f=function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(t){return t instanceof TypeError}},d=u||!f();r({target:"Array",proto:!0,arity:1,forced:d},{push:function(t){var e=i(this),n=o(e),r=arguments.length;s(n+r);for(var c=0;c<r;c++)e[n]=arguments[c],n++;return a(e,n),n}})},"14e5":function(t,e,n){"use strict";var r=n("23e7"),i=n("c65b"),o=n("59ed"),a=n("f069"),s=n("e667"),c=n("2266"),u=n("5eed");r({target:"Promise",stat:!0,forced:u},{all:function(t){var e=this,n=a.f(e),r=n.resolve,u=n.reject,f=s((function(){var n=o(e.resolve),a=[],s=0,f=1;c(t,(function(t){var o=s++,c=!1;f++,i(n,e,t).then((function(t){c||(c=!0,a[o]=t,--f||r(a))}),u)})),--f||r(a)}));return f.error&&u(f.value),n.promise}})},"157a":function(t,e,n){"use strict";var r=n("cfe9"),i=n("83ab"),o=Object.getOwnPropertyDescriptor;t.exports=function(t){if(!i)return r[t];var e=o(r,t);return e&&e.value}},"159b":function(t,e,n){"use strict";var r=n("cfe9"),i=n("fdbc"),o=n("785a"),a=n("17c2"),s=n("9112"),c=function(t){if(t&&t.forEach!==a)try{s(t,"forEach",a)}catch(e){t.forEach=a}};for(var u in i)i[u]&&c(r[u]&&r[u].prototype);c(o)},"15ec":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0;e.render=function(){var t=this,e=t._self._c;return e("canvas",{ref:"countdown",staticClass:"ne-canvas-countdown",attrs:{id:"countdown_"+t.uuid}})},e.staticRenderFns=[]},1626:function(t,e,n){"use strict";var r="object"==typeof document&&document.all;t.exports="undefined"==typeof r&&void 0!==r?function(t){return"function"==typeof t||t===r}:function(t){return"function"==typeof t}},"170b":function(t,e,n){"use strict";var r=n("ebb5"),i=n("50c4"),o=n("23cb"),a=n("b6b7"),s=r.aTypedArray,c=r.exportTypedArrayMethod;c("subarray",(function(t,e){var n=s(this),r=n.length,c=o(t,r),u=a(n);return new u(n.buffer,n.byteOffset+c*n.BYTES_PER_ELEMENT,i((void 0===e?r:o(e,r))-c))}))},1787:function(t,e,n){"use strict";var r=n("861d");t.exports=function(t){return r(t)||null===t}},"17c2":function(t,e,n){"use strict";var r=n("b727").forEach,i=n("a640"),o=i("forEach");t.exports=o?[].forEach:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}},"182d":function(t,e,n){"use strict";var r=n("f8cd"),i=RangeError;t.exports=function(t,e){var n=r(t);if(n%e)throw new i("Wrong offset");return n}},1838:function(t,e,n){t.exports=n.p+"static/img/wait-bg.200613b4.jpg"},1882:function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0");var i=r(n("9b68"));i.default.install=function(t){t.component(i.default.name,i.default)};e.default=i.default},"198c":function(t,e,n){"use strict";n.r(e);var r=n("8b86"),i=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=i.a},"19aa":function(t,e,n){"use strict";var r=n("3a9b"),i=TypeError;t.exports=function(t,e){if(r(e,t))return t;throw new i("Incorrect invocation")}},"1a2d":function(t,e,n){"use strict";var r=n("e330"),i=n("7b0b"),o=r({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return o(i(t),e)}},"1b85":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={name:"NeButton",inject:{neForm:{default:""},neFormItem:{default:""}},props:{type:{type:String,default:"default"},size:String,icon:{type:String,default:""},nativeType:{type:String,default:"button"},loading:Boolean,disabled:Boolean,plain:Boolean,autofocus:Boolean,round:Boolean,circle:Boolean},computed:{_neFormItemSize:function(){return(this.neFormItem||{}).neFormItemSize},buttonSize:function(){return this.size||this._neFormItemSize||(this.$NeosUI||{}).size},buttonDisabled:function(){return this.disabled||(this.elForm||{}).disabled}},methods:{handleClick:function(t){this.$emit("click",t)}}}},"1be4":function(t,e,n){"use strict";var r=n("d066");t.exports=r("document","documentElement")},"1c7e":function(t,e,n){"use strict";var r=n("b622"),i=r("iterator"),o=!1;try{var a=0,s={next:function(){return{done:!!a++}},return:function(){o=!0}};s[i]=function(){return this},Array.from(s,(function(){throw 2}))}catch(c){}t.exports=function(t,e){try{if(!e&&!o)return!1}catch(c){return!1}var n=!1;try{var r={};r[i]=function(){return{next:function(){return{done:n=!0}}}},t(r)}catch(c){}return n}},"1d02":function(t,e,n){"use strict";var r=n("ebb5"),i=n("a258").findLastIndex,o=r.aTypedArray,a=r.exportTypedArrayMethod;a("findLastIndex",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},"1d80":function(t,e,n){"use strict";var r=n("7234"),i=TypeError;t.exports=function(t){if(r(t))throw new i("Can't call method on "+t);return t}},"1da1":function(t,e,n){"use strict";function r(t,e,n,r,i,o,a){try{var s=t[o](a),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(r,i)}function i(t){return function(){var e=this,n=arguments;return new Promise((function(i,o){var a=t.apply(e,n);function s(t){r(a,i,o,s,c,"next",t)}function c(t){r(a,i,o,s,c,"throw",t)}s(void 0)}))}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=i,n("d3b7"),n("e6cf")},"1dde":function(t,e,n){"use strict";var r=n("d039"),i=n("b622"),o=n("1212"),a=i("species");t.exports=function(t){return o>=51||!r((function(){var e=[],n=e.constructor={};return n[a]=function(){return{foo:1}},1!==e[t](Boolean).foo}))}},"1de5":function(t,e,n){"use strict";t.exports=function(t,e){return e||(e={}),t=t&&t.__esModule?t.default:t,"string"!==typeof t?t:(/^['"].*['"]$/.test(t)&&(t=t.slice(1,-1)),e.hash&&(t+=e.hash),/["'() \t\n]/.test(t)||e.needQuotes?'"'.concat(t.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):t)}},"1eb2":function(t,e,n){"use strict";if("undefined"!==typeof window){var r=window.document.currentScript,i=n("8875");r=i(),"currentScript"in document||Object.defineProperty(document,"currentScript",{get:i});var o=r&&r.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);o&&(n.p=o[1])}},"1f68":function(t,e,n){"use strict";var r=n("83ab"),i=n("edd0"),o=n("861d"),a=n("1787"),s=n("7b0b"),c=n("1d80"),u=Object.getPrototypeOf,f=Object.setPrototypeOf,d=Object.prototype,l="__proto__";if(r&&u&&f&&!(l in d))try{i(d,l,{configurable:!0,get:function(){return u(s(this))},set:function(t){var e=c(this);a(t)&&o(e)&&f(e,t)}})}catch(h){}},2005:function(t,e,n){"use strict";var r=n("75bd"),i=TypeError;t.exports=function(t){if(r(t))throw new i("ArrayBuffer is detached");return t}},2115:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af"),n("a9e3");var r=n("ca00"),i=.83;e.default={name:"NeCarouselItem",props:{name:String,label:{type:[String,Number],default:""}},data:function(){return{hover:!1,translate:0,scale:1,active:!1,ready:!1,inStage:!1,animating:!1}},computed:{parentDirection:function(){return this.$parent.direction},itemStyle:function(){var t="vertical"===this.parentDirection?"translateY":"translateX",e="".concat(t,"(").concat(this.translate,"px) scale(").concat(this.scale,")"),n={transform:e};return(0,r.autoprefixer)(n)}},created:function(){this.$parent&&this.$parent.updateItems()},destroyed:function(){this.$parent&&this.$parent.updateItems()},methods:{processIndex:function(t,e,n){return 0===e&&t===n-1?-1:e===n-1&&0===t?n:t<e-1&&e-t>=n/2?n+1:t>e+1&&t-e>=n/2?-2:t},calcCardTranslate:function(t,e){var n=this.$parent.$el.offsetWidth;return this.inStage?n*((2-i)*(t-e)+1)/4:t<e?-(1+i)*n/4:(3+i)*n/4},calcTranslate:function(t,e,n){var r=this.$parent.$el[n?"offsetHeight":"offsetWidth"];return r*(t-e)},translateItem:function(t,e,n){var r=this.$parent.type,o=this.parentDirection,a=this.$parent.items.length;if("card"!==r&&void 0!==n&&(this.animating=t===e||t===n),t!==e&&a>2&&this.$parent.loop&&(t=this.processIndex(t,e,a)),"card"===r)this.inStage=Math.round(Math.abs(t-e))<=1,this.active=t===e,this.translate=this.calcCardTranslate(t,e),this.scale=this.active?1:i;else{this.active=t===e;var s="vertical"===o;this.translate=this.calcTranslate(t,e,s)}this.ready=!0},handleItemClick:function(){var t=this.$parent;if(t&&"card"===t.type){var e=t.items.indexOf(this);t.setActiveItem(e)}}}}},21159:function(t,e,n){"use strict";var r=n("30b0");n.o(r,"render")&&n.d(e,"render",(function(){return r["render"]})),n.o(r,"staticRenderFns")&&n.d(e,"staticRenderFns",(function(){return r["staticRenderFns"]}))},"219c":function(t,e,n){"use strict";var r=n("cfe9"),i=n("4625"),o=n("d039"),a=n("59ed"),s=n("addb"),c=n("ebb5"),u=n("3f7e"),f=n("99f4"),d=n("1212"),l=n("ea83"),h=c.aTypedArray,p=c.exportTypedArrayMethod,v=r.Uint16Array,b=v&&i(v.prototype.sort),m=!!b&&!(o((function(){b(new v(2),null)}))&&o((function(){b(new v(2),{})}))),g=!!b&&!o((function(){if(d)return d<74;if(u)return u<67;if(f)return!0;if(l)return l<602;var t,e,n=new v(516),r=Array(516);for(t=0;t<516;t++)e=t%4,n[t]=515-t,r[t]=t-2*e+3;for(b(n,(function(t,e){return(t/4|0)-(e/4|0)})),t=0;t<516;t++)if(n[t]!==r[t])return!0})),y=function(t){return function(e,n){return void 0!==t?+t(e,n)||0:n!==n?-1:e!==e?1:0===e&&0===n?1/e>0&&1/n<0?1:-1:e>n}};p("sort",(function(t){return void 0!==t&&a(t),g?b(this,t):s(h(this),y(t))}),!g||m)},"21bf":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.removeResizeListener=e.addResizeListener=void 0,n("14d9"),n("a434"),n("d3b7"),n("159b");var i=r(n("b85c")),o=r(n("6dd8")),a="undefined"===typeof window,s=function(t){var e,n=(0,i.default)(t);try{for(n.s();!(e=n.n()).done;){var r=e.value,o=r.target.__resizeListeners__||[];o.length&&o.forEach((function(t){t()}))}}catch(a){n.e(a)}finally{n.f()}};e.addResizeListener=function(t,e){a||(t.__resizeListeners__||(t.__resizeListeners__=[],t.__ro__=new o.default(s),t.__ro__.observe(t)),t.__resizeListeners__.push(e))},e.removeResizeListener=function(t,e){t&&t.__resizeListeners__&&(t.__resizeListeners__.splice(t.__resizeListeners__.indexOf(e),1),t.__resizeListeners__.length||t.__ro__.disconnect())}},2266:function(t,e,n){"use strict";var r=n("0366"),i=n("c65b"),o=n("825a"),a=n("0d51"),s=n("e95a"),c=n("07fa"),u=n("3a9b"),f=n("9a1f"),d=n("35a1"),l=n("2a62"),h=TypeError,p=function(t,e){this.stopped=t,this.result=e},v=p.prototype;t.exports=function(t,e,n){var b,m,g,y,x,w,_,A=n&&n.that,O=!(!n||!n.AS_ENTRIES),S=!(!n||!n.IS_RECORD),E=!(!n||!n.IS_ITERATOR),T=!(!n||!n.INTERRUPTED),C=r(e,A),R=function(t){return b&&l(b,"normal",t),new p(!0,t)},k=function(t){return O?(o(t),T?C(t[0],t[1],R):C(t[0],t[1])):T?C(t,R):C(t)};if(S)b=t.iterator;else if(E)b=t;else{if(m=d(t),!m)throw new h(a(t)+" is not iterable");if(s(m)){for(g=0,y=c(t);y>g;g++)if(x=k(t[g]),x&&u(v,x))return x;return new p(!1)}b=f(t,m)}w=S?t.next:b.next;while(!(_=i(w,b)).done){try{x=k(_.value)}catch(j){l(b,"throw",j)}if("object"==typeof x&&x&&u(v,x))return x}return new p(!1)}},"23cb":function(t,e,n){"use strict";var r=n("5926"),i=Math.max,o=Math.min;t.exports=function(t,e){var n=r(t);return n<0?i(n+e,0):o(n,e)}},"23dc":function(t,e,n){"use strict";var r=n("d44e");r(Math,"Math",!0)},"23e7":function(t,e,n){"use strict";var r=n("cfe9"),i=n("06cf").f,o=n("9112"),a=n("cb2d"),s=n("6374"),c=n("e893"),u=n("94ca");t.exports=function(t,e){var n,f,d,l,h,p,v=t.target,b=t.global,m=t.stat;if(f=b?r:m?r[v]||s(v,{}):r[v]&&r[v].prototype,f)for(d in e){if(h=e[d],t.dontCallGetSet?(p=i(f,d),l=p&&p.value):l=f[d],n=u(b?d:v+(m?".":"#")+d,t.forced),!n&&void 0!==l){if(typeof h==typeof l)continue;c(h,l)}(t.sham||l&&l.sham)&&o(h,"sham",!0),a(f,d,h,t)}}},"241c":function(t,e,n){"use strict";var r=n("ca84"),i=n("7839"),o=i.concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return r(t,o)}},"249d":function(t,e,n){"use strict";var r=n("23e7"),i=n("41f6");i&&r({target:"ArrayBuffer",proto:!0},{transfer:function(){return i(this,arguments.length?arguments[0]:void 0,!0)}})},"24f0":function(t,e,n){"use strict";var r=n("48f5");n.o(r,"render")&&n.d(e,"render",(function(){return r["render"]})),n.o(r,"staticRenderFns")&&n.d(e,"staticRenderFns",(function(){return r["staticRenderFns"]}))},"24fb":function(t,e,n){"use strict";function r(t,e){var n=t[1]||"",r=t[3];if(!r)return n;if(e&&"function"===typeof btoa){var o=i(r),a=r.sources.map((function(t){return"/*# sourceURL=".concat(r.sourceRoot||"").concat(t," */")}));return[n].concat(a).concat([o]).join("\n")}return[n].join("\n")}function i(t){var e=btoa(unescape(encodeURIComponent(JSON.stringify(t)))),n="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(e);return"/*# ".concat(n," */")}t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var n=r(e,t);return e[2]?"@media ".concat(e[2]," {").concat(n,"}"):n})).join("")},e.i=function(t,n,r){"string"===typeof t&&(t=[[null,t,""]]);var i={};if(r)for(var o=0;o<this.length;o++){var a=this[o][0];null!=a&&(i[a]=!0)}for(var s=0;s<t.length;s++){var c=[].concat(t[s]);r&&i[c[0]]||(n&&(c[2]?c[2]="".concat(n," and ").concat(c[2]):c[2]=n),e.push(c))}},e}},2532:function(t,e,n){"use strict";var r=n("23e7"),i=n("e330"),o=n("5a34"),a=n("1d80"),s=n("577e"),c=n("ab13"),u=i("".indexOf);r({target:"String",proto:!0,forced:!c("includes")},{includes:function(t){return!!~u(s(a(this)),s(o(t)),arguments.length>1?arguments[1]:void 0)}})},"257e":function(t,e,n){"use strict";function r(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}Object.defineProperty(e,"__esModule",{value:!0}),e.default=r,n("d9e2")},"25a1":function(t,e,n){"use strict";var r=n("ebb5"),i=n("d58f").right,o=r.aTypedArray,a=r.exportTypedArrayMethod;a("reduceRight",(function(t){var e=arguments.length;return i(o(this),t,e,e>1?arguments[1]:void 0)}))},"25f0":function(t,e,n){"use strict";var r=n("5e77").PROPER,i=n("cb2d"),o=n("825a"),a=n("577e"),s=n("d039"),c=n("90d8"),u="toString",f=RegExp.prototype,d=f[u],l=s((function(){return"/a/b"!==d.call({source:"a",flags:"b"})})),h=r&&d.name!==u;(l||h)&&i(f,u,(function(){var t=o(this),e=a(t.source),n=a(c(t));return"/"+e+"/"+n}),{unsafe:!0})},2626:function(t,e,n){"use strict";var r=n("d066"),i=n("edd0"),o=n("b622"),a=n("83ab"),s=o("species");t.exports=function(t){var e=r(t);a&&e&&!e[s]&&i(e,s,{configurable:!0,get:function(){return this}})}},"262e":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=o,n("d9e2");var r=i(n("b380"));function i(t){return t&&t.__esModule?t:{default:t}}function o(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&(0,r.default)(t,e)}},"26a1":function(t,e,n){"use strict";n("498a");var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.setStyle=e.removeClass=e.once=e.on=e.off=e.isServer=e.hasClass=e.getStyle=e.addClass=void 0;var i=r(n("53ca"));n("d9e2"),n("4de4"),n("a9e3"),n("d3b7"),n("ac1f"),n("5319");var o=r(n("8bbf")),a=n("ca00"),s=e.isServer=o.default.prototype.$isServer,c=s?0:Number(document.documentMode),u=e.hasClass=function(t,e){if(!t||!e)return!1;if(-1!==e.indexOf(" "))throw new Error("className should not contain space.");return t.classList?t.classList.contains(e):(" "+t.className+" ").indexOf(" "+e+" ")>-1},f=(e.addClass=function(t,e){if(t){for(var n=t.className,r=(e||"").split(" "),i=0,o=r.length;i<o;i++){var a=r[i];a&&(t.classList?t.classList.add(a):u(t,a)||(n+=" "+a))}t.classList||(t.className=n)}},e.removeClass=function(t,e){if(t&&e){for(var n=e.split(" "),r=" "+t.className+" ",i=0,o=n.length;i<o;i++){var s=n[i];s&&(t.classList?t.classList.remove(s):u(t,s)&&(r=r.replace(" "+s+" "," ")))}t.classList||(t.className=(0,a.trim)(r))}},e.getStyle=c<9?function(t,e){if(!s){if(!t||!e)return null;e=(0,a.camelCase)(e),"float"===e&&(e="styleFloat");try{switch(e){case"opacity":try{return t.filters.item("alpha").opacity/100}catch(n){return 1}default:return t.style[e]||t.currentStyle?t.currentStyle[e]:null}}catch(n){return t.style[e]}}}:function(t,e){if(!s){if(!t||!e)return null;e=(0,a.camelCase)(e),"float"===e&&(e="cssFloat");try{var n=document.defaultView.getComputedStyle(t,"");return t.style[e]||n?n[e]:null}catch(r){return t.style[e]}}},e.setStyle=function(t,e,n){if(t&&e)if("object"===(0,i.default)(e))for(var r in e)(0,a.hasOwn)(e,r)&&f(t,r,e[r]);else e=(0,a.camelCase)(e),"opacity"===e&&c<9?t.style.filter=isNaN(n)?"":"alpha(opacity="+100*n+")":t.style[e]=n}),d=e.on=function(){return!s&&document.addEventListener?function(t,e,n){t&&e&&n&&t.addEventListener(e,n,!1)}:function(t,e,n){t&&e&&n&&t.attachEvent("on"+e,n)}}(),l=e.off=function(){return!s&&document.removeEventListener?function(t,e,n){t&&e&&t.removeEventListener(e,n,!1)}:function(t,e,n){t&&e&&t.detachEvent("on"+e,n)}}();e.once=function(t,e,n){var r=function(){n&&n.apply(this,arguments),l(t,e,r)};d(t,e,r)}},"275b":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0");var i=r(n("6fc7"));i.default.install=function(t){t.component(i.default.name,i.default)};e.default=i.default},2834:function(t,e,n){"use strict";var r=n("ebb5"),i=n("e330"),o=n("59ed"),a=n("dfb9"),s=r.aTypedArray,c=r.getTypedArrayConstructor,u=r.exportTypedArrayMethod,f=i(r.TypedArrayPrototype.sort);u("toSorted",(function(t){void 0!==t&&o(t);var e=s(this),n=a(c(e),e);return f(n,t)}))},2877:function(t,e,n){"use strict";function r(t,e,n,r,i,o,a,s){var c,u="function"===typeof t?t.options:t;if(e&&(u.render=e,u.staticRenderFns=n,u._compiled=!0),r&&(u.functional=!0),o&&(u._scopeId="data-v-"+o),a?(c=function(t){t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,t||"undefined"===typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),i&&i.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(a)},u._ssrRegister=c):i&&(c=s?function(){i.call(this,(u.functional?this.parent:this).$root.$options.shadowRoot)}:i),c)if(u.functional){u._injectStyles=c;var f=u.render;u.render=function(t,e){return c.call(e),f(t,e)}}else{var d=u.beforeCreate;u.beforeCreate=d?[].concat(d,c):[c]}return{exports:t,options:u}}n.d(e,"a",(function(){return r}))},2954:function(t,e,n){"use strict";var r=n("ebb5"),i=n("b6b7"),o=n("d039"),a=n("f36a"),s=r.aTypedArray,c=r.exportTypedArrayMethod,u=o((function(){new Int8Array(1).slice()}));c("slice",(function(t,e){var n=a(s(this),t,e),r=i(this),o=0,c=n.length,u=new r(c);while(c>o)u[o]=n[o++];return u}),u)},"2a07":function(t,e,n){"use strict";var r=n("cfe9"),i=n("9adc");t.exports=function(t){if(i){try{return r.process.getBuiltinModule(t)}catch(e){}try{return Function('return require("'+t+'")')()}catch(e){}}}},"2a42":function(t,e,n){"use strict";n.r(e);var r=n("d78d"),i=n("ec2d");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("a0fb");var a=n("2877"),s=Object(a["a"])(i["default"],r["render"],r["staticRenderFns"],!1,null,"34920bb6",null);e["default"]=s.exports},"2a62":function(t,e,n){"use strict";var r=n("c65b"),i=n("825a"),o=n("dc4a");t.exports=function(t,e,n){var a,s;i(t);try{if(a=o(t,"return"),!a){if("throw"===e)throw n;return n}a=r(a,t)}catch(c){s=!0,a=c}if("throw"===e)throw n;if(s)throw a;return i(a),n}},"2a91":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0");var i=r(n("8860"));i.default.install=function(t){t.component(i.default.name,i.default)};e.default=i.default},"2ba4":function(t,e,n){"use strict";var r=n("40d5"),i=Function.prototype,o=i.apply,a=i.call;t.exports="object"==typeof Reflect&&Reflect.apply||(r?a.bind(o):function(){return a.apply(o,arguments)})},"2bea":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0");var i=r(n("5746"));i.default.install=function(t){t.component(i.default.name,i.default)};e.default=i.default},"2c66":function(t,e,n){"use strict";var r=n("83ab"),i=n("edd0"),o=n("75bd"),a=ArrayBuffer.prototype;r&&!("detached"in a)&&i(a,"detached",{configurable:!0,get:function(){return o(this)}})},"2cf4":function(t,e,n){"use strict";var r,i,o,a,s=n("cfe9"),c=n("2ba4"),u=n("0366"),f=n("1626"),d=n("1a2d"),l=n("d039"),h=n("1be4"),p=n("f36a"),v=n("cc12"),b=n("d6d6"),m=n("52c8"),g=n("9adc"),y=s.setImmediate,x=s.clearImmediate,w=s.process,_=s.Dispatch,A=s.Function,O=s.MessageChannel,S=s.String,E=0,T={},C="onreadystatechange";l((function(){r=s.location}));var R=function(t){if(d(T,t)){var e=T[t];delete T[t],e()}},k=function(t){return function(){R(t)}},j=function(t){R(t.data)},I=function(t){s.postMessage(S(t),r.protocol+"//"+r.host)};y&&x||(y=function(t){b(arguments.length,1);var e=f(t)?t:A(t),n=p(arguments,1);return T[++E]=function(){c(e,void 0,n)},i(E),E},x=function(t){delete T[t]},g?i=function(t){w.nextTick(k(t))}:_&&_.now?i=function(t){_.now(k(t))}:O&&!m?(o=new O,a=o.port2,o.port1.onmessage=j,i=u(a.postMessage,a)):s.addEventListener&&f(s.postMessage)&&!s.importScripts&&r&&"file:"!==r.protocol&&!l(I)?(i=I,s.addEventListener("message",j,!1)):i=C in v("script")?function(t){h.appendChild(v("script"))[C]=function(){h.removeChild(this),R(t)}}:function(t){setTimeout(k(t),0)}),t.exports={set:y,clear:x}},"2d0a":function(t,e,n){"use strict";n.r(e);var r=n("2115"),i=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=i.a},"30b0":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0;e.render=function(){var t=this,e=t._self._c;return e("div",{staticClass:"ne-checkbox-group",attrs:{role:"group","aria-label":"checkbox-group"}},[t._t("default")],2)},e.staticRenderFns=[]},3280:function(t,e,n){"use strict";var r=n("ebb5"),i=n("2ba4"),o=n("e58c"),a=r.aTypedArray,s=r.exportTypedArrayMethod;s("lastIndexOf",(function(t){var e=arguments.length;return i(o,a(this),e>1?[t,arguments[1]]:[t])}))},"336e":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("caad"),n("14d9"),n("a9e3"),n("d3b7"),n("2532"),n("159b");var i=r(n("8ed6")),o=n("ca00");e.default={name:"NeFillBlanks",filters:{renderMathJax:function(t){return t?-1!==t.indexOf("\\")||-1!==t.indexOf("^")||-1!==t.indexOf("_")?"\\[".concat(t,"\\]"):t:"-"}},props:{userConfig:{type:Object,default:function(){}},judgeConfig:{type:Object,default:function(){}},renderConfig:{type:Object,default:function(){}},quesContent:{type:Object,default:function(){}},countDownTime:{type:Number,default:0},submitText:{type:String,default:"Submit"},disableSubmit:{type:Boolean,default:!0},showResultToast:{type:Boolean,default:!1},customClass:{type:String,default:""},coin:{type:Number,default:0}},data:function(){return{clockCountdownOptions:{ratio:3,size:26,borderWidth:3,borderColor:"#FFAA0A",background:["#ffffff","#fff6b2"],pointerColor:"#ffc613",pointerWidth:2},defaultRenderConfig:{hideSource:!0,hideDifficulty:!0},userAnswerData:{},isSubmit:!1,answer:{},judge:{},userAnswer:[],needShowRight:!1}},computed:{qsRenderConfig:function(){return(0,i.default)(this.defaultRenderConfig,this.renderConfig)},isRightToast:function(){return 1===this.needShowRight||4===this.needShowRight}},watch:{},mounted:function(){var t=this;this.clockCountdownOptions.countDownTime=this.countDownTime,this.$nextTick((function(){t.quesContent&&t.userConfig.stuId&&(window.TalqsInteraction.setUserConfig(t.userConfig),t.renderQS(),t.judgeQS(),t.questionAnswerChange(),t.answer=t.chunkArray(t.quesContent.answer,2))}))},beforeDestroy:function(){},updated:function(){window.MathJax&&window.MathJax.Hub.Queue(["Typeset",window.MathJax.Hub,document.getElementById("ne-fillblanks__board")])},methods:{chunkArray:function(t,e){if(t&&0!==t.length){var n=Math.ceil(t.length/e),r=(0,o.chunk)(t,e);return{length:n,data:r}}},submit:function(){if(!this.isSubmit){var t=3,e=[],n=(0,o.flatten)(this.userAnswerData.answerData);this.userAnswerData.judgeData.forEach((function(t){e.push(t.judge)})),e=(0,o.flatten)(e),e.includes(1)&&(e.includes(0)||e.includes(-2))?t=4:e.includes(0)?t=2:e.every((function(t){return 1===t}))&&(t=1);var r={judgeData:e,answerData:n,isRight:4===t?2:t};this.$emit("submit",r),this.judge=this.chunkArray(e,2),this.userAnswer=this.chunkArray(n,2),this.isSubmit=!0,this.needShowRight=t}},renderQS:function(){var t=document.getElementById("fillBlanksContent");t&&(t.innerHTML=window.TalqsTemplate.render(this.quesContent,this.qsRenderConfig),this.$nextTick((function(){window.TalqsTemplate.autoLayout()})))},judgeQS:function(){window.TalqsInteraction.initTqiQs()},questionAnswerChange:function(){var t=this;window.TalqsInteraction.onChange=function(e){var n=window.judge.byId([e.queId],t.userConfig,t.judgeConfig);t.userAnswerData={answerData:e.data,judgeData:n},t.$emit("changeAnswer",t.userAnswerData)}}}}},3410:function(t,e,n){"use strict";var r=n("23e7"),i=n("d039"),o=n("7b0b"),a=n("e163"),s=n("e177"),c=i((function(){a(1)}));r({target:"Object",stat:!0,forced:c,sham:!s},{getPrototypeOf:function(t){return a(o(t))}})},3511:function(t,e,n){"use strict";var r=TypeError,i=9007199254740991;t.exports=function(t){if(t>i)throw r("Maximum allowed index exceeded");return t}},3529:function(t,e,n){"use strict";var r=n("23e7"),i=n("c65b"),o=n("59ed"),a=n("f069"),s=n("e667"),c=n("2266"),u=n("5eed");r({target:"Promise",stat:!0,forced:u},{race:function(t){var e=this,n=a.f(e),r=n.reject,u=s((function(){var a=o(e.resolve);c(t,(function(t){i(a,e,t).then(n.resolve,r)}))}));return u.error&&r(u.value),n.promise}})},"355e":function(t,e,n){"use strict";var r=n("da3e");n.o(r,"render")&&n.d(e,"render",(function(){return r["render"]})),n.o(r,"staticRenderFns")&&n.d(e,"staticRenderFns",(function(){return r["staticRenderFns"]}))},"35a1":function(t,e,n){"use strict";var r=n("f5df"),i=n("dc4a"),o=n("7234"),a=n("3f8c"),s=n("b622"),c=s("iterator");t.exports=function(t){if(!o(t))return i(t,c)||i(t,"@@iterator")||a[r(t)]}},"36f2":function(t,e,n){"use strict";var r,i,o,a,s=n("cfe9"),c=n("2a07"),u=n("dbe5"),f=s.structuredClone,d=s.ArrayBuffer,l=s.MessageChannel,h=!1;if(u)h=function(t){f(t,{transfer:[t]})};else if(d)try{l||(r=c("worker_threads"),r&&(l=r.MessageChannel)),l&&(i=new l,o=new d(2),a=function(t){i.port1.postMessage(null,[t])},2===o.byteLength&&(a(o),0===o.byteLength&&(h=a)))}catch(p){}t.exports=h},"37e8":function(t,e,n){"use strict";var r=n("83ab"),i=n("aed9"),o=n("9bf2"),a=n("825a"),s=n("fc6a"),c=n("df75");e.f=r&&!i?Object.defineProperties:function(t,e){a(t);var n,r=s(e),i=c(e),u=i.length,f=0;while(u>f)o.f(t,n=i[f++],r[n]);return t}},"3a34":function(t,e,n){"use strict";var r=n("83ab"),i=n("e8b5"),o=TypeError,a=Object.getOwnPropertyDescriptor,s=r&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}();t.exports=s?function(t,e){if(i(t)&&!a(t,"length").writable)throw new o("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e}},"3a7b":function(t,e,n){"use strict";var r=n("ebb5"),i=n("b727").findIndex,o=r.aTypedArray,a=r.exportTypedArrayMethod;a("findIndex",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},"3a9b":function(t,e,n){"use strict";var r=n("e330");t.exports=r({}.isPrototypeOf)},"3bbe":function(t,e,n){"use strict";var r=n("1787"),i=String,o=TypeError;t.exports=function(t){if(r(t))return t;throw new o("Can't set "+i(t)+" as a prototype")}},"3c5d":function(t,e,n){"use strict";var r=n("cfe9"),i=n("c65b"),o=n("ebb5"),a=n("07fa"),s=n("182d"),c=n("7b0b"),u=n("d039"),f=r.RangeError,d=r.Int8Array,l=d&&d.prototype,h=l&&l.set,p=o.aTypedArray,v=o.exportTypedArrayMethod,b=!u((function(){var t=new Uint8ClampedArray(2);return i(h,t,{length:1,0:3},1),3!==t[1]})),m=b&&o.NATIVE_ARRAY_BUFFER_VIEWS&&u((function(){var t=new d(2);return t.set(1),t.set("2",1),0!==t[0]||2!==t[1]}));v("set",(function(t){p(this);var e=s(arguments.length>1?arguments[1]:void 0,1),n=c(t);if(b)return i(h,this,n,e);var r=this.length,o=a(n),u=0;if(o+e>r)throw new f("Wrong length");while(u<o)this[e+u]=n[u++]}),!b||m)},"3ca3":function(t,e,n){"use strict";var r=n("6547").charAt,i=n("577e"),o=n("69f3"),a=n("c6d2"),s=n("4754"),c="String Iterator",u=o.set,f=o.getterFor(c);a(String,"String",(function(t){u(this,{type:c,string:i(t),index:0})}),(function(){var t,e=f(this),n=e.string,i=e.index;return i>=n.length?s(void 0,!0):(t=r(n,i),e.index+=t.length,s(t,!1))}))},"3cb8":function(t,e,n){"use strict";n.r(e);var r=n("ced7"),i=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=i.a},"3f7e":function(t,e,n){"use strict";var r=n("b5db"),i=r.match(/firefox\/(\d+)/i);t.exports=!!i&&+i[1]},"3f8c":function(t,e,n){"use strict";t.exports={}},"3fcc":function(t,e,n){"use strict";var r=n("ebb5"),i=n("b727").map,o=n("b6b7"),a=r.aTypedArray,s=r.exportTypedArrayMethod;s("map",(function(t){return i(a(this),t,arguments.length>1?arguments[1]:void 0,(function(t,e){return new(o(t))(e)}))}))},"3ff2":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0;e.render=function(){var t=this,e=t._self._c;return e("div",{staticClass:"ne-button",class:[t.type?"ne-button--"+t.type:"",t.buttonSize?"ne-button--"+t.buttonSize:"",{"is-disabled":t.buttonDisabled,"is-loading":t.loading,"is-plain":t.plain,"is-round":t.round,"is-circle":t.circle}],attrs:{type:t.nativeType,disabled:t.buttonDisabled||t.loading},on:{click:t.handleClick}},[t.loading?e("NeIcon",{attrs:{name:"loading"}}):t._e(),t.icon&&!t.loading?e("NeIcon",{attrs:{name:t.icon}}):t._e(),t.$slots.default?e("span",[t._t("default")],2):t._e()],1)},e.staticRenderFns=[]},"408a":function(t,e,n){"use strict";var r=n("e330");t.exports=r(1..valueOf)},"40d5":function(t,e,n){"use strict";var r=n("d039");t.exports=!r((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},"40e9":function(t,e,n){"use strict";var r=n("23e7"),i=n("41f6");i&&r({target:"ArrayBuffer",proto:!0},{transferToFixedLength:function(){return i(this,arguments.length?arguments[0]:void 0,!1)}})},"41f6":function(t,e,n){"use strict";var r=n("cfe9"),i=n("e330"),o=n("7282"),a=n("0b25"),s=n("2005"),c=n("b620"),u=n("36f2"),f=n("dbe5"),d=r.structuredClone,l=r.ArrayBuffer,h=r.DataView,p=Math.min,v=l.prototype,b=h.prototype,m=i(v.slice),g=o(v,"resizable","get"),y=o(v,"maxByteLength","get"),x=i(b.getInt8),w=i(b.setInt8);t.exports=(f||u)&&function(t,e,n){var r,i=c(t),o=void 0===e?i:a(e),v=!g||!g(t);if(s(t),f&&(t=d(t,{transfer:[t]}),i===o&&(n||v)))return t;if(i>=o&&(!n||v))r=m(t,0,o);else{var b=n&&!v&&y?{maxByteLength:y(t)}:void 0;r=new l(o,b);for(var _=new h(t),A=new h(r),O=p(o,i),S=0;S<O;S++)w(A,S,x(_,S))}return f||u(t),r}},"428f":function(t,e,n){"use strict";var r=n("cfe9");t.exports=r},4435:function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=r(n("d4ec")),o=r(n("bee2"));e.default=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,i.default)(this,t),this.options=e}return(0,o.default)(t,[{key:"getRightRate",value:function(t,e){return Math.round(t/e*100)}}])}()},"44ad":function(t,e,n){"use strict";var r=n("e330"),i=n("d039"),o=n("c6b6"),a=Object,s=r("".split);t.exports=i((function(){return!a("z").propertyIsEnumerable(0)}))?function(t){return"String"===o(t)?s(t,""):a(t)}:a},"44d2":function(t,e,n){"use strict";var r=n("b622"),i=n("7c73"),o=n("9bf2").f,a=r("unscopables"),s=Array.prototype;void 0===s[a]&&o(s,a,{configurable:!0,value:i(null)}),t.exports=function(t){s[a][t]=!0}},"44de":function(t,e,n){"use strict";t.exports=function(t,e){try{1===arguments.length?console.error(t):console.error(t,e)}catch(n){}}},"44e7":function(t,e,n){"use strict";var r=n("861d"),i=n("c6b6"),o=n("b622"),a=o("match");t.exports=function(t){var e;return r(t)&&(void 0!==(e=t[a])?!!e:"RegExp"===i(t))}},4508:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0;e.render=function(){var t=this,e=t._self._c;return e("transition",{on:{"after-leave":t.afterLeave}},[t.visible?e("div",{staticClass:"game-courseware-mask-bg",class:[t.maskClass]},[e("div",{staticClass:"ne-dialog"},[e("div",{staticClass:"ne-dialog--body"},[t._t("default")],2),t.$slots.footer?e("div",{staticClass:"ne-dialog--footer"},[t._t("footer")],2):t._e()])]):t._e()])},e.staticRenderFns=[]},"452b":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("cb29"),n("14d9"),n("a9e3"),n("d3b7"),n("25f0");var i=r(n("a215"));e.default={name:"NeCheckboxButton",mixins:[i.default],inject:{neForm:{default:""},neFormItem:{default:""}},props:{value:{},label:{},disabled:Boolean,checked:Boolean,name:String,trueLabel:[String,Number],falseLabel:[String,Number]},data:function(){return{selfModel:!1,focus:!1,isLimitExceeded:!1}},computed:{model:{get:function(){return this._checkboxGroup?this.store:void 0!==this.value?this.value:this.selfModel},set:function(t){this._checkboxGroup?(this.isLimitExceeded=!1,void 0!==this._checkboxGroup.min&&t.length<this._checkboxGroup.min&&(this.isLimitExceeded=!0),void 0!==this._checkboxGroup.max&&t.length>this._checkboxGroup.max&&(this.isLimitExceeded=!0),!1===this.isLimitExceeded&&this.dispatch("NeCheckboxGroup","input",[t])):void 0!==this.value?this.$emit("input",t):this.selfModel=t}},isChecked:function(){return"[object Boolean]"==={}.toString.call(this.model)?this.model:Array.isArray(this.model)?this.model.indexOf(this.label)>-1:null!==this.model&&void 0!==this.model?this.model===this.trueLabel:void 0},_checkboxGroup:function(){var t=this.$parent;while(t){if("NeCheckboxGroup"===t.$options.componentName)return t;t=t.$parent}return!1},store:function(){return this._checkboxGroup?this._checkboxGroup.value:this.value},activeStyle:function(){return{backgroundColor:this._checkboxGroup.fill||"",borderColor:this._checkboxGroup.fill||"",color:this._checkboxGroup.textColor||"","box-shadow":"-1px 0 0 0 "+this._checkboxGroup.fill}},_elFormItemSize:function(){return(this.elFormItem||{}).elFormItemSize},size:function(){return this._checkboxGroup.checkboxGroupSize||this._neFormItemSize||(this.$NeosUI||{}).size},isLimitDisabled:function(){var t=this._checkboxGroup,e=t.max,n=t.min;return!(!e&&!n)&&this.model.length>=e&&!this.isChecked||this.model.length<=n&&this.isChecked},isDisabled:function(){return this._checkboxGroup?this._checkboxGroup.disabled||this.disabled||(this.neForm||{}).disabled||this.isLimitDisabled:this.disabled||(this.neForm||{}).disabled}},created:function(){this.checked&&this.addToStore()},methods:{addToStore:function(){Array.isArray(this.model)&&-1===this.model.indexOf(this.label)?this.model.push(this.label):this.model=this.trueLabel||!0},handleChange:function(t){var e,n=this;this.isLimitExceeded||(e=t.target.checked?void 0===this.trueLabel||this.trueLabel:void 0!==this.falseLabel&&this.falseLabel,this.$emit("change",e,t),this.$nextTick((function(){n._checkboxGroup&&n.dispatch("NeCheckboxGroup","change",[n._checkboxGroup.value])})))}}}},4625:function(t,e,n){"use strict";var r=n("c6b6"),i=n("e330");t.exports=function(t){if("Function"===r(t))return i(t)}},"463d":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0;e.render=function(){var t=this,e=t._self._c;return e("canvas",{ref:"countDown",class:t.customClass,attrs:{id:t.countDownId}})},e.staticRenderFns=[]},"466d":function(t,e,n){"use strict";var r=n("c65b"),i=n("d784"),o=n("825a"),a=n("7234"),s=n("50c4"),c=n("577e"),u=n("1d80"),f=n("dc4a"),d=n("8aa5"),l=n("14c3");i("match",(function(t,e,n){return[function(e){var n=u(this),i=a(e)?void 0:f(e,t);return i?r(i,e,n):new RegExp(e)[t](c(n))},function(t){var r=o(this),i=c(t),a=n(e,r,i);if(a.done)return a.value;if(!r.global)return l(r,i);var u=r.unicode;r.lastIndex=0;var f,h=[],p=0;while(null!==(f=l(r,i))){var v=c(f[0]);h[p]=v,""===v&&(r.lastIndex=d(i,s(r.lastIndex),u)),p++}return 0===p?null:h}]}))},4738:function(t,e,n){"use strict";var r=n("cfe9"),i=n("d256"),o=n("1626"),a=n("94ca"),s=n("8925"),c=n("b622"),u=n("8558"),f=n("c430"),d=n("1212"),l=i&&i.prototype,h=c("species"),p=!1,v=o(r.PromiseRejectionEvent),b=a("Promise",(function(){var t=s(i),e=t!==String(i);if(!e&&66===d)return!0;if(f&&(!l["catch"]||!l["finally"]))return!0;if(!d||d<51||!/native code/.test(t)){var n=new i((function(t){t(1)})),r=function(t){t((function(){}),(function(){}))},o=n.constructor={};if(o[h]=r,p=n.then((function(){}))instanceof r,!p)return!0}return!e&&("BROWSER"===u||"DENO"===u)&&!v}));t.exports={CONSTRUCTOR:b,REJECTION_EVENT:v,SUBCLASSING:p}},4754:function(t,e,n){"use strict";t.exports=function(t,e){return{value:t,done:e}}},4840:function(t,e,n){"use strict";var r=n("825a"),i=n("5087"),o=n("7234"),a=n("b622"),s=a("species");t.exports=function(t,e){var n,a=r(t).constructor;return void 0===a||o(n=r(a)[s])?e:i(n)}},"485a":function(t,e,n){"use strict";var r=n("c65b"),i=n("1626"),o=n("861d"),a=TypeError;t.exports=function(t,e){var n,s;if("string"===e&&i(n=t.toString)&&!o(s=r(n,t)))return s;if(i(n=t.valueOf)&&!o(s=r(n,t)))return s;if("string"!==e&&i(n=t.toString)&&!o(s=r(n,t)))return s;throw new a("Can't convert object to primitive value")}},"48f5":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0,n("99af"),n("fb6a"),n("b0c0");e.render=function(){var t=this,e=t._self._c;return e("label",{staticClass:"ne-checkbox-button",class:[t.size?"ne-checkbox-button--"+t.size:"",{"is-disabled":t.isDisabled},{"is-checked":t.isChecked},{"is-focus":t.focus}],attrs:{role:"checkbox","aria-checked":t.isChecked,"aria-disabled":t.isDisabled}},[t.trueLabel||t.falseLabel?e("input",{directives:[{name:"model",rawName:"v-model",value:t.model,expression:"model"}],staticClass:"ne-checkbox-button__original",attrs:{type:"checkbox",name:t.name,disabled:t.isDisabled,"true-value":t.trueLabel,"false-value":t.falseLabel},domProps:{checked:Array.isArray(t.model)?t._i(t.model,null)>-1:t._q(t.model,t.trueLabel)},on:{change:[function(e){var n=t.model,r=e.target,i=r.checked?t.trueLabel:t.falseLabel;if(Array.isArray(n)){var o=null,a=t._i(n,o);r.checked?a<0&&(t.model=n.concat([o])):a>-1&&(t.model=n.slice(0,a).concat(n.slice(a+1)))}else t.model=i},t.handleChange],focus:function(e){t.focus=!0},blur:function(e){t.focus=!1}}}):e("input",{directives:[{name:"model",rawName:"v-model",value:t.model,expression:"model"}],staticClass:"ne-checkbox-button__original",attrs:{type:"checkbox",name:t.name,disabled:t.isDisabled},domProps:{value:t.label,checked:Array.isArray(t.model)?t._i(t.model,t.label)>-1:t.model},on:{change:[function(e){var n=t.model,r=e.target,i=!!r.checked;if(Array.isArray(n)){var o=t.label,a=t._i(n,o);r.checked?a<0&&(t.model=n.concat([o])):a>-1&&(t.model=n.slice(0,a).concat(n.slice(a+1)))}else t.model=i},t.handleChange],focus:function(e){t.focus=!0},blur:function(e){t.focus=!1}}}),t.$slots.default||t.label?e("span",{staticClass:"ne-checkbox-button__inner",style:t.isChecked?t.activeStyle:null},[t._t("default",(function(){return[t._v(t._s(t.label))]}))],2):t._e()])},e.staticRenderFns=[]},"498a":function(t,e,n){"use strict";var r=n("23e7"),i=n("58a8").trim,o=n("c8d2");r({target:"String",proto:!0,forced:o("trim")},{trim:function(){return i(this)}})},"499e":function(t,e,n){"use strict";function r(t,e){for(var n=[],r={},i=0;i<e.length;i++){var o=e[i],a=o[0],s=o[1],c=o[2],u=o[3],f={id:t+":"+i,css:s,media:c,sourceMap:u};r[a]?r[a].parts.push(f):n.push(r[a]={id:a,parts:[f]})}return n}n.r(e),n.d(e,"default",(function(){return p}));var i="undefined"!==typeof document;if("undefined"!==typeof DEBUG&&DEBUG&&!i)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var o={},a=i&&(document.head||document.getElementsByTagName("head")[0]),s=null,c=0,u=!1,f=function(){},d=null,l="data-vue-ssr-id",h="undefined"!==typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function p(t,e,n,i){u=n,d=i||{};var a=r(t,e);return v(a),function(e){for(var n=[],i=0;i<a.length;i++){var s=a[i],c=o[s.id];c.refs--,n.push(c)}e?(a=r(t,e),v(a)):a=[];for(i=0;i<n.length;i++){c=n[i];if(0===c.refs){for(var u=0;u<c.parts.length;u++)c.parts[u]();delete o[c.id]}}}}function v(t){for(var e=0;e<t.length;e++){var n=t[e],r=o[n.id];if(r){r.refs++;for(var i=0;i<r.parts.length;i++)r.parts[i](n.parts[i]);for(;i<n.parts.length;i++)r.parts.push(m(n.parts[i]));r.parts.length>n.parts.length&&(r.parts.length=n.parts.length)}else{var a=[];for(i=0;i<n.parts.length;i++)a.push(m(n.parts[i]));o[n.id]={id:n.id,refs:1,parts:a}}}}function b(){var t=document.createElement("style");return t.type="text/css",a.appendChild(t),t}function m(t){var e,n,r=document.querySelector("style["+l+'~="'+t.id+'"]');if(r){if(u)return f;r.parentNode.removeChild(r)}if(h){var i=c++;r=s||(s=b()),e=y.bind(null,r,i,!1),n=y.bind(null,r,i,!0)}else r=b(),e=x.bind(null,r),n=function(){r.parentNode.removeChild(r)};return e(t),function(r){if(r){if(r.css===t.css&&r.media===t.media&&r.sourceMap===t.sourceMap)return;e(t=r)}else n()}}var g=function(){var t=[];return function(e,n){return t[e]=n,t.filter(Boolean).join("\n")}}();function y(t,e,n,r){var i=n?"":r.css;if(t.styleSheet)t.styleSheet.cssText=g(e,i);else{var o=document.createTextNode(i),a=t.childNodes;a[e]&&t.removeChild(a[e]),a.length?t.insertBefore(o,a[e]):t.appendChild(o)}}function x(t,e){var n=e.css,r=e.media,i=e.sourceMap;if(r&&t.setAttribute("media",r),d.ssrId&&t.setAttribute(l,e.id),i&&(n+="\n/*# sourceURL="+i.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */"),t.styleSheet)t.styleSheet.cssText=n;else{while(t.firstChild)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(n))}}},"4a58":function(t,e,n){"use strict";var r=n("6bd5");n.o(r,"render")&&n.d(e,"render",(function(){return r["render"]})),n.o(r,"staticRenderFns")&&n.d(e,"staticRenderFns",(function(){return r["staticRenderFns"]}))},"4ae1":function(t,e,n){"use strict";var r=n("23e7"),i=n("d066"),o=n("2ba4"),a=n("0538"),s=n("5087"),c=n("825a"),u=n("861d"),f=n("7c73"),d=n("d039"),l=i("Reflect","construct"),h=Object.prototype,p=[].push,v=d((function(){function t(){}return!(l((function(){}),[],t)instanceof t)})),b=!d((function(){l((function(){}))})),m=v||b;r({target:"Reflect",stat:!0,forced:m,sham:m},{construct:function(t,e){s(t),c(e);var n=arguments.length<3?t:s(arguments[2]);if(b&&!v)return l(t,e,n);if(t===n){switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3])}var r=[null];return o(p,r,e),new(o(a,t,r))}var i=n.prototype,d=f(u(i)?i:h),m=o(t,d,e);return u(m)?m:d}})},"4b11":function(t,e,n){"use strict";t.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},"4c75":function(t,e,n){var r=n("529e");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var i=n("499e").default;i("b3f24874",r,!0,{sourceMap:!1,shadowMode:!1})},"4d64":function(t,e,n){"use strict";var r=n("fc6a"),i=n("23cb"),o=n("07fa"),a=function(t){return function(e,n,a){var s=r(e),c=o(s);if(0===c)return!t&&-1;var u,f=i(a,c);if(t&&n!==n){while(c>f)if(u=s[f++],u!==u)return!0}else for(;c>f;f++)if((t||f in s)&&s[f]===n)return t||f||0;return!t&&-1}};t.exports={includes:a(!0),indexOf:a(!1)}},"4de4":function(t,e,n){"use strict";var r=n("23e7"),i=n("b727").filter,o=n("1dde"),a=o("filter");r({target:"Array",proto:!0,forced:!a},{filter:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},"4df4":function(t,e,n){"use strict";var r=n("0366"),i=n("c65b"),o=n("7b0b"),a=n("9bdd"),s=n("e95a"),c=n("68ee"),u=n("07fa"),f=n("8418"),d=n("9a1f"),l=n("35a1"),h=Array;t.exports=function(t){var e=o(t),n=c(this),p=arguments.length,v=p>1?arguments[1]:void 0,b=void 0!==v;b&&(v=r(v,p>2?arguments[2]:void 0));var m,g,y,x,w,_,A=l(e),O=0;if(!A||this===h&&s(A))for(m=u(e),g=n?new this(m):h(m);m>O;O++)_=b?v(e[O],O):e[O],f(g,O,_);else for(g=n?new this:[],x=d(e,A),w=x.next;!(y=i(w,x)).done;O++)_=b?a(x,v,[y.value,O],!0):y.value,f(g,O,_);return g.length=O,g}},"4ea1":function(t,e,n){"use strict";var r=n("d429"),i=n("ebb5"),o=n("bcbf"),a=n("5926"),s=n("f495"),c=i.aTypedArray,u=i.getTypedArrayConstructor,f=i.exportTypedArrayMethod,d=!!function(){try{new Int8Array(1)["with"](2,{valueOf:function(){throw 8}})}catch(t){return 8===t}}();f("with",{with:function(t,e){var n=c(this),i=a(t),f=o(n)?s(e):+e;return r(n,u(n),i,f)}}["with"],!d)},"4ea4":function(t,e){function n(t){return t&&t.__esModule?t:{default:t}}t.exports=n,t.exports.__esModule=!0,t.exports["default"]=t.exports},"4f1a":function(t,e,n){var r=n("24fb"),i=n("1de5"),o=n("1838"),a=n("9fef"),s=n("073b");e=r(!1);var c=i(o),u=i(a),f=i(s);e.push([t.i,".gameCourseware-container[data-v-34920bb6]{position:absolute;z-index:2;top:25px;left:0;width:100%;height:calc(100% - 25px);overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}.gameCourseware-container p span[data-v-34920bb6]{color:#a2aab8;line-height:20px;font-size:14px;display:block;padding-top:15px}.gameCourseware-container.hiddenToolsBg[data-v-34920bb6]{top:0;height:100%}.gameCourseware-container.hiddenToolsBg button[data-v-34920bb6]{top:30px!important}.gameCourseware-container iframe[data-v-34920bb6]{width:100%;height:100%;background:transparent}.gameCourseware-container .game-courseware-mask-bg[data-v-34920bb6]{position:absolute;top:0;left:0;right:0;bottom:0;z-index:10;background:url("+c+") no-repeat 50%;background-size:100% 100%}.gameCourseware-container .gameCourseware-tools[data-v-34920bb6]{position:relative;z-index:9}.gameCourseware-container .gameCourseware-tools button[data-v-34920bb6]{width:30px;height:30px;border:0 none;background:#ffb60f;color:#fff;font-size:0;outline:0 none;position:absolute;top:5px}.gameCourseware-container .gameCourseware-tools button.exitView[data-v-34920bb6]{background:url("+u+") no-repeat 50%;background-size:100%;left:20px}.gameCourseware-container .gameCourseware-tools button.reloadView[data-v-34920bb6]{background:url("+f+") no-repeat 50%;background-size:100%;right:20px}",""]),t.exports=e},"4f90":function(t,e,n){"use strict";n.r(e);var r=n("e459"),i=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=i.a},"4fad":function(t,e,n){"use strict";var r=n("d039"),i=n("861d"),o=n("c6b6"),a=n("d86b"),s=Object.isExtensible,c=r((function(){s(1)}));t.exports=c||a?function(t){return!!i(t)&&((!a||"ArrayBuffer"!==o(t))&&(!s||s(t)))}:s},5087:function(t,e,n){"use strict";var r=n("68ee"),i=n("0d51"),o=TypeError;t.exports=function(t){if(r(t))return t;throw new o(i(t)+" is not a constructor")}},"50c4":function(t,e,n){"use strict";var r=n("5926"),i=Math.min;t.exports=function(t){var e=r(t);return e>0?i(e,9007199254740991):0}},"51eb":function(t,e,n){"use strict";var r=n("825a"),i=n("485a"),o=TypeError;t.exports=function(t){if(r(this),"string"===t||"default"===t)t="string";else if("number"!==t)throw new o("Incorrect hint");return i(this,t)}},5234:function(t,e,n){"use strict";n.r(e);var r=n("54a5"),i=n("0662");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);var a=n("2877"),s=Object(a["a"])(i["default"],r["render"],r["staticRenderFns"],!1,null,null,null);e["default"]=s.exports},"529e":function(t,e,n){var r=n("24fb");e=r(!1),e.push([t.i,".ne-fillblanks-result-mask[data-v-b9d29148]{background:rgba(0,0,0,.8);position:absolute;top:0;left:0;right:0;bottom:0;z-index:12}",""]),t.exports=e},"52c8":function(t,e,n){"use strict";var r=n("b5db");t.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(r)},5319:function(t,e,n){"use strict";var r=n("2ba4"),i=n("c65b"),o=n("e330"),a=n("d784"),s=n("d039"),c=n("825a"),u=n("1626"),f=n("7234"),d=n("5926"),l=n("50c4"),h=n("577e"),p=n("1d80"),v=n("8aa5"),b=n("dc4a"),m=n("0cb2"),g=n("14c3"),y=n("b622"),x=y("replace"),w=Math.max,_=Math.min,A=o([].concat),O=o([].push),S=o("".indexOf),E=o("".slice),T=function(t){return void 0===t?t:String(t)},C=function(){return"$0"==="a".replace(/./,"$0")}(),R=function(){return!!/./[x]&&""===/./[x]("a","$0")}(),k=!s((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}));a("replace",(function(t,e,n){var o=R?"$":"$0";return[function(t,n){var r=p(this),o=f(t)?void 0:b(t,x);return o?i(o,t,r,n):i(e,h(r),t,n)},function(t,i){var a=c(this),s=h(t);if("string"==typeof i&&-1===S(i,o)&&-1===S(i,"$<")){var f=n(e,a,s,i);if(f.done)return f.value}var p=u(i);p||(i=h(i));var b,y=a.global;y&&(b=a.unicode,a.lastIndex=0);var x,C=[];while(1){if(x=g(a,s),null===x)break;if(O(C,x),!y)break;var R=h(x[0]);""===R&&(a.lastIndex=v(s,l(a.lastIndex),b))}for(var k="",j=0,I=0;I<C.length;I++){x=C[I];for(var M,L=h(x[0]),P=w(_(d(x.index),s.length),0),F=[],N=1;N<x.length;N++)O(F,T(x[N]));var D=x.groups;if(p){var B=A([L],F,P,s);void 0!==D&&O(B,D),M=h(r(i,void 0,B))}else M=m(L,s,P,F,D,i);P>=j&&(k+=E(s,j,P)+M,j=P+L.length)}return k+E(s,j)}]}),!k||!C||R)},"53ca":function(t,e,n){"use strict";function r(t){return e.default=r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=r,n("a4d3"),n("e01a"),n("d28b"),n("e260"),n("d3b7"),n("3ca3"),n("ddb0")},"54a5":function(t,e,n){"use strict";var r=n("3ff2");n.o(r,"render")&&n.d(e,"render",(function(){return r["render"]})),n.o(r,"staticRenderFns")&&n.d(e,"staticRenderFns",(function(){return r["staticRenderFns"]}))},5692:function(t,e,n){"use strict";var r=n("c6cd");t.exports=function(t,e){return r[t]||(r[t]=e||{})}},"56ef":function(t,e,n){"use strict";var r=n("d066"),i=n("e330"),o=n("241c"),a=n("7418"),s=n("825a"),c=i([].concat);t.exports=r("Reflect","ownKeys")||function(t){var e=o.f(s(t)),n=a.f;return n?c(e,n(t)):e}},5746:function(t,e,n){"use strict";n.r(e);var r=n("a3f9"),i=n("198c");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);var a=n("2877"),s=Object(a["a"])(i["default"],r["render"],r["staticRenderFns"],!1,null,null,null);e["default"]=s.exports},"577e":function(t,e,n){"use strict";var r=n("f5df"),i=String;t.exports=function(t){if("Symbol"===r(t))throw new TypeError("Cannot convert a Symbol value to a string");return i(t)}},"57b9":function(t,e,n){"use strict";var r=n("c65b"),i=n("d066"),o=n("b622"),a=n("cb2d");t.exports=function(){var t=i("Symbol"),e=t&&t.prototype,n=e&&e.valueOf,s=o("toPrimitive");e&&!e[s]&&a(e,s,(function(t){return r(n,this)}),{arity:1})}},"584d":function(t,e,n){"use strict";n.r(e);var r=n("d82e"),i=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=i.a},5899:function(t,e,n){"use strict";t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},"58a8":function(t,e,n){"use strict";var r=n("e330"),i=n("1d80"),o=n("577e"),a=n("5899"),s=r("".replace),c=RegExp("^["+a+"]+"),u=RegExp("(^|[^"+a+"])["+a+"]+$"),f=function(t){return function(e){var n=o(i(e));return 1&t&&(n=s(n,c,"")),2&t&&(n=s(n,u,"$1")),n}};t.exports={start:f(1),end:f(2),trim:f(3)}},5926:function(t,e,n){"use strict";var r=n("b42e");t.exports=function(t){var e=+t;return e!==e||0===e?0:r(e)}},5981:function(t,e,n){var r=n("8bb9");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var i=n("499e").default;i("517fba71",r,!0,{sourceMap:!1,shadowMode:!1})},"59ed":function(t,e,n){"use strict";var r=n("1626"),i=n("0d51"),o=TypeError;t.exports=function(t){if(r(t))return t;throw new o(i(t)+" is not a function")}},"5a34":function(t,e,n){"use strict";var r=n("44e7"),i=TypeError;t.exports=function(t){if(r(t))throw new i("The method doesn't accept regular expressions");return t}},"5a47":function(t,e,n){"use strict";var r=n("23e7"),i=n("04f8"),o=n("d039"),a=n("7418"),s=n("7b0b"),c=!i||o((function(){a.f(1)}));r({target:"Object",stat:!0,forced:c},{getOwnPropertySymbols:function(t){var e=a.f;return e?e(s(t)):[]}})},"5bc4":function(t,e,n){"use strict";n.r(e);var r=n("8440"),i=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=i.a},"5c6c":function(t,e,n){"use strict";t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},"5cc6":function(t,e,n){"use strict";var r=n("74e8");r("Uint8",(function(t){return function(e,n,r){return t(this,e,n,r)}}))},"5e77":function(t,e,n){"use strict";var r=n("83ab"),i=n("1a2d"),o=Function.prototype,a=r&&Object.getOwnPropertyDescriptor,s=i(o,"name"),c=s&&"something"===function(){}.name,u=s&&(!r||r&&a(o,"name").configurable);t.exports={EXISTS:s,PROPER:c,CONFIGURABLE:u}},"5e7e":function(t,e,n){"use strict";var r,i,o,a,s=n("23e7"),c=n("c430"),u=n("9adc"),f=n("cfe9"),d=n("c65b"),l=n("cb2d"),h=n("d2bb"),p=n("d44e"),v=n("2626"),b=n("59ed"),m=n("1626"),g=n("861d"),y=n("19aa"),x=n("4840"),w=n("2cf4").set,_=n("b575"),A=n("44de"),O=n("e667"),S=n("01b4"),E=n("69f3"),T=n("d256"),C=n("4738"),R=n("f069"),k="Promise",j=C.CONSTRUCTOR,I=C.REJECTION_EVENT,M=C.SUBCLASSING,L=E.getterFor(k),P=E.set,F=T&&T.prototype,N=T,D=F,B=f.TypeError,G=f.document,z=f.process,U=R.f,$=U,V=!!(G&&G.createEvent&&f.dispatchEvent),W="unhandledrejection",q="rejectionhandled",H=0,Y=1,X=2,Z=1,K=2,Q=function(t){var e;return!(!g(t)||!m(e=t.then))&&e},J=function(t,e){var n,r,i,o=e.value,a=e.state===Y,s=a?t.ok:t.fail,c=t.resolve,u=t.reject,f=t.domain;try{s?(a||(e.rejection===K&&it(e),e.rejection=Z),!0===s?n=o:(f&&f.enter(),n=s(o),f&&(f.exit(),i=!0)),n===t.promise?u(new B("Promise-chain cycle")):(r=Q(n))?d(r,n,c,u):c(n)):u(o)}catch(l){f&&!i&&f.exit(),u(l)}},tt=function(t,e){t.notified||(t.notified=!0,_((function(){var n,r=t.reactions;while(n=r.get())J(n,t);t.notified=!1,e&&!t.rejection&&nt(t)})))},et=function(t,e,n){var r,i;V?(r=G.createEvent("Event"),r.promise=e,r.reason=n,r.initEvent(t,!1,!0),f.dispatchEvent(r)):r={promise:e,reason:n},!I&&(i=f["on"+t])?i(r):t===W&&A("Unhandled promise rejection",n)},nt=function(t){d(w,f,(function(){var e,n=t.facade,r=t.value,i=rt(t);if(i&&(e=O((function(){u?z.emit("unhandledRejection",r,n):et(W,n,r)})),t.rejection=u||rt(t)?K:Z,e.error))throw e.value}))},rt=function(t){return t.rejection!==Z&&!t.parent},it=function(t){d(w,f,(function(){var e=t.facade;u?z.emit("rejectionHandled",e):et(q,e,t.value)}))},ot=function(t,e,n){return function(r){t(e,r,n)}},at=function(t,e,n){t.done||(t.done=!0,n&&(t=n),t.value=e,t.state=X,tt(t,!0))},st=function(t,e,n){if(!t.done){t.done=!0,n&&(t=n);try{if(t.facade===e)throw new B("Promise can't be resolved itself");var r=Q(e);r?_((function(){var n={done:!1};try{d(r,e,ot(st,n,t),ot(at,n,t))}catch(i){at(n,i,t)}})):(t.value=e,t.state=Y,tt(t,!1))}catch(i){at({done:!1},i,t)}}};if(j&&(N=function(t){y(this,D),b(t),d(r,this);var e=L(this);try{t(ot(st,e),ot(at,e))}catch(n){at(e,n)}},D=N.prototype,r=function(t){P(this,{type:k,done:!1,notified:!1,parent:!1,reactions:new S,rejection:!1,state:H,value:void 0})},r.prototype=l(D,"then",(function(t,e){var n=L(this),r=U(x(this,N));return n.parent=!0,r.ok=!m(t)||t,r.fail=m(e)&&e,r.domain=u?z.domain:void 0,n.state===H?n.reactions.add(r):_((function(){J(r,n)})),r.promise})),i=function(){var t=new r,e=L(t);this.promise=t,this.resolve=ot(st,e),this.reject=ot(at,e)},R.f=U=function(t){return t===N||t===o?new i(t):$(t)},!c&&m(T)&&F!==Object.prototype)){a=F.then,M||l(F,"then",(function(t,e){var n=this;return new N((function(t,e){d(a,n,t,e)})).then(t,e)}),{unsafe:!0});try{delete F.constructor}catch(ct){}h&&h(F,D)}s({global:!0,constructor:!0,wrap:!0,forced:j},{Promise:N}),p(N,k,!1,!0),v(k)},"5eed":function(t,e,n){"use strict";var r=n("d256"),i=n("1c7e"),o=n("4738").CONSTRUCTOR;t.exports=o||!i((function(t){r.all(t).then(void 0,(function(){}))}))},"5f96":function(t,e,n){"use strict";var r=n("ebb5"),i=n("e330"),o=r.aTypedArray,a=r.exportTypedArrayMethod,s=i([].join);a("join",(function(t){return s(o(this),t)}))},6025:function(t,e,n){"use strict";n.r(e);var r=n("355e"),i=n("fa8e");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);var a=n("2877"),s=Object(a["a"])(i["default"],r["render"],r["staticRenderFns"],!1,null,null,null);e["default"]=s.exports},"60bd":function(t,e,n){"use strict";var r=n("cfe9"),i=n("d039"),o=n("e330"),a=n("ebb5"),s=n("e260"),c=n("b622"),u=c("iterator"),f=r.Uint8Array,d=o(s.values),l=o(s.keys),h=o(s.entries),p=a.aTypedArray,v=a.exportTypedArrayMethod,b=f&&f.prototype,m=!i((function(){b[u].call([1])})),g=!!b&&b.values&&b[u]===b.values&&"values"===b.values.name,y=function(){return d(p(this))};v("entries",(function(){return h(p(this))}),m),v("keys",(function(){return l(p(this))}),m),v("values",y,m||!g,{name:"values"}),v(u,y,m||!g,{name:"values"})},"618a":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0;e.render=function(){var t=this,e=t._self._c;return e("i",{staticClass:"ne-icon",class:[t.iconName,t.iconRotate,t.customClass],style:t.styles})},e.staticRenderFns=[]},"621a":function(t,e,n){"use strict";var r=n("cfe9"),i=n("e330"),o=n("83ab"),a=n("4b11"),s=n("5e77"),c=n("9112"),u=n("edd0"),f=n("6964"),d=n("d039"),l=n("19aa"),h=n("5926"),p=n("50c4"),v=n("0b25"),b=n("be8e"),m=n("77a7"),g=n("e163"),y=n("d2bb"),x=n("81d5"),w=n("f36a"),_=n("7156"),A=n("e893"),O=n("d44e"),S=n("69f3"),E=s.PROPER,T=s.CONFIGURABLE,C="ArrayBuffer",R="DataView",k="prototype",j="Wrong length",I="Wrong index",M=S.getterFor(C),L=S.getterFor(R),P=S.set,F=r[C],N=F,D=N&&N[k],B=r[R],G=B&&B[k],z=Object.prototype,U=r.Array,$=r.RangeError,V=i(x),W=i([].reverse),q=m.pack,H=m.unpack,Y=function(t){return[255&t]},X=function(t){return[255&t,t>>8&255]},Z=function(t){return[255&t,t>>8&255,t>>16&255,t>>24&255]},K=function(t){return t[3]<<24|t[2]<<16|t[1]<<8|t[0]},Q=function(t){return q(b(t),23,4)},J=function(t){return q(t,52,8)},tt=function(t,e,n){u(t[k],e,{configurable:!0,get:function(){return n(this)[e]}})},et=function(t,e,n,r){var i=L(t),o=v(n),a=!!r;if(o+e>i.byteLength)throw new $(I);var s=i.bytes,c=o+i.byteOffset,u=w(s,c,c+e);return a?u:W(u)},nt=function(t,e,n,r,i,o){var a=L(t),s=v(n),c=r(+i),u=!!o;if(s+e>a.byteLength)throw new $(I);for(var f=a.bytes,d=s+a.byteOffset,l=0;l<e;l++)f[d+l]=c[u?l:e-l-1]};if(a){var rt=E&&F.name!==C;d((function(){F(1)}))&&d((function(){new F(-1)}))&&!d((function(){return new F,new F(1.5),new F(NaN),1!==F.length||rt&&!T}))?rt&&T&&c(F,"name",C):(N=function(t){return l(this,D),_(new F(v(t)),this,N)},N[k]=D,D.constructor=N,A(N,F)),y&&g(G)!==z&&y(G,z);var it=new B(new N(2)),ot=i(G.setInt8);it.setInt8(0,2147483648),it.setInt8(1,2147483649),!it.getInt8(0)&&it.getInt8(1)||f(G,{setInt8:function(t,e){ot(this,t,e<<24>>24)},setUint8:function(t,e){ot(this,t,e<<24>>24)}},{unsafe:!0})}else N=function(t){l(this,D);var e=v(t);P(this,{type:C,bytes:V(U(e),0),byteLength:e}),o||(this.byteLength=e,this.detached=!1)},D=N[k],B=function(t,e,n){l(this,G),l(t,D);var r=M(t),i=r.byteLength,a=h(e);if(a<0||a>i)throw new $("Wrong offset");if(n=void 0===n?i-a:p(n),a+n>i)throw new $(j);P(this,{type:R,buffer:t,byteLength:n,byteOffset:a,bytes:r.bytes}),o||(this.buffer=t,this.byteLength=n,this.byteOffset=a)},G=B[k],o&&(tt(N,"byteLength",M),tt(B,"buffer",L),tt(B,"byteLength",L),tt(B,"byteOffset",L)),f(G,{getInt8:function(t){return et(this,1,t)[0]<<24>>24},getUint8:function(t){return et(this,1,t)[0]},getInt16:function(t){var e=et(this,2,t,arguments.length>1&&arguments[1]);return(e[1]<<8|e[0])<<16>>16},getUint16:function(t){var e=et(this,2,t,arguments.length>1&&arguments[1]);return e[1]<<8|e[0]},getInt32:function(t){return K(et(this,4,t,arguments.length>1&&arguments[1]))},getUint32:function(t){return K(et(this,4,t,arguments.length>1&&arguments[1]))>>>0},getFloat32:function(t){return H(et(this,4,t,arguments.length>1&&arguments[1]),23)},getFloat64:function(t){return H(et(this,8,t,arguments.length>1&&arguments[1]),52)},setInt8:function(t,e){nt(this,1,t,Y,e)},setUint8:function(t,e){nt(this,1,t,Y,e)},setInt16:function(t,e){nt(this,2,t,X,e,arguments.length>2&&arguments[2])},setUint16:function(t,e){nt(this,2,t,X,e,arguments.length>2&&arguments[2])},setInt32:function(t,e){nt(this,4,t,Z,e,arguments.length>2&&arguments[2])},setUint32:function(t,e){nt(this,4,t,Z,e,arguments.length>2&&arguments[2])},setFloat32:function(t,e){nt(this,4,t,Q,e,arguments.length>2&&arguments[2])},setFloat64:function(t,e){nt(this,8,t,J,e,arguments.length>2&&arguments[2])}});O(N,C),O(B,R),t.exports={ArrayBuffer:N,DataView:B}},"62b9":function(t,e,n){"use strict";n.r(e);var r=n("f4e5"),i=n("9f19");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("7500");var a=n("2877"),s=Object(a["a"])(i["default"],r["render"],r["staticRenderFns"],!1,null,"c173742c",null);e["default"]=s.exports},6374:function(t,e,n){"use strict";var r=n("cfe9"),i=Object.defineProperty;t.exports=function(t,e){try{i(r,t,{value:e,configurable:!0,writable:!0})}catch(n){r[t]=e}return e}},"649e":function(t,e,n){"use strict";var r=n("ebb5"),i=n("b727").some,o=r.aTypedArray,a=r.exportTypedArrayMethod;a("some",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},"64e8":function(t,e,n){"use strict";n.r(e);var r=n("8bbc"),i=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=i.a},6547:function(t,e,n){"use strict";var r=n("e330"),i=n("5926"),o=n("577e"),a=n("1d80"),s=r("".charAt),c=r("".charCodeAt),u=r("".slice),f=function(t){return function(e,n){var r,f,d=o(a(e)),l=i(n),h=d.length;return l<0||l>=h?t?"":void 0:(r=c(d,l),r<55296||r>56319||l+1===h||(f=c(d,l+1))<56320||f>57343?t?s(d,l):r:t?u(d,l,l+2):f-56320+(r-55296<<10)+65536)}};t.exports={codeAt:f(!1),charAt:f(!0)}},"65f0":function(t,e,n){"use strict";var r=n("0b42");t.exports=function(t,e){return new(r(t))(0===e?0:e)}},"65f5":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0,n("99af"),n("fb6a"),n("b0c0");e.render=function(){var t=this,e=t._self._c;return e("label",{staticClass:"ne-checkbox",class:[t.border&&t.checkboxSize?"ne-checkbox--"+t.checkboxSize:"",{"is-disabled":t.isDisabled},{"is-bordered":t.border},{"is-checked":t.isChecked}],attrs:{id:t.id}},[e("span",{staticClass:"ne-checkbox__input",class:{"is-disabled":t.isDisabled,"is-checked":t.isChecked,"is-indeterminate":t.indeterminate,"is-focus":t.focus},attrs:{tabindex:!!t.indeterminate&&0,role:!!t.indeterminate&&"checkbox","aria-checked":!!t.indeterminate&&"mixed"}},[e("span",{staticClass:"ne-checkbox__inner"}),t.trueLabel||t.falseLabel?e("input",{directives:[{name:"model",rawName:"v-model",value:t.model,expression:"model"}],staticClass:"ne-checkbox__original",attrs:{type:"checkbox","aria-hidden":t.indeterminate?"true":"false",name:t.name,disabled:t.isDisabled,"true-value":t.trueLabel,"false-value":t.falseLabel},domProps:{checked:Array.isArray(t.model)?t._i(t.model,null)>-1:t._q(t.model,t.trueLabel)},on:{change:[function(e){var n=t.model,r=e.target,i=r.checked?t.trueLabel:t.falseLabel;if(Array.isArray(n)){var o=null,a=t._i(n,o);r.checked?a<0&&(t.model=n.concat([o])):a>-1&&(t.model=n.slice(0,a).concat(n.slice(a+1)))}else t.model=i},t.handleChange],focus:function(e){t.focus=!0},blur:function(e){t.focus=!1}}}):e("input",{directives:[{name:"model",rawName:"v-model",value:t.model,expression:"model"}],staticClass:"ne-checkbox__original",attrs:{type:"checkbox","aria-hidden":t.indeterminate?"true":"false",disabled:t.isDisabled,name:t.name},domProps:{value:t.label,checked:Array.isArray(t.model)?t._i(t.model,t.label)>-1:t.model},on:{change:[function(e){var n=t.model,r=e.target,i=!!r.checked;if(Array.isArray(n)){var o=t.label,a=t._i(n,o);r.checked?a<0&&(t.model=n.concat([o])):a>-1&&(t.model=n.slice(0,a).concat(n.slice(a+1)))}else t.model=i},t.handleChange],focus:function(e){t.focus=!0},blur:function(e){t.focus=!1}}})]),t.$slots.default||t.label?e("span",{staticClass:"ne-checkbox__label"},[t._t("default"),t.$slots.default?t._e():[t._v(t._s(t.label))]],2):t._e()])},e.staticRenderFns=[]},"68ee":function(t,e,n){"use strict";var r=n("e330"),i=n("d039"),o=n("1626"),a=n("f5df"),s=n("d066"),c=n("8925"),u=function(){},f=s("Reflect","construct"),d=/^\s*(?:class|function)\b/,l=r(d.exec),h=!d.test(u),p=function(t){if(!o(t))return!1;try{return f(u,[],t),!0}catch(e){return!1}},v=function(t){if(!o(t))return!1;switch(a(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return h||!!l(d,c(t))}catch(e){return!0}};v.sham=!0,t.exports=!f||i((function(){var t;return p(p.call)||!p(Object)||!p((function(){t=!0}))||t}))?v:p},6964:function(t,e,n){"use strict";var r=n("cb2d");t.exports=function(t,e,n){for(var i in e)r(t,i,e[i],n);return t}},"69f3":function(t,e,n){"use strict";var r,i,o,a=n("cdce"),s=n("cfe9"),c=n("861d"),u=n("9112"),f=n("1a2d"),d=n("c6cd"),l=n("f772"),h=n("d012"),p="Object already initialized",v=s.TypeError,b=s.WeakMap,m=function(t){return o(t)?i(t):r(t,{})},g=function(t){return function(e){var n;if(!c(e)||(n=i(e)).type!==t)throw new v("Incompatible receiver, "+t+" required");return n}};if(a||d.state){var y=d.state||(d.state=new b);y.get=y.get,y.has=y.has,y.set=y.set,r=function(t,e){if(y.has(t))throw new v(p);return e.facade=t,y.set(t,e),e},i=function(t){return y.get(t)||{}},o=function(t){return y.has(t)}}else{var x=l("state");h[x]=!0,r=function(t,e){if(f(t,x))throw new v(p);return e.facade=t,u(t,x,e),e},i=function(t){return f(t,x)?t[x]:{}},o=function(t){return f(t,x)}}t.exports={set:r,get:i,has:o,enforce:m,getterFor:g}},"6ae3":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0;e.render=function(){var t=this,e=t._self._c;return e("div",{class:t.carouselClasses,on:{mouseenter:function(e){return e.stopPropagation(),t.handleMouseEnter.apply(null,arguments)},mouseleave:function(e){return e.stopPropagation(),t.handleMouseLeave.apply(null,arguments)}}},[e("div",{staticClass:"ne-carousel__container",style:{height:t.height}},[t.arrowDisplay?e("transition",{attrs:{name:"carousel-arrow-left"}},[e("button",{directives:[{name:"show",rawName:"v-show",value:("always"===t.arrow||t.hover)&&(t.loop||t.activeIndex>0),expression:"(arrow === 'always' || hover) && (loop || activeIndex > 0)"}],staticClass:"ne-carousel__arrow ne-carousel__arrow--left",attrs:{type:"button"},on:{mouseenter:function(e){return t.handleButtonEnter("left")},mouseleave:t.handleButtonLeave,click:function(e){return e.stopPropagation(),t.throttledArrowClick(t.activeIndex-1)}}},[e("NeIcon",{attrs:{name:"arrow-left"}})],1)]):t._e(),t.arrowDisplay?e("transition",{attrs:{name:"carousel-arrow-right"}},[e("button",{directives:[{name:"show",rawName:"v-show",value:("always"===t.arrow||t.hover)&&(t.loop||t.activeIndex<t.items.length-1),expression:"(arrow === 'always' || hover) && (loop || activeIndex < items.length - 1)"}],staticClass:"ne-carousel__arrow ne-carousel__arrow--right",attrs:{type:"button"},on:{mouseenter:function(e){return t.handleButtonEnter("right")},mouseleave:t.handleButtonLeave,click:function(e){return e.stopPropagation(),t.throttledArrowClick(t.activeIndex+1)}}},[e("NeIcon",{attrs:{name:"arrow-right"}})],1)]):t._e(),t._t("default")],2),"none"!==t.indicatorPosition?e("ul",{class:t.indicatorsClasses},t._l(t.items,(function(n,r){return e("li",{key:r,class:["ne-carousel__indicator","ne-carousel__indicator--"+t.direction,{"is-active":r===t.activeIndex}],on:{mouseenter:function(e){return t.throttledIndicatorHover(r)},click:function(e){return e.stopPropagation(),t.handleIndicatorClick(r)}}},[e("button",{staticClass:"ne-carousel__button"},[t.hasLabel?e("span",[t._v(t._s(n.label))]):t._e()])])})),0):t._e()])},e.staticRenderFns=[]},"6b75":function(t,e,n){"use strict";function r(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}Object.defineProperty(e,"__esModule",{value:!0}),e.default=r},"6b86":function(t,e,n){"use strict";n.r(e);var r=n("056e"),i=n("584d");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);var a=n("2877"),s=Object(a["a"])(i["default"],r["render"],r["staticRenderFns"],!1,null,null,null);e["default"]=s.exports},"6bd5":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0;e.render=function(){var t=this,e=t._self._c;return e("div",{staticClass:"ne-button-group"},[t._t("default")],2)},e.staticRenderFns=[]},"6ce5":function(t,e,n){"use strict";var r=n("df7e"),i=n("ebb5"),o=i.aTypedArray,a=i.exportTypedArrayMethod,s=i.getTypedArrayConstructor;a("toReversed",(function(){return r(o(this),s(this))}))},"6d8b":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0;e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"gameCoursewareContent",staticClass:"gameCourseware-container",class:[t.hiddenTools],attrs:{id:"gameCoursewareContent"}},[e("NeDialog",{attrs:{visible:t.showFailedDialog}},[e("p",{staticStyle:{"padding-top":"50px"}},[t._v("Failed to load")]),e("div",{attrs:{slot:"footer"},slot:"footer"},[e("button",{staticClass:"btn is-cancel",on:{click:function(e){return e.stopPropagation(),t.confirmExit.apply(null,arguments)}}},[t._v("Exit")]),e("button",{staticClass:"btn",on:{click:function(e){return e.stopPropagation(),t.reloadGamePage.apply(null,arguments)}}},[t._v("Reload")])])]),e("NeDialog",{attrs:{visible:t.showExitDialog,exitMask:!0}},[e("p",{staticStyle:{padding:"20px 15px 0 15px"}},[t._v(" Exit The Game "),e("span",[t._v("You cannot open the game again after exiting!")])]),e("div",{attrs:{slot:"footer"},slot:"footer"},[e("button",{staticClass:"btn is-cancel",on:{click:function(e){e.stopPropagation(),t.showExitDialog=!t.showExitDialog}}},[t._v("Cancel")]),e("button",{staticClass:"btn is-cancel",on:{click:function(e){return e.stopPropagation(),t.confirmExit.apply(null,arguments)}}},[t._v("Exit")])])]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.showTools,expression:"showTools"}],staticClass:"gameCourseware-tools"},[e("button",{staticClass:"exitView",on:{click:function(e){e.stopPropagation(),t.showExitDialog=!t.showExitDialog}}},[t._v("关闭")]),e("button",{staticClass:"reloadView",on:{click:function(e){return e.stopPropagation(),t.reloadGamePage.apply(null,arguments)}}},[t._v("刷新")])]),e("iframe",{ref:"gameCourseware",attrs:{src:t.url,frameborder:"0",scrolling:"no",id:"gameCourseware",name:"gameCourseware",width:"100%",height:"100%"}})],1)},e.staticRenderFns=[]},"6dd8":function(t,e,n){"use strict";n.r(e),function(t){var n=function(){if("undefined"!==typeof Map)return Map;function t(t,e){var n=-1;return t.some((function(t,r){return t[0]===e&&(n=r,!0)})),n}return function(){function e(){this.__entries__=[]}return Object.defineProperty(e.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),e.prototype.get=function(e){var n=t(this.__entries__,e),r=this.__entries__[n];return r&&r[1]},e.prototype.set=function(e,n){var r=t(this.__entries__,e);~r?this.__entries__[r][1]=n:this.__entries__.push([e,n])},e.prototype.delete=function(e){var n=this.__entries__,r=t(n,e);~r&&n.splice(r,1)},e.prototype.has=function(e){return!!~t(this.__entries__,e)},e.prototype.clear=function(){this.__entries__.splice(0)},e.prototype.forEach=function(t,e){void 0===e&&(e=null);for(var n=0,r=this.__entries__;n<r.length;n++){var i=r[n];t.call(e,i[1],i[0])}},e}()}(),r="undefined"!==typeof window&&"undefined"!==typeof document&&window.document===document,i=function(){return"undefined"!==typeof t&&t.Math===Math?t:"undefined"!==typeof self&&self.Math===Math?self:"undefined"!==typeof window&&window.Math===Math?window:Function("return this")()}(),o=function(){return"function"===typeof requestAnimationFrame?requestAnimationFrame.bind(i):function(t){return setTimeout((function(){return t(Date.now())}),1e3/60)}}(),a=2;function s(t,e){var n=!1,r=!1,i=0;function s(){n&&(n=!1,t()),r&&u()}function c(){o(s)}function u(){var t=Date.now();if(n){if(t-i<a)return;r=!0}else n=!0,r=!1,setTimeout(c,e);i=t}return u}var c=20,u=["top","right","bottom","left","width","height","size","weight"],f="undefined"!==typeof MutationObserver,d=function(){function t(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=s(this.refresh.bind(this),c)}return t.prototype.addObserver=function(t){~this.observers_.indexOf(t)||this.observers_.push(t),this.connected_||this.connect_()},t.prototype.removeObserver=function(t){var e=this.observers_,n=e.indexOf(t);~n&&e.splice(n,1),!e.length&&this.connected_&&this.disconnect_()},t.prototype.refresh=function(){var t=this.updateObservers_();t&&this.refresh()},t.prototype.updateObservers_=function(){var t=this.observers_.filter((function(t){return t.gatherActive(),t.hasActive()}));return t.forEach((function(t){return t.broadcastActive()})),t.length>0},t.prototype.connect_=function(){r&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),f?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},t.prototype.disconnect_=function(){r&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},t.prototype.onTransitionEnd_=function(t){var e=t.propertyName,n=void 0===e?"":e,r=u.some((function(t){return!!~n.indexOf(t)}));r&&this.refresh()},t.getInstance=function(){return this.instance_||(this.instance_=new t),this.instance_},t.instance_=null,t}(),l=function(t,e){for(var n=0,r=Object.keys(e);n<r.length;n++){var i=r[n];Object.defineProperty(t,i,{value:e[i],enumerable:!1,writable:!1,configurable:!0})}return t},h=function(t){var e=t&&t.ownerDocument&&t.ownerDocument.defaultView;return e||i},p=O(0,0,0,0);function v(t){return parseFloat(t)||0}function b(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return e.reduce((function(e,n){var r=t["border-"+n+"-width"];return e+v(r)}),0)}function m(t){for(var e=["top","right","bottom","left"],n={},r=0,i=e;r<i.length;r++){var o=i[r],a=t["padding-"+o];n[o]=v(a)}return n}function g(t){var e=t.getBBox();return O(0,0,e.width,e.height)}function y(t){var e=t.clientWidth,n=t.clientHeight;if(!e&&!n)return p;var r=h(t).getComputedStyle(t),i=m(r),o=i.left+i.right,a=i.top+i.bottom,s=v(r.width),c=v(r.height);if("border-box"===r.boxSizing&&(Math.round(s+o)!==e&&(s-=b(r,"left","right")+o),Math.round(c+a)!==n&&(c-=b(r,"top","bottom")+a)),!w(t)){var u=Math.round(s+o)-e,f=Math.round(c+a)-n;1!==Math.abs(u)&&(s-=u),1!==Math.abs(f)&&(c-=f)}return O(i.left,i.top,s,c)}var x=function(){return"undefined"!==typeof SVGGraphicsElement?function(t){return t instanceof h(t).SVGGraphicsElement}:function(t){return t instanceof h(t).SVGElement&&"function"===typeof t.getBBox}}();function w(t){return t===h(t).document.documentElement}function _(t){return r?x(t)?g(t):y(t):p}function A(t){var e=t.x,n=t.y,r=t.width,i=t.height,o="undefined"!==typeof DOMRectReadOnly?DOMRectReadOnly:Object,a=Object.create(o.prototype);return l(a,{x:e,y:n,width:r,height:i,top:n,right:e+r,bottom:i+n,left:e}),a}function O(t,e,n,r){return{x:t,y:e,width:n,height:r}}var S=function(){function t(t){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=O(0,0,0,0),this.target=t}return t.prototype.isActive=function(){var t=_(this.target);return this.contentRect_=t,t.width!==this.broadcastWidth||t.height!==this.broadcastHeight},t.prototype.broadcastRect=function(){var t=this.contentRect_;return this.broadcastWidth=t.width,this.broadcastHeight=t.height,t},t}(),E=function(){function t(t,e){var n=A(e);l(this,{target:t,contentRect:n})}return t}(),T=function(){function t(t,e,r){if(this.activeObservations_=[],this.observations_=new n,"function"!==typeof t)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=t,this.controller_=e,this.callbackCtx_=r}return t.prototype.observe=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!==typeof Element&&Element instanceof Object){if(!(t instanceof h(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)||(e.set(t,new S(t)),this.controller_.addObserver(this),this.controller_.refresh())}},t.prototype.unobserve=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!==typeof Element&&Element instanceof Object){if(!(t instanceof h(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)&&(e.delete(t),e.size||this.controller_.removeObserver(this))}},t.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},t.prototype.gatherActive=function(){var t=this;this.clearActive(),this.observations_.forEach((function(e){e.isActive()&&t.activeObservations_.push(e)}))},t.prototype.broadcastActive=function(){if(this.hasActive()){var t=this.callbackCtx_,e=this.activeObservations_.map((function(t){return new E(t.target,t.broadcastRect())}));this.callback_.call(t,e,t),this.clearActive()}},t.prototype.clearActive=function(){this.activeObservations_.splice(0)},t.prototype.hasActive=function(){return this.activeObservations_.length>0},t}(),C="undefined"!==typeof WeakMap?new WeakMap:new n,R=function(){function t(e){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=d.getInstance(),r=new T(e,n,this);C.set(this,r)}return t}();["observe","unobserve","disconnect"].forEach((function(t){R.prototype[t]=function(){var e;return(e=C.get(this))[t].apply(e,arguments)}}));var k=function(){return"undefined"!==typeof i.ResizeObserver?i.ResizeObserver:R}();e["default"]=k}.call(this,n("c8ba"))},"6f19":function(t,e,n){"use strict";var r=n("9112"),i=n("0d26"),o=n("b980"),a=Error.captureStackTrace;t.exports=function(t,e,n,s){o&&(a?a(t,e):r(t,"stack",i(n,s)))}},"6fc7":function(t,e,n){"use strict";n.r(e);var r=n("4a58"),i=n("c3c5");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);var a=n("2877"),s=Object(a["a"])(i["default"],r["render"],r["staticRenderFns"],!1,null,null,null);e["default"]=s.exports},7149:function(t,e,n){"use strict";var r=n("23e7"),i=n("d066"),o=n("c430"),a=n("d256"),s=n("4738").CONSTRUCTOR,c=n("cdf9"),u=i("Promise"),f=o&&!s;r({target:"Promise",stat:!0,forced:o||s},{resolve:function(t){return c(f&&this===u?a:this,t)}})},7156:function(t,e,n){"use strict";var r=n("1626"),i=n("861d"),o=n("d2bb");t.exports=function(t,e,n){var a,s;return o&&r(a=e.constructor)&&a!==n&&i(s=a.prototype)&&s!==n.prototype&&o(t,s),t}},7234:function(t,e,n){"use strict";t.exports=function(t){return null===t||void 0===t}},7276:function(t,e,n){"use strict";var r=n("65f5");n.o(r,"render")&&n.d(e,"render",(function(){return r["render"]})),n.o(r,"staticRenderFns")&&n.d(e,"staticRenderFns",(function(){return r["staticRenderFns"]}))},7282:function(t,e,n){"use strict";var r=n("e330"),i=n("59ed");t.exports=function(t,e,n){try{return r(i(Object.getOwnPropertyDescriptor(t,e)[n]))}catch(o){}}},"72f7":function(t,e,n){"use strict";var r=n("ebb5").exportTypedArrayMethod,i=n("d039"),o=n("cfe9"),a=n("e330"),s=o.Uint8Array,c=s&&s.prototype||{},u=[].toString,f=a([].join);i((function(){u.call({})}))&&(u=function(){return f(this)});var d=c.toString!==u;r("toString",u,d)},"735e":function(t,e,n){"use strict";var r=n("ebb5"),i=n("81d5"),o=n("f495"),a=n("f5df"),s=n("c65b"),c=n("e330"),u=n("d039"),f=r.aTypedArray,d=r.exportTypedArrayMethod,l=c("".slice),h=u((function(){var t=0;return new Int8Array(2).fill({valueOf:function(){return t++}}),1!==t}));d("fill",(function(t){var e=arguments.length;f(this);var n="Big"===l(a(this),0,3)?o(t):+t;return s(i,this,n,e>1?arguments[1]:void 0,e>2?arguments[2]:void 0)}),h)},7418:function(t,e,n){"use strict";e.f=Object.getOwnPropertySymbols},"74e8":function(t,e,n){"use strict";var r=n("23e7"),i=n("cfe9"),o=n("c65b"),a=n("83ab"),s=n("8aa7"),c=n("ebb5"),u=n("621a"),f=n("19aa"),d=n("5c6c"),l=n("9112"),h=n("eac5"),p=n("50c4"),v=n("0b25"),b=n("182d"),m=n("13a6"),g=n("a04b"),y=n("1a2d"),x=n("f5df"),w=n("861d"),_=n("d9b5"),A=n("7c73"),O=n("3a9b"),S=n("d2bb"),E=n("241c").f,T=n("a078"),C=n("b727").forEach,R=n("2626"),k=n("edd0"),j=n("9bf2"),I=n("06cf"),M=n("dfb9"),L=n("69f3"),P=n("7156"),F=L.get,N=L.set,D=L.enforce,B=j.f,G=I.f,z=i.RangeError,U=u.ArrayBuffer,$=U.prototype,V=u.DataView,W=c.NATIVE_ARRAY_BUFFER_VIEWS,q=c.TYPED_ARRAY_TAG,H=c.TypedArray,Y=c.TypedArrayPrototype,X=c.isTypedArray,Z="BYTES_PER_ELEMENT",K="Wrong length",Q=function(t,e){k(t,e,{configurable:!0,get:function(){return F(this)[e]}})},J=function(t){var e;return O($,t)||"ArrayBuffer"===(e=x(t))||"SharedArrayBuffer"===e},tt=function(t,e){return X(t)&&!_(e)&&e in t&&h(+e)&&e>=0},et=function(t,e){return e=g(e),tt(t,e)?d(2,t[e]):G(t,e)},nt=function(t,e,n){return e=g(e),!(tt(t,e)&&w(n)&&y(n,"value"))||y(n,"get")||y(n,"set")||n.configurable||y(n,"writable")&&!n.writable||y(n,"enumerable")&&!n.enumerable?B(t,e,n):(t[e]=n.value,t)};a?(W||(I.f=et,j.f=nt,Q(Y,"buffer"),Q(Y,"byteOffset"),Q(Y,"byteLength"),Q(Y,"length")),r({target:"Object",stat:!0,forced:!W},{getOwnPropertyDescriptor:et,defineProperty:nt}),t.exports=function(t,e,n){var a=t.match(/\d+/)[0]/8,c=t+(n?"Clamped":"")+"Array",u="get"+t,d="set"+t,h=i[c],g=h,y=g&&g.prototype,x={},_=function(t,e){var n=F(t);return n.view[u](e*a+n.byteOffset,!0)},O=function(t,e,r){var i=F(t);i.view[d](e*a+i.byteOffset,n?m(r):r,!0)},k=function(t,e){B(t,e,{get:function(){return _(this,e)},set:function(t){return O(this,e,t)},enumerable:!0})};W?s&&(g=e((function(t,e,n,r){return f(t,y),P(function(){return w(e)?J(e)?void 0!==r?new h(e,b(n,a),r):void 0!==n?new h(e,b(n,a)):new h(e):X(e)?M(g,e):o(T,g,e):new h(v(e))}(),t,g)})),S&&S(g,H),C(E(h),(function(t){t in g||l(g,t,h[t])})),g.prototype=y):(g=e((function(t,e,n,r){f(t,y);var i,s,c,u=0,d=0;if(w(e)){if(!J(e))return X(e)?M(g,e):o(T,g,e);i=e,d=b(n,a);var l=e.byteLength;if(void 0===r){if(l%a)throw new z(K);if(s=l-d,s<0)throw new z(K)}else if(s=p(r)*a,s+d>l)throw new z(K);c=s/a}else c=v(e),s=c*a,i=new U(s);N(t,{buffer:i,byteOffset:d,byteLength:s,length:c,view:new V(i)});while(u<c)k(t,u++)})),S&&S(g,H),y=g.prototype=A(Y)),y.constructor!==g&&l(y,"constructor",g),D(y).TypedArrayConstructor=g,q&&l(y,q,c);var j=g!==h;x[c]=g,r({global:!0,constructor:!0,forced:j,sham:!W},x),Z in g||l(g,Z,a),Z in y||l(y,Z,a),R(c)}):t.exports=function(){}},7500:function(t,e,n){"use strict";n("5981")},"75bd":function(t,e,n){"use strict";var r=n("cfe9"),i=n("4625"),o=n("b620"),a=r.ArrayBuffer,s=a&&a.prototype,c=s&&i(s.slice);t.exports=function(t){if(0!==o(t))return!1;if(!c)return!1;try{return c(t,0,0),!1}catch(e){return!0}}},7778:function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0");var i=r(n("6b86"));i.default.install=function(t){t.component(i.default.name,i.default)};e.default=i.default},"77a7":function(t,e,n){"use strict";var r=Array,i=Math.abs,o=Math.pow,a=Math.floor,s=Math.log,c=Math.LN2,u=function(t,e,n){var u,f,d,l=r(n),h=8*n-e-1,p=(1<<h)-1,v=p>>1,b=23===e?o(2,-24)-o(2,-77):0,m=t<0||0===t&&1/t<0?1:0,g=0;t=i(t),t!==t||t===1/0?(f=t!==t?1:0,u=p):(u=a(s(t)/c),d=o(2,-u),t*d<1&&(u--,d*=2),t+=u+v>=1?b/d:b*o(2,1-v),t*d>=2&&(u++,d/=2),u+v>=p?(f=0,u=p):u+v>=1?(f=(t*d-1)*o(2,e),u+=v):(f=t*o(2,v-1)*o(2,e),u=0));while(e>=8)l[g++]=255&f,f/=256,e-=8;u=u<<e|f,h+=e;while(h>0)l[g++]=255&u,u/=256,h-=8;return l[g-1]|=128*m,l},f=function(t,e){var n,r=t.length,i=8*r-e-1,a=(1<<i)-1,s=a>>1,c=i-7,u=r-1,f=t[u--],d=127&f;f>>=7;while(c>0)d=256*d+t[u--],c-=8;n=d&(1<<-c)-1,d>>=-c,c+=e;while(c>0)n=256*n+t[u--],c-=8;if(0===d)d=1-s;else{if(d===a)return n?NaN:f?-1/0:1/0;n+=o(2,e),d-=s}return(f?-1:1)*n*o(2,d-e)};t.exports={pack:u,unpack:f}},7839:function(t,e,n){"use strict";t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},"785a":function(t,e,n){"use strict";var r=n("cc12"),i=r("span").classList,o=i&&i.constructor&&i.constructor.prototype;t.exports=o===Object.prototype?void 0:o},"786b":function(t,e,n){"use strict";n.r(e);var r=n("0e6d"),i=n("934d");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("deca");var a=n("2877"),s=Object(a["a"])(i["default"],r["render"],r["staticRenderFns"],!1,null,"b9d29148",null);e["default"]=s.exports},"7a1a":function(t,e,n){(function(t,n){n(e)})(0,(function(t){"use strict";function e(t,e,n,r){var i,o=!1,a=0;function s(){i&&clearTimeout(i)}function c(){s(),o=!0}function u(){for(var c=arguments.length,u=new Array(c),f=0;f<c;f++)u[f]=arguments[f];var d=this,l=Date.now()-a;function h(){a=Date.now(),n.apply(d,u)}function p(){i=void 0}o||(r&&!i&&h(),s(),void 0===r&&l>t?h():!0!==e&&(i=setTimeout(r?p:h,void 0===r?t-l:t)))}return"boolean"!==typeof e&&(r=n,n=e,e=void 0),u.cancel=c,u}function n(t,n,r){return void 0===r?e(t,n,!1):e(t,r,!1!==n)}t.debounce=n,t.throttle=e,Object.defineProperty(t,"__esModule",{value:!0})}))},"7a54":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0");var i=r(n("bd0a"));i.default.install=function(t){t.component(i.default.name,i.default)};e.default=i.default},"7ab0":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0");var i=r(n("c67a"));i.default.install=function(t){t.component(i.default.name,i.default)};e.default=i.default},"7b0b":function(t,e,n){"use strict";var r=n("1d80"),i=Object;t.exports=function(t){return i(r(t))}},"7c73":function(t,e,n){"use strict";var r,i=n("825a"),o=n("37e8"),a=n("7839"),s=n("d012"),c=n("1be4"),u=n("cc12"),f=n("f772"),d=">",l="<",h="prototype",p="script",v=f("IE_PROTO"),b=function(){},m=function(t){return l+p+d+t+l+"/"+p+d},g=function(t){t.write(m("")),t.close();var e=t.parentWindow.Object;return t=null,e},y=function(){var t,e=u("iframe"),n="java"+p+":";return e.style.display="none",c.appendChild(e),e.src=String(n),t=e.contentWindow.document,t.open(),t.write(m("document.F=Object")),t.close(),t.F},x=function(){try{r=new ActiveXObject("htmlfile")}catch(e){}x="undefined"!=typeof document?document.domain&&r?g(r):y():g(r);var t=a.length;while(t--)delete x[h][a[t]];return x()};s[v]=!0,t.exports=Object.create||function(t,e){var n;return null!==t?(b[h]=i(t),n=new b,b[h]=null,n[v]=t):n=x(),void 0===e?n:o.f(n,e)}},"7d59":function(t,e,n){"use strict";n.r(e);var r=n("bdb8"),i=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=i.a},"7e84":function(t,e,n){"use strict";function r(t){return e.default=r=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},r(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=r,n("3410"),n("1f68"),n("131a")},8172:function(t,e,n){"use strict";var r=n("e065"),i=n("57b9");r("toPrimitive"),i()},"81b2":function(t,e,n){"use strict";var r=n("23e7"),i=n("cfe9"),o=n("d066"),a=n("e330"),s=n("c65b"),c=n("d039"),u=n("577e"),f=n("d6d6"),d=n("b917").c2i,l=/[^\d+/a-z]/i,h=/[\t\n\f\r ]+/g,p=/[=]{1,2}$/,v=o("atob"),b=String.fromCharCode,m=a("".charAt),g=a("".replace),y=a(l.exec),x=!!v&&!c((function(){return"hi"!==v("aGk=")})),w=x&&c((function(){return""!==v(" ")})),_=x&&!c((function(){v("a")})),A=x&&!c((function(){v()})),O=x&&1!==v.length,S=!x||w||_||A||O;r({global:!0,bind:!0,enumerable:!0,forced:S},{atob:function(t){if(f(arguments.length,1),x&&!w&&!_)return s(v,i,t);var e,n,r,a=g(u(t),h,""),c="",A=0,O=0;if(a.length%4===0&&(a=g(a,p,"")),e=a.length,e%4===1||y(l,a))throw new(o("DOMException"))("The string is not correctly encoded","InvalidCharacterError");while(A<e)n=m(a,A++),r=O%4?64*r+d[n]:d[n],O++%4&&(c+=b(255&r>>(-2*O&6)));return c}})},"81d5":function(t,e,n){"use strict";var r=n("7b0b"),i=n("23cb"),o=n("07fa");t.exports=function(t){var e=r(this),n=o(e),a=arguments.length,s=i(a>1?arguments[1]:void 0,n),c=a>2?arguments[2]:void 0,u=void 0===c?n:i(c,n);while(u>s)e[s++]=t;return e}},"825a":function(t,e,n){"use strict";var r=n("861d"),i=String,o=TypeError;t.exports=function(t){if(r(t))return t;throw new o(i(t)+" is not an object")}},"82f8":function(t,e,n){"use strict";var r=n("ebb5"),i=n("4d64").includes,o=r.aTypedArray,a=r.exportTypedArrayMethod;a("includes",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},"83ab":function(t,e,n){"use strict";var r=n("d039");t.exports=!r((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},8418:function(t,e,n){"use strict";var r=n("83ab"),i=n("9bf2"),o=n("5c6c");t.exports=function(t,e,n){r?i.f(t,e,o(0,n)):t[e]=n}},"841c":function(t,e,n){"use strict";var r=n("c65b"),i=n("d784"),o=n("825a"),a=n("7234"),s=n("1d80"),c=n("129f"),u=n("577e"),f=n("dc4a"),d=n("14c3");i("search",(function(t,e,n){return[function(e){var n=s(this),i=a(e)?void 0:f(e,t);return i?r(i,e,n):new RegExp(e)[t](u(n))},function(t){var r=o(this),i=u(t),a=n(e,r,i);if(a.done)return a.value;var s=r.lastIndex;c(s,0)||(r.lastIndex=0);var f=d(r,i);return c(r.lastIndex,s)||(r.lastIndex=s),null===f?-1:f.index}]}))},8440:function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i=r(n("a215"));e.default={name:"NeCheckboxGroup",componentName:"NeCheckboxGroup",mixins:[i.default],inject:{neFormItem:{default:""}},props:{value:{},disabled:Boolean,min:Number,max:Number,size:String,fill:String,textColor:String},computed:{_neFormItemSize:function(){return(this.neFormItem||{}).neFormItemSize},checkboxGroupSize:function(){return this.size||this._neFormItemSize||(this.$NeosUI||{}).size}},watch:{value:function(t){this.dispatch("NeFormItem","ne.form.change",[t])}}}},8558:function(t,e,n){"use strict";var r=n("cfe9"),i=n("b5db"),o=n("c6b6"),a=function(t){return i.slice(0,t.length)===t};t.exports=function(){return a("Bun/")?"BUN":a("Cloudflare-Workers")?"CLOUDFLARE":a("Deno/")?"DENO":a("Node.js/")?"NODE":r.Bun&&"string"==typeof Bun.version?"BUN":r.Deno&&"object"==typeof Deno.version?"DENO":"process"===o(r.process)?"NODE":r.window&&r.document?"BROWSER":"REST"}()},"857a":function(t,e,n){"use strict";var r=n("e330"),i=n("1d80"),o=n("577e"),a=/"/g,s=r("".replace);t.exports=function(t,e,n,r){var c=o(i(t)),u="<"+e;return""!==n&&(u+=" "+n+'="'+s(o(r),a,"&quot;")+'"'),u+">"+c+"</"+e+">"}},"861d":function(t,e,n){"use strict";var r=n("1626");t.exports=function(t){return"object"==typeof t?null!==t:r(t)}},8860:function(t,e,n){"use strict";n.r(e);var r=n("21159"),i=n("5bc4");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);var a=n("2877"),s=Object(a["a"])(i["default"],r["render"],r["staticRenderFns"],!1,null,null,null);e["default"]=s.exports},8875:function(t,e,n){var r,i,o;(function(n,a){i=[],r=a,o="function"===typeof r?r.apply(e,i):r,void 0===o||(t.exports=o)})("undefined"!==typeof self&&self,(function(){function t(){var e=Object.getOwnPropertyDescriptor(document,"currentScript");if(!e&&"currentScript"in document&&document.currentScript)return document.currentScript;if(e&&e.get!==t&&document.currentScript)return document.currentScript;try{throw new Error}catch(h){var n,r,i,o=/.*at [^(]*\((.*):(.+):(.+)\)$/gi,a=/@([^@]*):(\d+):(\d+)\s*$/gi,s=o.exec(h.stack)||a.exec(h.stack),c=s&&s[1]||!1,u=s&&s[2]||!1,f=document.location.href.replace(document.location.hash,""),d=document.getElementsByTagName("script");c===f&&(n=document.documentElement.outerHTML,r=new RegExp("(?:[^\\n]+?\\n){0,"+(u-2)+"}[^<]*<script>([\\d\\D]*?)<\\/script>[\\d\\D]*","i"),i=n.replace(r,"$1").trim());for(var l=0;l<d.length;l++){if("interactive"===d[l].readyState)return d[l];if(d[l].src===c)return d[l];if(c===f&&d[l].innerHTML&&d[l].innerHTML.trim()===i)return d[l]}return null}}return t}))},8925:function(t,e,n){"use strict";var r=n("e330"),i=n("1626"),o=n("c6cd"),a=r(Function.toString);i(o.inspectSource)||(o.inspectSource=function(t){return a(t)}),t.exports=o.inspectSource},"8aa5":function(t,e,n){"use strict";var r=n("6547").charAt;t.exports=function(t,e,n){return e+(n?r(t,e).length:1)}},"8aa7":function(t,e,n){"use strict";var r=n("cfe9"),i=n("d039"),o=n("1c7e"),a=n("ebb5").NATIVE_ARRAY_BUFFER_VIEWS,s=r.ArrayBuffer,c=r.Int8Array;t.exports=!a||!i((function(){c(1)}))||!i((function(){new c(-1)}))||!o((function(t){new c,new c(null),new c(1.5),new c(t)}),!0)||i((function(){return 1!==new c(new s(2),1,void 0).length}))},"8b09":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0");var i=r(n("5234"));i.default.install=function(t){t.component(i.default.name,i.default)};e.default=i.default},"8b86":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=r(n("a215"));e.default={name:"NeRadio",mixins:[i.default],inject:{neForm:{default:""},neFormItem:{default:""}},componentName:"NeRadio",props:{value:{},label:{},disabled:Boolean,name:String,border:Boolean,size:String},data:function(){return{focus:!1}},computed:{isGroup:function(){var t=this.$parent;while(t){if("NeRadioGroup"===t.$options.componentName)return this._radioGroup=t,!0;t=t.$parent}return!1},model:{get:function(){return this.isGroup?this._radioGroup.value:this.value},set:function(t){this.isGroup?this.dispatch("NeRadioGroup","input",[t]):this.$emit("input",t),this.$refs.radio&&(this.$refs.radio.checked=this.model===this.label)}},_neFormItemSize:function(){return(this.neFormItem||{}).neFormItemSize},radioSize:function(){var t=this.size||this._neFormItemSize||(this.$NeosUI||{}).size;return this.isGroup&&this._radioGroup.radioGroupSize||t},isDisabled:function(){return this.isGroup?this._radioGroup.disabled||this.disabled||(this.neForm||{}).disabled:this.disabled||(this.neForm||{}).disabled},tabIndex:function(){return this.isDisabled||this.isGroup&&this.model!==this.label?-1:0}},methods:{handleChange:function(){var t=this;this.$nextTick((function(){t.$emit("change",t.model),t.isGroup&&t.dispatch("NeRadioGroup","handleChange",t.model)}))}}}},"8bb9":function(t,e,n){var r=n("24fb");e=r(!1),e.push([t.i,'.game-dialog[data-v-c173742c]{z-index:20}.game-courseware-mask[data-v-c173742c],.game-dialog[data-v-c173742c]{position:fixed;top:0;left:0;bottom:0;right:0}.game-courseware-mask[data-v-c173742c]{background-color:rgba(0,0,0,.5)!important;background-image:none!important}.ne-dialog[data-v-c173742c]{background:#fff;border-radius:16px;width:340px;min-height:200px;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);-webkit-box-sizing:border-box;box-sizing:border-box;text-align:center}.ne-dialog--body[data-v-c173742c]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;padding:10px 0}.ne-dialog--body[data-v-c173742c],.ne-dialog--body[data-v-c173742c] p{font-family:Helvetica,PingFang SC,"sans-serif",Arial,Verdana,Microsoft YaHei}.ne-dialog--body[data-v-c173742c] p{margin:0;font-size:18px;font-weight:600;color:#172b4d;line-height:26px}.ne-dialog--footer[data-v-c173742c]{padding:10px;position:absolute;bottom:0;left:0;right:0}.ne-dialog--footer[data-v-c173742c] div{display:-webkit-box;display:-ms-flexbox;display:flex;-ms-flex-pack:distribute;justify-content:space-around}.ne-dialog--footer[data-v-c173742c] .btn{background:linear-gradient(45deg,#ffd518,#ffaa0a);border-radius:30px;font-size:16px;font-family:Helvetica,PingFang SC,"sans-serif",Arial,Verdana,Microsoft YaHei;font-weight:600;color:#fff;line-height:20px;border:0 none;padding:15px 0;width:45%;outline:0 none;text-align:center;cursor:pointer}.ne-dialog--footer[data-v-c173742c] .btn.is-cancel{background:#fff3dc;color:#ffaa0a}',""]),t.exports=e},"8bbc":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("cb29");var i=r(n("a215"));e.default={name:"NeRadioButton",mixins:[i.default],inject:{neForm:{default:""},neFormItem:{default:""}},props:{label:{},disabled:Boolean,name:String},data:function(){return{focus:!1}},computed:{value:{get:function(){return this._radioGroup.value},set:function(t){this._radioGroup.$emit("input",t)}},_radioGroup:function(){var t=this.$parent;while(t){if("NeRadioGroup"===t.$options.componentName)return t;t=t.$parent}return!1},activeStyle:function(){return{backgroundColor:this._radioGroup.fill||"",borderColor:this._radioGroup.fill||"",boxShadow:this._radioGroup.fill?"-1px 0 0 0 ".concat(this._radioGroup.fill):"",color:this._radioGroup.textColor||""}},_neFormItemSize:function(){return(this.elFormItem||{}).elFormItemSize},size:function(){return this._radioGroup.radioGroupSize||this._neFormItemSize||(this.$NeosUI||{}).size},isDisabled:function(){return this.disabled||this._radioGroup.disabled||(this.neForm||{}).disabled},tabIndex:function(){return this.isDisabled||this._radioGroup&&this.value!==this.label?-1:0}},methods:{handleChange:function(){var t=this;this.$nextTick((function(){t.dispatch("NeRadioGroup","handleChange",t.value)}))}}}},"8bbf":function(e,n){e.exports=t},"8bd4":function(t,e,n){"use strict";var r=n("d066"),i=n("d44e"),o="DOMException";i(r(o),o)},"8dbc":function(t,e,n){"use strict";var r=n("463d");n.o(r,"render")&&n.d(e,"render",(function(){return r["render"]})),n.o(r,"staticRenderFns")&&n.d(e,"staticRenderFns",(function(){return r["staticRenderFns"]}))},"8ed6":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=i;var r=n("ca00");function i(t){for(var e=1,n=arguments.length;e<n;e++){var i=arguments[e]||{};for(var o in i)if((0,r.hasOwn)(i,o)){var a=i[o];void 0!==a&&(t[o]=a)}}return t}},"8f33":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=s,n("4ae1");var r=a(n("7e84")),i=a(n("d967")),o=a(n("99de"));function a(t){return t&&t.__esModule?t:{default:t}}function s(t,e,n){return e=(0,r.default)(e),(0,o.default)(t,(0,i.default)()?Reflect.construct(e,n||[],(0,r.default)(t).constructor):e.apply(t,n))}},"8fde":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("d3b7"),n("25f0");var i=r(n("8ed6")),o=r(n("0530"));e.default={name:"NeClockCountdown",props:{options:{type:Object,default:function(){}},customClass:{type:String,default:""}},data:function(){return{countdown:null,opts:{ratio:2,size:60,countDownTime:0,borderWidth:2,borderColor:"#9DAAB6",pointerWidth:2,pointerColor:"#9DAAB6",pointerDotColor:"#ffffff",background:"#E6ECF1"},uuid:Math.random().toString(36).substring(3,20)}},computed:{countDownId:function(){return"countDown_".concat(this.uuid)}},watch:{options:{deep:!0,handler:function(t){t&&this.init()}}},mounted:function(){var t=this;this.$nextTick((function(){t.init(),t.countdown.on("complete",(function(){t.$emit("complete"),t.countdown.destroy()})),t.countdown.on("remain",(function(e){t.$emit("remain",e)}))}))},beforeDestroy:function(){this.countdown.destroy()},destroyed:function(){this.$destroy()},methods:{init:function(){var t=this.$refs.countDown;this.opts=(0,i.default)({dom:t},this.opts,this.options),this.countdown=new o.default(this.opts)}}}},"907a":function(t,e,n){"use strict";var r=n("ebb5"),i=n("07fa"),o=n("5926"),a=r.aTypedArray,s=r.exportTypedArrayMethod;s("at",(function(t){var e=a(this),n=i(e),r=o(t),s=r>=0?r:n+r;return s<0||s>=n?void 0:e[s]}))},"90d8":function(t,e,n){"use strict";var r=n("c65b"),i=n("1a2d"),o=n("3a9b"),a=n("ad6d"),s=RegExp.prototype;t.exports=function(t){var e=t.flags;return void 0!==e||"flags"in s||i(t,"flags")||!o(s,t)?e:r(a,t)}},"90e3":function(t,e,n){"use strict";var r=n("e330"),i=0,o=Math.random(),a=r(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+a(++i+o,36)}},9112:function(t,e,n){"use strict";var r=n("83ab"),i=n("9bf2"),o=n("5c6c");t.exports=r?function(t,e,n){return i.f(t,e,o(1,n))}:function(t,e,n){return t[e]=n,t}},9263:function(t,e,n){"use strict";var r=n("c65b"),i=n("e330"),o=n("577e"),a=n("ad6d"),s=n("9f7f"),c=n("5692"),u=n("7c73"),f=n("69f3").get,d=n("fce3"),l=n("107c"),h=c("native-string-replace",String.prototype.replace),p=RegExp.prototype.exec,v=p,b=i("".charAt),m=i("".indexOf),g=i("".replace),y=i("".slice),x=function(){var t=/a/,e=/b*/g;return r(p,t,"a"),r(p,e,"a"),0!==t.lastIndex||0!==e.lastIndex}(),w=s.BROKEN_CARET,_=void 0!==/()??/.exec("")[1],A=x||_||w||d||l;A&&(v=function(t){var e,n,i,s,c,d,l,A=this,O=f(A),S=o(t),E=O.raw;if(E)return E.lastIndex=A.lastIndex,e=r(v,E,S),A.lastIndex=E.lastIndex,e;var T=O.groups,C=w&&A.sticky,R=r(a,A),k=A.source,j=0,I=S;if(C&&(R=g(R,"y",""),-1===m(R,"g")&&(R+="g"),I=y(S,A.lastIndex),A.lastIndex>0&&(!A.multiline||A.multiline&&"\n"!==b(S,A.lastIndex-1))&&(k="(?: "+k+")",I=" "+I,j++),n=new RegExp("^(?:"+k+")",R)),_&&(n=new RegExp("^"+k+"$(?!\\s)",R)),x&&(i=A.lastIndex),s=r(p,C?n:A,I),C?s?(s.input=y(s.input,j),s[0]=y(s[0],j),s.index=A.lastIndex,A.lastIndex+=s[0].length):A.lastIndex=0:x&&s&&(A.lastIndex=A.global?s.index+s[0].length:i),_&&s&&s.length>1&&r(h,s[0],n,(function(){for(c=1;c<arguments.length-2;c++)void 0===arguments[c]&&(s[c]=void 0)})),s&&T)for(s.groups=d=u(null),c=0;c<T.length;c++)l=T[c],d[l[0]]=s[l[1]];return s}),t.exports=v},"934d":function(t,e,n){"use strict";n.r(e);var r=n("336e"),i=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=i.a},"944a":function(t,e,n){"use strict";var r=n("d066"),i=n("e065"),o=n("d44e");i("toStringTag"),o(r("Symbol"),"Symbol")},"94ca":function(t,e,n){"use strict";var r=n("d039"),i=n("1626"),o=/#|\.prototype\./,a=function(t,e){var n=c[s(t)];return n===f||n!==u&&(i(e)?r(e):!!e)},s=a.normalize=function(t){return String(t).replace(o,".").toLowerCase()},c=a.data={},u=a.NATIVE="N",f=a.POLYFILL="P";t.exports=a},"96db":function(t,e,n){"use strict";var r=n("f98f");n.o(r,"render")&&n.d(e,"render",(function(){return r["render"]})),n.o(r,"staticRenderFns")&&n.d(e,"staticRenderFns",(function(){return r["staticRenderFns"]}))},"986a":function(t,e,n){"use strict";var r=n("ebb5"),i=n("a258").findLast,o=r.aTypedArray,a=r.exportTypedArrayMethod;a("findLast",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},"99af":function(t,e,n){"use strict";var r=n("23e7"),i=n("d039"),o=n("e8b5"),a=n("861d"),s=n("7b0b"),c=n("07fa"),u=n("3511"),f=n("8418"),d=n("65f0"),l=n("1dde"),h=n("b622"),p=n("1212"),v=h("isConcatSpreadable"),b=p>=51||!i((function(){var t=[];return t[v]=!1,t.concat()[0]!==t})),m=function(t){if(!a(t))return!1;var e=t[v];return void 0!==e?!!e:o(t)},g=!b||!l("concat");r({target:"Array",proto:!0,arity:1,forced:g},{concat:function(t){var e,n,r,i,o,a=s(this),l=d(a,0),h=0;for(e=-1,r=arguments.length;e<r;e++)if(o=-1===e?a:arguments[e],m(o))for(i=c(o),u(h+i),n=0;n<i;n++,h++)n in o&&f(l,h,o[n]);else u(h+1),f(l,h++,o);return l.length=h,l}})},"99de":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=a,n("d9e2");var r=o(n("53ca")),i=o(n("257e"));function o(t){return t&&t.__esModule?t:{default:t}}function a(t,e){if(e&&("object"==(0,r.default)(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return(0,i.default)(t)}},"99f4":function(t,e,n){"use strict";var r=n("b5db");t.exports=/MSIE|Trident/.test(r)},"9a1f":function(t,e,n){"use strict";var r=n("c65b"),i=n("59ed"),o=n("825a"),a=n("0d51"),s=n("35a1"),c=TypeError;t.exports=function(t,e){var n=arguments.length<2?s(t):e;if(i(n))return o(r(n,t));throw new c(a(t)+" is not iterable")}},"9a8c":function(t,e,n){"use strict";var r=n("e330"),i=n("ebb5"),o=n("145e"),a=r(o),s=i.aTypedArray,c=i.exportTypedArrayMethod;c("copyWithin",(function(t,e){return a(s(this),t,e,arguments.length>2?arguments[2]:void 0)}))},"9adc":function(t,e,n){"use strict";var r=n("8558");t.exports="NODE"===r},"9b68":function(t,e,n){"use strict";n.r(e);var r=n("a3a8"),i=n("64e8");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);var a=n("2877"),s=Object(a["a"])(i["default"],r["render"],r["staticRenderFns"],!1,null,null,null);e["default"]=s.exports},"9bdd":function(t,e,n){"use strict";var r=n("825a"),i=n("2a62");t.exports=function(t,e,n,o){try{return o?e(r(n)[0],n[1]):e(n)}catch(a){i(t,"throw",a)}}},"9bf2":function(t,e,n){"use strict";var r=n("83ab"),i=n("0cfb"),o=n("aed9"),a=n("825a"),s=n("a04b"),c=TypeError,u=Object.defineProperty,f=Object.getOwnPropertyDescriptor,d="enumerable",l="configurable",h="writable";e.f=r?o?function(t,e,n){if(a(t),e=s(e),a(n),"function"===typeof t&&"prototype"===e&&"value"in n&&h in n&&!n[h]){var r=f(t,e);r&&r[h]&&(t[e]=n.value,n={configurable:l in n?n[l]:r[l],enumerable:d in n?n[d]:r[d],writable:!1})}return u(t,e,n)}:u:function(t,e,n){if(a(t),e=s(e),a(n),i)try{return u(t,e,n)}catch(r){}if("get"in n||"set"in n)throw new c("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},"9f19":function(t,e,n){"use strict";n.r(e);var r=n("c715"),i=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=i.a},"9f7f":function(t,e,n){"use strict";var r=n("d039"),i=n("cfe9"),o=i.RegExp,a=r((function(){var t=o("a","y");return t.lastIndex=2,null!==t.exec("abcd")})),s=a||r((function(){return!o("a","y").sticky})),c=a||r((function(){var t=o("^r","gy");return t.lastIndex=2,null!==t.exec("str")}));t.exports={BROKEN_CARET:c,MISSED_STICKY:s,UNSUPPORTED_Y:a}},"9f92":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("14d9"),n("a9e3"),n("d3b7"),n("25f0");var i=r(n("a215"));e.default={name:"NeCheckbox",mixins:[i.default],inject:{neForm:{default:""},neFormItem:{default:""}},componentName:"NeCheckbox",props:{value:{},label:{},indeterminate:Boolean,disabled:Boolean,checked:Boolean,name:String,trueLabel:[String,Number],falseLabel:[String,Number],id:String,controls:String,border:Boolean,size:String},data:function(){return{selfModel:!1,focus:!1,isLimitExceeded:!1}},computed:{model:{get:function(){return this.isGroup?this.store:void 0!==this.value?this.value:this.selfModel},set:function(t){this.isGroup?(this.isLimitExceeded=!1,void 0!==this._checkboxGroup.min&&t.length<this._checkboxGroup.min&&(this.isLimitExceeded=!0),void 0!==this._checkboxGroup.max&&t.length>this._checkboxGroup.max&&(this.isLimitExceeded=!0),!1===this.isLimitExceeded&&this.dispatch("NeCheckboxGroup","input",[t])):(this.$emit("input",t),this.selfModel=t)}},isChecked:function(){return"[object Boolean]"==={}.toString.call(this.model)?this.model:Array.isArray(this.model)?this.model.indexOf(this.label)>-1:null!==this.model&&void 0!==this.model?this.model===this.trueLabel:void 0},isGroup:function(){var t=this.$parent;while(t){if("NeCheckboxGroup"===t.$options.componentName)return this._checkboxGroup=t,!0;t=t.$parent}return!1},store:function(){return this._checkboxGroup?this._checkboxGroup.value:this.value},isLimitDisabled:function(){var t=this._checkboxGroup,e=t.max,n=t.min;return!(!e&&!n)&&this.model.length>=e&&!this.isChecked||this.model.length<=n&&this.isChecked},isDisabled:function(){return this.isGroup?this._checkboxGroup.disabled||this.disabled||(this.neForm||{}).disabled||this.isLimitDisabled:this.disabled||(this.neForm||{}).disabled},_neFormItemSize:function(){return(this.neFormItem||{}).neFormItemSize},checkboxSize:function(){var t=this.size||this._neFormItemSize||(this.$NeosUI||{}).size;return this.isGroup&&this._checkboxGroup.checkboxGroupSize||t}},watch:{value:function(t){this.dispatch("NeFormItem","ne.form.change",t)}},created:function(){this.checked&&this.addToStore()},mounted:function(){this.indeterminate&&this.$el.setAttribute("aria-controls",this.controls)},methods:{addToStore:function(){Array.isArray(this.model)&&-1===this.model.indexOf(this.label)?this.model.push(this.label):this.model=this.trueLabel||!0},handleChange:function(t){var e,n=this;this.isLimitExceeded||(e=t.target.checked?void 0===this.trueLabel||this.trueLabel:void 0!==this.falseLabel&&this.falseLabel,this.$emit("change",e,t),this.$nextTick((function(){n.isGroup&&n.dispatch("NeCheckboxGroup","change",[n._checkboxGroup.value])})))}}}},"9fef":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAADg0lEQVRoQ+WbXWjNYRzHP5vMml1oXGxatoM0RApZLTd2YbVESWmsGXNFIkLJncKEG7dMWVotIil0zpKalBtasqS8HduNUxLy0k76Hue/jrO/8z8v/zee5/Kc5/k9v8//efm9PM9ThndlOjAPmAvMBuYA1UAFMCPd7XfgB/AZ+AAkgDHgLfDTC9XKXBY6E1gGLAbqgWlFyp8A4sBzYAT4UqScKc3cAm4AWoCFQLlbyqXlJIGXwDDwplTZpQI3AuvSU7dUXfJpr6k+BLzOp7JdnWKBtRbXp6dvsX2X0k7T/G567RckpxjgpcAGoLKgntyv/A24BTwrRHQhwNqA2oDVhXTgQ93HwB1AG51jyRdYZmQrEHGUGEyFV8AAIDOXs+QDLFOzHahzEhbw/+NAv5MJcwLWyO74B2Ctby3oy7lGOhew1qxGNqzT+G8TStNbI227pnMBt4dwg8p31Wgju12IHZbp2ZKv9JDWG7QzWXYjLKdibwjsbKnfUXb6QrZzYge8OUAPqlTI7PbyyK5l/pgNLN9Yu/L/VLRrT/re2cA7fQwEaIhEqnrPnGttjMyvj8ffjR87cig6Ojqq2NjNooDjkiUwE1ghXrebPeWS1dS0pLp/YLCrpqZGiYFUGR97H29pXnXRAx36rNAyE7gDWORBZ1NE2sFalTa2t50fGXn6yWU9XgBXJdMClvt40IPgvSDYZDI5sWbl8tOJRMLt9I6SCGfldlrAzelIyOUP+6e4XCOrmjdvXI8e2LdHmQ0viiKqRxaw1q7WsGfFCXYoFh3u6e6MeqbA7/RQn4CVXTxaQsLNUccQwEpH+danBLwA6HTUusgKIYG1tL8i4LVAa5E8OZuFDFa6xgS8CVjhNnAIYYX4RMA96aS5a8whhRVfXMD7gVlu0YYYVogfBXwYqHIDuKKysvz+g4e7amvrdJ40pfhgepwwvgr4uFsmqWNbZ8OJk7220VYIYFOmyTfgWOze8O7uLi8dC6fRnQT2bUqHADo1pX3dtAKGTm1avpulAKFTZikQxyMg6JTjYZxraVzwYFx4KPtkVAJAwMaleIxL4mmUjUrTCti4RLygjTpqEbBxh2mCNuq4VMDGHYgL2qgrD1bmwKhLLYI27tqSoI26mGZNbaOuHlrQRl0utaCNuj6cmfM15oJ4JrRRTwAywY155JF9rGHMM55scGMeatkdZIXyKd4vTmWJRK3HdCcAAAAASUVORK5CYII="},a04b:function(t,e,n){"use strict";var r=n("c04e"),i=n("d9b5");t.exports=function(t){var e=r(t,"string");return i(e)?e:e+""}},a078:function(t,e,n){"use strict";var r=n("0366"),i=n("c65b"),o=n("5087"),a=n("7b0b"),s=n("07fa"),c=n("9a1f"),u=n("35a1"),f=n("e95a"),d=n("bcbf"),l=n("ebb5").aTypedArrayConstructor,h=n("f495");t.exports=function(t){var e,n,p,v,b,m,g,y,x=o(this),w=a(t),_=arguments.length,A=_>1?arguments[1]:void 0,O=void 0!==A,S=u(w);if(S&&!f(S)){g=c(w,S),y=g.next,w=[];while(!(m=i(y,g)).done)w.push(m.value)}for(O&&_>2&&(A=r(A,arguments[2])),n=s(w),p=new(l(x))(n),v=d(p),e=0;n>e;e++)b=O?A(w[e],e):w[e],p[e]=v?h(b):+b;return p}},a0fb:function(t,e,n){"use strict";n("10ba")},a15b:function(t,e,n){"use strict";var r=n("23e7"),i=n("e330"),o=n("44ad"),a=n("fc6a"),s=n("a640"),c=i([].join),u=o!==Object,f=u||!s("join",",");r({target:"Array",proto:!0,forced:f},{join:function(t){return c(a(this),void 0===t?",":t)}})},a215:function(t,e,n){"use strict";function r(t,e,n){this.$children.forEach((function(i){var o=i.$options.componentName;o===t?i.$emit.apply(i,[e].concat(n)):r.apply(i,[t,e].concat([n]))}))}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af"),n("d3b7"),n("159b");e.default={methods:{dispatch:function(t,e,n){var r=this.$parent||this.$root,i=r.$options.componentName;while(r&&(!i||i!==t))r=r.$parent,r&&(i=r.$options.componentName);r&&r.$emit.apply(r,[e].concat(n))},broadcast:function(t,e,n){r.call(this,t,e,n)}}}},a258:function(t,e,n){"use strict";var r=n("0366"),i=n("44ad"),o=n("7b0b"),a=n("07fa"),s=function(t){var e=1===t;return function(n,s,c){var u,f,d=o(n),l=i(d),h=a(l),p=r(s,c);while(h-- >0)if(u=l[h],f=p(u,h,d),f)switch(t){case 0:return u;case 1:return h}return e?-1:void 0}};t.exports={findLast:s(0),findLastIndex:s(1)}},a38e:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=a;var r=o(n("53ca")),i=o(n("af50"));function o(t){return t&&t.__esModule?t:{default:t}}function a(t){var e=(0,i.default)(t,"string");return"symbol"==(0,r.default)(e)?e:e+""}},a3a8:function(t,e,n){"use strict";var r=n("f107");n.o(r,"render")&&n.d(e,"render",(function(){return r["render"]})),n.o(r,"staticRenderFns")&&n.d(e,"staticRenderFns",(function(){return r["staticRenderFns"]}))},a3f9:function(t,e,n){"use strict";var r=n("ed13");n.o(r,"render")&&n.d(e,"render",(function(){return r["render"]})),n.o(r,"staticRenderFns")&&n.d(e,"staticRenderFns",(function(){return r["staticRenderFns"]}))},a434:function(t,e,n){"use strict";var r=n("23e7"),i=n("7b0b"),o=n("23cb"),a=n("5926"),s=n("07fa"),c=n("3a34"),u=n("3511"),f=n("65f0"),d=n("8418"),l=n("083a"),h=n("1dde"),p=h("splice"),v=Math.max,b=Math.min;r({target:"Array",proto:!0,forced:!p},{splice:function(t,e){var n,r,h,p,m,g,y=i(this),x=s(y),w=o(t,x),_=arguments.length;for(0===_?n=r=0:1===_?(n=0,r=x-w):(n=_-2,r=b(v(a(e),0),x-w)),u(x+n-r),h=f(y,r),p=0;p<r;p++)m=w+p,m in y&&d(h,p,y[m]);if(h.length=r,n<r){for(p=w;p<x-r;p++)m=p+r,g=p+n,m in y?y[g]=y[m]:l(y,g);for(p=x;p>x-r+n;p--)l(y,p-1)}else if(n>r)for(p=x-r;p>w;p--)m=p+r-1,g=p+n-1,m in y?y[g]=y[m]:l(y,g);for(p=0;p<n;p++)y[p+w]=arguments[p+2];return c(y,x-r+n),h}})},a4d3:function(t,e,n){"use strict";n("d9f5"),n("b4f8"),n("c513"),n("e9c4"),n("5a47")},a630:function(t,e,n){"use strict";var r=n("23e7"),i=n("4df4"),o=n("1c7e"),a=!o((function(t){Array.from(t)}));r({target:"Array",stat:!0,forced:a},{from:i})},a640:function(t,e,n){"use strict";var r=n("d039");t.exports=function(t,e){var n=[][t];return!!n&&r((function(){n.call(null,e||function(){return 1},1)}))}},a70b:function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0");var i=r(n("2a42"));i.default.install=function(t){t.component(i.default.name,i.default)};e.default=i.default},a975:function(t,e,n){"use strict";var r=n("ebb5"),i=n("b727").every,o=r.aTypedArray,a=r.exportTypedArrayMethod;a("every",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},a9e3:function(t,e,n){"use strict";var r=n("23e7"),i=n("c430"),o=n("83ab"),a=n("cfe9"),s=n("428f"),c=n("e330"),u=n("94ca"),f=n("1a2d"),d=n("7156"),l=n("3a9b"),h=n("d9b5"),p=n("c04e"),v=n("d039"),b=n("241c").f,m=n("06cf").f,g=n("9bf2").f,y=n("408a"),x=n("58a8").trim,w="Number",_=a[w],A=s[w],O=_.prototype,S=a.TypeError,E=c("".slice),T=c("".charCodeAt),C=function(t){var e=p(t,"number");return"bigint"==typeof e?e:R(e)},R=function(t){var e,n,r,i,o,a,s,c,u=p(t,"number");if(h(u))throw new S("Cannot convert a Symbol value to a number");if("string"==typeof u&&u.length>2)if(u=x(u),e=T(u,0),43===e||45===e){if(n=T(u,2),88===n||120===n)return NaN}else if(48===e){switch(T(u,1)){case 66:case 98:r=2,i=49;break;case 79:case 111:r=8,i=55;break;default:return+u}for(o=E(u,2),a=o.length,s=0;s<a;s++)if(c=T(o,s),c<48||c>i)return NaN;return parseInt(o,r)}return+u},k=u(w,!_(" 0o1")||!_("0b1")||_("+0x1")),j=function(t){return l(O,t)&&v((function(){y(t)}))},I=function(t){var e=arguments.length<1?0:_(C(t));return j(this)?d(Object(e),this,I):e};I.prototype=O,k&&!i&&(O.constructor=I),r({global:!0,constructor:!0,wrap:!0,forced:k},{Number:I});var M=function(t,e){for(var n,r=o?b(e):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),i=0;r.length>i;i++)f(e,n=r[i])&&!f(t,n)&&g(t,n,m(e,n))};i&&A&&M(s[w],A),(k||i)&&M(s[w],_)},aa1f:function(t,e,n){"use strict";var r=n("83ab"),i=n("d039"),o=n("825a"),a=n("e391"),s=Error.prototype.toString,c=i((function(){if(r){var t=Object.create(Object.defineProperty({},"name",{get:function(){return this===t}}));if("true"!==s.call(t))return!0}return"2: 1"!==s.call({message:1,name:2})||"Error"!==s.call({})}));t.exports=c?function(){var t=o(this),e=a(t.name,"Error"),n=a(t.message);return e?n?e+": "+n:e:n}:s},aae1:function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0");var i=r(n("c3d6"));i.default.install=function(t){t.component(i.default.name,i.default)};e.default=i.default},ab13:function(t,e,n){"use strict";var r=n("b622"),i=r("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(n){try{return e[i]=!1,"/./"[t](e)}catch(r){}}return!1}},ab36:function(t,e,n){"use strict";var r=n("861d"),i=n("9112");t.exports=function(t,e){r(e)&&"cause"in e&&i(t,"cause",e.cause)}},ac1f:function(t,e,n){"use strict";var r=n("23e7"),i=n("9263");r({target:"RegExp",proto:!0,forced:/./.exec!==i},{exec:i})},ac62:function(t,e,n){"use strict";n.r(e);var r=n("df26"),i=n("4f90");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);var a=n("2877"),s=Object(a["a"])(i["default"],r["render"],r["staticRenderFns"],!1,null,null,null);e["default"]=s.exports},ace4:function(t,e,n){"use strict";var r=n("23e7"),i=n("4625"),o=n("d039"),a=n("621a"),s=n("825a"),c=n("23cb"),u=n("50c4"),f=n("4840"),d=a.ArrayBuffer,l=a.DataView,h=l.prototype,p=i(d.prototype.slice),v=i(h.getUint8),b=i(h.setUint8),m=o((function(){return!new d(2).slice(1,void 0).byteLength}));r({target:"ArrayBuffer",proto:!0,unsafe:!0,forced:m},{slice:function(t,e){if(p&&void 0===e)return p(s(this),t);var n=s(this).byteLength,r=c(t,n),i=c(void 0===e?n:e,n),o=new(f(this,d))(u(i-r)),a=new l(this),h=new l(o),m=0;while(r<i)b(h,m++,v(a,r++));return o}})},ad6d:function(t,e,n){"use strict";var r=n("825a");t.exports=function(){var t=r(this),e="";return t.hasIndices&&(e+="d"),t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.unicodeSets&&(e+="v"),t.sticky&&(e+="y"),e}},addb:function(t,e,n){"use strict";var r=n("f36a"),i=Math.floor,o=function(t,e){var n=t.length;if(n<8){var a,s,c=1;while(c<n){s=c,a=t[c];while(s&&e(t[s-1],a)>0)t[s]=t[--s];s!==c++&&(t[s]=a)}}else{var u=i(n/2),f=o(r(t,0,u),e),d=o(r(t,u),e),l=f.length,h=d.length,p=0,v=0;while(p<l||v<h)t[p+v]=p<l&&v<h?e(f[p],d[v])<=0?f[p++]:d[v++]:p<l?f[p++]:d[v++]}return t};t.exports=o},ae93:function(t,e,n){"use strict";var r,i,o,a=n("d039"),s=n("1626"),c=n("861d"),u=n("7c73"),f=n("e163"),d=n("cb2d"),l=n("b622"),h=n("c430"),p=l("iterator"),v=!1;[].keys&&(o=[].keys(),"next"in o?(i=f(f(o)),i!==Object.prototype&&(r=i)):v=!0);var b=!c(r)||a((function(){var t={};return r[p].call(t)!==t}));b?r={}:h&&(r=u(r)),s(r[p])||d(r,p,(function(){return this})),t.exports={IteratorPrototype:r,BUGGY_SAFARI_ITERATORS:v}},aeb0:function(t,e,n){"use strict";var r=n("9bf2").f;t.exports=function(t,e,n){n in t||r(t,n,{configurable:!0,get:function(){return e[n]},set:function(t){e[n]=t}})}},aed9:function(t,e,n){"use strict";var r=n("83ab"),i=n("d039");t.exports=r&&i((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},af03:function(t,e,n){"use strict";var r=n("d039");t.exports=function(t){return r((function(){var e=""[t]('"');return e!==e.toLowerCase()||e.split('"').length>3}))}},af50:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=o,n("8172"),n("d9e2"),n("efec"),n("a9e3");var r=i(n("53ca"));function i(t){return t&&t.__esModule?t:{default:t}}function o(t,e){if("object"!=(0,r.default)(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e||"default");if("object"!=(0,r.default)(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}},b041:function(t,e,n){"use strict";var r=n("00ee"),i=n("f5df");t.exports=r?{}.toString:function(){return"[object "+i(this)+"]"}},b0c0:function(t,e,n){"use strict";var r=n("83ab"),i=n("5e77").EXISTS,o=n("e330"),a=n("edd0"),s=Function.prototype,c=o(s.toString),u=/function\b(?:\s|\/\*[\S\s]*?\*\/|\/\/[^\n\r]*[\n\r]+)*([^\s(/]*)/,f=o(u.exec),d="name";r&&!i&&a(s,d,{configurable:!0,get:function(){try{return f(u,c(this))[1]}catch(t){return""}}})},b2bb:function(t,e,n){"use strict";n.r(e);var r=n("8fde"),i=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=i.a},b380:function(t,e,n){"use strict";function r(t,n){return e.default=r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},r(t,n)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=r,n("1f68"),n("131a")},b39a:function(t,e,n){"use strict";var r=n("cfe9"),i=n("2ba4"),o=n("ebb5"),a=n("d039"),s=n("f36a"),c=r.Int8Array,u=o.aTypedArray,f=o.exportTypedArrayMethod,d=[].toLocaleString,l=!!c&&a((function(){d.call(new c(1))})),h=a((function(){return[1,2].toLocaleString()!==new c([1,2]).toLocaleString()}))||!a((function(){c.prototype.toLocaleString.call([1,2])}));f("toLocaleString",(function(){return i(d,l?s(u(this)):u(this),s(arguments))}),h)},b42e:function(t,e,n){"use strict";var r=Math.ceil,i=Math.floor;t.exports=Math.trunc||function(t){var e=+t;return(e>0?i:r)(e)}},b4f8:function(t,e,n){"use strict";var r=n("23e7"),i=n("d066"),o=n("1a2d"),a=n("577e"),s=n("5692"),c=n("0b43"),u=s("string-to-symbol-registry"),f=s("symbol-to-string-registry");r({target:"Symbol",stat:!0,forced:!c},{for:function(t){var e=a(t);if(o(u,e))return u[e];var n=i("Symbol")(e);return u[e]=n,f[n]=e,n}})},b575:function(t,e,n){"use strict";var r,i,o,a,s,c=n("cfe9"),u=n("157a"),f=n("0366"),d=n("2cf4").set,l=n("01b4"),h=n("52c8"),p=n("ebc1"),v=n("ec87"),b=n("9adc"),m=c.MutationObserver||c.WebKitMutationObserver,g=c.document,y=c.process,x=c.Promise,w=u("queueMicrotask");if(!w){var _=new l,A=function(){var t,e;b&&(t=y.domain)&&t.exit();while(e=_.get())try{e()}catch(n){throw _.head&&r(),n}t&&t.enter()};h||b||v||!m||!g?!p&&x&&x.resolve?(a=x.resolve(void 0),a.constructor=x,s=f(a.then,a),r=function(){s(A)}):b?r=function(){y.nextTick(A)}:(d=f(d,c),r=function(){d(A)}):(i=!0,o=g.createTextNode(""),new m(A).observe(o,{characterData:!0}),r=function(){o.data=i=!i}),w=function(t){_.head||r(),_.add(t)}}t.exports=w},b5db:function(t,e,n){"use strict";var r=n("cfe9"),i=r.navigator,o=i&&i.userAgent;t.exports=o?String(o):""},b620:function(t,e,n){"use strict";var r=n("cfe9"),i=n("7282"),o=n("c6b6"),a=r.ArrayBuffer,s=r.TypeError;t.exports=a&&i(a.prototype,"byteLength","get")||function(t){if("ArrayBuffer"!==o(t))throw new s("ArrayBuffer expected");return t.byteLength}},b622:function(t,e,n){"use strict";var r=n("cfe9"),i=n("5692"),o=n("1a2d"),a=n("90e3"),s=n("04f8"),c=n("fdbf"),u=r.Symbol,f=i("wks"),d=c?u["for"]||u:u&&u.withoutSetter||a;t.exports=function(t){return o(f,t)||(f[t]=s&&o(u,t)?u[t]:d("Symbol."+t)),f[t]}},b635:function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0"),n("d3b7"),n("159b");var i=r(n("a70b")),o=r(n("0c69")),a=r(n("8b09")),s=r(n("275b")),c=r(n("7a54")),u=r(n("7ab0")),f=r(n("2a91")),d=r(n("2bea")),l=r(n("05d5")),h=r(n("1882")),p=r(n("aae1")),v=r(n("113c")),b=r(n("0dbd")),m=r(n("07fc")),g=r(n("7778")),y=r(n("d474")),x=[i.default,o.default,a.default,s.default,c.default,u.default,f.default,d.default,l.default,h.default,p.default,v.default,b.default,m.default,g.default,y.default],w=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};x.forEach((function(e){t.component(e.name,e)})),t.prototype.$NeosUI={size:e.size||"",zIndex:e.zIndex||2e3}};"undefined"!==typeof window&&window.Vue&&w(window.Vue);e.default={version:"1.0.1",install:w,GameCourseware:i.default,FillBlanks:o.default,Button:a.default,ButtonGroup:s.default,Checkbox:c.default,CheckboxButton:u.default,CheckboxGroup:f.default,Radio:d.default,RadioGroup:l.default,RadioButton:h.default,Countdown:p.default,ClockCountdown:v.default,PieCountdown:b.default,Icon:m.default,Carousel:g.default,CarouselItem:y.default}},b636:function(t,e,n){"use strict";var r=n("e065");r("asyncIterator")},b64b:function(t,e,n){"use strict";var r=n("23e7"),i=n("7b0b"),o=n("df75"),a=n("d039"),s=a((function(){o(1)}));r({target:"Object",stat:!0,forced:s},{keys:function(t){return o(i(t))}})},b6b7:function(t,e,n){"use strict";var r=n("ebb5"),i=n("4840"),o=r.aTypedArrayConstructor,a=r.getTypedArrayConstructor;t.exports=function(t){return o(i(t,a(t)))}},b727:function(t,e,n){"use strict";var r=n("0366"),i=n("e330"),o=n("44ad"),a=n("7b0b"),s=n("07fa"),c=n("65f0"),u=i([].push),f=function(t){var e=1===t,n=2===t,i=3===t,f=4===t,d=6===t,l=7===t,h=5===t||d;return function(p,v,b,m){for(var g,y,x=a(p),w=o(x),_=s(w),A=r(v,b),O=0,S=m||c,E=e?S(p,_):n||l?S(p,0):void 0;_>O;O++)if((h||O in w)&&(g=w[O],y=A(g,O,x),t))if(e)E[O]=y;else if(y)switch(t){case 3:return!0;case 5:return g;case 6:return O;case 2:u(E,g)}else switch(t){case 4:return!1;case 7:u(E,g)}return d?-1:i||f?f:E}};t.exports={forEach:f(0),map:f(1),filter:f(2),some:f(3),every:f(4),find:f(5),findIndex:f(6),filterReject:f(7)}},b7ef:function(t,e,n){"use strict";var r=n("23e7"),i=n("cfe9"),o=n("d066"),a=n("5c6c"),s=n("9bf2").f,c=n("1a2d"),u=n("19aa"),f=n("7156"),d=n("e391"),l=n("cf98"),h=n("0d26"),p=n("83ab"),v=n("c430"),b="DOMException",m=o("Error"),g=o(b),y=function(){u(this,x);var t=arguments.length,e=d(t<1?void 0:arguments[0]),n=d(t<2?void 0:arguments[1],"Error"),r=new g(e,n),i=new m(e);return i.name=b,s(r,"stack",a(1,h(i.stack,1))),f(r,this,y),r},x=y.prototype=g.prototype,w="stack"in new m(b),_="stack"in new g(1,2),A=g&&p&&Object.getOwnPropertyDescriptor(i,b),O=!!A&&!(A.writable&&A.configurable),S=w&&!O&&!_;r({global:!0,constructor:!0,forced:v||S},{DOMException:S?y:g});var E=o(b),T=E.prototype;if(T.constructor!==E)for(var C in v||s(T,"constructor",a(1,E)),l)if(c(l,C)){var R=l[C],k=R.s;c(E,k)||s(E,k,a(6,R.c))}},b85c:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=o,n("a4d3"),n("e01a"),n("d28b"),n("d9e2"),n("e260"),n("d3b7"),n("3ca3"),n("ddb0");var r=i(n("06c5"));function i(t){return t&&t.__esModule?t:{default:t}}function o(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=(0,r.default)(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,o=function(){};return{s:o,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,c=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return s=t.done,t},e:function(t){c=!0,a=t},f:function(){try{s||null==n["return"]||n["return"]()}finally{if(c)throw a}}}}},b917:function(t,e,n){"use strict";var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",i=r+"+/",o=r+"-_",a=function(t){for(var e={},n=0;n<64;n++)e[t.charAt(n)]=n;return e};t.exports={i2c:i,c2i:a(i),i2cUrl:o,c2iUrl:a(o)}},b980:function(t,e,n){"use strict";var r=n("d039"),i=n("5c6c");t.exports=!r((function(){var t=new Error("a");return!("stack"in t)||(Object.defineProperty(t,"stack",i(1,7)),7!==t.stack)}))},bb2f:function(t,e,n){"use strict";var r=n("d039");t.exports=!r((function(){return Object.isExtensible(Object.preventExtensions({}))}))},bcbf:function(t,e,n){"use strict";var r=n("f5df");t.exports=function(t){var e=r(t);return"BigInt64Array"===e||"BigUint64Array"===e}},bd0a:function(t,e,n){"use strict";n.r(e);var r=n("7276"),i=n("da73");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);var a=n("2877"),s=Object(a["a"])(i["default"],r["render"],r["staticRenderFns"],!1,null,null,null);e["default"]=s.exports},bdb8:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var r=n("dec2");e.default={name:"NeCountdown",props:{millisecond:Boolean,time:{type:Number,default:0},format:{type:String,default:"HH:mm:ss"},autoStart:{type:Boolean,default:!0}},data:function(){return{remain:0}},computed:{timeData:function(){return(0,r.parseTimeData)(this.remain)},formattedTime:function(){return(0,r.parseFormat)(this.format,this.timeData)}},watch:{time:{immediate:!0,handler:"reset"}},activated:function(){this.keepAlivePaused&&(this.counting=!0,this.keepAlivePaused=!1,this.tick())},deactivated:function(){this.counting&&(this.pause(),this.keepAlivePaused=!0)},beforeDestroy:function(){this.pause()},methods:{start:function(){this.counting||(this.counting=!0,this.endTime=Date.now()+this.remain,this.tick())},pause:function(){this.counting=!1,(0,r.cancelRaf)(this.rafId)},reset:function(){this.pause(),this.remain=this.time,this.autoStart&&this.start()},tick:function(){this.millisecond?this.microTick():this.macroTick()},microTick:function(){var t=this;this.rafId=(0,r.raf)((function(){t.setRemain(t.getRemain()),0!==t.remain&&t.microTick()}))},macroTick:function(){var t=this;this.rafId=(0,r.raf)((function(){var e=t.getRemain();(0,r.isSameSecond)(e,t.remain)&&0!==e||t.setRemain(e),0!==t.remain&&t.macroTick()}))},getRemain:function(){return Math.max(this.endTime-Date.now(),0)},setRemain:function(t){this.remain=t,0===t?(this.pause(),this.$emit("complete")):this.$emit("remain",parseInt(t/1e3,10))}},render:function(t){return t("div",{attrs:{class:"countDown"}},this.$scopedSlots.default?this.$scopedSlots.default(this.timeData):this.formattedTime)}}},be8e:function(t,e,n){"use strict";var r=n("fc1b"),i=1.1920928955078125e-7,o=34028234663852886e22,a=11754943508222875e-54;t.exports=Math.fround||function(t){return r(t,i,o,a)}},bee2:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=a;var r=i(n("a38e"));function i(t){return t&&t.__esModule?t:{default:t}}function o(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,(0,r.default)(i.key),i)}}function a(t,e,n){return e&&o(t.prototype,e),n&&o(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}},c04e:function(t,e,n){"use strict";var r=n("c65b"),i=n("861d"),o=n("d9b5"),a=n("dc4a"),s=n("485a"),c=n("b622"),u=TypeError,f=c("toPrimitive");t.exports=function(t,e){if(!i(t)||o(t))return t;var n,c=a(t,f);if(c){if(void 0===e&&(e="default"),n=r(c,t,e),!i(n)||o(n))return n;throw new u("Can't convert object to primitive value")}return void 0===e&&(e="number"),s(t,e)}},c19f:function(t,e,n){"use strict";var r=n("23e7"),i=n("cfe9"),o=n("621a"),a=n("2626"),s="ArrayBuffer",c=o[s],u=i[s];r({global:!0,constructor:!0,forced:u!==c},{ArrayBuffer:c}),a(s)},c1ac:function(t,e,n){"use strict";var r=n("ebb5"),i=n("b727").filter,o=n("1448"),a=r.aTypedArray,s=r.exportTypedArrayMethod;s("filter",(function(t){var e=i(a(this),t,arguments.length>1?arguments[1]:void 0);return o(this,e)}))},c3c5:function(t,e,n){"use strict";n.r(e);var r=n("f216"),i=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=i.a},c3d6:function(t,e,n){"use strict";n.r(e);var r=n("7d59");for(var i in r)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(i);var o,a,s=n("2877"),c=Object(s["a"])(r["default"],o,a,!1,null,null,null);e["default"]=c.exports},c430:function(t,e,n){"use strict";t.exports=!1},c513:function(t,e,n){"use strict";var r=n("23e7"),i=n("1a2d"),o=n("d9b5"),a=n("0d51"),s=n("5692"),c=n("0b43"),u=s("symbol-to-string-registry");r({target:"Symbol",stat:!0,forced:!c},{keyFor:function(t){if(!o(t))throw new TypeError(a(t)+" is not a symbol");if(i(u,t))return u[t]}})},c59e:function(t,e,n){"use strict";n.r(e);var r=n("0c7d"),i=n("3cb8");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);var a=n("2877"),s=Object(a["a"])(i["default"],r["render"],r["staticRenderFns"],!1,null,null,null);e["default"]=s.exports},c65b:function(t,e,n){"use strict";var r=n("40d5"),i=Function.prototype.call;t.exports=r?i.bind(i):function(){return i.apply(i,arguments)}},c67a:function(t,e,n){"use strict";n.r(e);var r=n("24f0"),i=n("f84d");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);var a=n("2877"),s=Object(a["a"])(i["default"],r["render"],r["staticRenderFns"],!1,null,null,null);e["default"]=s.exports},c6b6:function(t,e,n){"use strict";var r=n("e330"),i=r({}.toString),o=r("".slice);t.exports=function(t){return o(i(t),8,-1)}},c6cd:function(t,e,n){"use strict";var r=n("c430"),i=n("cfe9"),o=n("6374"),a="__core-js_shared__",s=t.exports=i[a]||o(a,{});(s.versions||(s.versions=[])).push({version:"3.38.0",mode:r?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.38.0/LICENSE",source:"https://github.com/zloirock/core-js"})},c6d2:function(t,e,n){"use strict";var r=n("23e7"),i=n("c65b"),o=n("c430"),a=n("5e77"),s=n("1626"),c=n("dcc3"),u=n("e163"),f=n("d2bb"),d=n("d44e"),l=n("9112"),h=n("cb2d"),p=n("b622"),v=n("3f8c"),b=n("ae93"),m=a.PROPER,g=a.CONFIGURABLE,y=b.IteratorPrototype,x=b.BUGGY_SAFARI_ITERATORS,w=p("iterator"),_="keys",A="values",O="entries",S=function(){return this};t.exports=function(t,e,n,a,p,b,E){c(n,e,a);var T,C,R,k=function(t){if(t===p&&P)return P;if(!x&&t&&t in M)return M[t];switch(t){case _:return function(){return new n(this,t)};case A:return function(){return new n(this,t)};case O:return function(){return new n(this,t)}}return function(){return new n(this)}},j=e+" Iterator",I=!1,M=t.prototype,L=M[w]||M["@@iterator"]||p&&M[p],P=!x&&L||k(p),F="Array"===e&&M.entries||L;if(F&&(T=u(F.call(new t)),T!==Object.prototype&&T.next&&(o||u(T)===y||(f?f(T,y):s(T[w])||h(T,w,S)),d(T,j,!0,!0),o&&(v[j]=S))),m&&p===A&&L&&L.name!==A&&(!o&&g?l(M,"name",A):(I=!0,P=function(){return i(L,this)})),p)if(C={values:k(A),keys:b?P:k(_),entries:k(O)},E)for(R in C)(x||I||!(R in M))&&h(M,R,C[R]);else r({target:e,proto:!0,forced:x||I},C);return o&&!E||M[w]===P||h(M,w,P,{name:p}),v[e]=P,C}},c715:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={name:"NeDialog",props:{visible:{type:Boolean,default:!1},exitMask:{type:Boolean,default:!1}},computed:{maskClass:function(){return this.exitMask?"game-courseware-mask":""}},methods:{afterLeave:function(){this.$emit("closed")}}}},c7eb:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=o,n("a4d3"),n("e01a"),n("b636"),n("d28b"),n("944a"),n("d9e2"),n("e260"),n("14d9"),n("fb6a"),n("b0c0"),n("0c47"),n("23dc"),n("3410"),n("1f68"),n("131a"),n("d3b7"),n("e6cf"),n("3ca3"),n("159b"),n("ddb0");var r=i(n("53ca"));function i(t){return t&&t.__esModule?t:{default:t}}function o(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
e.default=o=function(){return n};var t,n={},i=Object.prototype,a=i.hasOwnProperty,s=Object.defineProperty||function(t,e,n){t[e]=n.value},c="function"==typeof Symbol?Symbol:{},u=c.iterator||"@@iterator",f=c.asyncIterator||"@@asyncIterator",d=c.toStringTag||"@@toStringTag";function l(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,n){return t[e]=n}}function h(t,e,n,r){var i=e&&e.prototype instanceof x?e:x,o=Object.create(i.prototype),a=new M(r||[]);return s(o,"_invoke",{value:R(t,n,a)}),o}function p(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}n.wrap=h;var v="suspendedStart",b="suspendedYield",m="executing",g="completed",y={};function x(){}function w(){}function _(){}var A={};l(A,u,(function(){return this}));var O=Object.getPrototypeOf,S=O&&O(O(L([])));S&&S!==i&&a.call(S,u)&&(A=S);var E=_.prototype=x.prototype=Object.create(A);function T(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function n(i,o,s,c){var u=p(t[i],t,o);if("throw"!==u.type){var f=u.arg,d=f.value;return d&&"object"==(0,r.default)(d)&&a.call(d,"__await")?e.resolve(d.__await).then((function(t){n("next",t,s,c)}),(function(t){n("throw",t,s,c)})):e.resolve(d).then((function(t){f.value=t,s(f)}),(function(t){return n("throw",t,s,c)}))}c(u.arg)}var i;s(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,i){n(t,r,e,i)}))}return i=i?i.then(o,o):o()}})}function R(e,n,r){var i=v;return function(o,a){if(i===m)throw Error("Generator is already running");if(i===g){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var s=r.delegate;if(s){var c=k(s,r);if(c){if(c===y)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===v)throw i=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=m;var u=p(e,n,r);if("normal"===u.type){if(i=r.done?g:b,u.arg===y)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=g,r.method="throw",r.arg=u.arg)}}}function k(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator["return"]&&(n.method="return",n.arg=t,k(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var o=p(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,y;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,y):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function I(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function M(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function L(e){if(e||""===e){var n=e[u];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(a.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError((0,r.default)(e)+" is not iterable")}return w.prototype=_,s(E,"constructor",{value:_,configurable:!0}),s(_,"constructor",{value:w,configurable:!0}),w.displayName=l(_,d,"GeneratorFunction"),n.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},n.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,l(t,d,"GeneratorFunction")),t.prototype=Object.create(E),t},n.awrap=function(t){return{__await:t}},T(C.prototype),l(C.prototype,f,(function(){return this})),n.AsyncIterator=C,n.async=function(t,e,r,i,o){void 0===o&&(o=Promise);var a=new C(h(t,e,r,i),o);return n.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},T(E),l(E,d,"Generator"),l(E,u,(function(){return this})),l(E,"toString",(function(){return"[object Generator]"})),n.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},n.values=L,M.prototype={constructor:M,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(I),!e)for(var n in this)"t"===n.charAt(0)&&a.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(r,i){return s.type="throw",s.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var c=a.call(o,"catchLoc"),u=a.call(o,"finallyLoc");if(c&&u){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&a.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),I(n),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;I(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:L(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),y}},n}},c8ba:function(t,e){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(r){"object"===typeof window&&(n=window)}t.exports=n},c8d2:function(t,e,n){"use strict";var r=n("5e77").PROPER,i=n("d039"),o=n("5899"),a="​᠎";t.exports=function(t){return i((function(){return!!o[t]()||a[t]()!==a||r&&o[t].name!==t}))}},c939:function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("dca8"),n("d3b7");var i=r(n("a215")),o=Object.freeze({LEFT:37,UP:38,RIGHT:39,DOWN:40});e.default={name:"NeRadioGroup",componentName:"NeRadioGroup",inject:{neFormItem:{default:""}},mixins:[i.default],props:{value:{},size:String,fill:String,textColor:String,disabled:Boolean},computed:{_neFormItemSize:function(){return(this.neFormItem||{}).neFormItemSize},_neTag:function(){return(this.$vnode.data||{}).tag||"div"},radioGroupSize:function(){return this.size||this._neFormItemSize||(this.$NeosUI||{}).size}},watch:{value:function(){this.dispatch("NeFormItem","ne.form.change",[this.value])}},created:function(){var t=this;this.$on("handleChange",(function(e){t.$emit("change",e)}))},mounted:function(){var t=this.$el.querySelectorAll("[type=radio]"),e=this.$el.querySelectorAll("[role=radio]")[0];![].some.call(t,(function(t){return t.checked}))&&e&&(e.tabIndex=0)},methods:{handleKeydown:function(t){var e=t.target,n="INPUT"===e.nodeName?"[type=radio]":"[role=radio]",r=this.$el.querySelectorAll(n),i=r.length,a=[].indexOf.call(r,e),s=this.$el.querySelectorAll("[role=radio]");switch(t.keyCode){case o.LEFT:case o.UP:t.stopPropagation(),t.preventDefault(),0===a?(s[i-1].click(),s[i-1].focus()):(s[a-1].click(),s[a-1].focus());break;case o.RIGHT:case o.DOWN:a===i-1?(t.stopPropagation(),t.preventDefault(),s[0].click(),s[0].focus()):(s[a+1].click(),s[a+1].focus());break;default:break}}}}},c96a:function(t,e,n){"use strict";var r=n("23e7"),i=n("857a"),o=n("af03");r({target:"String",proto:!0,forced:o("small")},{small:function(){return i(this,"small","","")}})},ca00:function(t,e,n){"use strict";n("498a");var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.valueEquals=e.typeOf=e.trimAll=e.trim=e.toObject=e.toNumber=e.toArray=e.sleep=e.requireAll=e.objToArray=e.noop=e.isPC=e.isMobile=e.isIE=e.isFirefox=e.isEmpty=e.isEdge=e.isAndroid=e.hyphenateCase=e.hasOwn=e.getValueByPath=e.getQuery=e.getPropByPath=e.getNowFormatDate=e.generateId=e.flatten=e.escapeRegexpString=e.default=e.dataURLtoBlob=e.dataURItoFile=e.chunk=e.changeCase=e.camelCase=e.autoprefixer=void 0;var i=r(n("c7eb")),o=r(n("1da1")),a=r(n("53ca"));n("d9e2"),n("99af"),n("e260"),n("a15b"),n("d81d"),n("13d5"),n("c19f"),n("ace4"),n("2c66"),n("249d"),n("40e9"),n("a9e3"),n("b64b"),n("d3b7"),n("e6cf"),n("ac1f"),n("00b4"),n("25f0"),n("466d"),n("5319"),n("841c"),n("5cc6"),n("907a"),n("9a8c"),n("a975"),n("735e"),n("c1ac"),n("d139"),n("3a7b"),n("986a"),n("1d02"),n("d5d6"),n("82f8"),n("e91f"),n("60bd"),n("5f96"),n("3280"),n("3fcc"),n("ca91"),n("25a1"),n("cd26"),n("3c5d"),n("2954"),n("649e"),n("219c"),n("170b"),n("b39a"),n("6ce5"),n("2834"),n("72f7"),n("4ea1"),n("81b2"),n("159b"),n("ddb0"),n("0eb6"),n("b7ef"),n("8bd4");var s=r(n("8bbf")),c=/([\:\-\_]+(.))/g,u=/^moz([A-Z])/,f=(e.noop=function(){},e.trim=function(t){return(t||"").replace(/^[\s\uFEFF]+|[\s\uFEFF]+$/g,"")},e.trimAll=function(t){return t.replace(/ /g,"")},{}),d=(e.requireAll=function(t,e){return t.keys().forEach((function(n){if(n!==e){var r=n.match(/\/(\S+)\.js$/)[1];f[r]=t(n).default}})),f},e.isEmpty=function(t){if(null==t)return!0;if("boolean"===typeof t)return!1;if("number"===typeof t)return!t;if(t instanceof Error)return""===t.message;switch(Object.prototype.toString.call(t)){case"[object String]":case"[object Array]":return!t.length;case"[object File]":case"[object Map]":case"[object Set]":return!t.size;case"[object Object]":return!Object.keys(t).length}return!1}),l=(e.isIE=function(){return!s.default.prototype.$isServer&&!isNaN(Number(document.documentMode))},e.isEdge=function(){return!s.default.prototype.$isServer&&navigator.userAgent.indexOf("Edge")>-1},e.isFirefox=function(){return!s.default.prototype.$isServer&&!!window.navigator.userAgent.match(/firefox/i)},e.autoprefixer=function(t){if("object"!==(0,a.default)(t))return t;var e=["transform","transition","animation"],n=["ms-","webkit-"];return e.forEach((function(e){var r=t[e];e&&r&&n.forEach((function(n){t[n+e]=r}))})),t},e.sleep=function(t){return new Promise((function(e){return setTimeout(e,t)}))},e.getQuery=function(t){for(var e=window.location.search.substring(1),n=e.split("&"),r=0;r<n.length;r++){var i=n[r].split("=");if(i[0]==t)return i[1]}return!1},e.isAndroid=function(){var t=window.navigator.userAgent;return t.indexOf("Android")>0||t.indexOf("Adr")>0||/Android/.test(t)}),h=e.isMobile=function(){var t=window.navigator.userAgent;return!!(/Mobile/i.test(t)||/iP[ao]d|iPhone/i.test(t)||/Android/.test(t))},p=(e.isPC=function(){return!h()&&!l()},e.camelCase=function(t){return p(t,5).replace(c,(function(t,e,n,r){return r?n.toUpperCase():n})).replace(u,"Moz$1")},e.changeCase=function(t,e){switch(e=e||4,e){case 1:return t.replace(/\b\w+\b/g,(function(t){return t.substring(0,1).toUpperCase()+t.substring(1).toLowerCase()}));case 2:return t.replace(/\b\w+\b/g,(function(t){return t.substring(0,1).toLowerCase()+t.substring(1).toUpperCase()}));case 3:return t.split("").map((function(t){return/[a-z]/.test(t)?t.toUpperCase():t.toLowerCase()})).join("");case 4:return t.toUpperCase();case 5:return t.toLowerCase();default:return t}}),v=(e.objToArray=function(t){return Array.isArray(t)?t:d(t)?[]:[t]},Object.prototype.hasOwnProperty),b=(e.hasOwn=function(t,e){return v.call(t,e)},e.typeOf=function(t){var e=Object.prototype.toString,n={"[object Boolean]":"boolean","[object Number]":"number","[object String]":"string","[object Function]":"function","[object Array]":"array","[object Date]":"date","[object RegExp]":"regExp","[object Undefined]":"undefined","[object Null]":"null","[object Object]":"object"};return n[e.call(t)]},function(t){var e=Object.create(null);return function(n){var r=e[n];return r||(e[n]=t(n))}}),m=/\B([A-Z])/g,g=(e.hyphenateCase=b((function(t){return t.replace(m,"-$1").toLowerCase()})),function(t,e){for(var n in e)t[n]=e[n];return t}),y=(e.toObject=function(t){for(var e={},n=0;n<t.length;n++)t[n]&&g(e,t[n]);return e},e.toNumber=function(t){var e=parseFloat(t);return isNaN(e)?t:e},e.toArray=function(t,e){e=e||0;var n=t.length-e,r=new Array(n);while(n--)r[n]=t[n+e];return r},e.getValueByPath=function(t,e){e=e||"";for(var n=e.split("."),r=t,i=null,o=0,a=n.length;o<a;o++){var s=n[o];if(!r)break;if(o===a-1){i=r[s];break}r=r[s]}return i},e.getPropByPath=function(t,e,n){var r=t;e=e.replace(/\[(\w+)\]/g,".$1"),e=e.replace(/^\./,"");for(var i=e.split("."),o=0,a=i.length;o<a-1;++o){if(!r&&!n)break;var s=i[o];if(!(s in r)){if(n)throw new Error("please transfer a valid prop path to form item!");break}r=r[s]}return{o:r,k:i[o],v:r?r[i[o]]:null}},e.valueEquals=function(t,e){if(t===e)return!0;if(!(t instanceof Array))return!1;if(!(e instanceof Array))return!1;if(t.length!==e.length)return!1;for(var n=0;n!==t.length;++n)if(t[n]!==e[n])return!1;return!0},e.escapeRegexpString=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return String(t).replace(/[|\\{}()[\]^$+*?.]/g,"\\$&")},e.generateId=function(){return Math.floor(1e4*Math.random())},e.dataURItoFile=function(){var t=(0,o.default)((0,i.default)().mark((function t(e,n){var r,o,a,s;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:for(r=atob(e.split(",")[1]),o=new ArrayBuffer(r.length),a=new Uint8Array(o),s=0;s<r.length;s++)a=r.charCodeAt(s);return t.abrupt("return",new File([a],n,{type:"image/jpeg",lastModified:Date.now()}));case 5:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}(),e.dataURLtoBlob=function(t){var e=t.split(","),n=e[1].substring(0,e[1].length-2),r=e[0].match(/:(.*?);/)[1],i=atob(n),o=i.length,a=new Uint8Array(o);while(o--)a[o]=i.charCodeAt(o);return new Blob([a],{type:r})},e.getNowFormatDate=function(){var t=new Date,e="-",n=t.getFullYear(),r=t.getMonth()+1,i=t.getDate();r>=1&&r<=9&&(r="0"+r),i>=0&&i<=9&&(i="0"+i);var o=n+e+r+e+i;return o},e.chunk=function(t,e){for(var n=new Array,r=0,i=t.length/e,o=0;o<i;o++){for(var a=new Array,s=0;s<e;s++)if(a[s]=t[r++],r==t.length)break;n[o]=a}return n},e.flatten=function(t){return t.reduce((function(t,e){return t.concat(Array.isArray(e)?y(e):e)}),[])});e.default={created:function(){this.uuid=Math.random().toString(36).substring(3,20)}}},ca84:function(t,e,n){"use strict";var r=n("e330"),i=n("1a2d"),o=n("fc6a"),a=n("4d64").indexOf,s=n("d012"),c=r([].push);t.exports=function(t,e){var n,r=o(t),u=0,f=[];for(n in r)!i(s,n)&&i(r,n)&&c(f,n);while(e.length>u)i(r,n=e[u++])&&(~a(f,n)||c(f,n));return f}},ca91:function(t,e,n){"use strict";var r=n("ebb5"),i=n("d58f").left,o=r.aTypedArray,a=r.exportTypedArrayMethod;a("reduce",(function(t){var e=arguments.length;return i(o(this),t,e,e>1?arguments[1]:void 0)}))},caad:function(t,e,n){"use strict";var r=n("23e7"),i=n("4d64").includes,o=n("d039"),a=n("44d2"),s=o((function(){return!Array(1).includes()}));r({target:"Array",proto:!0,forced:s},{includes:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}}),a("includes")},cb29:function(t,e,n){"use strict";var r=n("23e7"),i=n("81d5"),o=n("44d2");r({target:"Array",proto:!0},{fill:i}),o("fill")},cb2d:function(t,e,n){"use strict";var r=n("1626"),i=n("9bf2"),o=n("13d2"),a=n("6374");t.exports=function(t,e,n,s){s||(s={});var c=s.enumerable,u=void 0!==s.name?s.name:e;if(r(n)&&o(n,u,s),s.global)c?t[e]=n:a(e,n);else{try{s.unsafe?t[e]&&(c=!0):delete t[e]}catch(f){}c?t[e]=n:i.f(t,e,{value:n,enumerable:!1,configurable:!s.nonConfigurable,writable:!s.nonWritable})}return t}},cc12:function(t,e,n){"use strict";var r=n("cfe9"),i=n("861d"),o=r.document,a=i(o)&&i(o.createElement);t.exports=function(t){return a?o.createElement(t):{}}},cc98:function(t,e,n){"use strict";var r=n("23e7"),i=n("c430"),o=n("4738").CONSTRUCTOR,a=n("d256"),s=n("d066"),c=n("1626"),u=n("cb2d"),f=a&&a.prototype;if(r({target:"Promise",proto:!0,forced:o,real:!0},{catch:function(t){return this.then(void 0,t)}}),!i&&c(a)){var d=s("Promise").prototype["catch"];f["catch"]!==d&&u(f,"catch",d,{unsafe:!0})}},cd26:function(t,e,n){"use strict";var r=n("ebb5"),i=r.aTypedArray,o=r.exportTypedArrayMethod,a=Math.floor;o("reverse",(function(){var t,e=this,n=i(e).length,r=a(n/2),o=0;while(o<r)t=e[o],e[o++]=e[--n],e[n]=t;return e}))},cdce:function(t,e,n){"use strict";var r=n("cfe9"),i=n("1626"),o=r.WeakMap;t.exports=i(o)&&/native code/.test(String(o))},cdf9:function(t,e,n){"use strict";var r=n("825a"),i=n("861d"),o=n("f069");t.exports=function(t,e){if(r(t),i(e)&&e.constructor===t)return e;var n=o.f(t),a=n.resolve;return a(e),n.promise}},ced7:function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("cb29"),n("d3b7"),n("25f0");var i=r(n("8ed6"));e.default={name:"NePieCountdown",props:{options:{type:Object,default:function(){}}},data:function(){return{settings:{size:60,outerBorderWidth:4,innerBorderWidth:5,borderColor:"#ffaa0a",outerColor:"#ffaa0a",scheduleColor:"#ffffff",fontColor:"#3370ff",ringColor:"#ffaa0a",innerColor:"#FFD518",fontSize:0,beginTime:+new Date,nowTime:+new Date,countDownTime:0,drawInnerRing:!1,stepRing:100,ratio:2},timer:null,uuid:Math.random().toString(36).substring(3,20)}},watch:{options:{deep:!0,handler:function(t){t&&this.init()}}},mounted:function(){var t=this;this.$nextTick((function(){t.init()}))},beforeDestroy:function(){this.timer&&(clearInterval(this.timer),this.timer=null)},destroyed:function(){this.$destroy()},methods:{init:function(){this.settings=(0,i.default)({},this.settings,this.options),this.obj=this.$refs.countdown,this.obj.width=this.settings.size*this.settings.ratio,this.obj.height=this.settings.size*this.settings.ratio,this.obj.style.width="".concat(this.settings.size,"px"),this.obj.style.height="".concat(this.settings.size,"px"),this.ctx=this.obj.getContext("2d"),this.ctx.scale(this.settings.ratio,this.settings.ratio),this.settings.countDownTime&&this.startCountdown()},drawBackground:function(){this.drawCircle(0,360,0,this.settings.outerColor)},drawProcess:function(){this.drawCircle(0,360,4,this.settings.ringColor)},drawInner:function(){this.drawCircle(0,360,50,this.settings.innerColor),this.strokeBorder(this.settings.innerBorderWidth)},drawAnimate:function(){var t=Math.PI/180,e=360*this.schedule,n=-90,r=-90+e;this.ctx.beginPath(),this.ctx.moveTo(this.settings.size/2,this.settings.size/2),this.ctx.arc(this.settings.size/2,this.settings.size/2,this.settings.size/2-this.settings.outerBorderWidth,n*t,r*t,!1),this.ctx.fillStyle=this.settings.scheduleColor,this.ctx.fill(),this.ctx.closePath()},strokeBorder:function(t){this.ctx.lineWidth=t,this.ctx.strokeStyle=this.settings.borderColor,this.ctx.stroke()},strokeText:function(t){this.ctx.textAlign="center",this.ctx.textBaseline="middle",this.ctx.font=this.settings.fontSize+"px microsoft yahei",this.ctx.fillStyle=this.settings.fontColor,this.ctx.fillText(t,this.settings.size/2,this.settings.size/2)},drawCircle:function(t,e,n,r){var i=Math.PI/180;this.ctx.beginPath(),this.ctx.arc(this.settings.size/2,this.settings.size/2,this.settings.size/2-n,t*i,e*i,!1),this.ctx.fillStyle=r,this.ctx.fill(),this.ctx.closePath()},startCountdown:function(){var t=this,e=this.settings.beginTime,n=this.settings.nowTime;this.timer=setInterval((function(){var r=1e3*t.settings.countDownTime;n+=t.settings.stepRing,t.schedule=(n-e)/r,t.drawCanvas(t.schedule),n-e>=r&&(t.drawBackground(),t.drawProcess(),t.drawAnimate(),t.settings.drawInnerRing&&t.drawInner(),t.settings.fontSize&&t.strokeText(0),clearInterval(t.timer),t.timer=null,t.$emit("complete"))}),this.settings.stepRing)},drawCanvas:function(t){t=t>=1?1:t;var e=parseInt(this.settings.countDownTime*(1-t))+1;this.ctx.clearRect(0,0,this.settings.size,this.settings.size),this.drawBackground(),this.drawProcess(),this.drawAnimate(),this.$emit("remain",e),this.settings.drawInnerRing&&this.drawInner(),this.settings.fontSize&&this.strokeText(e)}}}},cf98:function(t,e,n){"use strict";t.exports={IndexSizeError:{s:"INDEX_SIZE_ERR",c:1,m:1},DOMStringSizeError:{s:"DOMSTRING_SIZE_ERR",c:2,m:0},HierarchyRequestError:{s:"HIERARCHY_REQUEST_ERR",c:3,m:1},WrongDocumentError:{s:"WRONG_DOCUMENT_ERR",c:4,m:1},InvalidCharacterError:{s:"INVALID_CHARACTER_ERR",c:5,m:1},NoDataAllowedError:{s:"NO_DATA_ALLOWED_ERR",c:6,m:0},NoModificationAllowedError:{s:"NO_MODIFICATION_ALLOWED_ERR",c:7,m:1},NotFoundError:{s:"NOT_FOUND_ERR",c:8,m:1},NotSupportedError:{s:"NOT_SUPPORTED_ERR",c:9,m:1},InUseAttributeError:{s:"INUSE_ATTRIBUTE_ERR",c:10,m:1},InvalidStateError:{s:"INVALID_STATE_ERR",c:11,m:1},SyntaxError:{s:"SYNTAX_ERR",c:12,m:1},InvalidModificationError:{s:"INVALID_MODIFICATION_ERR",c:13,m:1},NamespaceError:{s:"NAMESPACE_ERR",c:14,m:1},InvalidAccessError:{s:"INVALID_ACCESS_ERR",c:15,m:1},ValidationError:{s:"VALIDATION_ERR",c:16,m:0},TypeMismatchError:{s:"TYPE_MISMATCH_ERR",c:17,m:1},SecurityError:{s:"SECURITY_ERR",c:18,m:1},NetworkError:{s:"NETWORK_ERR",c:19,m:1},AbortError:{s:"ABORT_ERR",c:20,m:1},URLMismatchError:{s:"URL_MISMATCH_ERR",c:21,m:1},QuotaExceededError:{s:"QUOTA_EXCEEDED_ERR",c:22,m:1},TimeoutError:{s:"TIMEOUT_ERR",c:23,m:1},InvalidNodeTypeError:{s:"INVALID_NODE_TYPE_ERR",c:24,m:1},DataCloneError:{s:"DATA_CLONE_ERR",c:25,m:1}}},cfe9:function(t,e,n){"use strict";(function(e){var n=function(t){return t&&t.Math===Math&&t};t.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof e&&e)||n("object"==typeof this&&this)||function(){return this}()||Function("return this")()}).call(this,n("c8ba"))},d012:function(t,e,n){"use strict";t.exports={}},d039:function(t,e,n){"use strict";t.exports=function(t){try{return!!t()}catch(e){return!0}}},d066:function(t,e,n){"use strict";var r=n("cfe9"),i=n("1626"),o=function(t){return i(t)?t:void 0};t.exports=function(t,e){return arguments.length<2?o(r[t]):r[t]&&r[t][e]}},d139:function(t,e,n){"use strict";var r=n("ebb5"),i=n("b727").find,o=r.aTypedArray,a=r.exportTypedArrayMethod;a("find",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},d1e7:function(t,e,n){"use strict";var r={}.propertyIsEnumerable,i=Object.getOwnPropertyDescriptor,o=i&&!r.call({1:2},1);e.f=o?function(t){var e=i(this,t);return!!e&&e.enumerable}:r},d256:function(t,e,n){"use strict";var r=n("cfe9");t.exports=r.Promise},d28b:function(t,e,n){"use strict";var r=n("e065");r("iterator")},d2bb:function(t,e,n){"use strict";var r=n("7282"),i=n("861d"),o=n("1d80"),a=n("3bbe");t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,n={};try{t=r(Object.prototype,"__proto__","set"),t(n,[]),e=n instanceof Array}catch(s){}return function(n,r){return o(n),a(r),i(n)?(e?t(n,r):n.__proto__=r,n):n}}():void 0)},d3b7:function(t,e,n){"use strict";var r=n("00ee"),i=n("cb2d"),o=n("b041");r||i(Object.prototype,"toString",o,{unsafe:!0})},d429:function(t,e,n){"use strict";var r=n("07fa"),i=n("5926"),o=RangeError;t.exports=function(t,e,n,a){var s=r(t),c=i(n),u=c<0?s+c:c;if(u>=s||u<0)throw new o("Incorrect index");for(var f=new e(s),d=0;d<s;d++)f[d]=d===u?a:t[d];return f}},d44e:function(t,e,n){"use strict";var r=n("9bf2").f,i=n("1a2d"),o=n("b622"),a=o("toStringTag");t.exports=function(t,e,n){t&&!n&&(t=t.prototype),t&&!i(t,a)&&r(t,a,{configurable:!0,value:e})}},d474:function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0");var i=r(n("d624"));i.default.install=function(t){t.component(i.default.name,i.default)};e.default=i.default},d4ec:function(t,e,n){"use strict";function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(e,"__esModule",{value:!0}),e.default=r,n("d9e2")},d58f:function(t,e,n){"use strict";var r=n("59ed"),i=n("7b0b"),o=n("44ad"),a=n("07fa"),s=TypeError,c="Reduce of empty array with no initial value",u=function(t){return function(e,n,u,f){var d=i(e),l=o(d),h=a(d);if(r(n),0===h&&u<2)throw new s(c);var p=t?h-1:0,v=t?-1:1;if(u<2)while(1){if(p in l){f=l[p],p+=v;break}if(p+=v,t?p<0:h<=p)throw new s(c)}for(;t?p>=0:h>p;p+=v)p in l&&(f=n(f,l[p],p,d));return f}};t.exports={left:u(!1),right:u(!0)}},d5d6:function(t,e,n){"use strict";var r=n("ebb5"),i=n("b727").forEach,o=r.aTypedArray,a=r.exportTypedArrayMethod;a("forEach",(function(t){i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},d624:function(t,e,n){"use strict";n.r(e);var r=n("96db"),i=n("2d0a");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);var a=n("2877"),s=Object(a["a"])(i["default"],r["render"],r["staticRenderFns"],!1,null,null,null);e["default"]=s.exports},d6d6:function(t,e,n){"use strict";var r=TypeError;t.exports=function(t,e){if(t<e)throw new r("Not enough arguments");return t}},d784:function(t,e,n){"use strict";n("ac1f");var r=n("c65b"),i=n("cb2d"),o=n("9263"),a=n("d039"),s=n("b622"),c=n("9112"),u=s("species"),f=RegExp.prototype;t.exports=function(t,e,n,d){var l=s(t),h=!a((function(){var e={};return e[l]=function(){return 7},7!==""[t](e)})),p=h&&!a((function(){var e=!1,n=/a/;return"split"===t&&(n={},n.constructor={},n.constructor[u]=function(){return n},n.flags="",n[l]=/./[l]),n.exec=function(){return e=!0,null},n[l](""),!e}));if(!h||!p||n){var v=/./[l],b=e(l,""[t],(function(t,e,n,i,a){var s=e.exec;return s===o||s===f.exec?h&&!a?{done:!0,value:r(v,e,n,i)}:{done:!0,value:r(t,n,e,i)}:{done:!1}}));i(String.prototype,t,b[0]),i(f,l,b[1])}d&&c(f[l],"sham",!0)}},d78d:function(t,e,n){"use strict";var r=n("6d8b");n.o(r,"render")&&n.d(e,"render",(function(){return r["render"]})),n.o(r,"staticRenderFns")&&n.d(e,"staticRenderFns",(function(){return r["staticRenderFns"]}))},d81d:function(t,e,n){"use strict";var r=n("23e7"),i=n("b727").map,o=n("1dde"),a=o("map");r({target:"Array",proto:!0,forced:!a},{map:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},d82e:function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("14d9"),n("b0c0"),n("a9e3"),n("d3b7"),n("25f0"),n("159b");var i=n("7a1a"),o=n("21bf"),a=r(n("07fc"));e.default={name:"NeCarousel",components:{NeIcon:a.default},props:{initialIndex:{type:Number,default:0},height:String,trigger:{type:String,default:"hover"},autoplay:{type:Boolean,default:!0},interval:{type:Number,default:3e3},indicatorPosition:String,indicator:{type:Boolean,default:!0},arrow:{type:String,default:"hover"},type:String,loop:{type:Boolean,default:!0},direction:{type:String,default:"horizontal",validator:function(t){return-1!==["horizontal","vertical"].indexOf(t)}}},data:function(){return{items:[],activeIndex:-1,containerWidth:0,timer:null,hover:!1}},computed:{arrowDisplay:function(){return"never"!==this.arrow&&"vertical"!==this.direction},hasLabel:function(){return this.items.some((function(t){return t.label.toString().length>0}))},carouselClasses:function(){var t=["ne-carousel","ne-carousel--"+this.direction];return"card"===this.type&&t.push("ne-carousel--card"),t},indicatorsClasses:function(){var t=["ne-carousel__indicators","ne-carousel__indicators--"+this.direction];return this.hasLabel&&t.push("ne-carousel__indicators--labels"),"outside"!==this.indicatorPosition&&"card"!==this.type||t.push("ne-carousel__indicators--outside"),t}},watch:{items:function(t){t.length>0&&this.setActiveItem(this.initialIndex)},activeIndex:function(t,e){this.resetItemPosition(e),e>-1&&this.$emit("change",t,e)},autoplay:function(t){t?this.startTimer():this.pauseTimer()},loop:function(){this.setActiveItem(this.activeIndex)}},created:function(){var t=this;this.throttledArrowClick=(0,i.throttle)(300,!0,(function(e){t.setActiveItem(e)})),this.throttledIndicatorHover=(0,i.throttle)(300,(function(e){t.handleIndicatorHover(e)}))},mounted:function(){var t=this;this.updateItems(),this.$nextTick((function(){(0,o.addResizeListener)(t.$el,t.resetItemPosition),t.initialIndex<t.items.length&&t.initialIndex>=0&&(t.activeIndex=t.initialIndex),t.startTimer()}))},beforeDestroy:function(){this.$el&&(0,o.removeResizeListener)(this.$el,this.resetItemPosition),this.pauseTimer()},methods:{handleMouseEnter:function(){this.hover=!0,this.pauseTimer()},handleMouseLeave:function(){this.hover=!1,this.startTimer()},itemInStage:function(t,e){var n=this.items.length;return e===n-1&&t.inStage&&this.items[0].active||t.inStage&&this.items[e+1]&&this.items[e+1].active?"left":!!(0===e&&t.inStage&&this.items[n-1].active||t.inStage&&this.items[e-1]&&this.items[e-1].active)&&"right"},handleButtonEnter:function(t){var e=this;"vertical"!==this.direction&&this.items.forEach((function(n,r){t===e.itemInStage(n,r)&&(n.hover=!0)}))},handleButtonLeave:function(){"vertical"!==this.direction&&this.items.forEach((function(t){t.hover=!1}))},updateItems:function(){this.items=this.$children.filter((function(t){return"NeCarouselItem"===t.$options.name}))},resetItemPosition:function(t){var e=this;this.items.forEach((function(n,r){n.translateItem(r,e.activeIndex,t)}))},playSlides:function(){this.activeIndex<this.items.length-1?this.activeIndex++:this.loop&&(this.activeIndex=0)},pauseTimer:function(){this.timer&&(clearInterval(this.timer),this.timer=null)},startTimer:function(){this.interval<=0||!this.autoplay||this.timer||(this.timer=setInterval(this.playSlides,this.interval))},setActiveItem:function(t){if("string"===typeof t){var e=this.items.filter((function(e){return e.name===t}));e.length>0&&(t=this.items.indexOf(e[0]))}if(t=Number(t),!isNaN(t)&&t===Math.floor(t)){var n=this.items.length,r=this.activeIndex;this.activeIndex=t<0?this.loop?n-1:0:t>=n?this.loop?0:n-1:t,r===this.activeIndex&&this.resetItemPosition(r)}},prev:function(){this.setActiveItem(this.activeIndex-1)},next:function(){this.setActiveItem(this.activeIndex+1)},handleIndicatorClick:function(t){this.activeIndex=t},handleIndicatorHover:function(t){"hover"===this.trigger&&t!==this.activeIndex&&(this.activeIndex=t)}}}},d86b:function(t,e,n){"use strict";var r=n("d039");t.exports=r((function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}}))},d967:function(t,e,n){"use strict";function r(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(e.default=r=function(){return!!t})()}Object.defineProperty(e,"__esModule",{value:!0}),e.default=r,n("4ae1")},d9b5:function(t,e,n){"use strict";var r=n("d066"),i=n("1626"),o=n("3a9b"),a=n("fdbf"),s=Object;t.exports=a?function(t){return"symbol"==typeof t}:function(t){var e=r("Symbol");return i(e)&&o(e.prototype,s(t))}},d9e2:function(t,e,n){"use strict";var r=n("23e7"),i=n("cfe9"),o=n("2ba4"),a=n("e5cb"),s="WebAssembly",c=i[s],u=7!==new Error("e",{cause:7}).cause,f=function(t,e){var n={};n[t]=a(t,e,u),r({global:!0,constructor:!0,arity:1,forced:u},n)},d=function(t,e){if(c&&c[t]){var n={};n[t]=a(s+"."+t,e,u),r({target:s,stat:!0,constructor:!0,arity:1,forced:u},n)}};f("Error",(function(t){return function(e){return o(t,this,arguments)}})),f("EvalError",(function(t){return function(e){return o(t,this,arguments)}})),f("RangeError",(function(t){return function(e){return o(t,this,arguments)}})),f("ReferenceError",(function(t){return function(e){return o(t,this,arguments)}})),f("SyntaxError",(function(t){return function(e){return o(t,this,arguments)}})),f("TypeError",(function(t){return function(e){return o(t,this,arguments)}})),f("URIError",(function(t){return function(e){return o(t,this,arguments)}})),d("CompileError",(function(t){return function(e){return o(t,this,arguments)}})),d("LinkError",(function(t){return function(e){return o(t,this,arguments)}})),d("RuntimeError",(function(t){return function(e){return o(t,this,arguments)}}))},d9f5:function(t,e,n){"use strict";var r=n("23e7"),i=n("cfe9"),o=n("c65b"),a=n("e330"),s=n("c430"),c=n("83ab"),u=n("04f8"),f=n("d039"),d=n("1a2d"),l=n("3a9b"),h=n("825a"),p=n("fc6a"),v=n("a04b"),b=n("577e"),m=n("5c6c"),g=n("7c73"),y=n("df75"),x=n("241c"),w=n("057f"),_=n("7418"),A=n("06cf"),O=n("9bf2"),S=n("37e8"),E=n("d1e7"),T=n("cb2d"),C=n("edd0"),R=n("5692"),k=n("f772"),j=n("d012"),I=n("90e3"),M=n("b622"),L=n("e538"),P=n("e065"),F=n("57b9"),N=n("d44e"),D=n("69f3"),B=n("b727").forEach,G=k("hidden"),z="Symbol",U="prototype",$=D.set,V=D.getterFor(z),W=Object[U],q=i.Symbol,H=q&&q[U],Y=i.RangeError,X=i.TypeError,Z=i.QObject,K=A.f,Q=O.f,J=w.f,tt=E.f,et=a([].push),nt=R("symbols"),rt=R("op-symbols"),it=R("wks"),ot=!Z||!Z[U]||!Z[U].findChild,at=function(t,e,n){var r=K(W,e);r&&delete W[e],Q(t,e,n),r&&t!==W&&Q(W,e,r)},st=c&&f((function(){return 7!==g(Q({},"a",{get:function(){return Q(this,"a",{value:7}).a}})).a}))?at:Q,ct=function(t,e){var n=nt[t]=g(H);return $(n,{type:z,tag:t,description:e}),c||(n.description=e),n},ut=function(t,e,n){t===W&&ut(rt,e,n),h(t);var r=v(e);return h(n),d(nt,r)?(n.enumerable?(d(t,G)&&t[G][r]&&(t[G][r]=!1),n=g(n,{enumerable:m(0,!1)})):(d(t,G)||Q(t,G,m(1,g(null))),t[G][r]=!0),st(t,r,n)):Q(t,r,n)},ft=function(t,e){h(t);var n=p(e),r=y(n).concat(vt(n));return B(r,(function(e){c&&!o(lt,n,e)||ut(t,e,n[e])})),t},dt=function(t,e){return void 0===e?g(t):ft(g(t),e)},lt=function(t){var e=v(t),n=o(tt,this,e);return!(this===W&&d(nt,e)&&!d(rt,e))&&(!(n||!d(this,e)||!d(nt,e)||d(this,G)&&this[G][e])||n)},ht=function(t,e){var n=p(t),r=v(e);if(n!==W||!d(nt,r)||d(rt,r)){var i=K(n,r);return!i||!d(nt,r)||d(n,G)&&n[G][r]||(i.enumerable=!0),i}},pt=function(t){var e=J(p(t)),n=[];return B(e,(function(t){d(nt,t)||d(j,t)||et(n,t)})),n},vt=function(t){var e=t===W,n=J(e?rt:p(t)),r=[];return B(n,(function(t){!d(nt,t)||e&&!d(W,t)||et(r,nt[t])})),r};u||(q=function(){if(l(H,this))throw new X("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?b(arguments[0]):void 0,e=I(t),n=function(t){var r=void 0===this?i:this;r===W&&o(n,rt,t),d(r,G)&&d(r[G],e)&&(r[G][e]=!1);var a=m(1,t);try{st(r,e,a)}catch(s){if(!(s instanceof Y))throw s;at(r,e,a)}};return c&&ot&&st(W,e,{configurable:!0,set:n}),ct(e,t)},H=q[U],T(H,"toString",(function(){return V(this).tag})),T(q,"withoutSetter",(function(t){return ct(I(t),t)})),E.f=lt,O.f=ut,S.f=ft,A.f=ht,x.f=w.f=pt,_.f=vt,L.f=function(t){return ct(M(t),t)},c&&(C(H,"description",{configurable:!0,get:function(){return V(this).description}}),s||T(W,"propertyIsEnumerable",lt,{unsafe:!0}))),r({global:!0,constructor:!0,wrap:!0,forced:!u,sham:!u},{Symbol:q}),B(y(it),(function(t){P(t)})),r({target:z,stat:!0,forced:!u},{useSetter:function(){ot=!0},useSimple:function(){ot=!1}}),r({target:"Object",stat:!0,forced:!u,sham:!c},{create:dt,defineProperty:ut,defineProperties:ft,getOwnPropertyDescriptor:ht}),r({target:"Object",stat:!0,forced:!u},{getOwnPropertyNames:pt}),F(),N(q,z),j[G]=!0},da3e:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0;e.render=function(){var t=this,e=t._self._c;return e(t._neTag,{tag:"component",staticClass:"ne-radio-group",attrs:{role:"radiogroup"},on:{keydown:t.handleKeydown}},[t._t("default")],2)},e.staticRenderFns=[]},da73:function(t,e,n){"use strict";n.r(e);var r=n("9f92"),i=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=i.a},dbe5:function(t,e,n){"use strict";var r=n("cfe9"),i=n("d039"),o=n("1212"),a=n("8558"),s=r.structuredClone;t.exports=!!s&&!i((function(){if("DENO"===a&&o>92||"NODE"===a&&o>94||"BROWSER"===a&&o>97)return!1;var t=new ArrayBuffer(8),e=s(t,{transfer:[t]});return 0!==t.byteLength||8!==e.byteLength}))},dc4a:function(t,e,n){"use strict";var r=n("59ed"),i=n("7234");t.exports=function(t,e){var n=t[e];return i(n)?void 0:r(n)}},dca8:function(t,e,n){"use strict";var r=n("23e7"),i=n("bb2f"),o=n("d039"),a=n("861d"),s=n("f183").onFreeze,c=Object.freeze,u=o((function(){c(1)}));r({target:"Object",stat:!0,forced:u,sham:!i},{freeze:function(t){return c&&a(t)?c(s(t)):t}})},dcc3:function(t,e,n){"use strict";var r=n("ae93").IteratorPrototype,i=n("7c73"),o=n("5c6c"),a=n("d44e"),s=n("3f8c"),c=function(){return this};t.exports=function(t,e,n,u){var f=e+" Iterator";return t.prototype=i(r,{next:o(+!u,n)}),a(t,f,!1,!0),s[f]=c,t}},ddb0:function(t,e,n){"use strict";var r=n("cfe9"),i=n("fdbc"),o=n("785a"),a=n("e260"),s=n("9112"),c=n("d44e"),u=n("b622"),f=u("iterator"),d=a.values,l=function(t,e){if(t){if(t[f]!==d)try{s(t,f,d)}catch(r){t[f]=d}if(c(t,e,!0),i[e])for(var n in a)if(t[n]!==a[n])try{s(t,n,a[n])}catch(r){t[n]=a[n]}}};for(var h in i)l(r[h]&&r[h].prototype,h);l(o,"DOMTokenList")},dec2:function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.raf=e.parseTimeData=e.parseFormat=e.isSameSecond=e.doubleRaf=e.cancelRaf=void 0,n("ac1f"),n("5319");var r=n("26a1"),i=1e3,o=60*i,a=60*o,s=24*a,c=function(t){return t>=0&&t<10?"0".concat(t):String(t)},u=(e.parseTimeData=function(t){var e=Math.floor(t/s),n=Math.floor(t%s/a),r=Math.floor(t%a/o),c=Math.floor(t%o/i),u=Math.floor(t%i);return{days:e,hours:n,minutes:r,seconds:c,milliseconds:u}},e.parseFormat=function(t,e){var n=e.days,r=e.hours,i=e.minutes,o=e.seconds,a=e.milliseconds;return-1===t.indexOf("DD")?r+=24*n:t=t.replace("DD",c(n)),-1===t.indexOf("HH")?i+=60*r:t=t.replace("HH",c(r)),-1===t.indexOf("mm")?o+=60*i:t=t.replace("mm",c(i)),-1===t.indexOf("ss")?a+=1e3*o:t=t.replace("ss",c(o)),t.replace("SSS",c(a,3))},e.isSameSecond=function(t,e){return Math.floor(t/1e3)===Math.floor(e/1e3)},Date.now()),f=function(t){var e=Date.now(),n=Math.max(0,16-(e-u)),r=setTimeout(t,n);return u=e+n,r},d=r.isServer?t:window,l=d.requestAnimationFrame||f,h=d.cancelAnimationFrame||d.clearTimeout,p=e.raf=function(t){return l.call(d,t)};e.doubleRaf=function(t){p((function(){p(t)}))},e.cancelRaf=function(t){h.call(d,t)}}).call(this,n("c8ba"))},deca:function(t,e,n){"use strict";n("4c75")},df26:function(t,e,n){"use strict";var r=n("618a");n.o(r,"render")&&n.d(e,"render",(function(){return r["render"]})),n.o(r,"staticRenderFns")&&n.d(e,"staticRenderFns",(function(){return r["staticRenderFns"]}))},df75:function(t,e,n){"use strict";var r=n("ca84"),i=n("7839");t.exports=Object.keys||function(t){return r(t,i)}},df7e:function(t,e,n){"use strict";var r=n("07fa");t.exports=function(t,e){for(var n=r(t),i=new e(n),o=0;o<n;o++)i[o]=t[n-o-1];return i}},dfb9:function(t,e,n){"use strict";var r=n("07fa");t.exports=function(t,e,n){var i=0,o=arguments.length>2?n:r(e),a=new t(o);while(o>i)a[i]=e[i++];return a}},e01a:function(t,e,n){"use strict";var r=n("23e7"),i=n("83ab"),o=n("cfe9"),a=n("e330"),s=n("1a2d"),c=n("1626"),u=n("3a9b"),f=n("577e"),d=n("edd0"),l=n("e893"),h=o.Symbol,p=h&&h.prototype;if(i&&c(h)&&(!("description"in p)||void 0!==h().description)){var v={},b=function(){var t=arguments.length<1||void 0===arguments[0]?void 0:f(arguments[0]),e=u(p,this)?new h(t):void 0===t?h():h(t);return""===t&&(v[e]=!0),e};l(b,h),b.prototype=p,p.constructor=b;var m="Symbol(description detection)"===String(h("description detection")),g=a(p.valueOf),y=a(p.toString),x=/^Symbol\((.*)\)[^)]+$/,w=a("".replace),_=a("".slice);d(p,"description",{configurable:!0,get:function(){var t=g(this);if(s(v,t))return"";var e=y(t),n=m?_(e,7,-1):w(e,x,"$1");return""===n?void 0:n}}),r({global:!0,constructor:!0,forced:!0},{Symbol:b})}},e065:function(t,e,n){"use strict";var r=n("428f"),i=n("1a2d"),o=n("e538"),a=n("9bf2").f;t.exports=function(t){var e=r.Symbol||(r.Symbol={});i(e,t)||a(e,t,{value:o.f(t)})}},e163:function(t,e,n){"use strict";var r=n("1a2d"),i=n("1626"),o=n("7b0b"),a=n("f772"),s=n("e177"),c=a("IE_PROTO"),u=Object,f=u.prototype;t.exports=s?u.getPrototypeOf:function(t){var e=o(t);if(r(e,c))return e[c];var n=e.constructor;return i(n)&&e instanceof n?n.prototype:e instanceof u?f:null}},e177:function(t,e,n){"use strict";var r=n("d039");t.exports=!r((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))},e260:function(t,e,n){"use strict";var r=n("fc6a"),i=n("44d2"),o=n("3f8c"),a=n("69f3"),s=n("9bf2").f,c=n("c6d2"),u=n("4754"),f=n("c430"),d=n("83ab"),l="Array Iterator",h=a.set,p=a.getterFor(l);t.exports=c(Array,"Array",(function(t,e){h(this,{type:l,target:r(t),index:0,kind:e})}),(function(){var t=p(this),e=t.target,n=t.index++;if(!e||n>=e.length)return t.target=void 0,u(void 0,!0);switch(t.kind){case"keys":return u(n,!1);case"values":return u(e[n],!1)}return u([n,e[n]],!1)}),"values");var v=o.Arguments=o.Array;if(i("keys"),i("values"),i("entries"),!f&&d&&"values"!==v.name)try{s(v,"name",{value:"values"})}catch(b){}},e267:function(t,e,n){"use strict";var r=n("e330"),i=n("e8b5"),o=n("1626"),a=n("c6b6"),s=n("577e"),c=r([].push);t.exports=function(t){if(o(t))return t;if(i(t)){for(var e=t.length,n=[],r=0;r<e;r++){var u=t[r];"string"==typeof u?c(n,u):"number"!=typeof u&&"Number"!==a(u)&&"String"!==a(u)||c(n,s(u))}var f=n.length,d=!0;return function(t,e){if(d)return d=!1,e;if(i(this))return e;for(var r=0;r<f;r++)if(n[r]===t)return e}}}},e330:function(t,e,n){"use strict";var r=n("40d5"),i=Function.prototype,o=i.call,a=r&&i.bind.bind(o,o);t.exports=r?a:function(t){return function(){return o.apply(t,arguments)}}},e391:function(t,e,n){"use strict";var r=n("577e");t.exports=function(t,e){return void 0===t?arguments.length<2?"":e:r(t)}},e459:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0"),n("a9e3");e.default={name:"NeIcon",props:{name:{type:String,default:""},color:{type:String,default:""},size:{type:Number,default:0},rotate:{type:Boolean,default:!1},customClass:{type:String,default:""}},computed:{iconName:function(){return"ne-icon--".concat(this.name)},iconRotate:function(){return this.rotate&&"is-rotate"},styles:function(){var t={};return this.color&&(t.color=this.color),this.size&&(t.width="".concat(this.size,"px"),t.height="".concat(this.size,"px")),t}}}},e538:function(t,e,n){"use strict";var r=n("b622");e.f=r},e58c:function(t,e,n){"use strict";var r=n("2ba4"),i=n("fc6a"),o=n("5926"),a=n("07fa"),s=n("a640"),c=Math.min,u=[].lastIndexOf,f=!!u&&1/[1].lastIndexOf(1,-0)<0,d=s("lastIndexOf"),l=f||!d;t.exports=l?function(t){if(f)return r(u,this,arguments)||0;var e=i(this),n=a(e);if(0===n)return-1;var s=n-1;for(arguments.length>1&&(s=c(s,o(arguments[1]))),s<0&&(s=n+s);s>=0;s--)if(s in e&&e[s]===t)return s||0;return-1}:u},e5cb:function(t,e,n){"use strict";var r=n("d066"),i=n("1a2d"),o=n("9112"),a=n("3a9b"),s=n("d2bb"),c=n("e893"),u=n("aeb0"),f=n("7156"),d=n("e391"),l=n("ab36"),h=n("6f19"),p=n("83ab"),v=n("c430");t.exports=function(t,e,n,b){var m="stackTraceLimit",g=b?2:1,y=t.split("."),x=y[y.length-1],w=r.apply(null,y);if(w){var _=w.prototype;if(!v&&i(_,"cause")&&delete _.cause,!n)return w;var A=r("Error"),O=e((function(t,e){var n=d(b?e:t,void 0),r=b?new w(t):new w;return void 0!==n&&o(r,"message",n),h(r,O,r.stack,2),this&&a(_,this)&&f(r,this,O),arguments.length>g&&l(r,arguments[g]),r}));if(O.prototype=_,"Error"!==x?s?s(O,A):c(O,A,{name:!0}):p&&m in w&&(u(O,w,m),u(O,w,"prepareStackTrace")),c(O,w),!v)try{_.name!==x&&o(_,"name",x),_.constructor=O}catch(S){}return O}}},e667:function(t,e,n){"use strict";t.exports=function(t){try{return{error:!1,value:t()}}catch(e){return{error:!0,value:e}}}},e6cf:function(t,e,n){"use strict";n("5e7e"),n("14e5"),n("cc98"),n("3529"),n("f22b"),n("7149")},e893:function(t,e,n){"use strict";var r=n("1a2d"),i=n("56ef"),o=n("06cf"),a=n("9bf2");t.exports=function(t,e,n){for(var s=i(e),c=a.f,u=o.f,f=0;f<s.length;f++){var d=s[f];r(t,d)||n&&r(n,d)||c(t,d,u(e,d))}}},e8b5:function(t,e,n){"use strict";var r=n("c6b6");t.exports=Array.isArray||function(t){return"Array"===r(t)}},e91f:function(t,e,n){"use strict";var r=n("ebb5"),i=n("4d64").indexOf,o=r.aTypedArray,a=r.exportTypedArrayMethod;a("indexOf",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},e95a:function(t,e,n){"use strict";var r=n("b622"),i=n("3f8c"),o=r("iterator"),a=Array.prototype;t.exports=function(t){return void 0!==t&&(i.Array===t||a[o]===t)}},e9c4:function(t,e,n){"use strict";var r=n("23e7"),i=n("d066"),o=n("2ba4"),a=n("c65b"),s=n("e330"),c=n("d039"),u=n("1626"),f=n("d9b5"),d=n("f36a"),l=n("e267"),h=n("04f8"),p=String,v=i("JSON","stringify"),b=s(/./.exec),m=s("".charAt),g=s("".charCodeAt),y=s("".replace),x=s(1..toString),w=/[\uD800-\uDFFF]/g,_=/^[\uD800-\uDBFF]$/,A=/^[\uDC00-\uDFFF]$/,O=!h||c((function(){var t=i("Symbol")("stringify detection");return"[null]"!==v([t])||"{}"!==v({a:t})||"{}"!==v(Object(t))})),S=c((function(){return'"\\udf06\\ud834"'!==v("\udf06\ud834")||'"\\udead"'!==v("\udead")})),E=function(t,e){var n=d(arguments),r=l(e);if(u(r)||void 0!==t&&!f(t))return n[1]=function(t,e){if(u(r)&&(e=a(r,this,p(t),e)),!f(e))return e},o(v,null,n)},T=function(t,e,n){var r=m(n,e-1),i=m(n,e+1);return b(_,t)&&!b(A,i)||b(A,t)&&!b(_,r)?"\\u"+x(g(t,0),16):t};v&&r({target:"JSON",stat:!0,arity:3,forced:O||S},{stringify:function(t,e,n){var r=d(arguments),i=o(O?E:v,null,r);return S&&"string"==typeof i?y(i,w,T):i}})},ea83:function(t,e,n){"use strict";var r=n("b5db"),i=r.match(/AppleWebKit\/(\d+)\./);t.exports=!!i&&+i[1]},eac5:function(t,e,n){"use strict";var r=n("861d"),i=Math.floor;t.exports=Number.isInteger||function(t){return!r(t)&&isFinite(t)&&i(t)===t}},ebb5:function(t,e,n){"use strict";var r,i,o,a=n("4b11"),s=n("83ab"),c=n("cfe9"),u=n("1626"),f=n("861d"),d=n("1a2d"),l=n("f5df"),h=n("0d51"),p=n("9112"),v=n("cb2d"),b=n("edd0"),m=n("3a9b"),g=n("e163"),y=n("d2bb"),x=n("b622"),w=n("90e3"),_=n("69f3"),A=_.enforce,O=_.get,S=c.Int8Array,E=S&&S.prototype,T=c.Uint8ClampedArray,C=T&&T.prototype,R=S&&g(S),k=E&&g(E),j=Object.prototype,I=c.TypeError,M=x("toStringTag"),L=w("TYPED_ARRAY_TAG"),P="TypedArrayConstructor",F=a&&!!y&&"Opera"!==l(c.opera),N=!1,D={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},B={BigInt64Array:8,BigUint64Array:8},G=function(t){if(!f(t))return!1;var e=l(t);return"DataView"===e||d(D,e)||d(B,e)},z=function(t){var e=g(t);if(f(e)){var n=O(e);return n&&d(n,P)?n[P]:z(e)}},U=function(t){if(!f(t))return!1;var e=l(t);return d(D,e)||d(B,e)},$=function(t){if(U(t))return t;throw new I("Target is not a typed array")},V=function(t){if(u(t)&&(!y||m(R,t)))return t;throw new I(h(t)+" is not a typed array constructor")},W=function(t,e,n,r){if(s){if(n)for(var i in D){var o=c[i];if(o&&d(o.prototype,t))try{delete o.prototype[t]}catch(a){try{o.prototype[t]=e}catch(u){}}}k[t]&&!n||v(k,t,n?e:F&&E[t]||e,r)}},q=function(t,e,n){var r,i;if(s){if(y){if(n)for(r in D)if(i=c[r],i&&d(i,t))try{delete i[t]}catch(o){}if(R[t]&&!n)return;try{return v(R,t,n?e:F&&R[t]||e)}catch(o){}}for(r in D)i=c[r],!i||i[t]&&!n||v(i,t,e)}};for(r in D)i=c[r],o=i&&i.prototype,o?A(o)[P]=i:F=!1;for(r in B)i=c[r],o=i&&i.prototype,o&&(A(o)[P]=i);if((!F||!u(R)||R===Function.prototype)&&(R=function(){throw new I("Incorrect invocation")},F))for(r in D)c[r]&&y(c[r],R);if((!F||!k||k===j)&&(k=R.prototype,F))for(r in D)c[r]&&y(c[r].prototype,k);if(F&&g(C)!==k&&y(C,k),s&&!d(k,M))for(r in N=!0,b(k,M,{configurable:!0,get:function(){return f(this)?this[L]:void 0}}),D)c[r]&&p(c[r],L,r);t.exports={NATIVE_ARRAY_BUFFER_VIEWS:F,TYPED_ARRAY_TAG:N&&L,aTypedArray:$,aTypedArrayConstructor:V,exportTypedArrayMethod:W,exportTypedArrayStaticMethod:q,getTypedArrayConstructor:z,isView:G,isTypedArray:U,TypedArray:R,TypedArrayPrototype:k}},ebc1:function(t,e,n){"use strict";var r=n("b5db");t.exports=/ipad|iphone|ipod/i.test(r)&&"undefined"!=typeof Pebble},ec2d:function(t,e,n){"use strict";n.r(e);var r=n("fe52"),i=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=i.a},ec87:function(t,e,n){"use strict";var r=n("b5db");t.exports=/web0s(?!.*chrome)/i.test(r)},ed13:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0,n("b0c0");e.render=function(){var t=this,e=t._self._c;return e("label",{staticClass:"ne-radio",class:[t.border&&t.radioSize?"ne-radio--"+t.radioSize:"",{"is-disabled":t.isDisabled},{"is-focus":t.focus},{"is-bordered":t.border},{"is-checked":t.model===t.label}],attrs:{role:"radio","aria-checked":t.model===t.label,"aria-disabled":t.isDisabled,tabindex:t.tabIndex},on:{keydown:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"space",32,e.key,[" ","Spacebar"]))return null;e.stopPropagation(),e.preventDefault(),t.model=t.isDisabled?t.model:t.label}}},[e("span",{staticClass:"ne-radio__input",class:{"is-disabled":t.isDisabled,"is-checked":t.model===t.label}},[e("span",{staticClass:"ne-radio__inner"}),e("input",{directives:[{name:"model",rawName:"v-model",value:t.model,expression:"model"}],ref:"radio",staticClass:"ne-radio__original",attrs:{type:"radio","aria-hidden":"true",name:t.name,disabled:t.isDisabled,tabindex:"-1"},domProps:{value:t.label,checked:t._q(t.model,t.label)},on:{focus:function(e){t.focus=!0},blur:function(e){t.focus=!1},change:[function(e){t.model=t.label},t.handleChange]}})]),e("span",{staticClass:"ne-radio__label",on:{keydown:function(t){t.stopPropagation()}}},[t._t("default"),t.$slots.default?t._e():[t._v(t._s(t.label))]],2)])},e.staticRenderFns=[]},edd0:function(t,e,n){"use strict";var r=n("13d2"),i=n("9bf2");t.exports=function(t,e,n){return n.get&&r(n.get,e,{getter:!0}),n.set&&r(n.set,e,{setter:!0}),i.f(t,e,n)}},efec:function(t,e,n){"use strict";var r=n("1a2d"),i=n("cb2d"),o=n("51eb"),a=n("b622"),s=a("toPrimitive"),c=Date.prototype;r(c,s)||i(c,s,o)},f069:function(t,e,n){"use strict";var r=n("59ed"),i=TypeError,o=function(t){var e,n;this.promise=new t((function(t,r){if(void 0!==e||void 0!==n)throw new i("Bad Promise constructor");e=t,n=r})),this.resolve=r(e),this.reject=r(n)};t.exports.f=function(t){return new o(t)}},f107:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0,n("b0c0");e.render=function(){var t=this,e=t._self._c;return e("label",{staticClass:"ne-radio-button",class:[t.size?"ne-radio-button--"+t.size:"",{"is-active":t.value===t.label},{"is-disabled":t.isDisabled},{"is-focus":t.focus}],attrs:{role:"radio","aria-checked":t.value===t.label,"aria-disabled":t.isDisabled,tabindex:t.tabIndex},on:{keydown:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"space",32,e.key,[" ","Spacebar"]))return null;e.stopPropagation(),e.preventDefault(),t.value=t.isDisabled?t.value:t.label}}},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.value,expression:"value"}],staticClass:"ne-radio-button__orig-radio",attrs:{type:"radio",name:t.name,disabled:t.isDisabled,tabindex:"-1"},domProps:{value:t.label,checked:t._q(t.value,t.label)},on:{change:[function(e){t.value=t.label},t.handleChange],focus:function(e){t.focus=!0},blur:function(e){t.focus=!1}}}),e("span",{staticClass:"ne-radio-button__inner",style:t.value===t.label?t.activeStyle:null,on:{keydown:function(t){t.stopPropagation()}}},[t._t("default"),t.$slots.default?t._e():[t._v(t._s(t.label))]],2)])},e.staticRenderFns=[]},f183:function(t,e,n){"use strict";var r=n("23e7"),i=n("e330"),o=n("d012"),a=n("861d"),s=n("1a2d"),c=n("9bf2").f,u=n("241c"),f=n("057f"),d=n("4fad"),l=n("90e3"),h=n("bb2f"),p=!1,v=l("meta"),b=0,m=function(t){c(t,v,{value:{objectID:"O"+b++,weakData:{}}})},g=function(t,e){if(!a(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!s(t,v)){if(!d(t))return"F";if(!e)return"E";m(t)}return t[v].objectID},y=function(t,e){if(!s(t,v)){if(!d(t))return!0;if(!e)return!1;m(t)}return t[v].weakData},x=function(t){return h&&p&&d(t)&&!s(t,v)&&m(t),t},w=function(){_.enable=function(){},p=!0;var t=u.f,e=i([].splice),n={};n[v]=1,t(n).length&&(u.f=function(n){for(var r=t(n),i=0,o=r.length;i<o;i++)if(r[i]===v){e(r,i,1);break}return r},r({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:f.f}))},_=t.exports={enable:w,fastKey:g,getWeakData:y,onFreeze:x};o[v]=!0},f216:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={name:"NeButtonGroup"}},f22b:function(t,e,n){"use strict";var r=n("23e7"),i=n("f069"),o=n("4738").CONSTRUCTOR;r({target:"Promise",stat:!0,forced:o},{reject:function(t){var e=i.f(this),n=e.reject;return n(t),e.promise}})},f36a:function(t,e,n){"use strict";var r=n("e330");t.exports=r([].slice)},f495:function(t,e,n){"use strict";var r=n("c04e"),i=TypeError;t.exports=function(t){var e=r(t,"number");if("number"==typeof e)throw new i("Can't convert number to bigint");return BigInt(e)}},f4e5:function(t,e,n){"use strict";var r=n("4508");n.o(r,"render")&&n.d(e,"render",(function(){return r["render"]})),n.o(r,"staticRenderFns")&&n.d(e,"staticRenderFns",(function(){return r["staticRenderFns"]}))},f5df:function(t,e,n){"use strict";var r=n("00ee"),i=n("1626"),o=n("c6b6"),a=n("b622"),s=a("toStringTag"),c=Object,u="Arguments"===o(function(){return arguments}()),f=function(t,e){try{return t[e]}catch(n){}};t.exports=r?o:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=f(e=c(t),s))?n:u?o(e):"Object"===(r=o(e))&&i(e.callee)?"Arguments":r}},f748:function(t,e,n){"use strict";t.exports=Math.sign||function(t){var e=+t;return 0===e||e!==e?e:e<0?-1:1}},f772:function(t,e,n){"use strict";var r=n("5692"),i=n("90e3"),o=r("keys");t.exports=function(t){return o[t]||(o[t]=i(t))}},f84d:function(t,e,n){"use strict";n.r(e);var r=n("452b"),i=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=i.a},f8cd:function(t,e,n){"use strict";var r=n("5926"),i=RangeError;t.exports=function(t){var e=r(t);if(e<0)throw new i("The argument can't be less than 0");return e}},f98f:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0;e.render=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"show",rawName:"v-show",value:t.ready,expression:"ready"}],staticClass:"ne-carousel__item",class:{"is-active":t.active,"ne-carousel__item--card":"card"===t.$parent.type,"is-in-stage":t.inStage,"is-hover":t.hover,"is-animating":t.animating},style:t.itemStyle,on:{click:t.handleItemClick}},["card"===t.$parent.type?e("div",{directives:[{name:"show",rawName:"v-show",value:!t.active,expression:"!active"}],staticClass:"ne-carousel__mask"}):t._e(),t._t("default")],2)},e.staticRenderFns=[]},f9aa:function(t,e,n){"use strict";n.r(e);var r=n("8dbc"),i=n("b2bb");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);var a=n("2877"),s=Object(a["a"])(i["default"],r["render"],r["staticRenderFns"],!1,null,null,null);e["default"]=s.exports},fa8e:function(t,e,n){"use strict";n.r(e);var r=n("c939"),i=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=i.a},faa1:function(t,e,n){"use strict";var r,i="object"===typeof Reflect?Reflect:null,o=i&&"function"===typeof i.apply?i.apply:function(t,e,n){return Function.prototype.apply.call(t,e,n)};function a(t){console&&console.warn&&console.warn(t)}r=i&&"function"===typeof i.ownKeys?i.ownKeys:Object.getOwnPropertySymbols?function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:function(t){return Object.getOwnPropertyNames(t)};var s=Number.isNaN||function(t){return t!==t};function c(){c.init.call(this)}t.exports=c,t.exports.once=x,c.EventEmitter=c,c.prototype._events=void 0,c.prototype._eventsCount=0,c.prototype._maxListeners=void 0;var u=10;function f(t){if("function"!==typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t)}function d(t){return void 0===t._maxListeners?c.defaultMaxListeners:t._maxListeners}function l(t,e,n,r){var i,o,s;if(f(n),o=t._events,void 0===o?(o=t._events=Object.create(null),t._eventsCount=0):(void 0!==o.newListener&&(t.emit("newListener",e,n.listener?n.listener:n),o=t._events),s=o[e]),void 0===s)s=o[e]=n,++t._eventsCount;else if("function"===typeof s?s=o[e]=r?[n,s]:[s,n]:r?s.unshift(n):s.push(n),i=d(t),i>0&&s.length>i&&!s.warned){s.warned=!0;var c=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=t,c.type=e,c.count=s.length,a(c)}return t}function h(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function p(t,e,n){var r={fired:!1,wrapFn:void 0,target:t,type:e,listener:n},i=h.bind(r);return i.listener=n,r.wrapFn=i,i}function v(t,e,n){var r=t._events;if(void 0===r)return[];var i=r[e];return void 0===i?[]:"function"===typeof i?n?[i.listener||i]:[i]:n?y(i):m(i,i.length)}function b(t){var e=this._events;if(void 0!==e){var n=e[t];if("function"===typeof n)return 1;if(void 0!==n)return n.length}return 0}function m(t,e){for(var n=new Array(e),r=0;r<e;++r)n[r]=t[r];return n}function g(t,e){for(;e+1<t.length;e++)t[e]=t[e+1];t.pop()}function y(t){for(var e=new Array(t.length),n=0;n<e.length;++n)e[n]=t[n].listener||t[n];return e}function x(t,e){return new Promise((function(n,r){function i(n){t.removeListener(e,o),r(n)}function o(){"function"===typeof t.removeListener&&t.removeListener("error",i),n([].slice.call(arguments))}_(t,e,o,{once:!0}),"error"!==e&&w(t,i,{once:!0})}))}function w(t,e,n){"function"===typeof t.on&&_(t,"error",e,n)}function _(t,e,n,r){if("function"===typeof t.on)r.once?t.once(e,n):t.on(e,n);else{if("function"!==typeof t.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof t);t.addEventListener(e,(function i(o){r.once&&t.removeEventListener(e,i),n(o)}))}}Object.defineProperty(c,"defaultMaxListeners",{enumerable:!0,get:function(){return u},set:function(t){if("number"!==typeof t||t<0||s(t))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");u=t}}),c.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},c.prototype.setMaxListeners=function(t){if("number"!==typeof t||t<0||s(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this},c.prototype.getMaxListeners=function(){return d(this)},c.prototype.emit=function(t){for(var e=[],n=1;n<arguments.length;n++)e.push(arguments[n]);var r="error"===t,i=this._events;if(void 0!==i)r=r&&void 0===i.error;else if(!r)return!1;if(r){var a;if(e.length>0&&(a=e[0]),a instanceof Error)throw a;var s=new Error("Unhandled error."+(a?" ("+a.message+")":""));throw s.context=a,s}var c=i[t];if(void 0===c)return!1;if("function"===typeof c)o(c,this,e);else{var u=c.length,f=m(c,u);for(n=0;n<u;++n)o(f[n],this,e)}return!0},c.prototype.addListener=function(t,e){return l(this,t,e,!1)},c.prototype.on=c.prototype.addListener,c.prototype.prependListener=function(t,e){return l(this,t,e,!0)},c.prototype.once=function(t,e){return f(e),this.on(t,p(this,t,e)),this},c.prototype.prependOnceListener=function(t,e){return f(e),this.prependListener(t,p(this,t,e)),this},c.prototype.removeListener=function(t,e){var n,r,i,o,a;if(f(e),r=this._events,void 0===r)return this;if(n=r[t],void 0===n)return this;if(n===e||n.listener===e)0===--this._eventsCount?this._events=Object.create(null):(delete r[t],r.removeListener&&this.emit("removeListener",t,n.listener||e));else if("function"!==typeof n){for(i=-1,o=n.length-1;o>=0;o--)if(n[o]===e||n[o].listener===e){a=n[o].listener,i=o;break}if(i<0)return this;0===i?n.shift():g(n,i),1===n.length&&(r[t]=n[0]),void 0!==r.removeListener&&this.emit("removeListener",t,a||e)}return this},c.prototype.off=c.prototype.removeListener,c.prototype.removeAllListeners=function(t){var e,n,r;if(n=this._events,void 0===n)return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[t]&&(0===--this._eventsCount?this._events=Object.create(null):delete n[t]),this;if(0===arguments.length){var i,o=Object.keys(n);for(r=0;r<o.length;++r)i=o[r],"removeListener"!==i&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(e=n[t],"function"===typeof e)this.removeListener(t,e);else if(void 0!==e)for(r=e.length-1;r>=0;r--)this.removeListener(t,e[r]);return this},c.prototype.listeners=function(t){return v(this,t,!0)},c.prototype.rawListeners=function(t){return v(this,t,!1)},c.listenerCount=function(t,e){return"function"===typeof t.listenerCount?t.listenerCount(e):b.call(t,e)},c.prototype.listenerCount=b,c.prototype.eventNames=function(){return this._eventsCount>0?r(this._events):[]}},fb15:function(t,e,n){"use strict";n.r(e);n("1eb2");var r=n("b635"),i=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=i.a},fb6a:function(t,e,n){"use strict";var r=n("23e7"),i=n("e8b5"),o=n("68ee"),a=n("861d"),s=n("23cb"),c=n("07fa"),u=n("fc6a"),f=n("8418"),d=n("b622"),l=n("1dde"),h=n("f36a"),p=l("slice"),v=d("species"),b=Array,m=Math.max;r({target:"Array",proto:!0,forced:!p},{slice:function(t,e){var n,r,d,l=u(this),p=c(l),g=s(t,p),y=s(void 0===e?p:e,p);if(i(l)&&(n=l.constructor,o(n)&&(n===b||i(n.prototype))?n=void 0:a(n)&&(n=n[v],null===n&&(n=void 0)),n===b||void 0===n))return h(l,g,y);for(r=new(void 0===n?b:n)(m(y-g,0)),d=0;g<y;g++,d++)g in l&&f(r,d,l[g]);return r.length=d,r}})},fc1b:function(t,e,n){"use strict";var r=n("f748"),i=Math.abs,o=2220446049250313e-31,a=1/o,s=function(t){return t+a-a};t.exports=function(t,e,n,a){var c=+t,u=i(c),f=r(c);if(u<a)return f*s(u/a/e)*a*e;var d=(1+e/o)*u,l=d-(d-u);return l>n||l!==l?f*(1/0):f*l}},fc6a:function(t,e,n){"use strict";var r=n("44ad"),i=n("1d80");t.exports=function(t){return r(i(t))}},fce3:function(t,e,n){"use strict";var r=n("d039"),i=n("cfe9"),o=i.RegExp;t.exports=r((function(){var t=o(".","s");return!(t.dotAll&&t.test("\n")&&"s"===t.flags)}))},fd1d:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0;e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"fillBlanks",staticClass:"ne-fillblanks",class:t.customClass,attrs:{id:"fillBlanksContainer"}},[e("div",{staticClass:"ne-fillblanks__countdown"},[e("NeClockCountdown",{attrs:{options:t.clockCountdownOptions}}),e("NeCountdown",{attrs:{time:t.countDownTime,format:"mm:ss"},on:{complete:function(e){return t.$emit("countDownComplete")}}})],1),e("div",{staticClass:"ne-fillblanks__content",attrs:{id:"fillBlanksContent","talqs-app":""}}),t.isSubmit?t._e():e("button",{staticClass:"ne-fillblanks__submit",class:{"is-disabled":t.disableSubmit},on:{click:function(e){return e.stopPropagation(),t.submit.apply(null,arguments)}}},[e("span",[t._v(t._s(t.submitText))]),e("svg",{attrs:{width:"28px",height:"28px",viewBox:"0 0 28 28",version:"1.1"}},[e("defs",[e("linearGradient",{attrs:{x1:"50%",y1:"35.4886634%",x2:"50%",y2:"168.492388%",id:"linearGradient-1"}},[e("stop",{attrs:{"stop-color":"#FFFFFF",offset:"0%"}}),e("stop",{attrs:{"stop-color":"#FFEA5C",offset:"100%"}})],1),e("linearGradient",{attrs:{x1:"0%",y1:"79.5857988%",x2:"100%",y2:"20.4142012%",id:"linearGradient-2"}},[e("stop",{attrs:{"stop-color":"#FFD518",offset:"0%"}}),e("stop",{attrs:{"stop-color":"#FFAA0A",offset:"100%"}})],1)],1),e("g",{attrs:{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"}},[e("g",{attrs:{transform:"translate(-459.000000, -611.000000)"}},[e("g",{attrs:{transform:"translate(375.000000, 603.000000)"}},[e("g",{attrs:{transform:"translate(84.000000, 8.000000)"}},[e("circle",{attrs:{fill:"url(#linearGradient-1)",cx:"14",cy:"14",r:"12"}}),e("path",{attrs:{d:"M13.8095546,18.7004623 C13.4554695,19.0713185 12.8970984,19.0978082 12.5138296,18.7799315 L12.4286639,18.7004623 L8.28599183,14.361574 C7.90466939,13.9621904 7.90466939,13.3146616 8.28599183,12.9152779 L8.97643718,12.1921299 C9.35775962,11.7927463 9.97600545,11.7927463 10.3573279,12.1921299 L13.1191093,15.0847221 L18.6426721,9.29953773 C19.0239946,8.90015409 19.6422404,8.90015409 20.0235628,9.29953773 L20.7140082,10.0226858 C21.0953306,10.4220694 21.0953306,11.0695982 20.7140082,11.4689819 L13.8095546,18.7004623 Z",fill:"url(#linearGradient-2)"}})])])])])])]),t.showResultToast?e("div",{staticClass:"ne-fillblanks-result-mask"}):t._e(),t.showResultToast&&(t.judge.data||t.userAnswer["data"])?e("div",{staticClass:"ne-fillblanks__board",attrs:{id:"ne-fillblanks__board"}},[t._m(0),e("ne-carousel",{attrs:{height:"82px",autoplay:!1,loop:!1,arrow:"always",indicatorPosition:"none"}},t._l(t.answer["length"],(function(n){return e("ne-carousel-item",{key:n},[t.judge["data"][n-1]?e("ul",[t._l(t.judge["data"][n-1].length,(function(r){return[e("li",{key:"judge_"+r,class:1===t.judge["data"][n-1][r-1]?"correct":0===t.judge["data"][n-1][r-1]?"wrong":"nojoin"},[e("i"),e("span",{attrs:{id:"judge_"+n+r}},[t._v(t._s(t._f("renderMathJax")(t.userAnswer["data"][n-1][r-1])))])]),e("li",{key:"answer_"+r,domProps:{innerHTML:t._s(t.answer["data"][n-1][r-1][0])}})]}))],2):t._e()])})),1)],1):t._e()])},e.staticRenderFns=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"ne-fillblanks__board--title"},[e("span",[t._v("My answer")]),e("span",[t._v("Answer")])])}]},fdbc:function(t,e,n){"use strict";t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},fdbf:function(t,e,n){"use strict";var r=n("04f8");t.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},fe52:function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=r(n("c7eb")),o=r(n("1da1"));n("99af"),n("a9e3");var a=r(n("8ed6")),s=n("ca00"),c=r(n("4435")),u=r(n("62b9"));e.default={name:"NeGamecourseware",components:{NeDialog:u.default},props:{visible:{type:Boolean,default:!1},gameUrl:{type:String,default:""},nickName:{type:String,default:""},role:{type:String,default:"student"},lang:{type:String,default:"en"},isAnswer:{type:[Number,String],default:2},platform:{type:String,default:""},showResultToast:{type:Boolean,default:!1},rightCoin:{type:Number,default:0},locale:{type:Object,default:function(){}}},data:function(){return{loaderGameTimer:null,timeout:1e4,url:null,isLoaded:!1,showFailedDialog:!1,showExitDialog:!1,showTools:!0,showLoading:!0,rightRate:0,messageHandlerBinded:function(){},defaultLocale:{rightMsg:"Rewarding Coins For You!",errorMsg:"You are almost there",nojoinMsg:"Try it next time"}}},computed:{hiddenTools:function(){return!(0,s.isPC)()||this.platform&&("IOS"===this.platform.toUpperCase()||"ANDROID"===this.platform.toUpperCase())?"hiddenToolsBg":""}},created:function(){var t=this;this.url="".concat(this.gameUrl,"?lan=").concat(this.lang),this.removeMessageListener(),this.$nextTick((function(){t.gameCourseware=new c.default({nickName:t.nickName,isAnswer:1*t.isAnswer,locale:(0,a.default)(t.defaultLocale,t.locale)}),t.messageHandlerBinded=t.messageHandler.bind(t),t.addMessageListener()}))},mounted:function(){var t=this;this.$nextTick((function(){var e=document.getElementById("gameCourseware");e.onload=function(){t.gameLoaded()},t.loaderTimer()}))},destroyed:function(){this.removeMessageListener(),this.clearLoaderTimer()},methods:{loaderTimer:function(){var t=this;this.loaderGameTimer=window.setTimeout((function(){t.isLoaded||(t.showLoading=!1,t.showFailedDialog=!t.showFailedDialog,t.$emit("fail")),t.clearLoaderTimer()}),this.timeout)},clearLoaderTimer:function(){this.loaderGameTimer&&(clearTimeout(this.loaderGameTimer),this.loaderGameTimer=null)},addMessageListener:function(){window.addEventListener("message",this.messageHandlerBinded)},removeMessageListener:function(){window.removeEventListener("message",this.messageHandlerBinded)},messageHandler:function(t){var e=t.data.type;switch(e){case"game_load_start":console.log("Game Start Loading");break;case"game_start":console.log("Game Loaded");break;case"score":var n=t.data.scoreData,r=n.detail.resultDetail.rightNum,i=n.detail.resultDetail.questionSum;this.rightRate=this.gameCourseware.getRightRate(r,i);var o=n.detail.resultDetail.answerPics,a=n.judge,s={rightRate:this.rightRate,answerPics:o,judge:a};this.submitUserAnswer(s);break;case"game_over":console.log("Game Over");break;default:break}},submitUserAnswer:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};1!==this.isAnswer&&"student"===this.role&&this.$emit("submit",t)},gameLoaded:function(){var t=this;return(0,o.default)((0,i.default)().mark((function e(){return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.isLoaded=t.showTools=!0,t.showExitDialog=t.showLoading=!1,t.clearLoaderTimer(),t.$emit("loaded"),console.log("game load success");case 5:case"end":return e.stop()}}),e)})))()},reloadGamePage:function(){this.$emit("reload"),this.showLoading=!0,this.showFailedDialog=this.showExitDialog=!1;var t=+new Date;this.url=this.gameUrl&&"".concat(this.gameUrl,"?lan=").concat(this.lang,"&t=").concat(t),this.loaderTimer()},confirmExit:function(){this.showExitDialog=this.showLoading=this.showFailedDialog=this.showTools=!1,this.$emit("exit")}}}}})["default"]}));