<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <link
      rel="stylesheet"
      href="./libs/ui/neosui.min.css?t=<%= htmlWebpackPlugin.options.timestamp %>"
      type="text/css"
    />
    <link rel="stylesheet" href="./font/iconfont.css">
    <script src="./libs/libpag/libpag.min.js"></script>
    <!-- 内容云试题SDK {-->
    <link
      rel="stylesheet"
      href="./libs/talqs/index.min.css?t=<%= htmlWebpackPlugin.options.timestamp %>"
      type="text/css"
    />
    <script src="./libs/talqs/jquery.min.js"></script>
    <script src="./libs/talqs/index.min.js?t=<%= htmlWebpackPlugin.options.timestamp %>"></script>
    <!-- 内容云试题SDK }-->
    <script type="text/x-mathjax-config">
      MathJax.Hub.Register.StartupHook("TeX Jax Ready", function() {
      MathJax.InputJax.TeX.prefilterHooks.Add(function(data) { if (!data.display) { data.math="\\displaystyle{" +
      data.math + "}" } }); }); MathJax.Hub.Config({ jax: ["input/TeX", "output/SVG" ], extensions: ["tex2jax.js"], TeX: {
      extensions: ["AMSmath.js", "AMSsymbols.js" , "noErrors.js" , "noUndefined.js" ] }, skipStartupTypeset: true,
      showProcessingMessages: false, messageStyle: 'none' , tex2jax: { ignoreClass: 'ignore_math' , preview: "none" ,
      inlineMath: [["$", "$" ], ["\\", "\\" ] ]}, showMathMenu: false, displayAlign: "left" , "SVG" : { minScaleAdjust:
      100, linebreaks: { automatic: true, width: "container" } } });
    </script>
    <title>ThinkAcademy</title>
  </head>
  <body oncontextmenu="return false" onselectstart="return false">
    <script>
      document.addEventListener(
        'drop',
        function(event) {
          event.preventDefault() //禁止浏览器默认行为
          return false //禁止浏览器默认行为
        },
        false
      )
      document.addEventListener(
        'dragover',
        function(event) {
          event.preventDefault() //禁止浏览器默认行为
          return false //禁止浏览器默认行为
        },
        false
      )
      window.addEventListener('popstate', function(event) {
        window.location.reload()
      })
    </script>
    <div id="app"></div>
    <script src="./libs/vue/vue.min.js"></script>
    <script src="./libs/vue/vue-router.min.js"></script>
    <script src="./libs/vue/vuex.min.js"></script>
    <script src="./libs/sdk/chat.web.3.3.3.js"></script>
    <script src="./libs/sdk/hls.min.js?v=2.0.0"></script>
    <script src="./libs/ui/neosui.umd.min.js?t=<%= htmlWebpackPlugin.options.timestamp %>"></script>
    <script src="./libs/libpag/libpag.min.js"></script>
  </body>
</html>
