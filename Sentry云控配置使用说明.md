# Sentry 云控配置使用说明

## 📋 当前配置概览

### 云控配置内容

```json
{
  "enabled": true,
  "dsn": "https://<EMAIL>/4509625945948160",
  "sampleRate": 1,
  "attachStacktrace": false,
  "debug": false,
  "maxBreadcrumbs": 100,
  "maxValueLength": 250,
  "autoSessionTracking": false,
  "tracesSampleRate": 0.1,
  "profilesSampleRate": 0,
  "replaysSessionSampleRate": 0,
  "replaysOnErrorSampleRate": 0
}
```

### 默认配置（无云控时）

```javascript
{
  enabled: true,  // 非development环境默认启用
  dsn: 'https://<EMAIL>/4',  // 旧平台
  environment: process.env.VUE_APP_MODE,
  sampleRate: 1,
  attachStacktrace: false,
  sendDefaultPii: false,
  debug: false,
  maxBreadcrumbs: 100,
  maxValueLength: 250,
  autoSessionTracking: false,
  tracesSampleRate: 0.1,
  profilesSampleRate: 0,
  replaysSessionSampleRate: 0,
  replaysOnErrorSampleRate: 0
}
```

## 🎯 环境行为

### Development 环境

- **默认状态**: 强制禁用 (`enabled: false`)
- **云控影响**: 无论云控如何配置，development 环境都保持禁用
- **目标平台**: 无（不发送）

### Beta/Production 环境

- **无云控**: 启用，发送到旧平台 `sentry.thethinkacademy.com`
- **有云控**: 启用，发送到新平台 `o4509625935724546.ingest.us.sentry.io`

## 🔧 云控配置方法

### 1. 启用云控配置

```bash
curl -X POST "https://beta-one.thethinkacademy.com/api/smartline/v1/setCloudConfig" \
  -H "Content-Type: application/json" \
  -d '{
    "appCode": "1005",
    "deviceId": "ThinkAcademy",
    "configKey": "sentryConfig",
    "configValue": "{\"enabled\":true,\"dsn\":\"https://<EMAIL>/4509625945948160\",\"sampleRate\":1,\"debug\":false}",
    "configGrayHit": true
  }'
```

### 2. 禁用云控配置

```bash
curl -X POST "https://beta-one.thethinkacademy.com/api/smartline/v1/setCloudConfig" \
  -H "Content-Type: application/json" \
  -d '{
    "appCode": "1005",
    "deviceId": "ThinkAcademy",
    "configKey": "sentryConfig",
    "configValue": "{\"enabled\":false}",
    "configGrayHit": true
  }'
```

### 3. 删除云控配置

```bash
curl -X POST "https://beta-one.thethinkacademy.com/api/smartline/v1/setCloudConfig" \
  -H "Content-Type: application/json" \
  -d '{
    "appCode": "1005",
    "deviceId": "ThinkAcademy",
    "configKey": "sentryConfig",
    "configGrayHit": false
  }'
```

## 📊 配置优先级

1. **Development 环境**: 强制禁用（最高优先级）
2. **云控配置**: 如果存在且`configGrayHit: true`
3. **默认配置**: 启用旧平台

## 🔍 调试方法

### 查看当前配置状态

应用启动时会在日志中显示：

```
development环境：使用缓存配置并强制禁用Sentry
使用的DSN: https://...
```

### 测试错误上报

```javascript
// 主进程测试
Sentry.captureMessage('测试消息', 'error')
Sentry.captureException(new Error('测试错误'))

// 渲染进程测试
window.$Sentry.captureMessage('测试消息', 'error')
window.$Sentry.captureException(new Error('测试错误'))
```

## 📝 配置说明

### 关键参数

- **enabled**: 是否启用 Sentry 上报
- **dsn**: Sentry 项目的 DSN 地址
- **sampleRate**: 采样率 (0-1)，1 表示 100%采样
- **debug**: 是否开启调试模式（建议生产环境关闭）
- **tracesSampleRate**: 性能监控采样率
- **replaysSessionSampleRate**: 会话回放采样率

### 平台对应

- **旧平台**: `sentry.thethinkacademy.com` - 内部 Sentry 服务
- **新平台**: `o4509625935724546.ingest.us.sentry.io` - Sentry.io 官方服务

## 🚨 注意事项

1. **Development 环境隔离**: 无论如何配置，development 环境都不会发送错误到 Sentry
2. **配置生效时间**: 云控配置会在应用重启后生效
3. **缓存机制**: 应用会缓存云控配置，即使网络断开也能工作
4. **进程架构**: 渲染进程错误会自动转发到主进程统一处理

## 🔄 常用操作

### 临时启用新平台（测试用）

```bash
# 启用新平台
curl -X POST "https://beta-one.thethinkacademy.com/api/smartline/v1/setCloudConfig" \
  -H "Content-Type: application/json" \
  -d '{
    "appCode": "1005",
    "deviceId": "ThinkAcademy",
    "configKey": "sentryConfig",
    "configValue": "{\"enabled\":true,\"dsn\":\"https://<EMAIL>/4509625945948160\"}",
    "configGrayHit": true
  }'

# 测试完毕后恢复旧平台
curl -X POST "https://beta-one.thethinkacademy.com/api/smartline/v1/setCloudConfig" \
  -H "Content-Type: application/json" \
  -d '{
    "appCode": "1005",
    "deviceId": "ThinkAcademy",
    "configKey": "sentryConfig",
    "configGrayHit": false
  }'
```

### 紧急禁用所有错误上报

```bash
curl -X POST "https://beta-one.thethinkacademy.com/api/smartline/v1/setCloudConfig" \
  -H "Content-Type: application/json" \
  -d '{
    "appCode": "1005",
    "deviceId": "ThinkAcademy",
    "configKey": "sentryConfig",
    "configValue": "{\"enabled\":false}",
    "configGrayHit": true
  }'
```

## 🏗️ 系统架构

### 配置管理流程

```
应用启动 → 主进程获取云控配置 → 缓存到electron-store → 初始化Sentry
           ↓ 失败
         使用缓存配置 → 初始化Sentry
           ↓ 失败
         使用默认配置 → 初始化Sentry
```

### 存储方式

- **主进程**: 使用 `electron-store` 存储配置（名为'sentry-config'）
- **渲染进程**: 通过主进程获取配置，无独立存储

### 错误处理流程

```
渲染进程错误 → 自动转发到主进程 → 统一过滤和发送
```

---

📅 **更新时间**: 2025-01-07
📝 **维护者**: Tal
🔗 **相关文档**: [Sentry 官方文档](https://docs.sentry.io/)
