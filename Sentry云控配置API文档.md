# Sentry 云控配置 API 文档

## 🌐 云控 API 接口

### 基础信息

- **域名**: `https://beta-one.thethinkacademy.com`
- **接口**: `/api/smartline/v1/setCloudConfig`
- **方法**: POST
- **Content-Type**: application/json

### 应用参数

- **appCode**: `"1005"`
- **deviceId**: `"ThinkAcademy"`
- **configKey**: `"sentryConfig"`

## 📋 当前 Sentry 配置

### 云控配置内容

```json
{
  "enabled": true,
  "dsn": "https://<EMAIL>/4509625945948160",
  "sampleRate": 1,
  "attachStacktrace": false,
  "debug": false,
  "maxBreadcrumbs": 100,
  "maxValueLength": 250,
  "autoSessionTracking": false,
  "tracesSampleRate": 0.1,
  "profilesSampleRate": 0,
  "replaysSessionSampleRate": 0,
  "replaysOnErrorSampleRate": 0
}
```

### 默认配置（无云控时）

```javascript
{
  enabled: true,  // 非development环境默认启用
  dsn: 'https://<EMAIL>/4',  // 旧平台
  environment: process.env.VUE_APP_MODE,
  sampleRate: 1,
  attachStacktrace: false,
  sendDefaultPii: false,
  debug: false,
  maxBreadcrumbs: 100,
  maxValueLength: 250,
  autoSessionTracking: false,
  tracesSampleRate: 0.1,
  profilesSampleRate: 0,
  replaysSessionSampleRate: 0,
  replaysOnErrorSampleRate: 0
}
```

## 🎯 环境行为规则

### Development 环境

- ✅ **强制禁用**: 无论云控如何配置都不发送
- 🎯 **目标**: 避免开发时产生噪音数据

### Beta/Production 环境

- ❌ **无云控**: 发送到旧平台 `sentry.thethinkacademy.com`
- ✅ **有云控**: 发送到新平台 `o4509625935724546.ingest.us.sentry.io`

## 🔧 API 使用方法

### 1️⃣ 启用新平台

```bash
curl -X POST "https://beta-one.thethinkacademy.com/api/smartline/v1/setCloudConfig" \
  -H "Content-Type: application/json" \
  -d '{
    "appCode": "1005",
    "deviceId": "ThinkAcademy",
    "configKey": "sentryConfig",
    "configValue": "{\"enabled\":true,\"dsn\":\"https://<EMAIL>/4509625945948160\",\"sampleRate\":1,\"debug\":false}",
    "configGrayHit": true
  }'
```

### 2️⃣ 禁用错误上报

```bash
curl -X POST "https://beta-one.thethinkacademy.com/api/smartline/v1/setCloudConfig" \
  -H "Content-Type: application/json" \
  -d '{
    "appCode": "1005",
    "deviceId": "ThinkAcademy",
    "configKey": "sentryConfig",
    "configValue": "{\"enabled\":false}",
    "configGrayHit": true
  }'
```

### 3️⃣ 恢复旧平台（删除云控）

```bash
curl -X POST "https://beta-one.thethinkacademy.com/api/smartline/v1/setCloudConfig" \
  -H "Content-Type: application/json" \
  -d '{
    "appCode": "1005",
    "deviceId": "ThinkAcademy",
    "configKey": "sentryConfig",
    "configGrayHit": false
  }'
```

## 🔍 配置验证

### 查看应用日志

应用启动时会显示当前配置：

```
使用缓存Sentry配置 (环境: beta, 配置启用: true)
使用的DSN: https://492c774426d48e457840d55b8d009543@...
```

### 手动测试错误上报

```javascript
// 测试消息
Sentry.captureMessage('云控配置测试', 'error')

// 测试异常
Sentry.captureException(new Error('云控配置测试异常'))
```

## 📊 配置优先级

1. **Development 环境** → 强制禁用（最高优先级）
2. **云控配置** → 如果存在且 `configGrayHit: true`
3. **默认配置** → 启用旧平台

## 📝 配置说明

### 关键参数

- **enabled**: 是否启用 Sentry 上报
- **dsn**: Sentry 项目的 DSN 地址
- **sampleRate**: 采样率 (0-1)，1 表示 100%采样
- **debug**: 是否开启调试模式（建议生产环境关闭）
- **tracesSampleRate**: 性能监控采样率
- **replaysSessionSampleRate**: 会话回放采样率

### 平台对应

- **旧平台**: `sentry.thethinkacademy.com` - 内部 Sentry 服务
- **新平台**: `o4509625935724546.ingest.us.sentry.io` - Sentry.io 官方服务

## 🚨 注意事项

- ⏰ **生效时间**: 应用重启后生效
- 💾 **缓存机制**: 配置会本地缓存，网络异常时使用缓存
- 🔄 **进程同步**: 渲染进程自动使用主进程配置
- 🛡️ **Development 保护**: 开发环境永远不会发送到生产 Sentry

## 🔄 常用操作

### 临时启用新平台（测试用）

```bash
# 启用新平台
curl -X POST "https://beta-one.thethinkacademy.com/api/smartline/v1/setCloudConfig" \
  -H "Content-Type: application/json" \
  -d '{
    "appCode": "1005",
    "deviceId": "ThinkAcademy",
    "configKey": "sentryConfig",
    "configValue": "{\"enabled\":true,\"dsn\":\"https://<EMAIL>/4509625945948160\"}",
    "configGrayHit": true
  }'

# 测试完毕后恢复旧平台
curl -X POST "https://beta-one.thethinkacademy.com/api/smartline/v1/setCloudConfig" \
  -H "Content-Type: application/json" \
  -d '{
    "appCode": "1005",
    "deviceId": "ThinkAcademy",
    "configKey": "sentryConfig",
    "configGrayHit": false
  }'
```

### 紧急禁用所有错误上报

```bash
curl -X POST "https://beta-one.thethinkacademy.com/api/smartline/v1/setCloudConfig" \
  -H "Content-Type: application/json" \
  -d '{
    "appCode": "1005",
    "deviceId": "ThinkAcademy",
    "configKey": "sentryConfig",
    "configValue": "{\"enabled\":false}",
    "configGrayHit": true
  }'
```

## ✨ 特性总结

通过 Sentry 云控配置系统，我们实现了：

- **🚀 快速启动**: 缓存优先策略，不阻塞应用启动
- **🔒 环境隔离**: Development 环境完全隔离，避免噪音数据
- **🎯 灵活切换**: 随时在新旧平台间切换
- **💾 离线可用**: 网络异常时使用本地缓存
- **🔄 实时控制**: 无需发版即可调整配置

---

📅 文档更新: 2025-01-07  
👤 维护者: Tal
