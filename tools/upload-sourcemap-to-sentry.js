const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

class SourcemapUploader {
  constructor() {
    this.sourcemapDir = './dist/bundled/static/js'
    this.commitHash = this.getCommitHash()
    this.env = process.argv[2] || 'prod'
  }

  getCommitHash() {
    try {
      return execSync('git rev-parse HEAD')
        .toString()
        .trim()
    } catch (error) {
      console.error('❌ 获取 commit hash 失败:', error.message)
      process.exit(1)
    }
  }

  async uploadSourcemaps() {
    try {
      console.log(`🚀 开始上传 sourcemap 到 Sentry (${this.env} 环境)`)
      console.log(`📝 Commit Hash: ${this.commitHash}`)

      // 检查 sourcemap 目录
      if (!fs.existsSync(this.sourcemapDir)) {
        throw new Error(`Sourcemap 目录不存在: ${this.sourcemapDir}`)
      }

      // 获取所有 .js.map 文件
      // 获取所有需要上传的文件（JS文件和sourcemap文件）
      const allFiles = fs.readdirSync(this.sourcemapDir)
      const mapFiles = allFiles
        .filter(file => file.endsWith('.js.map'))
        .map(file => path.join(this.sourcemapDir, file))

      const jsFiles = allFiles
        .filter(file => file.endsWith('.js') && !file.endsWith('.js.map'))
        .map(file => path.join(this.sourcemapDir, file))

      const uploadFiles = [...jsFiles, ...mapFiles]
      if (uploadFiles.length === 0) {
        throw new Error('未找到需要上传的文件')
      }

      console.log(`📁 找到 ${jsFiles.length} 个 JS 文件和 ${mapFiles.length} 个 sourcemap 文件`)

      // 只上传到 Sentry
      await this.uploadToSentry(uploadFiles)

      console.log(`🎉 Sourcemap 上传到 Sentry 完成!`)
      console.log(`📍 Release: ${this.commitHash}`)

      // 删除本地 sourcemap（只删除.map文件，保留JS文件）
      this.cleanupLocalSourcemaps(mapFiles)
    } catch (error) {
      console.error('❌ 上传失败:', error.message)
      process.exit(1)
    }
  }

  async uploadToSentry(uploadFiles) {
    console.log('📤 开始批量上传到 Sentry...')

    try {
      // 创建 release
      await this.createSentryRelease()

      // 批量上传所有文件（JS + sourcemap）
      await this.uploadSentrySourcemapsBatch(uploadFiles)

      // 完成 release
      await this.finalizeSentryRelease()

      console.log(`🎉 Sentry 批量上传完成`)
    } catch (error) {
      throw new Error(`Sentry 上传失败: ${error.message}`)
    }
  }

  async createSentryRelease() {
    console.log(`📝 创建 Sentry release: ${this.commitHash}`)

    const command = `npx @sentry/cli releases new ${this.commitHash}`

    const env = {
      ...process.env
      // 使用 .sentryclirc 配置文件
    }

    try {
      execSync(command, { stdio: 'pipe', env })
      console.log(`✅ Sentry release 创建成功`)
    } catch (error) {
      // 如果 release 已存在，忽略错误
      if (error.message.includes('already exists')) {
        console.log(`ℹ️  Sentry release 已存在`)
      } else {
        throw error
      }
    }
  }

  async uploadSentrySourcemapsBatch(uploadFiles) {
    console.log(`📤 批量上传 ${uploadFiles.length} 个文件到 Sentry...`)

    // 构建 URL 路径，对应 Sentry 中看到的路径格式
    const urlPrefix = `app:///static/js/`

    // 批量上传命令 - 上传整个目录
    const command =
      `npx @sentry/cli releases files ${this.commitHash} upload-sourcemaps ` +
      `--url-prefix "${urlPrefix}" ` +
      `--validate ` +
      `"${this.sourcemapDir}"`

    const env = {
      ...process.env
      // 使用 .sentryclirc 配置文件
    }

    try {
      const output = execSync(command, { stdio: 'pipe', env, encoding: 'utf8' })
      console.log(`✅ Sentry 批量上传成功`)
      console.log(output)
    } catch (error) {
      console.error(`❌ Sentry 批量上传失败:`, error.message)
      if (error.stdout) console.log('stdout:', error.stdout)
      if (error.stderr) console.log('stderr:', error.stderr)
      throw error
    }
  }

  async uploadSentrySourcemap(filePath) {
    const fileName = path.basename(filePath)

    // 构建 URL 路径，对应 Sentry 中看到的路径格式
    const urlPrefix = `app:///static/js/`

    console.log(`📤 Sentry: ${fileName}`)

    const command =
      `npx @sentry/cli releases files ${this.commitHash} upload-sourcemaps ` +
      `--url-prefix "${urlPrefix}" ` +
      `"${filePath}"`

    const env = {
      ...process.env
      // 使用 .sentryclirc 配置文件
    }

    try {
      execSync(command, { stdio: 'pipe', env })
      console.log(`✅ Sentry: ${fileName}`)
    } catch (error) {
      console.warn(`⚠️  Sentry sourcemap 上传失败: ${fileName} - ${error.message}`)
      // 不抛出错误，继续上传其他文件
    }
  }

  async finalizeSentryRelease() {
    console.log(`🏁 完成 Sentry release`)

    const command = `npx @sentry/cli releases finalize ${this.commitHash}`

    const env = {
      ...process.env
      // 使用 .sentryclirc 配置文件
    }

    try {
      execSync(command, { stdio: 'pipe', env })
      console.log(`✅ Sentry release 已完成`)
    } catch (error) {
      console.warn(`⚠️  Sentry release 完成失败: ${error.message}`)
    }
  }

  cleanupLocalSourcemaps(mapFiles) {
    console.log('🧹 删除本地 sourcemap 文件...')
    mapFiles.forEach(filePath => {
      try {
        fs.unlinkSync(filePath)
        console.log(`🗑️  删除: ${path.basename(filePath)}`)
      } catch (error) {
        console.warn(`⚠️  删除失败: ${filePath}`, error.message)
      }
    })
    console.log('✅ 本地 sourcemap 清理完成')
  }
}

// 检查环境参数
const allowedEnvs = ['prod', 'beta', 'preprod']
const env = process.argv[2]

if (!env || !allowedEnvs.includes(env)) {
  console.error('❌ 请指定正确的环境: prod, beta, preprod')
  console.log('用法: node tools/upload-sourcemap-to-s3.js <env>')
  process.exit(1)
}

// 执行上传
const uploader = new SourcemapUploader()
uploader.uploadSourcemaps()
