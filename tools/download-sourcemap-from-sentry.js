const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

class SentrySourcemapDownloader {
  constructor() {
    this.sentryConfig = {
      url: 'https://sea-sentry.thinkbuddy.com',
      org: 'sentry',
      project: 'student-client',
      authToken: '4ed723dfbfd64622bfc23a17f2c09038642eaba76ebf48ab8375e49bee6d0a05'
    }

    this.releaseVersion = process.argv[2]
    this.outputDir = process.argv[3] || './downloaded-sourcemaps'
  }

  /**
   * 获取release中的所有文件
   */
  async getReleaseFiles() {
    console.log(`📋 获取 release ${this.releaseVersion} 的文件列表`)

    const command = `npx @sentry/cli releases files ${this.releaseVersion} list`

    const env = {
      ...process.env
      // 使用 .sentryclirc 配置文件
    }

    try {
      const output = execSync(command, { stdio: 'pipe', env, encoding: 'utf8' })
      console.log('✅ 文件列表获取成功')
      return output
    } catch (error) {
      console.error('❌ 获取文件列表失败:', error.message)
      throw error
    }
  }

  /**
   * 使用API下载sourcemap文件
   */
  async downloadSourcemaps() {
    try {
      console.log(`📥 开始下载 release ${this.releaseVersion} 的 sourcemap 文件`)

      if (!this.releaseVersion) {
        throw new Error('请提供 release 版本号')
      }

      // 创建输出目录
      if (!fs.existsSync(this.outputDir)) {
        fs.mkdirSync(this.outputDir, { recursive: true })
        console.log(`📁 创建输出目录: ${this.outputDir}`)
      }

      // 获取文件列表
      const filesList = await this.getReleaseFiles()

      // 解析文件列表，查找 .js.map 文件
      const lines = filesList.split('\n')
      const sourcemapFiles = []

      for (const line of lines) {
        if (line.includes('.js.map')) {
          // 从表格格式中提取文件名
          const parts = line.split('|').map(part => part.trim())
          if (parts.length > 1) {
            const fileName = parts[1]
            if (fileName && fileName.endsWith('.js.map')) {
              sourcemapFiles.push(fileName)
            }
          }
        }
      }

      if (sourcemapFiles.length === 0) {
        console.warn('⚠️  未找到 sourcemap 文件')
        return
      }

      console.log(`📁 找到 ${sourcemapFiles.length} 个 sourcemap 文件:`)
      sourcemapFiles.forEach(file => console.log(`  - ${file}`))

      // 使用 sentry-cli 下载文件
      for (const fileName of sourcemapFiles) {
        await this.downloadFile(fileName)
      }

      console.log(`🎉 下载完成，文件保存在: ${this.outputDir}`)
    } catch (error) {
      console.error('❌ 下载失败:', error.message)
      process.exit(1)
    }
  }

  /**
   * 下载单个文件
   */
  async downloadFile(fileName) {
    console.log(`📤 下载: ${fileName}`)

    try {
      // 使用 HTTP API 获取文件列表
      const apiUrl = `${this.sentryConfig.url}/api/0/projects/${this.sentryConfig.org}/${this.sentryConfig.project}/releases/${this.releaseVersion}/files/`

      const listCommand = `curl -s -H "Authorization: Bearer ${this.sentryConfig.authToken}" "${apiUrl}"`
      const listOutput = execSync(listCommand, { encoding: 'utf8' })
      const files = JSON.parse(listOutput)

      // 查找匹配的文件
      const file = files.find(f => f.name === fileName)
      if (!file) {
        throw new Error(`文件 ${fileName} 未找到`)
      }

      // 获取文件详细信息
      const fileApiUrl = `${this.sentryConfig.url}/api/0/projects/${this.sentryConfig.org}/${this.sentryConfig.project}/releases/${this.releaseVersion}/files/${file.id}/`
      const fileCommand = `curl -s -H "Authorization: Bearer ${this.sentryConfig.authToken}" "${fileApiUrl}"`
      const fileOutput = execSync(fileCommand, { encoding: 'utf8' })
      const fileInfo = JSON.parse(fileOutput)

      // 尝试直接下载文件内容
      const downloadApiUrl = `${this.sentryConfig.url}/api/0/projects/${this.sentryConfig.org}/${this.sentryConfig.project}/releases/${this.releaseVersion}/files/${file.id}/?download=1`
      const downloadCommand = `curl -s -H "Authorization: Bearer ${this.sentryConfig.authToken}" "${downloadApiUrl}"`
      const content = execSync(downloadCommand, { encoding: 'utf8' })

      // 检查是否是JSON错误响应
      try {
        const errorCheck = JSON.parse(content)
        if (errorCheck.error || errorCheck.detail) {
          throw new Error(`API错误: ${errorCheck.detail || errorCheck.error}`)
        }
      } catch (parseError) {
        // 如果不能解析为JSON，说明是文件内容，这是正常的
      }

      // 保存文件
      const outputPath = path.join(this.outputDir, path.basename(fileName))
      fs.writeFileSync(outputPath, content)

      console.log(`✅ 下载完成: ${outputPath} (${content.length} 字节)`)
    } catch (error) {
      console.error(`❌ 下载文件 ${fileName} 失败:`, error.message)
    }
  }

  /**
   * 显示使用帮助
   */
  static showHelp() {
    console.log(`
📖 Sentry Sourcemap 下载工具

用法:
  node tools/download-sourcemap-from-sentry.js <release_version> [output_dir]

参数:
  release_version  - Sentry release 版本号 (必需)
  output_dir      - 输出目录 (可选，默认: ./downloaded-sourcemaps)

示例:
  node tools/download-sourcemap-from-sentry.js 46a0cee57c9ad45236fb603b22d7d187ba9ba742
  node tools/download-sourcemap-from-sentry.js 46a0cee57c9ad45236fb603b22d7d187ba9ba742 ./my-sourcemaps

注意:
  - 需要在项目根目录下有 .sentryclirc 配置文件
  - 或者设置相应的环境变量
`)
  }
}

// 执行下载
if (require.main === module) {
  const downloader = new SentrySourcemapDownloader()

  if (!downloader.releaseVersion) {
    SentrySourcemapDownloader.showHelp()
    process.exit(1)
  }

  downloader.downloadSourcemaps()
}
