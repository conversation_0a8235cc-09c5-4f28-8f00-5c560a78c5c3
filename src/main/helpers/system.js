const path = require('path')
const os = require('os')
const fs = require('fs-extra')
const { getLogger } = require('./loggerManager')
const { app, globalShortcut, powerSaveBlocker, powerMonitor } = require('electron')
const child_process = require('child_process')
const logger = getLogger('SystemHelper')

class SystemHelper {
  constructor() {
    this.powerID = null
    this.appName = app.getName()
  }
  // 字节单位转换
  static bytesToSize(bytes) {
    if (bytes === 0) return '0 B'
    let k = 1024
    let sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
    let i = Math.floor(Math.log(bytes) / Math.log(k))
    return Number((bytes / Math.pow(k, i)).toPrecision(3)) + sizes[i]
  }

  // 获取系统内存
  getSystemMem() {
    const totalMem = os.totalmem()
    return {
      mem: SystemHelper.bytesToSize(totalMem)
    }
  }

  setAppPath() {
    const userDataPath = global.APP_CLIENT_INFO.APP_DIR
    const tempPath = path.join(app.getPath('temp'), this.appName)
    !fs.existsSync(userDataPath) && fs.mkdirSync(userDataPath)
    !fs.existsSync(tempPath) && fs.mkdirSync(tempPath)
    app.setPath('userData', userDataPath) // 设置用户目录
    app.setPath('temp', tempPath) // 设置缓存目录
  }

  setAppVersion() {
    app.setAboutPanelOptions({
      applicationVersion: app.getVersion(),
      version: app.getVersion()
    })
  }
  // 设置UserAgent
  setUserAgent() {
    const env = global.APP_CLIENT_INFO.ENV
    const platform = global.APP_CLIENT_INFO.PLATFORM
    let ua = app.userAgentFallback
    app.userAgentFallback = ua + ` ${this.appName}/${env} ${this.appName}/${platform}`
  }

  shortcutUnregisterAll() {
    globalShortcut.unregisterAll()
  }

  powerSaveBlocker() {
    const id = powerSaveBlocker.start('prevent-display-sleep')
    this.powerID = id
  }
  systemRelease() {
    this.powerID && powerSaveBlocker.stop(this.powerID)
  }
  ignoreCertificateErrors() {
    app.commandLine.appendSwitch('--ignore-certificate-errors', 'true')
    // app.commandLine.appendSwitch('--ignore-certificate-errors')
    // app.commandLine.appendSwitch('--disable-http-cache')
  }

  killProcess(processName) {
    if (os.platform() === 'win32') {
      return child_process.execSync('taskkill /f /im ' + processName + '.exe')
    } else if (os.platform() === 'darwin') {
      return child_process.execSync('pkill -9 ' + processName)
    } else {
      console.warn(`killProcess on ${os.platform} is not supported!`)
    }
    return ''
  }

  handlePowerMonitor() {
    // powerMonitor 监听事件
    powerMonitor.on('suspend', () => {
      logger.error('suspend 当系统即将进入睡眠模式时触发。')
    })
    powerMonitor.on('resume', () => {
      logger.error('resume 当系统从睡眠状态唤醒时触发')
    })
    powerMonitor.on('on-ac', () => {
      logger.error('on-ac 当系统从电池供电切换到交流电源供电时触发。')
    })
    powerMonitor.on('on-battery', () => {
      logger.error('on-battery 当系统从交流电源供电切换到电池供电时触发。')
    })
    powerMonitor.on('shutdown', () => {
      logger.error('shutdown 当系统即将关闭时触发')
    })
    powerMonitor.on('lock-screen', () => {
      logger.error('lock-screen 当系统锁定屏幕时触发')
    })
    powerMonitor.on('unlock-screen', () => {
      logger.error('unlock-screen 当系统解锁屏幕时触发')
    })
    powerMonitor.on('session-end', () => {
      logger.error('session-end 当用户注销时触发')
    })
    powerMonitor.on('session-start', () => {
      logger.error('session-start 当用户登录时触发')
    })
    powerMonitor.on('user-did-change', () => {
      logger.error('user-did-change 当用户登录或注销时触发')
    })
    powerMonitor.on('idle', () => {
      logger.error('idle 当系统处于空闲状态时触发')
    })
    powerMonitor.on('active', () => {
      logger.error('active 当系统从空闲状态唤醒时触发')
    })
    powerMonitor.on('user-active', () => {
      logger.error('user-active 当用户在系统上活动时触发')
    })
    powerMonitor.on('brightness-changed', () => {
      logger.error('brightness-changed 当屏幕亮度发生变化时触发')
    })
    powerMonitor.on('suspend-to-ram', () => {
      logger.error('suspend-to-ram 当系统即将进入睡眠模式并将内存保存到RAM中时触发')
    })
    powerMonitor.on('suspend-to-disk', () => {
      logger.error('suspend-to-disk 当系统即将进入睡眠模式并将内存保存到磁盘中时触发')
    })
    powerMonitor.on('resume-from-ram', () => {
      logger.error('resume-from-ram 当系统从睡眠模式中恢复并从RAM中恢复内存时触发')
    })
    powerMonitor.on('resume-from-disk', () => {
      logger.error('resume-from-disk 当系统从睡眠模式中恢复并从磁盘中恢复内存时触发')
    })
  }
}

module.exports = new SystemHelper()
