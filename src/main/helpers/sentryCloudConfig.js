// 主进程Sentry云控配置（electron-store版本）
const { net, app } = require('electron')
const Store = require('electron-store')
const path = require('path')
const fs = require('fs')
const { getLogger } = require('./loggerManager')

class MainSentryCloudConfig {
  constructor() {
    this.configKey = 'sentryConfig'
    this.baseUrl = process.env.VUE_APP_URL_ONE || 'http://beta-one.thethinkacademy.com'

    // 初始化日志记录器
    this.logger = getLogger('SentryCloudConfig')

    try {
      // 使用 electron-store 管理配置缓存
      this.store = new Store({
        name: 'sentry-config',
        defaults: {
          config: null
        }
      })

      // 添加详细的调试信息
      this.logger.info('=== Sentry配置调试信息 ===')
      this.logger.info('baseUrl:', this.baseUrl)
      this.logger.info('VUE_APP_URL_ONE:', process.env.VUE_APP_URL_ONE)
      this.logger.info('app.isPackaged:', app.isPackaged)
      this.logger.info('app.getPath(userData):', app.getPath('userData'))
      this.logger.info('store.path:', this.store.path)

      // 检查配置文件是否存在
      const configExists = fs.existsSync(this.store.path)
      this.logger.info('配置文件是否存在:', configExists)

      if (configExists) {
        const stats = fs.statSync(this.store.path)
        this.logger.info('配置文件大小:', stats.size, 'bytes')
        this.logger.info('配置文件修改时间:', stats.mtime)

        // 读取原始文件内容
        try {
          const rawContent = fs.readFileSync(this.store.path, 'utf8')
          this.logger.info('配置文件原始内容:', rawContent)
        } catch (readError) {
          this.logger.warn('读取配置文件失败:', readError.message)
        }
      }

      this.logger.info('Sentry配置使用 electron-store 管理')
    } catch (error) {
      this.logger.error('初始化 electron-store 失败:', error)
      this.store = null
    }
  }

  // 同步读取缓存配置
  getCachedConfig() {
    try {
      this.logger.info('=== 开始读取缓存配置 ===')

      if (!this.store) {
        this.logger.warn('store 未初始化，无法读取缓存')
        return null
      }

      this.logger.info('store.path:', this.store.path)
      this.logger.info('store.size:', this.store.size)

      // 获取所有存储的数据
      const allData = this.store.store
      this.logger.info('store 中所有数据:', JSON.stringify(allData, null, 2))

      const config = this.store.get('config', null)
      this.logger.info('读取到的 config 值:', config)

      if (config) {
        this.logger.info('使用缓存的Sentry配置:', JSON.stringify(config, null, 2))
        return config
      } else {
        this.logger.info('无Sentry配置缓存')
        return null
      }
    } catch (error) {
      this.logger.error('读取Sentry配置缓存失败:', error.message)
      this.logger.error('错误堆栈:', error.stack)
      return null
    }
  }

  // 保存配置到缓存
  saveCachedConfig(config) {
    try {
      this.logger.info('=== 开始保存配置到缓存 ===')

      if (!this.store) {
        this.logger.warn('store 未初始化，无法保存缓存')
        return
      }

      this.logger.info('准备保存的配置:', JSON.stringify(config, null, 2))
      this.logger.info('store.path:', this.store.path)

      // 保存前检查文件状态
      const beforeExists = fs.existsSync(this.store.path)
      this.logger.info('保存前文件是否存在:', beforeExists)

      this.store.set('config', config)

      // 保存后检查文件状态
      const afterExists = fs.existsSync(this.store.path)
      this.logger.info('保存后文件是否存在:', afterExists)

      if (afterExists) {
        const stats = fs.statSync(this.store.path)
        this.logger.info('保存后文件大小:', stats.size, 'bytes')

        // 验证保存的内容
        const savedConfig = this.store.get('config', null)
        this.logger.info('验证保存的配置:', JSON.stringify(savedConfig, null, 2))
      }

      this.logger.info('Sentry配置已保存到缓存')
    } catch (error) {
      this.logger.error('保存Sentry配置缓存失败:', error.message)
      this.logger.error('错误堆栈:', error.stack)
    }
  }

  // 从云端获取Sentry配置
  async fetchConfig() {
    return new Promise((resolve, reject) => {
      try {
        this.logger.info('=== 开始从云端获取Sentry配置 ===')

        const requestBody = JSON.stringify({
          appCode: '1005',
          deviceId: 'ThinkAcademy'
        })

        const url = `${this.baseUrl}/api/smartline/v1/getCloudConfigs`
        this.logger.info('请求URL:', url)
        this.logger.info('请求体:', requestBody)

        const request = net.request({
          method: 'POST',
          url: url,
          headers: {
            'Content-Type': 'application/json'
          }
        })

        // 设置超时
        const timeout = setTimeout(() => {
          this.logger.warn('网络请求超时，中止请求')
          request.abort()
          reject(new Error('请求超时'))
        }, 8000)
        let responseData = ''

        request.on('response', response => {
          this.logger.info('收到响应，状态码:', response.statusCode)
          this.logger.info('响应头:', response.headers)

          if (response.statusCode !== 200) {
            clearTimeout(timeout)
            this.logger.error('HTTP状态码错误:', response.statusCode)
            reject(new Error(`HTTP ${response.statusCode}`))
            return
          }

          response.on('data', chunk => {
            responseData += chunk
            this.logger.info('收到数据块，当前总长度:', responseData.length)
          })

          response.on('end', () => {
            clearTimeout(timeout)
            this.logger.info('响应接收完成，总数据长度:', responseData.length)
            this.logger.info('原始响应数据:', responseData)

            try {
              const result = JSON.parse(responseData)
              this.logger.info('解析后的响应:', JSON.stringify(result, null, 2))

              if (!result || result.code !== 0) {
                this.logger.error('云控API返回错误:', result)
                reject(new Error(`云控API错误: code=${result && result.code}`))
                return
              }

              // 查找Sentry配置
              const configs = (result.data && result.data.configs) || []
              this.logger.info('云控配置数量:', configs.length)

              for (let i = 0; i < configs.length; i++) {
                const configItem = configs[i]

                if (configItem.configKey === this.configKey && configItem.configGrayHit) {
                  try {
                    this.logger.info('找到匹配的Sentry配置，开始解析...')
                    const sentryConfig = JSON.parse(configItem.configValue)
                    this.logger.info('解析成功的Sentry配置:', JSON.stringify(sentryConfig, null, 2))
                    resolve(sentryConfig)
                    return
                  } catch (parseError) {
                    this.logger.error('解析Sentry配置失败:', parseError)
                  }
                }
              }

              this.logger.warn('未找到匹配的Sentry配置')
              reject(new Error('云端无Sentry配置'))
            } catch (error) {
              this.logger.error('解析响应JSON失败:', error.message)
              reject(new Error(`解析响应失败: ${error.message}`))
            }
          })
        })

        request.on('error', error => {
          clearTimeout(timeout)
          this.logger.error('网络请求错误:', error)
          reject(error)
        })

        request.write(requestBody)
        request.end()
      } catch (error) {
        this.logger.error('fetchConfig 异常:', error)
        reject(error)
      }
    })
  }

  // 获取默认配置
  getDefaultConfig() {
    const environment = process.env.VUE_APP_MODE || 'development'

    return {
      // 默认启用状态（非development环境下默认开启）
      enabled: true,
      dsn: 'https://<EMAIL>//119',
      environment: environment,
      release: process.env.VUE_APP_RELEASE_VERSION,
      sampleRate: 1,
      attachStacktrace: false,
      sendDefaultPii: false,
      debug: false, // 生产环境关闭调试模式
      maxBreadcrumbs: 100,
      maxValueLength: 250,
      autoSessionTracking: false,
      tracesSampleRate: 0.1,
      profilesSampleRate: 0,
      replaysSessionSampleRate: 0,
      replaysOnErrorSampleRate: 0
    }
  }

  // 同步获取配置（优先缓存，然后默认配置）
  getConfigSync() {
    const cachedConfig = this.getCachedConfig()
    const defaultConfig = this.getDefaultConfig()
    const environment = process.env.VUE_APP_MODE || 'development'

    // development环境临时开启（测试用）
    if (environment === 'development') {
      // 如果有缓存配置，使用缓存配置但强制启用
      if (cachedConfig) {
        const devConfig = { ...defaultConfig, ...cachedConfig, enabled: false }
        this.logger.info(`development环境：使用缓存配置并强制启用Sentry用于测试`)
        this.logger.info(`使用的DSN: ${devConfig.dsn}`)
        return devConfig
      } else {
        const devConfig = { ...defaultConfig, enabled: false }
        this.logger.info(`development环境：使用默认配置并启用Sentry用于测试`)
        return devConfig
      }
    }

    // 非development环境：根据配置决定
    if (cachedConfig) {
      // 使用云端配置
      const mergedConfig = { ...defaultConfig, ...cachedConfig }
      this.logger.info(
        `使用缓存Sentry配置 (环境: ${environment}, 配置启用: ${mergedConfig.enabled})`
      )
      return mergedConfig
    }

    // 使用默认配置（默认开启）
    this.logger.info(
      `使用默认Sentry配置 (环境: ${environment}, 默认启用: ${defaultConfig.enabled})`
    )
    return defaultConfig
  }

  // 异步更新配置（从云端获取并保存到缓存，确保在 app ready 之后执行）
  async updateConfigAsync() {
    const doUpdate = async () => {
      try {
        this.logger.info('=== 开始异步更新Sentry配置 ===')
        this.logger.info('当前时间:', new Date().toISOString())
        this.logger.info('app.isPackaged:', app.isPackaged)
        this.logger.info('process.env.NODE_ENV:', process.env.NODE_ENV)
        this.logger.info('process.env.VUE_APP_MODE:', process.env.VUE_APP_MODE)

        const cloudConfig = await this.fetchConfig()

        if (cloudConfig) {
          this.logger.info('云端Sentry配置获取成功:', JSON.stringify(cloudConfig, null, 2))
          this.saveCachedConfig(cloudConfig)
          this.logger.info('配置保存完成')
        } else {
          this.logger.warn('云端Sentry配置获取失败：无配置数据')
        }
      } catch (error) {
        this.logger.error('异步更新Sentry配置失败:', error.message)
        this.logger.error('错误堆栈:', error.stack)
      }
    }

    // 确保在 app ready 之后执行网络请求
    if (app.isReady()) {
      this.logger.info('app已ready，立即执行异步更新')
      await doUpdate()
    } else {
      this.logger.info('app未ready，等待ready后执行异步更新')
      app.whenReady().then(() => {
        this.logger.info('app ready完成，开始异步更新')
        doUpdate()
      })
    }
  }

  // 获取配置（同步返回缓存/默认，异步更新云端配置）
  getConfig() {
    // 同步返回配置
    const config = this.getConfigSync()

    // 异步更新配置（不阻塞返回）
    this.updateConfigAsync().catch(error => {
      this.logger.error('异步更新配置出错:', error.message)
    })

    return config
  }
}

export default MainSentryCloudConfig
