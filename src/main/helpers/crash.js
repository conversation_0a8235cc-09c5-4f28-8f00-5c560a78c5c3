const { dialog } = require('electron')
const { getLogger } = require('./loggerManager')
class CrashHelper {
  // 渲染进程崩溃
  rendererCrash(_event, webContents, details) {
    let logger = getLogger('Application')
    logger.error(`CrashHelper renderer process crashed ${JSON.stringify(details)}`)
    const clicked = dialog.showMessageBoxSync(global.__win, {
      detail: details.reason,
      message: global.getLocale('render_process_exited'),
      type: 'error',
      buttons: [global.getLocale('yes'), global.getLocale('no')]
    })
    if (clicked == 0) {
      setImmediate(() => {
        global.__win.reload()
      })
    }
  }

  async start() {}
}

module.exports = new CrashHelper()
