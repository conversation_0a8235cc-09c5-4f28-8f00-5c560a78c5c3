import Store from 'electron-store'
import { getLogger } from './loggerManager'
const logger = getLogger('ConfigManager')

export default class ConfigManager {
  constructor() {
    this.userConfig = {}
    this.init()
  }

  init() {
    this.initUserConfig()
  }

  initUserConfig() {
    this.userConfig = new Store({
      name: 'user',
      defaults: {
        skipPasswordSettings: false,
        timezoneRuleType: 'school',
        timezone: '',
        timezoneList: [],
        local: '',
        language: '',
        campusRuleType: '',
        studentIdHistoryRecord: '',
        emailHistoryRecord: ''
      }
    })
    logger.info('init user config')
  }

  getUserConfig(key, defaultValue) {
    if (typeof key === 'undefined' && typeof defaultValue === 'undefined') {
      return this.userConfig.store
    }
    return this.userConfig.get(key, defaultValue)
  }

  setUserConfig(key, value = '') {
    this.userConfig.set(key, value)
  }

  deleteUserConfig(key) {
    this.userConfig.delete(key)
  }

  reset() {
    this.userConfig.clear()
  }
}
