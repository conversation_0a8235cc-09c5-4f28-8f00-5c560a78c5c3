// 主进程 Sentry 管理器
import { getLogger } from './loggerManager'
import sentryCloudConfig from './sentryCloudConfig' // 缓存配置供IPC使用
// === 导入必要的模块 ===
const Sentry = require('@sentry/electron/main')
let sentryConfig = null
// 同步初始化主进程Sentry（在 setAppPath 之后调用）
function initMainSentrySync() {
  const logger = getLogger('SentryInit')

  try {
    logger.info('开始同步初始化主进程Sentry...')

    // 直接使用配置管理器实例并同步获取配置
    const sentryCloudConfigManager = new sentryCloudConfig()
    const config = sentryCloudConfigManager.getConfig() // 同步调用
    sentryConfig = config
    if (!config.enabled) {
      logger.info('主进程Sentry已禁用')
      return
    }

    logger.info('使用配置初始化主进程Sentry:', {
      dsn: config.dsn ? '***已配置***' : '未配置',
      environment: config.environment,
      sampleRate: config.sampleRate
    })

    // 立即初始化Sentry
    Sentry.init({
      dsn: config.dsn,
      environment: config.environment,
      release: config.release,
      enabled: config.enabled,
      sampleRate: config.sampleRate,
      debug: config.debug, // 生产环境关闭调试
      maxBreadcrumbs: config.maxBreadcrumbs,
      maxValueLength: config.maxValueLength,
      autoSessionTracking: config.autoSessionTracking,
      tracesSampleRate: config.tracesSampleRate,
      profilesSampleRate: config.profilesSampleRate,

      beforeSend(event) {
        // 如果是异常事件但级别未定义，设置为error级别
        if (event.exception && event.exception.values && event.exception.values.length > 0) {
          event.level = 'error'
        }

        // 只记录错误和警告级别的事件
        if (event.level !== 'error' && event.level !== 'warning') {
          return null
        }

        return event
      },

      beforeBreadcrumb(breadcrumb) {
        // 过滤敏感信息和无用的面包屑
        if (breadcrumb.category === 'fetch' || breadcrumb.category === 'xhr') {
          // 移除请求中的敏感信息
          if (breadcrumb.data && breadcrumb.data.url) {
            breadcrumb.data.url = breadcrumb.data.url.replace(/token=[^&]+/gi, 'token=***')
          }
        }

        // 限制控制台日志的面包屑
        if (breadcrumb.category === 'console' && breadcrumb.level !== 'error') {
          return null
        }

        return breadcrumb
      }
    })

    logger.info('主进程Sentry初始化完成')
  } catch (error) {
    logger.error('主进程Sentry初始化失败:', error)
  }
}
const getSentryConfig = () => {
  return sentryConfig
}

export { initMainSentrySync, getSentryConfig }
