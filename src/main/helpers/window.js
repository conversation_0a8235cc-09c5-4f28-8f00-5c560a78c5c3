/**
 * 窗口函数
 */
const windowClass = require('../window') // 窗口集合
const { getLogger } = require('./loggerManager')
const { app } = require('electron')
const fs = require('fs-extra')
let allWins = new Object()

class WindowHelper {
  // 创建窗口
  async createWindow(type) {
    let logger = getLogger('Application')
    logger.info(`createWindow ${type}`)
    let win = null
    await this.secondWindow()
    logger.info(`${type}Window:: beforeCreate`)
    if (type == 'main') {
      logger.info(`${type}Window:: startCreate`)
      win = windowClass.mainWindow.createMainWindow()
    }
    if (win) {
      allWins[type] = win
      logger.info(`${type}Window:: createFinish`)
      win.on('close', () => {
        logger.info(`${type}Window:: beforeDestroy`)
      })
      win.on('closed', () => {
        logger.info(`${type}Window:: Destroyed`)
        allWins[type] = null
      })
    }
  }
  // 是否有相同的窗口
  secondWindow(type) {
    let logger = getLogger('Application')
    logger.info(`secondWindow ${type}`)
    return new Promise(resolve => {
      let win = allWins[type]
      if (win) {
        allWins[type] = null
        win.close()
        win.on('closed', () => {
          let logger = getLogger('Application')
          logger.info(`${type}Window:: 相同实例触发Destroyed`)
          allWins[type] = null
          resolve()
        })
      } else {
        resolve()
      }
    })
  }
  // 重新打开窗口
  reOpenWindow(type, data) {
    let logger = getLogger('Application')
    logger.info(`重新打开窗口 ${type}`)
    allWins[type] ? this.focusWindow(type) : this.createWindow(type, data)
  }
  // 获取窗口
  getWindow(type) {
    if (type == 'main-webview') type = 'main'
    return allWins[type]
  }
  // 获取窗口数据
  getWindowData(type) {
    let win = allWins[type]
    if (!win) return false
    return win.getData && win.getData()
  }
  // 显示窗口
  showWindow(type) {
    let win = allWins[type]
    if (!win) return false
    win.show()
  }
  // 隐藏窗口
  hideWindow(type) {
    let win = allWins[type]
    if (!win) return false
    win.hide()
  }
  // 聚焦窗口
  focusWindow(type) {
    let win = allWins[type]
    if (!win) return false
    win.show()
    win.focus()
  }
  // 关闭窗口
  closeWindow(type) {
    let win = allWins[type]
    if (!win) return false
    if (type == 'main') global.APP_CLIENT_STATUS.QUIT = true
    win.close()
  }
  // 新开一个测试窗口
  async loadTestWindow(url) {
    // 判断是否有test窗口,如果有,加载url,如果没有则先创建
    let logger = getLogger('Application')
    logger.info(`loadTestWindow ${url}`)
    let win = allWins['test']
    if (!win) {
      win = await windowClass.testWindow.createTestWindow()
    }
    win.testUrl(url)
  }
  screenShotToLogs({ type, key, planId, interactId, imgQuality = 1 }) {
    const path = app.getPath('userData')
    const dateStr = new Date().toLocaleDateString().replaceAll('/', '-')
    const filePath = `${path}/Imgs/${dateStr}/${planId}/${key}_${interactId}`
    const fileName = `${filePath}/thinkAcademy-${new Date().getTime()}.jpeg`
    let win = allWins[type]
    win.capturePage().then(img => {
      fs.ensureDirSync(filePath)
      fs.writeFile(fileName, img.toJPEG(imgQuality))
    })
  }
  async screenShotToPath({ type, filePath, fileName, imgQuality = 100 }) {
    if (!filePath || !fileName) return false
    let win = allWins[type]
    const img = await win.capturePage()
    fs.ensureDirSync(filePath)
    const imgFile = img.toPNG(imgQuality)
    fs.writeFile(fileName, imgFile)
    return 'data:image/jpeg;base64,' + imgFile.toString('base64')
  }
}

module.exports = new WindowHelper()
