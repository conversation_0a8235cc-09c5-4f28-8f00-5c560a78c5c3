const os = require('os')
const path = require('path')
const { app } = require('electron')
const userDataPath = app.getPath('userData')
let isProduction = ['production', 'beta', 'preprod'].includes(process.env.VUE_APP_MODE)
class GlobalHelper {
  getPlatform() {
    if (os.platform() == 'darwin') return 'mac'
    if (os.platform() == 'win32') return 'windows'
  }
  appClientInfo() {
    return {
      PLATFORM: this.getPlatform(),
      PLATFORM_VERSION: os.release(),
      VERSION: app.getVersion(),
      LANG: 'en-US',
      ENV: process.env.VUE_APP_MODE,
      CW_SERVER_ADDRESS: '',
      CW_PORT: 10020,
      CW_WEBROOT: 'cw_webroot',
      APP_DIR: path.resolve(userDataPath, process.env.VUE_APP_MODE)
    }
  }
  appClientStatus() {
    return {
      QUIT: false,
      USERINFO: {},
      DEVTOOL: process.env.VUE_APP_MODE !== 'production'
    }
  }
}

global.APP_CLIENT_INFO = new GlobalHelper().appClientInfo()
global.APP_CLIENT_STATUS = new GlobalHelper().appClientStatus()
global.isProduction = isProduction
