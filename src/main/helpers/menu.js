const { Menu, app, shell } = require('electron')
const { getLogger } = require('./loggerManager')

class MenuHandler {
  async start() {
    let logger = getLogger('Application')
    logger.info('Menu Create Main Menu')
    // 创建主菜单
    const appMenutemplate = MenuHandler.getAppMenuTemplate(process.platform)
    const menuFromTemplate = Menu.buildFromTemplate(appMenutemplate)
    Menu.setApplicationMenu(menuFromTemplate)
  }

  static getAppMenuTemplate() {
    const darwinTemplate = [
      {
        label: global.getLocale('menu_label_thinkacademy'),
        submenu: [
          {
            label: `${global.getLocale('menu_about')} ${global.getLocale(
              'menu_label_thinkacademy'
            )}`,
            role: 'about'
          },
          {
            type: 'separator'
          },
          {
            type: 'separator'
          },
          {
            label: `${global.getLocale('menu_hide')} ${global.getLocale(
              'menu_label_thinkacademy'
            )}`,
            accelerator: 'CommandOrControl+H',
            selector: 'hide:',
            role: 'hide'
          },
          {
            label: global.getLocale('menu_hideOthers'),
            accelerator: 'CmdOrCtrl+Alt+H',
            selector: 'hideOtherApplications:',
            role: 'hideOthers'
          },
          {
            type: 'separator'
          },
          {
            label: `${global.getLocale('menu_quit')} ${global.getLocale(
              'menu_label_thinkacademy'
            )}`,
            accelerator: 'CommandOrControl+Q',
            role: 'quite',
            click: MenuHandler._quitApp
          }
        ]
      },
      {
        label: global.getLocale('menu_label_edit'),
        submenu: [
          {
            label: global.getLocale('menu_undo'),
            role: 'undo'
          },
          {
            label: global.getLocale('menu_redo'),
            role: 'redo'
          },
          {
            type: 'separator'
          },
          {
            label: global.getLocale('menu_cut'),
            role: 'cut'
          },
          {
            label: global.getLocale('menu_copy'),
            role: 'copy'
          },
          {
            label: global.getLocale('menu_paste'),
            role: 'paste'
          },
          {
            label: global.getLocale('menu_delete'),
            role: 'delete'
          },
          {
            label: global.getLocale('menu_selectAll'),
            role: 'selectAll'
          }
        ]
      },
      {
        label: global.getLocale('menu_label_window'),
        submenu: [
          {
            label: global.getLocale('menu_minimize'),
            role: 'minimize'
          },
          {
            label: global.getLocale('menu_close'),
            role: 'close'
          },
          {
            label: global.getLocale('menu_togglefullscreen'),
            role: 'togglefullscreen'
          }
        ]
      }
    ]
    if (process.env.NODE_ENV === 'development') {
      darwinTemplate.push({
        label: '调试',
        submenu: [
          {
            label: '开发者工具',
            accelerator: 'CommandOrControl+Shift+I',
            role: 'toggleDevTools'
          },
          {
            label: '打开数据目录',
            click: MenuHandler._openUserData
          }
        ]
      })

      darwinTemplate.push({
        label: '帮助',
        role: 'help',
        submenu: [
          {
            label: '开发文档',
            click: async () => {
              await shell.openExternal('https://thinkacademy-docs.neosjs.com/')
            }
          }
        ]
      })
    }
    return darwinTemplate
  }

  static _quitApp() {
    app.exit(0)
  }

  static _devTools() {
    global.__win.webContents.toggleDevTools()
  }

  static _openUserData() {
    shell.showItemInFolder(app.getPath('userData'))
  }
}

module.exports = new MenuHandler()
