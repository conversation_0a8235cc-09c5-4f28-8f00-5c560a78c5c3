const express = require('express')
const cors = require('cors')
const history = require('connect-history-api-fallback')
const net = require('net')
const { getLogger } = require('./loggerManager')

let logger = getLogger('Application')

class LocalServer {
  // 检查端口是否被占用
  static portIsOccupied(port) {
    const server = net.createServer().listen(port, '127.0.0.1')
    return new Promise((resolve, reject) => {
      server.on('listening', () => {
        logger.info(`LocalServer:: local server is runnint on port ${port}`)
        server.close()
        // process.env.DEV_PORT = port
        // process.env.PROD_PORT = port
        // global.APP_CLIENT_INFO.PORT = port
        resolve(port)
      })

      server.on('error', err => {
        if (err.code === 'EADDRINUSE') {
          resolve(LocalServer.portIsOccupied(port + 1))
          logger.error(`LocalServer:: this port ${port} is occupied.try another.`)
        } else {
          reject(err)
        }
      })
    })
  }
  // 启动本地服务器
  start(dirname, port) {
    logger.info(`express start dirname:${dirname}, port:${port}`)
    if (typeof dirname === 'number') {
      port = dirname
      dirname = __dirname
    }

    this.root = dirname
    this.port = port

    logger.info(`express __dirname:${__dirname}, port:${port}`)
    return new Promise((resolve, reject) => {
      let server = express()
      server.use(cors())
      server.use(history())
      logger.info(`express static dirname:${dirname}`)
      server.use(express.static(dirname))
      LocalServer.portIsOccupied(port)
        .then(port => {
          logger.info(`express port:${port}`)
          this.serverHandler = server.listen(port, '127.0.0.1')
          resolve({
            port,
            host: `http://localhost:${port}/`
          })
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  stop() {
    this.serverHandler.close()
    this.serverHandler = null
    this.root = ''
    this.port = ''
    console.log('课件服务关闭 static server')
    logger.info(`server:课件服务关闭 static server`)
  }

  async restart() {
    const root = this.root
    const port = this.port
    await this.stop()
    await this.start(root, port)
    logger.warn(`课件服务 restart`)
  }
}

global.LocalServer = new LocalServer()
module.exports = new LocalServer()
