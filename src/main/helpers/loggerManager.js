// import is from 'electron-is'
import logger from 'electron-log'
import { app } from 'electron'
const moment = require('moment')
import { resolve } from 'path'
// 这里需要区分：开发、测试、线上、预发布等环境，防止缓存错用
const loggerUserData = resolve(app.getPath('userData'), process.env.VUE_APP_MODE)
/**
 * 创建日志实例
 * @param {String} filename  日志的文件名
 * @param {Array} levels 日志级别
 * @returns
 */

const loggerFileName = `${moment(new Date()).format('yyyy-MM-DD')}.log`
logger.levels.add('notice', 2)
logger.transports.file.resolvePath = () => resolve(`${loggerUserData}/Logs`, loggerFileName)
logger.transports.file.fileName = loggerFileName
logger.transports.file.level = 'silly'
logger.transports.console.level = true // 控制台日志
logger.transports.sync = false
logger.transports.file.maxSize = 100 * 1024 * 1024 // 文件大小
logger.transports.console.format = `[{m}-{d} {h}:{i}:{s}] [{level}] {text}`
logger.transports.ipc.level = false

const getLogger = name => logger.scope(name)

logger.info('==================================')
logger.info(`${logger.transports.file.fileName}`)
logger.scope('Logger').info('init logger')

export default logger
export { getLogger }
