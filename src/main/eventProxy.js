const { ipcMain, screen, shell } = require('electron')
const { ipcMainManager } = require('../renderer/utils/electronIpc/main/index')
const { getLogger } = require('./helpers/loggerManager')
const fs = require('fs-extra')
const messageCenter = ipcMainManager()
import ConfigManager from './helpers/config'
const windowHelper = require('./helpers/window')
const nativeApiHandler = require('./nativeApi/handle') // 覆盖electron-ipc包 注册handler
const downloaderHandler = require('./downloaderApi/handle')

class EventProxy {
  constructor() {
    this.config = new ConfigManager()
    this.start()
  }
  async start() {
    let logger = getLogger('Application')
    logger.info('EventProxy:: Start')
    messageCenter.initApiHandler()
    try {
      await nativeApiHandler.loadHandler()
    } catch (e) {
      logger.info('nativeApiHandler.loadHandler', e)
    }
    try {
      // 注册downloaderHandler相关监听
      await downloaderHandler.initDownloader()
    } catch (e) {
      logger.info('downloaderHandler.initDownloader', e)
    }

    this.writeLog()
    this.workAreaSize()
    this.listenerRendererMsg()
  }
  listenerRendererMsg() {
    ipcMain.on('application:removeDeviceCheckFile', (_event, filePath) => {
      if (fs.ensureFile(filePath)) fs.remove(filePath)
    })

    ipcMain.handle('setStoreValue', (event, key, value) => {
      return this.config.setUserConfig(key, value)
    })

    ipcMain.on('deleteStoreValue', (event, key) => {
      this.config.deleteUserConfig(key)
    })

    ipcMain.handle('getStoreValue', (event, key) => {
      return this.config.getUserConfig(key)
    })
    ipcMain.on('test:url', (event, url) => {
      windowHelper.loadTestWindow(url)
    })
    ipcMain.on('openUrl', (event, url) => {
      shell.openExternal(url)
    })
    ipcMain.on('screenShotToLogs', (event, { key, planId = 0, interactId = 0 }) => {
      windowHelper.screenShotToLogs({
        type: 'main',
        key,
        planId,
        interactId
      })
    })
    ipcMain.handle('screenShotToPath', async (event, { filePath, fileName }) => {
      return await windowHelper.screenShotToPath({
        type: 'main',
        filePath,
        fileName
      })
    })
  }
  // 监听写日志，这里的写入，是从渲染进来来的
  writeLog() {
    ipcMain.on('application:write-logger', (_event, level, ...msg) => {
      let logger = getLogger('Renderer')
      logger[level](...msg)
    })
  }
  // 监听获取屏幕分辨率消息并返回
  workAreaSize() {
    ipcMain.on('application:work-area-size', event => {
      const workAreaSize = screen.getPrimaryDisplay().workAreaSize
      const winW = workAreaSize.width
      const winH = workAreaSize.height
      event.sender.send('application:work-area-size-reply', winW, winH)
    })
  }
}

process.on('exit', () => {
  let logger = getLogger('Application')
  logger.info(`process:SIGTERM 系统退出 kill process: ${process.pid}`)
})

export default EventProxy
