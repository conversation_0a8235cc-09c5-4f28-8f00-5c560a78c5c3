const { join } = require('path')
const { app, BrowserWindow, globalShortcut } = require('electron')
const { enable } = require('@electron/remote/main')

const { ipcMainManager } = require('../../renderer/utils/electronIpc/main/index')
const { getLogger } = require('../helpers/loggerManager')
let logger = getLogger('Application')
const messageCenter = ipcMainManager()

class MainWindow extends BrowserWindow {
  constructor(props) {
    super(props)
    this.constructor = BrowserWindow
    this.url = global.isProduction
      ? require('path').join('file://', __dirname, '/index.html')
      : process.env.WEBPACK_DEV_SERVER_URL
  }

  init() {
    logger.info(`mainWindow:: loadURL: ${this.url}`)
    this.loadURL(this.url)
    global.timeLine.loadURL = +new Date()
    this.registerDevToolsShortcut()
  }

  registerDevToolsShortcut() {
    globalShortcut.register('CommandOrControl+Shift+I', () => {
      logger.info(`mainWindow:: 打开控制台: ${this.url} ${this.webContents}`)
      this.webContents.toggleDevTools()
    })
  }
}

// 创建主窗口
const createMainWindow = () => {
  const defaultOptions = {
    width: 1280,
    height: 760,
    minWidth: 640,
    minHeight: 360,
    show: false,
    movable: true,
    frame: false,
    resizable: false,
    titleBarStyle: 'hidden', //'hiddenInset',
    trafficLightPosition: {
      x: 8,
      y: 8
    },
    center: true,
    webPreferences: {
      // devTools: true,
      autoplayPolicy: 'no-user-gesture-required',
      webSecurity: false,
      nodeIntegration: true,
      webviewTag: true,
      javascript: true,
      plugins: true,
      nodeIntegrationInWorker: true,
      enableRemoteModule: true,
      contextIsolation: false, // 升级之后，contextIsolation的默认值已弃用，将从false更改为true，手动设置
      preload: join(app.getAppPath(), 'preload.js')
    }
  }
  let mainWin = new MainWindow(defaultOptions)
  enable(mainWin.webContents)
  global.__win = mainWin
  mainWin.init()

  // 窗口关闭
  mainWin.on('close', function(e) {
    logger.info(`mainWindow:: mainWin:close ${e}`)
    global.__app && global.__app.quit()

    // e.preventDefault()
    // let isFull = mainWin.isMaximized() || mainWin.isFullScreen()
    // if (isFull) {
    //   mainWin.setFullScreen(false)
    //   setTimeout(() => mainWin.hide(), 1000)
    // }
    // mainWin.hide()
  })

  // 窗口已经关闭
  mainWin.on('closed', () => {
    logger.info(`mainWindow:: mainWin:closed`)
    mainWin = null
    global.__win = mainWin
  })

  // 监听窗口最小
  mainWin.on('minimize', () => {
    logger.info(`mainWindow:: minimize`)
    messageCenter.sendToRender('minimize')
  })
  // 等比缩放
  mainWin.setAspectRatio(1280 / 760)

  // 窗口从最小化状态恢复时
  mainWin.on('restore', () => {
    logger.info(`mainWindow:: restore`)
    mainWin.focusOnWebView()
    messageCenter.sendToRender('restore')
  })
  // 监听窗口最大化
  mainWin.on('maximize', () => {
    logger.info(`mainWindow:: maximize`)
    messageCenter.sendToRender('maximize')
  })
  mainWin.on('enter-full-screen', () => {
    logger.info(`mainWindow:: EnterFullScreen`)
    // 进入全屏
    messageCenter.sendToRender('enter-full-screen')
  })
  mainWin.on('leave-full-screen', () => {
    logger.info(`mainWindow:: LeaveFullScreen`)
    // 退出全屏
    messageCenter.sendToRender('leave-full-screen')
  })
  // 窗口失焦
  mainWin.on('blur', () => {
    logger.info(`mainWindow:: blur`)
    mainWin.webContents.send('window_blur', true)
  })
  // 窗口聚焦
  mainWin.on('focus', () => {
    logger.info(`mainWindow:: focus`)
    mainWin.webContents.send('window_blur', false)
  })

  mainWin.once('ready-to-show', () => {
    mainWin.show()
    global.timeLine.showWindow = +new Date()
    mainWin.focus()

    logger.info(`MainWindow:: Show`)
    // 发送清除localstorage操作
    mainWin.webContents.send('clearLocalStorage')
  })
  return mainWin
}

module.exports.createMainWindow = createMainWindow
