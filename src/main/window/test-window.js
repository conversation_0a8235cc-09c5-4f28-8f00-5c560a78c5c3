const { join } = require('path')
const { app, BrowserWindow } = require('electron')
const { ipcMainManager } = require('../../renderer/utils/electronIpc/main/index')
const { getLogger } = require('../helpers/loggerManager')
let logger = getLogger('TestWindow')
const messageCenter = ipcMainManager()

class MainWindow extends BrowserWindow {
  constructor(props) {
    super(props)
    this.constructor = BrowserWindow
  }

  testUrl(url) {
    this.loadURL(url)
    // this.registerDevToolsShortcut()
  }
}

// 创建主窗口
const createTestWindow = () => {
  const defaultOptions = {
    width: 200,
    height: 200,
    show: false,
    movable: true,
    frame: false,
    // resizable: false,
    titleBarStyle: 'hidden', //'hiddenInset',
    trafficLightPosition: {
      x: 8,
      y: 8
    },
    center: true,
    webPreferences: {
      // devTools: true,
      autoplayPolicy: 'no-user-gesture-required',
      webSecurity: false,
      nodeIntegration: true,
      webviewTag: true,
      javascript: true,
      plugins: true,
      nodeIntegrationInWorker: true,
      enableRemoteModule: true,
      contextIsolation: false, // 升级之后，contextIsolation的默认值已弃用，将从false更改为true，手动设置
      preload: join(app.getAppPath(), 'preload.js')
    }
  }
  let mainWin = new MainWindow(defaultOptions)
  // 监听url加载完成
  mainWin.webContents.on('did-finish-load', () => {
    logger.info(`testWindow:: did-finish-load ${mainWin.webContents.getTitle()}`)
    // 加载成功,获取网页title,通知主进程
    messageCenter.sendToRender('testWindow:did-finish-load', mainWin.webContents.getTitle())
    // 关闭当前窗口
    mainWin.close()
  })
  // 监听url加载失败
  mainWin.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    logger.error(`testWindow:: did-fail-load ${errorCode} ${errorDescription} ${validatedURL}}`)
    // 加载失败通知主进程
    messageCenter.sendToRender('testWindow:did-fail-load')
    mainWin.close()
  })
  return mainWin
}

module.exports.createTestWindow = createTestWindow
