import { nativeApi } from 'utils/electronIpc'
import { ostt_app_performance } from '@/utils/sensorTrack/classLive'
import { getValueByCloudConfigKey } from 'utils/util'
export const useReportPerformance = () => {
  const configValue = getValueByCloudConfigKey('enableReportPerformance', 'configValue')
  const isEnable = !!+(configValue || 0)

  let performanceTimer = null
  const clearPerformanceTimer = () => {
    if (performanceTimer) {
      clearInterval(performanceTimer)
      performanceTimer = null
    }
  }
  const reportPerformanceSensor = () => {
    clearPerformanceTimer()
    performanceTimer = setInterval(() => {
      nativeApi.getPerformance().then(res => {
        ostt_app_performance(res.appUsageMem, res.appUsageCPU)
      })
    }, 10000)
  }
  if (isEnable) {
    reportPerformanceSensor()
    console.info('启动性能采集')
  } else {
    console.info('未启动性能采集')
  }
  return clearPerformanceTimer
}
