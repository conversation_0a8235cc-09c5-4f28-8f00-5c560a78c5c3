import { onMounted, onUnmounted, getCurrentInstance } from '@vue/composition-api'
import { chatMsgPriority } from '@/LargeClass/base/interaction-handler/interaction-conf'
import logger from 'utils/logger'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import { notFocusedPush } from 'api/classroom/index'

export const useWindowBlur = store => {
  const { proxy } = getCurrentInstance()
  /**
   * 失焦状态上报
   */
  const reportData = async ({ studentId, planId, startTime, endTime }) => {
    const duration = parseInt((endTime - startTime) / 1000)
    await notFocusedPush({ planId, studentId, duration, startTime, endTime }).catch(err => {
      logger.send({
        tag: '开小差',
        content: { msg: `状态上报接口报错${JSON.stringify(err)}` },
        level: 'error'
      })
    })
  }
  let blurTimer = null
  let isWindowBlur = null
  let startTime = null
  let endTime = null
  const windowBlur = (event, arg) => {
    const { stuInfo, configs, planInfo } = store.state.smallClass.baseData
    if (!stuInfo) {
      console.warn('监听window_blur,stuinfo不存在')
      return
    }
    const studentId = stuInfo.id
    const teacherIrcId = configs.teacherIrcId
    const planId = planInfo.id
    // @log-ignore
    clearTimeout(blurTimer)
    if (isWindowBlur === arg) {
      return
    }
    console.info('监听window_blur,学生端聚焦/失焦', arg)
    const time = !arg ? 0 : 5000
    const content = JSON.stringify({
      type: 'isBlur',
      isBlur: arg,
      id: studentId
    })
    // 失焦的情况下关闭麦克风
    if (arg) {
      console.info('失焦情况下,触发关闭麦克风')
      proxy.$bus.$emit('close-speaking')
    }
    blurTimer = setTimeout(() => {
      window?.ChatClient?.PeerChatManager?.sendPeerMessage(
        [{ nickname: teacherIrcId }],
        content,
        chatMsgPriority.notice
      )
      if (arg) {
        startTime = new Date().getTime()
      } else {
        endTime = new Date().getTime()
        startTime && reportData({ studentId, planId, startTime, endTime })
      }
      isWindowBlur = arg
      classLiveSensor.osta_app_not_focused(arg)
      console.log('0606 [windowBlur] content:', content, 'nickname', teacherIrcId)
      logger.send({
        tag: '开小差',
        content,
        teacherIrcId
      })
    }, time)
  }
  onMounted(() => {
    window.thinkApi.ipc.on('window_blur', windowBlur)
  })
  onUnmounted(() => {
    window.thinkApi.ipc.removeListener('window_blur', windowBlur)
  })
}
