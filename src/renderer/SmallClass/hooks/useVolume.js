import { onBeforeUnmount, getCurrentInstance, onMounted, computed } from '@vue/composition-api'
import { getValueByCloudConfigKey } from 'utils/util'
export const useVolume = () => {
  const { proxy } = getCurrentInstance()

  const onstageVolume = Number(getValueByCloudConfigKey('onStageVolume', 'configValue')) || 100
  const downStageVolume = Number(getValueByCloudConfigKey('downStageVolume', 'configValue')) || 50
  console.log('云控音量', onstageVolume, downStageVolume)

  // 降低其他同学音量
  const studentList = computed(() => proxy.$store.state.smallClass.studentList)
  function reduceOtherVolume() {
    console.info('降低其他同学音量')
    // 循环所有学生
    studentList.value.forEach(item => {
      adjustVolume(downStageVolume, item.userId)
      console.info('降低其他同学音量', downStageVolume, item.userId)
    })
  }

  // 调整音量
  function adjustVolume(volume, uid) {
    console.info('调整音量:', volume, uid)
    proxy.thinkClass.RtcService.setRemoteVoiceVolume(volume, uid)
  }
  function onLinkAdjustVolume(v, uid) {
    console.info('上下台调整学员声音音量:', v, uid)
    if (v === 'up') {
      adjustVolume(onstageVolume, uid)
      return
    }
    if (v === 'down') {
      adjustVolume(downStageVolume, uid)
      return
    }
  }

  onMounted(() => {
    // 上台或者私聊的时候会触发
    proxy.$bus.$on('onlink_adjust_volume', onLinkAdjustVolume)
  })

  onBeforeUnmount(() => {
    proxy.$bus.$off('onlink_adjust_volume', onLinkAdjustVolume)
  })
  return {
    reduceOtherVolume
  }
}
