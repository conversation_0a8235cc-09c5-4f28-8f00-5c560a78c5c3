import {
  getCurrentInstance,
  computed,
  ref,
  onMounted,
  onBeforeUnmount,
  watch
} from '@vue/composition-api'
import { useMediaAccess } from './useMediaAccess'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'

const SPKEAK_TYPE = {
  MUTE: 1,
  PRESS: 2,
  UNMUTE: 3,
  CLICK: 4
}
export function useMicroPhoneStatus() {
  const { proxy } = getCurrentInstance()
  const { microphoneAccess, getMicrophoneAccess } = useMediaAccess()
  const store = proxy.$store
  const microphoneStatus = computed(() => store.state.smallClass.microphoneStatus)
  const isOnLink = computed(() => store.state.smallClass.selfVideoMicLink)
  const currentVolume = computed(() => store.state.smallClass.currentVolume)
  const currentSpeakType = computed(() => store.state.smallClass.currentSpeakType)
  const sliderVisible = computed(() => store.state.smallClass.sliderVisible)
  const isShowClick = computed(() => currentSpeakType.value == SPKEAK_TYPE.CLICK)
  watch(isOnLink, newVal => {
    console.info('是否上台', newVal)
    if (sessionSpeakType.value == SPKEAK_TYPE.UNMUTE) {
      console.info('当前为全员开麦状态，上下台无须改变')
      return
    }
    if (newVal) {
      if (isPress.value) {
        // 如果当前按下发言中，上台触发抬起按键
        triggerPressUp('上台触发')
      }
      store.dispatch('smallClass/updateCurrentSpeakType', SPKEAK_TYPE.UNMUTE) // 上台进入保持开麦状态
      handleMicroPhoneSwitch(true)
    } else {
      store.dispatch('smallClass/updateCurrentSpeakType', sessionSpeakType.value) // 下台恢复上台前状态
      handleMicroPhoneSwitch(false)
    }
  })

  const micVolumeStyle = ref({
    height: '0rem'
  })
  const updateMicBoxStyle = volume => {
    // @log-ignore
    const height = (volume / 225) * 0.24
    micVolumeStyle.value = {
      height: `${height}rem`
    }
  }

  const sessionSpeakType = ref(SPKEAK_TYPE.CLICK)

  // 是否显示静音图标
  const showMuteIcon = computed(
    () => currentSpeakType.value == SPKEAK_TYPE.MUTE && !microphoneStatus.value
  )

  /**
   * 切换麦克风状态
   * @param {boolean} status
   * @returns
   */

  async function handleMicroPhoneSwitch(status) {
    console.info('麦克风开关', status, '当前麦克风状态:', microphoneStatus.value)
    if (status) {
      await getMicrophoneAccess()
      if (!microphoneAccess.value) {
        // 权限校验弹窗
        proxy.$bus.$emit('showMediaSecurityAccess', 'microphone')
        return
      }
    }
    proxy.$store.dispatch('smallClass/updateMicrophoneStatus', status)
    proxy.thinkClass.RtcService.muteLocalAudio(!status)
  }
  // 点击切换麦克风状态
  function handleClickMicrophone() {
    console.info('点击麦克风按钮')
    handleMicroPhoneSwitch(!microphoneStatus.value)
  }

  // 老师更改讲话方式
  const speakTypeChangeHandle = msg => {
    const { needToast, msgSpeakType, isOpenMic } = msg
    let speakType = msgSpeakType
    if (msgSpeakType == SPKEAK_TYPE.PRESS && isOpenMic == 1) {
      speakType = SPKEAK_TYPE.CLICK
    }
    sessionSpeakType.value = speakType
    if (isOnLink.value) {
      console.info('当前上台状态，不响应声音管控')
      return
    }
    console.info('speakTypeChange', speakType, isOpenMic)
    store.dispatch('smallClass/updateCurrentSpeakType', sessionSpeakType.value)
    // 关闭引导提示
    proxy.$bus.$emit('closeSpeakGuide')
    handleMicroPhoneSwitch(false)
    if (isPress.value) {
      // 如果当前按下发言中，老师更改讲话方式触发抬起按键
      triggerPressUp('老师更改讲话方式')
    }

    if (needToast) {
      proxy.$bus.$emit('all_audio_mute_status', speakType)
    } else {
      console.info('不需要提示状态')
    }
    switch (speakType) {
      case 1:
        // 保持静音
        break
      case 2:
        // 按住发言
        proxy.$bus.$emit('showSpeakGuide')
        break
      case 3:
        // 保持开麦
        handleMicroPhoneSwitch(true)
        break
      case 4:
        // 点击发言
        break
      default:
        break
    }
  }

  const speakingBeginTime = ref(0)
  const isPress = ref(false)
  const keydownHandler = event => {
    if (event.code === 'Space') {
      if (
        document.activeElement.tagName === 'INPUT' ||
        document.activeElement.tagName === 'TEXTAREA'
      ) {
        console.info('当前光标在input or textarea中,所以忽略空格键事件')
        return
      }
      if (currentSpeakType.value != SPKEAK_TYPE.PRESS) {
        return
      }
      triggerPressDown('键盘')
    }
  }
  const keyupHandler = event => {
    if (event.code === 'Space') {
      if (currentSpeakType.value != SPKEAK_TYPE.PRESS) {
        console.info('非按下发言，不响应空格键抬起')
        return
      }
      triggerPressUp('键盘抬起')
    }
  }
  const triggerPressDown = from => {
    if (!sliderVisible.value) {
      console.info('侧边栏不课件，不响应按下')
      return
    }
    if (isPress.value) {
      return
    }
    console.info(from + '触发按下')
    // 通知tip显示
    proxy.$bus.$emit('keyDownUpChange', 'down')
    // 关闭按下讲话引导
    proxy.$bus.$emit('closeSpeakGuide')
    // 开启麦克风
    handleMicroPhoneSwitch(true)
    isPress.value = true
    speakingBeginTime.value = new Date().getTime()
  }
  const triggerPressUp = from => {
    if (!isPress.value) {
      console.info('无配对的按下，不响应抬起事件')
      return
    }
    console.info(`${from}触发`)
    // 通知tip显示
    proxy.$bus.$emit('keyDownUpChange', 'up')
    // 关闭麦克风
    handleMicroPhoneSwitch(false)
    isPress.value = false
    sensorOstaSpeak()
  }
  const sensorOstaSpeak = () => {
    if (speakingBeginTime.value) {
      const duration = new Date().getTime() - speakingBeginTime.value
      if (duration > 200) {
        console.info('学生麦克风发言时长:', duration)
        classLiveSensor.osta_speak(duration)
      }
      speakingBeginTime.value = 0
    }
  }
  function mouseDownEvent(event) {
    if (event.button === 0) {
      console.info('鼠标按下麦克风按钮')
      if (currentSpeakType.value != SPKEAK_TYPE.PRESS) {
        console.info('当前非按下发言状态，无须响应按下鼠标')
        return
      }
      triggerPressDown('鼠标')
      document.addEventListener('mouseup', mouseUpEvent)
    }
  }
  const mouseUpEvent = event => {
    console.log('抬起鼠标')
    document.removeEventListener('mouseup', mouseUpEvent)
    if (event.button === 0) {
      console.info('抬起了鼠标左键')
      if (currentSpeakType.value != SPKEAK_TYPE.PRESS) {
        console.info('当前非按下发言状态，无须响应抬起鼠标')
        return
      }
      triggerPressUp('鼠标触发')
    }
  }
  const blurTriggerPressUp = () => {
    if (isPress.value) {
      triggerPressUp('失去焦点触发抬起')
    }
  }

  onMounted(() => {
    window.addEventListener('keydown', keydownHandler)
    window.addEventListener('keyup', keyupHandler)
    proxy.$bus.$on('speakTypeChange', speakTypeChangeHandle)
    proxy.$bus.$on('close-speaking', blurTriggerPressUp)
  })
  onBeforeUnmount(() => {
    window.removeEventListener('keydown', keydownHandler)
    window.removeEventListener('keyup', keyupHandler)
    proxy.$bus.$off('speakTypeChange', speakTypeChangeHandle)
    proxy.$bus.$off('close-speaking', blurTriggerPressUp)
  })

  return {
    handleMicroPhoneSwitch,
    handleClickMicrophone,
    microphoneStatus,
    micVolumeStyle,
    currentVolume,
    updateMicBoxStyle,
    showMuteIcon,
    mouseDownEvent,
    isShowClick
  }
}
// kv消息相关
export const useMicroPhoneKvEvent = () => {
  const { proxy } = getCurrentInstance()

  const { microphoneAccess, getMicrophoneAccess } = useMediaAccess()
  const store = proxy.$store
  const currentSpeakType = computed(() => store.state.smallClass.currentSpeakType)
  const self = computed(() => store.state.smallClass.studentList.find(item => item.isSelf))

  async function handleMicroPhoneSwitch(status) {
    console.info('麦克风开关', status)
    if (status) {
      await getMicrophoneAccess()
      if (!microphoneAccess.value) {
        // 权限校验弹窗
        proxy.$bus.$emit('showMediaSecurityAccess', 'microphone')
        return
      }
    }
    proxy.$store.dispatch('smallClass/updateMicrophoneStatus', status)
    proxy.thinkClass.RtcService.muteLocalAudio(!status)
  }
  // 老师私聊控制麦克风开关
  const TeacherSwitchMicrophoneHandle = status => {
    console.info('TeacherSwitchMicrophone', status)
    if (currentSpeakType.value !== SPKEAK_TYPE.CLICK) {
      return
    }
    handleMicroPhoneSwitch(status)
  }
  // 老师全体静音控制麦克风开关
  const allAudioMuteHandle = status => {
    console.info('all_audio_mute', status)
    if (currentSpeakType.value !== SPKEAK_TYPE.CLICK) {
      return
    }
    handleMicroPhoneSwitch(false)
  }

  const changeCollectVolumeHandle = students => {
    const volume = students[self.value.userId]
    if (volume == undefined) {
      return
    }
    proxy.thinkClass.RtcService.setParameters('{"che.audio.record.signal.volume":' + volume + '}')
    console.info(`改变麦克风采集音量:${volume}`)
  }
  onMounted(async () => {
    await getMicrophoneAccess()
    if (!microphoneAccess.value) {
      // 权限校验弹窗
      proxy.$store.dispatch('smallClass/updateMicrophoneStatus', false)
      proxy.thinkClass.RtcService.muteLocalAudio(!false)
    }
    proxy.$bus.$on('TeacherSwitchMicrophone', TeacherSwitchMicrophoneHandle)
    proxy.$bus.$on('all_audio_mute', allAudioMuteHandle)
    proxy.$bus.$on('changeCollectVolume', changeCollectVolumeHandle)
  })
  onBeforeUnmount(() => {
    proxy.$bus.$off('TeacherSwitchMicrophone', TeacherSwitchMicrophoneHandle)
    proxy.$bus.$off('all_audio_mute', allAudioMuteHandle)
    proxy.$bus.$on('changeCollectVolume', changeCollectVolumeHandle)
  })
}
