import { nativeApi } from 'utils/electronIpc'
import { ref } from '@vue/composition-api'
export function useMediaAccess() {
  const cameraAccess = ref(false)
  async function getCameraAccess() {
    console.info('调用getCameraAccess方法')
    const cameraAccessStatus = await nativeApi.getCameraAccessStatus()
    console.info('调用getCameraAccess方法，cameraAccessStatus：', cameraAccessStatus)
    if (cameraAccessStatus !== 'granted') {
      console.warn('摄像头没有授权')
      cameraAccess.value = false
    } else {
      cameraAccess.value = true
    }
  }
  const microphoneAccess = ref(false)
  async function getMicrophoneAccess() {
    const microphoneAccessStatus = await nativeApi.getMicAccessStatus()
    if (microphoneAccessStatus !== 'granted') {
      console.warn('麦克风没有授权')
      microphoneAccess.value = false
    } else {
      microphoneAccess.value = true
    }
  }

  return {
    cameraAccess,
    microphoneAccess,
    getCameraAccess,
    getMicrophoneAccess
  }
}
