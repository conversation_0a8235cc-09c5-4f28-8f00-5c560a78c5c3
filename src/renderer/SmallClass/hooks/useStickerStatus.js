import { getCurrentInstance, computed, onMounted, onUnmounted } from '@vue/composition-api'
import logger from 'utils/logger'
import _debounce from 'lodash/debounce'

export function useStickerStatus() {
  const { proxy } = getCurrentInstance()
  const baseData = proxy.$store.state.smallClass.baseData
  const isAudition = computed(() => {
    return baseData.commonOption.isParent || baseData.commonOption.isAudition
  })
  const arOpen = computed(() => {
    return proxy.$store.state.smallClass.arOpen
  })

  const hasStickerSwitch = computed(() => {
    return proxy.$store.state.smallClass.hasStickerSwitch
  })

  const forbidVideoSticker = computed(() => {
    return proxy.$store.state.smallClass.forbidVideoSticker
  })
  function forbidVideoStickerHandle(noticeContent) {
    if (isAudition.value) return
    if (hasStickerSwitch.value) {
      const forbidVideoSticker = noticeContent.pub === 1 ? true : false
      proxy.$store.dispatch('smallClass/updateForbidVideoSticker', forbidVideoSticker)
      sendLogger(`收到老师信令是：${forbidVideoSticker ? '关闭' : '开启'}贴纸状态`)
      if (forbidVideoSticker && arOpen.value) {
        proxy.$store.dispatch('smallClass/updateArStatus', !arOpen.value)
        proxy.$bus.$emit('showEffect')
        proxy.$Message.info(proxy.$t('classroom.smallClass.visualEffect.teacherClose'))
        sendLogger('收到老师信令是关闭贴纸状态，关闭本地的贴纸状态')
      }
      if (!forbidVideoSticker) {
        // 如果是老师信令是开启状态，本地是关闭状态
        if (!arOpen.value) {
          proxy.$bus.$emit('showAllowStickerTipToast')
          sendLogger('收到老师信令是开启贴纸状态，本地是关闭贴纸状态，给用户提示')
        }
      }
    } else {
      sendLogger('收到老师信令贴纸状态信令，但是背包中无启用的形象贴纸，无开关入口')
    }
  }
  const arChange = _debounce(
    function() {
      if (forbidVideoSticker.value && !arOpen.value) {
        // 禁止情况，点击开启，给提示
        proxy.$bus.$emit('showNotAllowStickerTipModal')
        sendLogger('教师端禁止情况，手动点击开启贴纸，提示无法开启')
        return
      }
      sendLogger(`点击切换贴纸开关为：${!arOpen.value ? '开启' : '关闭'}`)
      proxy.$store.dispatch('smallClass/updateArStatus', !arOpen.value)
      proxy.$bus.$emit('showEffect')
    },
    300,
    { leading: true, trailing: false }
  )

  onMounted(() => {
    proxy.$bus.$on('forbid_video_sticker', forbidVideoStickerHandle)
  })
  onUnmounted(() => {
    proxy.$bus.$off('forbid_video_sticker', forbidVideoStickerHandle)
  })

  /**
   * 日志上报
   */
  function sendLogger(msg) {
    logger.send({
      tag: 'SwitchSticker',
      content: {
        msg: msg
      }
    })
  }
  return {
    forbidVideoStickerHandle,
    arChange,
    arOpen,
    hasStickerSwitch,
    forbidVideoSticker
  }
}
