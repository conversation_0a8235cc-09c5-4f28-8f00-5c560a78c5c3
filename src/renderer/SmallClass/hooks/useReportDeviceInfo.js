import { pushStudentDeviceInfo } from 'api/classroom/index'
export function useReportDeviceInfo(baseData) {
  // @log-ignore
  // 上报硬件信息
  let reportChannel = 'enterroom'
  let workAreaSize = ''

  const pushStudentDeviceInfoHandle = () => {
    // 上传位置 enterroom quitroom
    let params = {
      planId: baseData.commonOption.planId,
      // 屏幕分辨率
      resolution: workAreaSize,
      // 频道信息
      ircName: baseData?.configs?.stuIrcId,
      ircChannel: baseData?.configs?.ircServer?.ircLocation,
      // 上传位置 enterroom quitroom
      reportChannel: reportChannel,
      extra: ''
    }
    pushStudentDeviceInfo(params)
      .then(res => {
        console.log(res)
        if (res.code !== 0) {
          console.error('pushStudentDeviceInfo error', res)
        }
      })
      .catch(err => {
        console.error('pushStudentDeviceInfo error', err)
      })
  }
  window.thinkApi.ipc.on('application:work-area-size-reply', (event, windowWidth, windowHeight) => {
    workAreaSize = `${windowWidth}*${windowHeight}`
    pushStudentDeviceInfoHandle()
  })

  window.thinkApi.ipc.send('application:work-area-size') //渲染完毕给主进程发送获取分辨率消息
  const leaveReportDeviceInfo = () => {
    reportChannel = 'quitroom'
    window.thinkApi.ipc.send('application:work-area-size')
  }
  return leaveReportDeviceInfo
}
