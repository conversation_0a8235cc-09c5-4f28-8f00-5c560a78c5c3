import { getCurrentInstance, computed } from '@vue/composition-api'
import logger from 'utils/logger'
const sendLogger = (msg, level = 'info', params = {}) => {
  logger.send({
    tag: 'videoGroup',
    level,
    content: {
      msg,
      params
    }
  })
}
export function useRemoteAudioStatus() {
  const { proxy } = getCurrentInstance()
  const studentList = computed(() => proxy.$store.state.smallClass.studentList)
  const isParent = computed(() => proxy.$store.state.smallClass.baseData.commonOption.isParent)
  const selfId = computed(() => proxy.$store.state.smallClass.baseData.commonOption.stuId)
  const interactionStatus = computed(() => proxy.$store.state.smallClass.interactionStatus)
  const videoMicLinkUsers = computed(() => proxy.$store.state.smallClass.videoMicLinkUsers)
  const privateChatInfo = computed(() => proxy.$store.state.smallClass.privateChatInfo)

  const remoteAudioStatus = computed(() => proxy.$store.state.smallClass.remoteAudioStatus)
  const canShowOtherChild = computed(() => proxy.$store.state.smallClass.canShowOtherChild)
  const audioClose = thinkClass => {
    // @log-ignore
    studentList.value.forEach(item => {
      // 上台学员不静音 || 家长旁听自己孩子不静音
      if (
        (interactionStatus.value.mult_video_mic &&
          videoMicLinkUsers.value.includes(Number(item.userId))) ||
        (isParent.value && item.userId == selfId.value)
        // todo 家长是否在学生列表里
      ) {
        sendLogger('关闭互听不静音的学员', 'warn', {
          interactionStatus: interactionStatus.value,
          videoMicLinkUsers: videoMicLinkUsers.value,
          stuInfo: item,
          isParent: isParent.value,
          selfId: selfId.value
        })
        return
      }
      // 其他学员静音处理
      thinkClass.RtcService.muteRemoteAudio(item.userId, true)
    })
  }
  const audioOpen = thinkClass => {
    const { pub, userId } = privateChatInfo.value
    if (pub) {
      if (selfId.value == userId) {
        sendLogger(`当前用户是私聊用户，那不响应互听`, 'warn')
      } else {
        studentList.value.forEach(item => {
          const isMute = item.userId == userId
          thinkClass.RtcService.muteRemoteAudio(item.userId, isMute)
        })
      }
    } else {
      studentList.value.forEach(item => {
        thinkClass.RtcService.muteRemoteAudio(item.userId, false)
      })
    }
  }
  const listenRemoteAudioEvent = thinkClass => {
    // @log-ignore
    proxy.$bus.$on('remoteAudioStatus', status => {
      if (isParent.value && !canShowOtherChild.value) {
        sendLogger(`家长旁听不能看到其他学生, 不响应互听`, 'warn')
        return
      }
      proxy.$store.dispatch('smallClass/updateRemoteAudioStatus', status)

      if (status) {
        audioOpen(thinkClass)
      } else {
        audioClose(thinkClass)
      }
      sendLogger(`收到语音管理状态变化, status: ${status}`)
    })
    // 本地加入房间后触发，默认静音其他人
    proxy.$bus.$on('closeStudentAudio', () => {
      if (!remoteAudioStatus.value) {
        sendLogger('进课默认静音其他人')
        audioClose(thinkClass)
      } else {
        sendLogger('irc已先触发开启语音', 'warn')
      }
    })
  }

  return {
    listenRemoteAudioEvent
  }
}
