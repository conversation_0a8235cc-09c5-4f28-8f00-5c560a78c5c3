import ThinkClass from 'core/ThinkClass'
import { ref } from '@vue/composition-api'
import Vue from 'vue'

export function useThinkClass() {
  let thinkClass = null
  const thinkClassReady = ref(false)
  function initThinkClass(baseData, enableBeauty) {
    // @log-ignore
    const { configs, stuInfo, planInfo, commonOption } = baseData
    thinkClass = new ThinkClass({
      planId: planInfo.id, // 课程ID
      businessId: configs.liveTypeId, // 业务ID
      userId: stuInfo.id.toString(), // 学生ID
      rtcConfig: configs.rtcConfig, // RTC配置, 接口下发
      isAudition: commonOption.isAudition, // 是否旁听身份
      // IRC配置
      ircOptions: {
        appId: configs.appId,
        appKey: configs.appKey,
        nickname: configs.stuIrcId,
        ircRooms: configs.ircRooms,
        location: configs.ircServer.location,
        confService: configs.ircServer.confService,
        logService: configs.ircServer.logService
      },
      enableBeauty: enableBeauty && !commonOption.isAudition
    })
    return new Promise(resolve => {
      thinkClass.initService().then(() => {
        Vue.prototype.thinkClass = thinkClass
        thinkClassReady.value = true
        resolve()
      })
    })
  }
  function thinkClassLogin() {
    thinkClass.RtcService.on('localJoinChannel', () => {
      if (thinkClass.ircStatus === 'success') {
        thinkClass.ircLogin()
      } else {
        console.error('irc初始化失败')
      }
    })
    thinkClass.rtcLogin()
  }
  function releaseThinkClass() {
    thinkClass.release()
  }

  return {
    initThinkClass,
    releaseThinkClass,
    thinkClassLogin,
    thinkClassReady
  }
}
