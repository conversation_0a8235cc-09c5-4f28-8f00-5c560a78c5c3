import { onMounted, onBeforeUnmount } from '@vue/composition-api'
import logger from 'utils/logger'

export function useLogger(tag = 'smallClass') {
  onMounted(() => {
    // @log-ignore
    logger.send({
      tag: tag,
      content: {
        msg: `${tag}-onMounted挂载`
      },
      level: 'info'
    })
  })
  onBeforeUnmount(() => {
    // @log-ignore
    logger.send({
      tag: tag,
      content: {
        msg: `${tag}-onBeforeUnmount销毁`
      },
      level: 'info'
    })
  })
  function sendLogger(msg, params = {}, level = 'info') {
    // @log-ignore
    logger.send({
      tag: tag,
      content: {
        msg: `${tag}-${msg}`,
        params
      },
      level
    })
  }
  return {
    sendLogger
  }
}
