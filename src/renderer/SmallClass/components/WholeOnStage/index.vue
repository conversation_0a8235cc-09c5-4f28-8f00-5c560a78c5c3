<template>
  <div>
    <div class="header">
      <div class="wrapper" data-log="header">
        <div class="drag-bar" :class="platform"></div>
        <div class="title" style="color: #fff">
          <span class="plan-name" v-if="baseData.planInfo.name">
            {{ baseData.planInfo.name }}
          </span>
        </div>
        <div class="operation-wrapper">
          <div class="operation-buttons">
            <div class="item">
              <div class="button button-exit" @click="handleExit" data-log="课中退出按钮"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <WholeOnStageContainer />
  </div>
</template>
<script>
import { defineComponent, getCurrentInstance } from '@vue/composition-api'
import WholeOnStageContainer from './WholeOnStageContainer'
import { useExit } from '../../hooks/useExit'

export default defineComponent({
  name: 'WholeOnStage',
  components: {
    WholeOnStageContainer
  },
  setup() {
    const { proxy } = getCurrentInstance()
    const baseData = proxy.$store.state.smallClass.baseData
    const { handleExit } = useExit(baseData.planInfo)

    return {
      baseData,
      handleExit
    }
  }
})
</script>
<style scoped lang="scss">
.header {
  background: #ffaa0a;
  .wrapper {
    position: relative;
    height: 44px;
    display: flex;
    align-items: center;
  }
  .drag-bar {
    -webkit-app-region: drag;
    position: absolute;
    z-index: -1;
    height: 44px;
    &.mac {
      left: 0;
      right: 0;
    }
    &.win {
      right: 50%;
      left: 50%;
    }
  }
  .title {
    width: 100%;
    height: 44px;
    line-height: 44px;
    font-size: 16px;
    color: #dee2e7;
    font-weight: 500;
    text-align: center;
  }
  .windows-exit {
    position: absolute;
    left: 0;
    left: 12px;
    .button-exit {
      transform: rotate(180deg);
    }
  }
  .operation-wrapper {
    position: absolute;
    right: 12px;
    display: flex;
    align-items: center;
  }
  .windows-action-bar-wrapper {
    margin: 0 0 0 40px;
  }
  .operation-buttons {
    display: flex;
    .item {
      margin-left: 20px;
    }
  }
  .button {
    width: 30px;
    height: 30px;
    background-size: cover;
    cursor: pointer;
  }
  .button-exit {
    background-image: url('~assets/images/live/icon-exit.png');
    &:hover {
      background-image: url('~assets/images/live/icon-exit-hover.png');
    }
  }
}
</style>
