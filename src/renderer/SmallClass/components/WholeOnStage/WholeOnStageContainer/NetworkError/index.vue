<template>
  <div class="network-error-wrapper" id="networkError"></div>
</template>

<script>
import logger from 'utils/logger'
import { route_href } from 'utils/routeUtil'
export default {
  data() {
    return {
      isShow: false
    }
  },
  mounted() {
    this.thinkClass.SignalService.on('showNotification', this.showNotice)
    this.thinkClass.SignalService.on('closeNotification', this.closeNotice)
  },
  beforeD<PERSON><PERSON>() {
    this.thinkClass.SignalService.off('showNotification', this.showNotice)
    this.thinkClass.SignalService.off('closeNotification', this.closeNotice)
  },
  methods: {
    showNotice(code) {
      if (this.isShow) return
      const networkError = this.$t('classroom.modules.networkError')
      this.isShow = true
      this.$Notification.open({
        key: 'networkError',
        class: 'notification-network-error',
        placement: 'bottomRight',
        bottom: '0px',
        duration: null,
        description: () => {
          return (
            <div>
              <i class="netError"></i>
              <div class="description">
                {networkError.notice[0]} <br /> {`${networkError.notice[1]},code:${code}`}
              </div>
            </div>
          )
        },
        btn: () => {
          return (
            <a-button class="ant-btn-primary" shape="round">
              {this.$t('common.exit')}
            </a-button>
          )
        },
        getContainer: () => {
          return document.getElementById('networkError')
        },
        onClick: () => {
          this.sendLogger('click NetworkError')
          const curBackUrl = window.location.href.split('backUrl=')[1] || '/home'

          route_href('/#' + curBackUrl)
        }
      })
    },
    closeNotice() {
      this.isShow = false
      this.$Notification.destroy()
    },
    sendLogger(msg) {
      logger.send({
        tag: 'Net',
        content: {
          msg: msg
        }
      })
    }
  }
}
</script>

<style lang="scss">
.network-error-wrapper {
  position: absolute;
  width: 100%;
  height: auto;
  top: 0;
  z-index: 1001;
  // Notification样式位置重置
  .ant-notification {
    width: 400px;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 9999;
  }
  // 网络错误消息提示
  .notification-network-error {
    .netError {
      width: 32px;
      height: 32px;
      background: url(~assets/images/icon_nowifi_weak.png) no-repeat center center;
      background-size: 100%;
      display: inline-block;
      position: absolute;
      top: 50%;
      left: 16px;
      transform: translate(0, -50%);
    }
    .netBad {
      width: 32px;
      height: 32px;
      background: url(~assets/images/icon_badwifi_weak.png) no-repeat center center;
      background-size: 100%;
      display: inline-block;
      position: absolute;
      top: 50%;
      left: 16px;
      transform: translate(0, -50%);
    }
    .description {
      padding: 0 50px;
    }
    .ant-notification-notice-btn {
      position: absolute;
      float: none;
      top: 50%;
      right: 16px;
      transform: translate(0, -50%);
      margin-top: 0;
    }
    .ant-notification-notice-message,
    .ant-notification-notice-close {
      display: none;
    }
  }
}
</style>
