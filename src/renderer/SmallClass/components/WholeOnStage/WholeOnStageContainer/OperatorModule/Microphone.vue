<template>
  <div class="allOnStage-microphone" data-log="静音" @click="handleMicrophoneStatusClick">
    <div class="microphone-inner">
      <div class="icon" data-log="静音图标">
        <AudioWaves
          microphoneStyle="allOnStage"
          :volumeValue="localVolumeValue"
          :microphoneHeight="26"
          :microphoneStatus="microphoneStatus"
          offMicrophoneStyle="allOnStage"
        />
      </div>
    </div>
    <span
      class="text"
      :class="[microphoneStatus ? 'textUnmute' : 'textMute', forbidClickMute ? 'disabled' : '']"
      :data-log="
        microphoneStatus
          ? $t('classroom.smallClass.microphone.buttonName')[0]
          : $t('classroom.smallClass.microphone.buttonName')[1]
      "
      >{{
        microphoneStatus
          ? $t('classroom.smallClass.microphone.buttonName')[0]
          : $t('classroom.smallClass.microphone.buttonName')[1]
      }}</span
    >
    <MediaSecurityAccess ref="MediaSecurityAccessMicrophone" :visible="false" type="microphone" />
  </div>
</template>

<script>
import AudioWaves from './AudioWaves'
import MediaSecurityAccess from 'components/Common/MediaSecurityAccess'
import { getMicrophoneStatus } from 'utils/mediaAccess'
import { getMute } from 'api/classroom'
import logger from 'utils/logger'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import _debounce from 'lodash/debounce'

export default {
  name: 'Microphone',
  components: {
    AudioWaves,
    MediaSecurityAccess
  },
  data() {
    return {
      baseData: this.$store.state.smallClass.baseData,
      localVolumeValue: 0,
      // 老师静音时是否允许学生开启麦克风
      allowOpenMicrophone: null,
      // 老师授权打开麦克风状态
      teacherAllowConfirmStatus: false,
      // 禁止学员关麦状态
      forbidMuteAudio: false
    }
  },
  computed: {
    // 麦克风开启状态
    microphoneStatus() {
      return this.$store.state.smallClass.microphoneStatus
    },
    // 能否静音
    forbidClickMute() {
      return this.forbidMuteAudio && this.microphoneStatus
    }
  },
  mounted() {
    // this.queryMuteStatus()
    this.listenerEvent()
    this.listenerRtcService()
    this.listenerSignalService()
  },
  methods: {
    /**
     * 老师授权打开麦克风状态
     */
    teacherAllowMicrophone() {
      this.teacherAllowConfirmStatus = false
    },
    localVolume(volume) {
      // @log-ignore
      this.localVolumeValue = volume
    },

    // irc kv消息监听
    async kvHandle(options) {
      // @log-ignore
      const { key, noticeContent } = options
      if (key === 'allow_open_microphone') {
        // 静音时是否允许学生开启麦克风
        const allowOpenMicrophone = noticeContent.pub
        this.allowOpenMicrophone = allowOpenMicrophone
        this.sendLogger(`静音时是否允许学生开启麦克风，res: ${JSON.stringify(noticeContent)}`)
      } else if (key === 'forbid_mute_audio') {
        // 禁止学员关麦
        this.forbidMuteAudio = noticeContent.pub == 1 ? true : false
        if (this.forbidMuteAudio) {
          const microphoneAccessStatus = await getMicrophoneStatus()
          // 校验麦克风权限
          if (!microphoneAccessStatus) {
            return
          }
          this.updateMicrophoneStatus(true)
        }
        this.sendLogger(`是否禁止学生关闭麦克风，res: ${JSON.stringify(noticeContent)}`)
      }
    },
    // 群聊消息
    recvRoomMessage(res) {
      const { ircType, data } = res.content
      // 老师控制全员麦克风开关
      if (ircType === 'all_audio_mute') {
        const status = !data.pub
        if (status) {
          if (this.microphoneStatus) return
          this.teacherAllowConfirmStatus = true
          this.$bus.$emit('showMicrophoneAllowConfirm')
        } else {
          if (!this.microphoneStatus) return
          this.handleMicrophoneStatus(status)
        }
        this.sendLogger(
          `老师控制全员麦克风${status ? '打开' : '关闭'}，res: ${JSON.stringify(res)}`
        )
      }
    },
    // irc 私人消息
    recvPeerMessage(res) {
      const { ircType, data } = res.content

      // 老师控制某个学生麦克风开关
      if (ircType === 'user_audio_mute') {
        const status = !data.pub
        this.sendLogger(
          `老师控制单独学生麦克风${status ? '打开' : '关闭'}，res: ${JSON.stringify(
            res
          )},microphoneStatus: ${this.microphoneStatus}`
        )
        if (status) {
          if (this.microphoneStatus) return
          this.teacherAllowConfirmStatus = true
          this.$bus.$emit('showMicrophoneAllowConfirm')
        } else {
          if (!this.microphoneStatus) return
          this.handleMicrophoneStatus(status)
        }
      }
    },
    /**
     * 监听Vue事件
     */
    listenerEvent() {
      // 监听麦克风开启事件
      this.$bus.$on('openMicrophone', this.handleMicrophoneStatus)
      // 授权窗口关闭
      this.$bus.$on('closeMicrophoneAllowConfirm', this.teacherAllowMicrophone)
    },
    /**
     * 监听RTC消息
     */
    listenerRtcService() {
      const RtcService = this.thinkClass.RtcService
      // 监听本地音量
      RtcService.on('localAudioVolume', this.localVolume)
    },
    // 监听信令消息
    listenerSignalService() {
      const SignalService = this.thinkClass.SignalService
      // 监听kv消息
      SignalService.on('onRecvRoomDataUpdateNotice', this.kvHandle)
      // 监听群聊消息
      SignalService.on('onRecvRoomMessage', this.recvRoomMessage)
      // 监听私聊消息
      SignalService.on('onRecvPeerMessage', this.recvPeerMessage)
    },
    removeAllListener() {
      this.$bus.$off('openMicrophone', this.handleMicrophoneStatus)
      this.$bus.$off('closeMicrophoneAllowConfirm', this.teacherAllowMicrophone)
      const RtcService = this.thinkClass.RtcService
      RtcService.off('localAudioVolume', this.localVolume)
      const SignalService = this.thinkClass.SignalService
      SignalService.off('onRecvRoomDataUpdateNotice', this.kvHandle)
      SignalService.off('onRecvRoomMessage', this.recvRoomMessage)
      SignalService.off('onRecvPeerMessage', this.recvPeerMessage)
    },
    /**
     * 处理麦克风状态
     */
    handleMicrophoneStatusClick: _debounce(function(status) {
      this.handleMicrophoneStatus(status)
    }, 300),
    async handleMicrophoneStatus(status) {
      // 是否为点击触发
      let isClick = typeof status !== 'boolean'
      const microphoneAccessStatus = await getMicrophoneStatus()
      // 校验麦克风权限
      if (!microphoneAccessStatus) {
        this.$refs['MediaSecurityAccessMicrophone'].checkAccess()
        return
      }
      const microphoneStatus = isClick ? !this.microphoneStatus : status
      // 如果学员在台上，可以直接打开麦克风，并且初始上台自动开麦 或 老师授权打开麦克风
      if (microphoneStatus && this.teacherAllowConfirmStatus) {
        this.updateMicrophoneStatus(microphoneStatus)
        return
      }
      // 校验老师静音时是否允许学生开启麦克风开关
      if (
        !this.teacherAllowConfirmStatus &&
        microphoneStatus &&
        this.allowOpenMicrophone === false
      ) {
        // 弹窗显示禁止开麦
        this.$bus.$emit('showTeacherDisabledMicrophoneNotice', 'on')
        return
      }
      this.updateMicrophoneStatus(microphoneStatus)
      classLiveSensor.osta_rtc_audio_change({ microphoneStatus })
    },

    /**
     * 更新麦克风状态
     */
    updateMicrophoneStatus(microphoneStatus) {
      this.$store.dispatch('smallClass/updateMicrophoneStatus', microphoneStatus)
      this.thinkClass.RtcService.muteLocalAudio(!microphoneStatus)
    },

    /**
     * 查询静音状态
     * 教师端开关: 学生进入课堂静音开关
     */
    async queryMuteStatus() {
      const res = await getMute({
        planId: this.baseData.planInfo.id
      })
      if (!res || res.code != 0) {
        console.info('进入课堂默认静音接口返回异常', res)
        return
      }
      const { data } = res
      const enterRoomMute = data.enterRoomMute
      // 静音状态
      if (enterRoomMute == 1) {
        this.handleMicrophoneStatus(false)
      }
      this.sendLogger(
        `学生进入课堂静音开关，状态: ${enterRoomMute == 1 ? '静音' : '不静音'},
        res: ${JSON.stringify(res)}`
      )
    },
    /**
     * 日志上报
     */
    sendLogger(msg) {
      logger.send({
        tag: 'audioMute',
        content: {
          msg: msg
        }
      })
    }
  },
  beforeDestroy() {
    this.removeAllListener()
  }
}
</script>
<style scoped lang="scss">
// 全员上台
.allOnStage-microphone {
  display: flex;
  align-items: center;
  cursor: pointer;

  .microphone-inner {
    width: 36px;
    height: 36px;
    background: #fff3dc;
    border-radius: 8px;
    display: flex;
    align-items: center;
    padding-left: 4px;
    cursor: pointer;
  }

  .text {
    font-size: 14px;
    font-weight: 600;
    line-height: 16px;
    margin-left: 14px;

    &.textUnmute {
      color: #ffaa0a;
    }

    &.textMute {
      color: #ffaa0a;
    }
  }
}
</style>
