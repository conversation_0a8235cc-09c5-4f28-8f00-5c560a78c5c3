<template>
  <div class="operator">
    <div class="operator-container">
      <div class="microphone">
        <MicroPhone />
      </div>
      <div class="camera">
        <SwitchCamera />
      </div>
      <div class="sticker" v-if="hasStickerSwitch">
        <SwitchSticker />
      </div>
      <div class="raise-hand">
        <SendEmoji />
      </div>
    </div>
    <section class="allow-confirm-tip">
      <MicrophoneAllowConfirm :isAllOnStage="true" ref="MicrophoneAllowConfirm" />
      <CameraAllowConfirm :isAllOnStage="true" ref="CameraAllowConfirm" />
      <TeacherDisabledMicrophoneNotice :isAllOnStage="true" ref="TeacherDisabledMicrophoneNotice" />
      <LiveKickout ref="LiveKickout" :options="options" />
    </section>
  </div>
</template>

<script>
import MicroPhone from './Microphone.vue'
import SwitchCamera from './Camera/SwitchCamera.vue'
import SwitchSticker from './SwitchSticker'
import SendEmoji from './SendEmoji'
// // 麦克风允许提示
import MicrophoneAllowConfirm from './MicrophoneAllowConfirm'
// // 摄像头允许提示
import CameraAllowConfirm from '../../../LayerFloat/CameraAllowConfirm'
// // 老师静音时不允许学员 打开麦克风
import TeacherDisabledMicrophoneNotice from '../../../LayerFloat/TeacherDisabledMicrophoneNotice'
// // IRC踢出提示
import LiveKickout from '../../../liveKickout'
import { useStickerStatus } from '../../../../hooks/useStickerStatus'

export default {
  components: {
    MicroPhone,
    SwitchCamera,
    SwitchSticker,
    SendEmoji,
    MicrophoneAllowConfirm,
    CameraAllowConfirm,
    TeacherDisabledMicrophoneNotice,
    LiveKickout
  },
  computed: {
    options() {
      return this.$store.state.smallClass.baseData.commonOption
    }
  },
  setup() {
    const { hasStickerSwitch } = useStickerStatus()
    return {
      hasStickerSwitch
    }
  }
}
</script>

<style lang="scss" scoped>
.operator {
  position: relative;
  width: 100%;

  .operator-container {
    display: flex;
    justify-content: center;
    position: relative;

    .microphone {
      width: 180px;
      display: flex;
    }

    .sticker {
      width: 180px;
      &::before {
        content: '';
        display: block;
        position: absolute;
        width: 2px;
        height: 14px;
        background: #ffaa0a;
        border-radius: 1px;
        margin-top: 11px;
      }
    }
    .camera {
      width: 180px;

      &::before {
        content: '';
        display: block;
        position: absolute;
        width: 2px;
        height: 14px;
        background: #ffaa0a;
        border-radius: 1px;
        margin-top: 11px;
      }
    }

    .raise-hand {
      width: 206px;

      &::before {
        content: '';
        display: block;
        position: absolute;
        width: 2px;
        height: 14px;
        background: #ffaa0a;
        border-radius: 1px;
        margin-top: 11px;
      }
    }
  }

  .allow-confirm-tip {
    position: absolute;
    left: 50%;
    margin-left: -180px;
    width: 360px;
    z-index: 20;
    top: 2px;
  }
}
</style>
