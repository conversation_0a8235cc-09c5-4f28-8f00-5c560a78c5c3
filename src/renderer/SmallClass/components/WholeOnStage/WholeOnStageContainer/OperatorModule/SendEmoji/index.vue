<template>
  <div class="allOnStage-emoji-container">
    <div v-if="showForbiddenTip" class="forbiddenTips">
      {{ $t('classroom.smallClass.sendEmoji.forbiddenSendMessage') }}
    </div>
    <div @click="openEmojiPane" class="emoji-operator" id="emoji-operator">
      <div class="icon" :class="isSelected ? 'selected' : ''">
        <span :class="isSelected ? 'selected' : 'normal'"></span>
      </div>
      <span class="text" :class="isSelected ? 'selected' : 'normal'">
        {{ $t('classroom.smallClass.sendEmoji.buttonName')[0] }}
      </span>
    </div>
    <EmojiPane
      v-clickoutside="closeEmojiPane"
      ref="emojiPaneRef"
      @handleSendEmoji="handleSendEmoji"
      :allOnStage="true"
      class="emoji-pane allOnStage-emoji-pane"
      :style="{ left: left + 'px' }"
    />
  </div>
</template>

<script>
import EmojiPane from '@/SmallClass/components/LayerFloat/EmojiPane'
import Clickoutside from 'utils/clickoutside'
import { openEmojiBurryPoint } from 'utils/emojiUtils'
export default {
  name: 'Emojis',
  components: {
    EmojiPane
  },
  data() {
    return {
      isSelected: false,
      showProgress: false,
      progressTime: 300,
      countdownTime: 30,
      forbiddenTimer: null, // 次数限制倒计时
      // contDownOnstage: 30,
      showCountDown: false,
      firstSendEmojiTime: null, // 记录第一次发表情的时间
      sendEmojiCount: 0, // 表情发送次数
      isOpenPane: false,
      progressTextColor: '#FF850A',
      emojiForbiddenStatus: false, // 表情是否是禁用状态
      showForbiddenTip: false,
      left: 0
    }
  },
  computed: {
    commonOptions() {
      return this.$store.state.smallClass.baseData.commonOption
    },
    packageId() {
      return this.$store.state.smallClass.baseData.planInfo.packageId
    },
    courseWareBounce() {
      return this.$store.state.smallClass.coursesPosition
    },
    emojiStatusStyle() {
      return {
        forbidden: this.emojiForbiddenStatus,
        selected: this.isSelected,
        disabled: this.showProgress
      }
    }
  },
  directives: {
    Clickoutside
  },
  mounted() {
    this.bindEvents()
  },
  methods: {
    // 打开表情面板
    openEmojiPane() {
      // 当前面板非打开状态 点击打开时埋点
      if (!this.isSelected) {
        // 表情面板打开埋点
        openEmojiBurryPoint(this.commonOptions, true, this.packageId)
      }
      this.isSelected = true
      if (document.querySelector('.courseware-board-wrapper')) {
        this.left = document.querySelector('.courseware-board-wrapper').getBoundingClientRect().left
      }
      this.$refs.emojiPaneRef.openEmojiPane()
    },

    closeEmojiPane(e) {
      const wrapperDom = document.querySelector('#emoji-operator')
      const clickDom = wrapperDom == e.target || wrapperDom.contains(e.target)
      if (clickDom && !this.isOpenPane) {
        this.isOpenPane = true
        return
      }
      this.handleClosePane()
    },
    // close pane 关闭面板
    handleClosePane() {
      this.$refs.emojiPaneRef.closeEmojiPane()
      this.isOpenPane = this.isSelected = false
    },
    //为了防止用户刷屏，如果用户最近3次的表情发送总间隔时间小于30s，则此按钮进入持续30s的冷却状态，
    handleSendEmoji() {
      this.isSelected = false
      this.isOpenPane = false
      this.sendEmojiCount += 1
      if (this.sendEmojiCount == 1) {
        this.firstSendEmojiTime = new Date().getTime()
      } else {
        if (this.sendEmojiCount == 3) {
          const delay = Math.floor(new Date().getTime() - this.firstSendEmojiTime) / 1000
          if (delay <= 30) {
            this.showProgress = true
            this.forbiddenTimer = window.setTimeout(() => {
              this.getResetTimer()
            }, 30000)
          } else {
            this.sendEmojiCount = 1
            this.firstSendEmojiTime = new Date().getTime()
          }
        }
      }
    },
    bindEvents() {
      this.$bus.$on('forbidden_student_emoji', this.getEmojiStatus)
    },
    getEmojiStatus(params) {
      this.emojiForbiddenStatus = params?.pub
      if (params?.pub && this.forbiddenTimer) {
        // 如果老师禁止发送表情 冷却重置
        this.clearLimitEmojiTimer()
        this.getResetTimer()
      }
    },
    // 重置倒计时方法
    getResetTimer() {
      this.sendEmojiCount = 0
      this.firstSendEmojiTime = new Date().getTime()
      this.showProgress = false
    },
    // 清除倒计时
    clearLimitEmojiTimer() {
      window.clearTimeout(this.forbiddenTimer)
      this.forbiddenTimer = null
    }
  },
  beforeDestroy() {
    this.$bus.$off('forbidden_student_emoji')
    this.clearLimitEmojiTimer()
  }
}
</script>
<style scoped lang="scss">
.sidebar-emoji-container {
  position: relative;
  cursor: pointer;
  .forbiddenTips {
    position: absolute;
    width: 119px;
    height: 26px;
    background: rgba(15, 25, 42, 0.9);
    border-radius: 15px;
    text-align: center;
    line-height: 26px;
    // font-weight: 500;
    font-size: 12px;
    color: #ffffff;
    top: -35px;
    &::after {
      content: '';
      display: inline-block;
      width: 0;
      height: 0;
      border: 8px solid transparent;
      border-top: 8px solid rgba(15, 25, 42, 0.9);
      position: absolute;
      bottom: -14px;
      stroke-width: 30;
      left: 52px;
    }
  }
  .emoji-pane {
    position: fixed;
    z-index: 1003;
    bottom: 0px;
    margin: auto;
  }
  .emoji-operator {
    width: 125px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 100px;
    display: flex;
    align-items: center;
    padding-left: 4px;
    position: relative;
    .icon {
      width: 32px;
      height: 32px;
      position: relative;
      z-index: 2;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      background: #2c2c2c;
      margin-right: 10px;
      span {
        width: 20px;
        height: 20px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: cover;
        &.normal {
          background-image: url('./imgs/icon_emoji_normal.png');
        }
        &.selected {
          background-image: url('./imgs/icon_emoji_selected.png');
        }
        &.disabled {
          background-image: url('./imgs/icon_emoji_disabled.png');
        }
        &.forbidden {
          background-image: url('./imgs/icon_emoji_forbidden.png');
        }
      }
    }
    .count-down-container {
      width: 86px;
      position: absolute;
      left: 32px;
      z-index: 0;
    }
    .text {
      font-size: 14px;
      font-weight: 600;
      line-height: 16px;
      &.selected {
        color: rgba(255, 170, 10, 1);
      }
      &.normal {
        color: #a2aab8;
      }
    }
  }
}
// 多人上台
.allOnStage-emoji-container {
  cursor: pointer;
  .emoji-pane {
    width: 854px;
    height: 256px;
    position: absolute;
    z-index: 1003;
    bottom: 44px;
    left: 50% !important;
    transform: translateX(calc(-50% - 10px));
  }
  .emoji-operator {
    // width: 125px;
    // height: 40px;
    padding-left: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 100px;
    display: flex;
    align-items: center;
    // padding-left: 4px;
    position: relative;
    // text-align: center;
    // justify-content: center;
    .icon {
      width: 36px;
      height: 36px;
      background: #fff3dc;
      border-radius: 8px;
      position: relative;
      z-index: 2;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 10px;
      &.selected {
        background: #fff;
      }
      span {
        width: 26px;
        height: 26px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: cover;
        &.normal {
          background-image: url('./imgs/icon_emoji_normal.png');
        }
        &.selected {
          background-image: url('./imgs/icon_emoji_selected.png');
        }
        &.disabled {
          background-image: url('./imgs/icon_emoji_disabled.png');
        }
      }
    }
    .onStage-progress {
      // width: 36px;
      // height: 36px;
      // background: #fff1d9;
      margin-right: 8px;
      display: flex;
      align-items: center;
    }
    .count-down-container {
      width: 86px;
      position: absolute;
      height: 22px;
      left: 32px;
      z-index: 0;
    }
    .text {
      font-size: 14px;
      font-weight: 600;
      line-height: 16px;
      &.selected {
        color: rgba(255, 170, 10, 1);
      }
      &.normal {
        color: rgba(255, 170, 10, 1);
      }
    }
  }
  ::v-deep .ant-progress-text {
    color: rgba(255, 170, 10, 1);
  }
  ::v-deep .ant-progress {
    background: rgba(255, 243, 220, 1);
    border-radius: 50%;
    .ant-progress-circle-trail {
      stroke: rgba(255, 243, 220, 1) !important;
    }
  }
}
::v-deep .ant-progress-inner {
  display: block;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 0 100px 100px 0 !important;
}
</style>
