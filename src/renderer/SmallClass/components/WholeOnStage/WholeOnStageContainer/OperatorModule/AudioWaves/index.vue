<template>
  <div :style="{ width: microphoneHeightForVh, height: microphoneHeightForVh }" class="audio-waves">
    <template v-if="microphoneStatus">
      <span
        :style="{ width: microphoneHeightForVh, height: microphoneHeightForVh }"
        class="volume-bottom"
        :class="`bottom-${microphoneStyle}`"
      ></span>
      <span
        :style="{ width: microphoneHeightForVh, height: volumeHeightForVh }"
        class="volume-top"
        :class="`top-${microphoneStyle}`"
      ></span>
    </template>
    <template v-else>
      <span
        :style="{ width: microphoneHeightForVh, height: microphoneHeightForVh }"
        class="volume-closed"
        :class="`${offMicrophoneStyle}_microphone_off`"
      >
      </span>
    </template>
  </div>
</template>

<script>
import { px2vh } from 'utils/util'

export default {
  name: 'AudioWaves',

  data() {
    return {
      volumeMultiple: 1.5,
      volumeHeight: 0
    }
  },
  props: {
    volumeValue: {
      type: Number,
      default: 0
    },
    microphoneHeight: {
      type: Number,
      default: 20
    },
    microphoneStyle: {
      type: String,
      default: ''
    },
    microphoneStatus: {
      type: Boolean,
      default: false
    },
    offMicrophoneStyle: {
      type: String,
      default: 'icon'
    }
  },
  watch: {
    volumeValue: {
      handler(newValue) {
        // @log-ignore
        this.$nextTick(() => {
          // @log-ignore
          const volumeRatio = (newValue * this.volumeMultiple) / 100
          const volumeVavesHeight = Math.floor(this.microphoneHeight * volumeRatio)
          this.volumeHeight =
            volumeVavesHeight >= this.microphoneHeight ? this.microphoneHeight : volumeVavesHeight
        })
      }
    }
  },
  computed: {
    microphoneHeightForVh() {
      // @log-ignore
      return px2vh(this.microphoneHeight)
    },
    volumeHeightForVh() {
      // @log-ignore
      return px2vh(this.volumeHeight)
    }
  },
  mounted() {},
  methods: {}
}
</script>
<style scoped lang="scss">
.audio-waves {
  position: relative;
  overflow: hidden;
  .volume-top,
  .volume-bottom,
  .volume-closed {
    position: absolute;
    display: inline-block;
    background-repeat: no-repeat;
    background-size: cover;
    bottom: 0;
  }
  .volume-bottom {
    &.bottom-bottleGreen {
      background-image: url('./imgs/bottom-bottleGreen.png');
    }
    &.bottom-laurelGreen {
      background-image: url('./imgs/bottom-laurelGreen.png');
    }
    &.bottom-allOnStage {
      background-image: url('./imgs/bottom-allOnStage.png');
    }
  }
  .volume-top {
    background-position: 100% 100%;
    transition: all 0.6s;
    &.top-bottleGreen {
      background-image: url('./imgs/top-bottleGreen.png');
    }
    &.top-laurelGreen {
      background-image: url('./imgs/top-laurelGreen.png');
    }
    &.top-allOnStage {
      background-image: url('./imgs/top-allOnStage.png');
    }
  }
  .icon_microphone_off {
    background-image: url('./imgs/icon_microphone_off.png');
  }
  .allOnStage_microphone_off {
    background-image: url('./imgs/allOnStage_microphone_off.png');
  }
}
</style>
