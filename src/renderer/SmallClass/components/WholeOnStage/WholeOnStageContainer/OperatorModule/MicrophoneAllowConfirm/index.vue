<template>
  <div class="microphone-allow-confirm"></div>
</template>

<script>
import logger from 'utils/logger'
export default {
  name: 'MicrophoneAllowConfirm',
  watch: {
    '$store.state.smallClass.microphoneStatus': {
      handler(val) {
        if (val) {
          this.closeDialog()
        }
      }
    }
  },

  mounted() {
    this.$bus.$on('showMicrophoneAllowConfirm', () => {
      this.handleOk()
    })
  },
  methods: {
    handleOk() {
      this.$bus.$emit('openMicrophone', true)
      this.closeDialog()
      this.sendLogger('学生同意打开麦克风')
    },
    closeDialog() {
      setTimeout(() => {
        this.$bus.$emit('closeMicrophoneAllowConfirm')
      }, 0)
    },
    sendLogger(msg) {
      logger.send({
        tag: 'audioMute',
        content: {
          msg: msg
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.microphone-allow-confirm {
  position: absolute;
  right: 10px;
  bottom: 10px;
}
</style>
