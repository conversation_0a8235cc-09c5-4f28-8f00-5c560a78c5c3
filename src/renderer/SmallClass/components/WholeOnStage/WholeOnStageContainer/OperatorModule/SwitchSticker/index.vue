<template>
  <div class="allOnStage-sticker" @click="arChange">
    <span class="setting-icon">
      <span class="icon">
        <span :class="arOpen ? 'on' : 'off'"></span>
        <div
          v-if="stickerTipToast"
          class="open-sticker-tip disappear-in-3"
          @animationend="stickerTipToast = false"
        >
          <div class="tip">
            {{ $t('classroom.smallClass.visualEffect.openTip') }}
          </div>
        </div>
      </span>
      <span class="text" :class="arOpen ? 'text-active' : ''">
        {{ $t('classroom.smallClass.visualEffect.buttonName') }}
      </span>
    </span>
    <Model
      dialogClass="sticker-forbid got-info"
      :isShowModal="showStickerTipModal"
      :rightBtnText="$t('classroom.smallClass.visualEffect.confirm')"
      :title="$t('classroom.smallClass.visualEffect.unableOpen')"
      :subTitle="$t('classroom.smallClass.visualEffect.teacherClose')"
      :zIndex="9999"
      @rightBtnOperation="showStickerTipModal = false"
    />
  </div>
</template>

<script>
import logger from 'utils/logger'
import Model from 'components/Common/Model'
import _debounce from 'lodash/debounce'

export default {
  name: 'SwitchSticker',
  components: {
    Model
  },
  data() {
    return {
      showStickerTipModal: false,
      stickerTipToast: false
    }
  },
  computed: {
    arOpen() {
      return this.$store.state.smallClass.arOpen
    },
    forbidVideoSticker() {
      return this.$store.state.smallClass.forbidVideoSticker
    }
  },
  beforeDestory() {
    this.$bus.$off('showNotAllowStickerTipModal', this.changeShowStickerTipModal)
    this.$bus.$off('showAllowStickerTipToast', this.changeStickerTipToast)
  },
  mounted() {
    this.$bus.$on('showNotAllowStickerTipModal', this.changeShowStickerTipModal)
    this.$bus.$on('showAllowStickerTipToast', this.changeStickerTipToast)
  },
  methods: {
    changeShowStickerTipModal() {
      this.showStickerTipModal = !this.showStickerTipModal
    },
    changeStickerTipToast() {
      this.stickerTipToast = true
    },
    arChange: _debounce(
      function() {
        this.stickerTipToast = false
        if (this.forbidVideoSticker && !this.arOpen) {
          this.showStickerTipModal = true
          return
        }
        this.$store.dispatch('smallClass/updateArStatus', !this.arOpen)
        this.$bus.$emit('showEffect')
      },
      300,
      {
        leading: true,
        trailing: false
      }
    ),
    /**
     * 日志上报
     */
    sendLogger(msg) {
      logger.send({
        tag: 'SwitchSticker',
        content: {
          msg: msg
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.allOnStage-sticker {
  position: relative;
  align-items: center;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  justify-content: center;
  .setting-icon {
    display: flex;
    align-items: center;
    .icon {
      position: relative;
      width: 36px;
      height: 36px;
      cursor: pointer;
      background: #fff3dc;
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 9px;
      span {
        width: 26px;
        height: 26px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: cover;
        &.on {
          background-image: url('./imgs/effect_on.png');
        }
        &.off {
          background-image: url('./imgs/effect_off.png');
        }
      }
    }
    .text {
      font-size: 14px;
      font-weight: 600;
      color: #ffaa0a;
      &.text-active {
        color: #ffaa0a;
      }
    }
  }
}
::v-deep .ant-switch:not(.ant-switch-checked) {
  background-color: #0f192a;
}
.sticker-forbid {
  .ant-btn[disabled],
  .ant-btn-primary[disabled] {
    color: #fff;
    &::before {
      background: rgba(255, 255, 255, 0.7);
      display: block;
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
    }
  }
}
::v-deep .got-info {
  .bottom-wrapper {
    justify-content: center;
  }
  .left-btn {
    display: none;
  }
}
.open-sticker-tip {
  position: absolute;
  top: -200%;
  left: -180%;
  background: #1a1a1a;
  border-radius: 8px;
  z-index: 999;
  color: #fff;
  & > div {
    padding: 12px 0;
    border-radius: 4px;
    color: #fff;
    font-size: 12px;
    cursor: pointer;
  }
  & > div:not(:last-child) {
    border-bottom: solid 1px rgba(255, 255, 255, 0.1);
  }
  &::before {
    position: absolute;
    top: 100%;
    left: 33.9%;
    content: '';
    width: 8px;
    height: 6px;
    background-image: url('./imgs/arrow-down.png');
    background-size: cover;
  }
}
.open-sticker-tip {
  width: 230px;
  padding: 12px;
  .tip {
    position: relative;
    padding: 0 0 0 32px;
    &::before {
      position: absolute;
      top: 3px;
      left: 0px;
      content: '';
      width: 24px;
      height: 24px;
      background-image: url('./imgs/icon-info.png');
      background-size: cover;
    }
  }
}
@keyframes disappear {
  0% {
    opacity: 0.9;
  }
  80% {
    opacity: 0.9;
  }
  100% {
    opacity: 0;
  }
}

.disappear-in-3 {
  animation-name: disappear;
  animation-duration: 3s;
  animation-fill-mode: forwards;
}
</style>
