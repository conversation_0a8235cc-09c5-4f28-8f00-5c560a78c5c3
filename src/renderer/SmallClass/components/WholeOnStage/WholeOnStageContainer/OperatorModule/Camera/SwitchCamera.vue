<template>
  <div data-log="全员上台视频按钮" class="allOnStage-camera" @click="onStageHandleStatus">
    <span class="setting-icon">
      <span class="icon"><span :class="cameraStatus ? 'on' : 'off'"></span></span>
      <span class="text" :class="cameraStatus ? 'text-active' : ''">
        {{ $t('classroom.smallClass.camera.buttonName') }}
      </span>
    </span>
    <MediaSecurityAccess ref="MediaSecurityAccessCamera" :visible="false" type="camera" />
  </div>
</template>

<script>
import MediaSecurityAccess from 'components/Common/MediaSecurityAccess'
import { getCameraStatus } from 'utils/mediaAccess'
import logger from 'utils/logger'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
export default {
  name: 'SwitchVideo',
  components: {
    MediaSecurityAccess
  },
  data() {
    return {}
  },
  computed: {
    // 摄像头开启状态
    cameraStatus() {
      return this.$store.state.smallClass.cameraStatus
    }
  },

  mounted() {
    this.listenerSignalService()
    this.listenerEvent()
  },
  methods: {
    /**
     * 监听Vue事件
     */
    listenerEvent() {
      // 监听摄像头开启事件
      this.$bus.$on('openCamera', this.handleCameraStatus)
    },
    // 监听信令消息
    listenerSignalService() {
      const SignalService = this.thinkClass.SignalService
      // 监听私聊消息
      SignalService.on('onRecvPeerMessage', this.recvPeerMessage)
    },
    removeAllListener() {
      this.$bus.$off('openCamera', this.handleCameraStatus)
      this.thinkClass.SignalService.off('onRecvPeerMessage', this.recvPeerMessage)
    },
    recvPeerMessage(res) {
      const { ircType, data } = res.content
      // 老师控制某个学生摄像头开关
      if (ircType === 'user_video_mute') {
        const status = !data.pub
        if (status) {
          this.$bus.$emit('showCameraAllowConfirm')
        } else {
          this.handleCameraStatus(status)
        }
        this.sendLogger(
          `老师控制单独学生摄像头${status ? '打开' : '关闭'}，res: ${JSON.stringify(res)}`
        )
      }
    },
    /**
     * 处理摄像头状态
     */
    async handleCameraStatus(status) {
      const cameraAccessStatus = await getCameraStatus()
      if (!cameraAccessStatus) {
        this.$refs['MediaSecurityAccessCamera'].checkAccess()
        this.$store.dispatch('smallClass/updateCameraStatus', false)
        return
      }
      const cameraStatus = typeof status === 'boolean' ? status : !this.cameraStatus
      this.$bus.$emit('updateLocalDisplayVideoStatus', cameraStatus) // 兼容历史大班互动监听事件
      this.$store.dispatch('smallClass/updateCameraStatus', cameraStatus)
      this.$bus.$emit('updateCameraStatus', cameraStatus)
      this.thinkClass.RtcService.muteLocalVideo(!status)
      classLiveSensor.osta_rtc_vidio_change(cameraStatus)
    },
    /**
     * 多人上台处理摄像头状态
     */
    onStageHandleStatus() {
      this.handleCameraStatus(!this.cameraStatus)
    },
    /**
     * 日志上报
     */
    sendLogger(msg) {
      logger.send({
        tag: 'cameraStatus',
        content: {
          msg: msg
        }
      })
    }
  },
  beforeDestroy() {
    this.removeAllListener()
  }
}
</script>
<style scoped lang="scss">
.allOnStage-camera {
  align-items: center;
  cursor: pointer;
  // padding: 0 5px 0 4px;
  display: flex;
  justify-content: space-between;
  justify-content: center;
  .setting-icon {
    display: flex;
    align-items: center;
    .icon {
      width: 36px;
      height: 36px;
      cursor: pointer;
      background: #fff3dc;
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 9px;
      span {
        width: 26px;
        height: 26px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: cover;
        &.on {
          background-image: url('./imgs/camera_on.png');
        }
        &.off {
          background-image: url('./imgs/camera_off.png');
        }
      }
    }
    .text {
      font-size: 14px;
      font-weight: 600;
      color: #ffaa0a;
      &.text-active {
        color: #ffaa0a;
      }
    }
  }
}
::v-deep .ant-switch:not(.ant-switch-checked) {
  background-color: #0f192a;
}
</style>
