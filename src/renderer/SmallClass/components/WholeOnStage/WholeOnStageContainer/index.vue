<template>
  <div class="whole-on-satge-container">
    <div class="whole-on-satge">
      <div class="left-module">
        <TeacherVideo :isAllOnStage="true"></TeacherVideo>
        <ChatBox
          class="chat-box"
          ref="chatboxRef"
          :showHeader="false"
          :isWholeOnsatge="true"
        ></ChatBox>
      </div>
      <div class="right-module">
        <div class="student-video-container">
          <div class="student-video-list">
            <StudentVideo
              class="video-item default-back"
              :isAllOnStage="true"
              :class="{
                'three-line-status': studentList.length <= 9,
                'four-line-status': studentList.length > 9
              }"
            />
            <RemoteVideoItem
              v-for="item in inClassStudentList"
              :key="item.userId"
              :remoteStuInfo="item"
              :remoteAudioStatus="remoteAudioStatus"
              :hideRemoteVideo="false"
              :isAllOnStage="true"
              class="video-item default-back"
              :class="{
                'three-line-status': studentList.length <= 9,
                'four-line-status': studentList.length > 9
              }"
            />
          </div>
        </div>
        <div class="operator-zone">
          <OperatorModule />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ChatBox from '../../ChatBox'
import TeacherVideo from '../../VideoGroup/TeacherVideo'
import StudentVideo from '../../VideoGroup/LocalVideo/StudentVideo.vue'
import RemoteVideoItem from '../../VideoGroup/RemoteVideo/RemoteVideoItem.vue'
import OperatorModule from './OperatorModule/index.vue'
import { ref, defineComponent, computed, getCurrentInstance, onMounted } from '@vue/composition-api'

export default defineComponent({
  name: 'WholeOnStage',
  components: {
    ChatBox,
    TeacherVideo,
    StudentVideo,
    RemoteVideoItem,
    OperatorModule
  },
  setup() {
    const {
      proxy: { $store: store }
    } = getCurrentInstance()

    const studentList = computed(() => store.state.smallClass.studentList)
    const self = computed(() => studentList.value.find(item => item.isSelf))
    const inClassStudentList = computed(() => {
      return studentList.value.filter(item => item.inClass && !item.isSelf)
    })
    const remoteAudioStatus = computed(() => store.state.smallClass.remoteAudioStatus)
    const chatboxRef = ref(null)

    onMounted(() => {
      chatboxRef.value.handleIsShowBox()
    })
    return {
      chatboxRef,
      studentList,
      inClassStudentList,
      remoteAudioStatus,
      self
    }
  }
})
</script>

<style lang="scss" scoped>
.whole-on-satge-container {
  background-image: url('~assets/images/live/beforeLive/bg.png');
  background-repeat: no-repeat;
  background-size: cover;
  width: 100vw;
  height: calc(100vh - 44px);
}

.whole-on-satge {
  width: 100%;
  height: 100%;
  padding: 12px;
  display: flex;

  .left-module {
    width: 26%;
    margin-right: 12px;
    min-width: 300px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    // background: rebeccapurple;
    .teacher-video-wrap {
      width: 100%;
      border-radius: 10px;
      display: flex;
      height: calc(225 / 300 * 100vw / 4 + 0px);
      min-height: 225px; // 设置视频框最小高度
    }
  }

  .right-module {
    width: 73%;
    display: flex;
    flex-direction: column;

    .student-video-container {
      border-radius: 10px 10px 0px 0px;
      height: calc(100vh - 24px - 44px - 50px + 0px);
      // flex: 1;
      background: #ffe7b8;
      display: flex;
      align-content: center;
      min-height: max-content;
      align-items: center;
      padding: 6px;
      .student-video-list {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        height: 100%;
        justify-content: center;
        align-items: center;
        overflow-y: scroll;
        &::-webkit-scrollbar {
          width: 4px;
        }
        &::-webkit-scrollbar-thumb {
          height: 40px;
          background: rgba(0, 0, 0, 0.5);
          border-radius: 2px;
        }
        .video-item {
          position: relative;
          width: 103px;
          height: 103px;
          flex-shrink: 0;
          margin-right: 10px;
          margin-bottom: 6px;
          background-size: 100% 100%;
          overflow: hidden;
          border-radius: 10px;
          transition: all 0.3s linear;
          border: 4px solid #ffe7b8;
          &.default-back {
            background: repeating-linear-gradient(
              -45deg,
              RGBA(255, 220, 154, 1),
              8px,
              RGBA(255, 210, 139, 1) 8px,
              RGBA(255, 210, 139, 1) 16px
            );
          }
        }
        .four-line-status {
          width: 23.5%;
          height: calc(0.75 * 0.75 * 100vw * 0.235 + 0px);
        }
        .three-line-status {
          width: 30%;
          height: calc(0.75 * 0.75 * 100vw * 0.3 + 0px);
        }
      }
    }

    .operator-zone {
      border-radius: 0px 0px 10px 10px;
      height: 50px;
      margin-top: 1px;
      background: #ffe7b8;
      display: flex;
      align-items: center;
    }
  }
}
</style>
