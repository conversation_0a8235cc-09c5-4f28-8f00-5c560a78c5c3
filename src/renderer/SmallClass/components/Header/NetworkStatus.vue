<template>
  <div v-if="networkStatus" class="network-status" :class="networkStatusClass">
    <div class="status-button" @mouseenter="handleMouseenter" @mouseleave="handleMouseleave"></div>
    <div v-if="showStatusPanel" class="status-panel">
      <div class="panel-wrapper">
        <div class="title-wrapper">
          <div class="icon" />
          <div class="title">
            {{ statusConfig.title }}
          </div>
        </div>
        <div v-if="statusConfig.description" class="description">
          {{ statusConfig.description }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      // RTC实例
      rtcengine: null,
      // 是否显示状态面版
      showStatusPanel: false
    }
  },
  computed: {
    // 下行网络质量
    downlinkNetworkQuality() {
      // @log-ignore
      return this.$store.state.smallClass.rtcDownlinkNetworkQuality
    },

    /**
     * 网络状态
     */
    networkStatus() {
      // @log-ignore
      const statusMap = {
        // '0': 'weak',
        1: 'good',
        2: 'good',
        3: 'normal',
        4: 'weak',
        5: 'weak',
        6: 'weak'
      }
      return statusMap[this.downlinkNetworkQuality] || ''
    },

    /**
     * 状态文案配置
     */
    statusConfig() {
      const statusMapConfig = this.$t('classroom.modules.networkStatus.statusMap')
      const statusMap = {
        good: {
          title: statusMapConfig.good.title
        },
        normal: {
          title: statusMapConfig.normal.title
        },
        weak: {
          title: statusMapConfig.weak.title,
          description: statusMapConfig.weak.description
        }
      }
      return statusMap[this.networkStatus] || {}
    },

    /**
     * 网络状态样式
     */
    networkStatusClass() {
      return this.networkStatus ? `status-${this.networkStatus}` : ''
    }
  },
  mounted() {},
  methods: {
    handleMouseenter() {
      this.showStatusPanel = true
    },
    handleMouseleave() {
      this.showStatusPanel = false
    }
  }
}
</script>
<style lang="scss" scoped>
.network-status {
  position: relative;
  z-index: 1;
  &.status-good {
    .status-button {
      background-image: url('./images/icon-wifi-good.png');
    }
    .panel-wrapper {
      min-width: 215px;
    }
    .status-panel {
      .icon {
        background-image: url('./images/icon-wifi-good-tone.png');
      }
      .title {
        color: #02ca8a;
      }
    }
  }
  &.status-normal {
    .status-button {
      background-image: url('./images/icon-wifi-normal.png');
    }
    .panel-wrapper {
      min-width: 225px;
    }
    .status-panel {
      .icon {
        background-image: url('./images/icon-wifi-normal-tone.png');
      }
      .title {
        color: #ffaa0a;
      }
    }
  }
  &.status-weak {
    .status-button {
      background-image: url('./images/icon-wifi-weak.png');
    }
    .panel-wrapper {
      min-width: 225px;
    }
    .status-panel {
      .icon {
        background-image: url('./images/icon-wifi-weak-tone.png');
      }
      .title {
        color: #ff503f;
      }
    }
  }

  .status-button {
    width: 30px;
    height: 30px;
    background-size: cover;
    cursor: pointer;
    margin-left: 20px;
  }
  .status-panel {
    position: absolute;
    top: 38px;
    right: 0;
    white-space: nowrap;
    .panel-wrapper {
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0px 1px 6px 0px rgba(188, 188, 188, 0.4);
    }
    .title-wrapper {
      display: flex;
      align-items: center;
      height: 36px;
      padding: 0 8px;
      background: #fff;
      .icon {
        width: 22px;
        height: 22px;
        margin-right: 8px;
        background-size: cover;
      }
      .title {
        font-size: 14px;
      }
    }
    .description {
      padding: 8px;
      background: #f4f6fa;
      color: #a2aab8;
    }
  }
}
</style>
