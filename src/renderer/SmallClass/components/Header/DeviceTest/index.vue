<template>
  <a-modal
    v-model="visible"
    :width="dialogWidth"
    :maskClosable="false"
    :closable="false"
    :centered="true"
    :title="$t('classroom.modules.deviceTest.dialogTitle')"
    dialogClass="modal-simple"
    :okText="$t('common.confirm')"
    :cancelText="$t('common.cancel')"
    @ok="handleOk"
  >
    <div class="modal-device-test">
      <div class="device-item">
        <div class="device-head">
          <div class="title">
            <span class="color-gray">
              {{ $t('classroom.modules.deviceTest.cameraTitle') }}
            </span>
            {{ cameraStatusInfo.text }}
          </div>
          <div class="icon" :class="cameraStatusInfo.className"></div>
        </div>
        <a-select
          :value="defaultVideoDeviceId"
          :disabled="isMac && cameraStatus === false"
          :default-value="defaultVideoDeviceId"
          :style="{ width: selectWidth }"
          :suffixIcon="suffixIcon"
          @change="handleChangeVideoDevice"
        >
          <a-select-option
            v-for="(item, index) in videoDevices"
            :value="item.deviceid"
            :key="index"
          >
            {{ item.devicename }}
          </a-select-option>
          <template v-slot:suffixIcon>
            <a-icon :component="arrowBottomSvg" />
          </template>
        </a-select>
        <div class="authorize-guide" v-if="isMac && cameraStatus === false">
          {{ $t('classroom.modules.deviceTest.authorizeGuide')[0] }}
          <span @click="handleCameraAccess">
            {{ $t('classroom.modules.deviceTest.authorizeGuide')[1] }}
          </span>
        </div>
      </div>
      <div class="device-item">
        <div class="device-head">
          <div class="title">
            <span class="color-gray">
              {{ $t('classroom.modules.deviceTest.microphoneTitle') }}
            </span>
            {{ microphoneStatusInfo.text }}
          </div>
          <div class="icon" :class="microphoneStatusInfo.className"></div>
        </div>
        <a-select
          :disabled="isMac && microphoneStatus === false"
          :value="defaultAudioRecordingDeviceId"
          :default-value="defaultAudioRecordingDeviceId"
          :suffixIcon="suffixIcon"
          :style="{ width: selectWidth }"
          @change="handleChangeAudioRecordingDevice"
        >
          <a-select-option
            v-for="(item, index) in audioRecordingDevices"
            :value="item.deviceid"
            :key="index"
          >
            {{ item.devicename }}
          </a-select-option>
          <template v-slot:suffixIcon>
            <a-icon :component="arrowBottomSvg" />
          </template>
        </a-select>
        <div class="authorize-guide" v-if="isMac && microphoneStatus === false">
          {{ $t('classroom.modules.deviceTest.authorizeGuide')[0] }}
          <span @click="handleMicrophoneAccess">
            {{ $t('classroom.modules.deviceTest.authorizeGuide')[1] }}
          </span>
        </div>
      </div>
      <div class="device-item">
        <div class="device-head">
          <div class="title">
            <span class="color-gray">
              {{ $t('classroom.modules.deviceTest.audioTitle') }}
            </span>
            {{ audioPlaybackStatusInfo.text }}
          </div>
          <div class="icon" :class="audioPlaybackStatusInfo.className"></div>
        </div>
        <a-select
          :value="defaultAudioPlaybackDeviceId"
          :default-value="defaultAudioPlaybackDeviceId"
          :suffixIcon="suffixIcon"
          :style="{ width: selectWidth }"
          @change="handleChangeAudioPlaybackDevice"
        >
          <a-select-option
            v-for="(item, index) in audioPlaybackDevices"
            :value="item.deviceid"
            :key="index"
          >
            {{ item.devicename }}
          </a-select-option>
          <template v-slot:suffixIcon>
            <a-icon :component="arrowBottomSvg" />
          </template>
        </a-select>
      </div>
    </div>
  </a-modal>
</template>

<script>
import arrowBottomSvg from 'assets/svg/icon-arrow-bottom.svg'
import { isMac, px2vh } from 'utils/util'
import { nativeApi } from 'utils/electronIpc'
import { getMicrophoneStatus, getCameraStatus } from 'utils/mediaAccess'
import {
  setDefaultVideoDevice,
  setDefaultAudioRecordingDevice,
  setDefaultAudioPlaybackDevice
} from 'utils/deviceSettings'
import logger from 'utils/logger'
export default {
  data() {
    return {
      isMac: isMac(),
      arrowBottomSvg,
      visible: false,
      rtcEngine: null,
      videoDevices: [], // 摄像头设备列表
      audioRecordingDevices: [], // 麦克风设备列表
      audioPlaybackDevices: [], // 音频播放设备列表
      defaultVideoDeviceId: '', // 默认摄像头设备ID
      defaultAudioRecordingDeviceId: '', // 默认麦克风设备ID
      defaultAudioPlaybackDeviceId: '', // 默认扬声器设备ID
      cameraStatus: null, // 摄像头权限状态
      microphoneStatus: null // 麦克风权限状态
    }
  },
  computed: {
    dialogWidth() {
      return px2vh(400)
    },
    selectWidth() {
      return px2vh(340)
    },
    // 状态名称
    statusNames() {
      return this.$t('classroom.modules.deviceTest.statusNames')
    },

    /**
     * 摄像头状态信息
     */
    cameraStatusInfo() {
      if (!this.videoDevices.length) {
        return {
          className: 'icon-error',
          text: this.statusNames.disabled
        }
      } else if (this.cameraStatus === true) {
        return {
          className: 'icon-success',
          text: this.statusNames.usable
        }
      } else if (this.cameraStatus === false) {
        return {
          className: 'icon-warn',
          text: this.statusNames.unauthorized
        }
      } else {
        return {}
      }
    },

    /**
     * 麦克风状态信息
     */
    microphoneStatusInfo() {
      if (!this.audioRecordingDevices.length) {
        return {
          className: 'icon-error',
          text: this.statusNames.disabled
        }
      } else if (this.microphoneStatus === true) {
        return {
          className: 'icon-success',
          text: this.statusNames.usable
        }
      } else if (this.microphoneStatus === false) {
        return {
          className: 'icon-warn',
          text: this.statusNames.unauthorized
        }
      } else {
        return {}
      }
    },

    /**
     * 扬声器状态信息
     */
    audioPlaybackStatusInfo() {
      if (!this.audioPlaybackDevices.length) {
        return {
          className: 'icon-error',
          text: this.statusNames.disabled
        }
      } else {
        return {
          className: 'icon-success',
          text: this.statusNames.usable
        }
      }
    }
  },
  mounted() {
    this.$bus.$on('deviceTestShow', () => {
      this.showModal()
    })
  },
  methods: {
    showModal() {
      this.visible = true
      this.rtcEngine = this.thinkClass.RtcService.rtcEngine
      this.getDevices()
      this.getDefaultDevices()
      this.getMediaAccess()
    },
    hideModal() {
      this.visible = false
    },
    async handleOk() {
      await setDefaultVideoDevice(this.defaultVideoDeviceId)
      await setDefaultAudioRecordingDevice(this.defaultAudioRecordingDeviceId)
      await setDefaultAudioPlaybackDevice(this.defaultAudioPlaybackDeviceId)
      this.rtcEngine.setVideoDevice(this.defaultVideoDeviceId)
      this.rtcEngine.setAudioRecordingDevice(this.defaultAudioRecordingDeviceId)
      this.rtcEngine.setAudioPlaybackDevice(this.defaultAudioPlaybackDeviceId)
      this.sendLogger(
        `设置默认设备, 摄像头: ${this.defaultVideoDeviceId}, 麦克风: ${this.defaultAudioRecordingDeviceId}, 扬声器: ${this.defaultAudioPlaybackDeviceId}`
      )
      this.hideModal()
    },

    /**
     * 摄像头设备变更事件
     */
    handleChangeVideoDevice(val) {
      this.defaultVideoDeviceId = val
    },

    /**
     * 麦克风设备变更事件
     */
    handleChangeAudioRecordingDevice(val) {
      this.defaultAudioRecordingDeviceId = val
    },

    /**
     * 扬声器设备变更事件
     */
    handleChangeAudioPlaybackDevice(val) {
      this.defaultAudioPlaybackDeviceId = val
    },

    /**
     * 获取设备列表
     */
    getDevices() {
      const videoDevices = this.rtcEngine.getVideoDevices()
      const audioRecordingDevices = this.rtcEngine.getAudioRecordingDevices()
      const audioPlaybackDevices = this.rtcEngine.getAudioPlaybackDevices()
      this.videoDevices = videoDevices
      this.audioRecordingDevices = audioRecordingDevices
      this.audioPlaybackDevices = audioPlaybackDevices
      this.sendLogger(
        `查询设备列表, 摄像头: ${JSON.stringify(videoDevices)}, 麦克风: ${JSON.stringify(
          audioRecordingDevices
        )}, 扬声器: ${JSON.stringify(audioPlaybackDevices)}`
      )
    },

    /**
     * 获取默认设备
     */
    getDefaultDevices() {
      const defaultVideoDeviceId = this.rtcEngine.getCurrentVideoDevice()
      const defaultAudioRecordingDeviceId = this.rtcEngine.getCurrentAudioRecordingDevice()
      const defaultAudioPlaybackDeviceId = this.rtcEngine.getCurrentAudioPlaybackDevice()
      this.defaultVideoDeviceId = defaultVideoDeviceId
      this.defaultAudioRecordingDeviceId = defaultAudioRecordingDeviceId
      this.defaultAudioPlaybackDeviceId = defaultAudioPlaybackDeviceId
      this.sendLogger(
        `查询当前默认使用设备, 摄像头: ${defaultVideoDeviceId}, 麦克风: ${defaultAudioRecordingDeviceId}, 扬声器: ${defaultAudioPlaybackDeviceId}`
      )
    },

    /**
     * 获取媒体权限
     */
    async getMediaAccess() {
      this.cameraStatus = await getCameraStatus()
      this.microphoneStatus = await getMicrophoneStatus()
    },

    /**
     * 跳转系统摄像头权限设置
     */
    handleCameraAccess() {
      nativeApi.openPreferences('security', 'camera')
    },

    /**
     * 跳转系统麦克风权限设置
     */
    handleMicrophoneAccess() {
      nativeApi.openPreferences('security', 'microphone')
    },

    /**
     * 日志上报
     */
    sendLogger(msg) {
      logger.send({
        tag: 'deviceTest',
        content: {
          msg: msg
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.modal-device-test {
  position: relative;
  width: 340px;
  margin: 30px auto 20px auto;
  .device-item {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .device-head {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .icon {
      width: 14px;
      height: 14px;
      margin-left: 8px;
      background-repeat: no-repeat;
      background-size: cover;
    }
    .icon-success {
      background-image: url('./assets/icon-success.png');
    }
    .icon-error {
      background-image: url('./assets/icon-error.png');
    }
    .icon-warn {
      background-image: url('./assets/icon-warn.png');
    }
  }
  .authorize-guide {
    margin: 5px 0 0 10px;
    color: #3370ff;
    span {
      text-decoration: underline;
      cursor: pointer;
    }
  }
  .color-gray {
    color: #a2aab8;
  }
}
</style>
