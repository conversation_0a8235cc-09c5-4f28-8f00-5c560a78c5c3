<template>
  <div v-if="thumbnailBase64" class="thumbnail-wrapper" @click.stop="handleScreenshotPath">
    <div class="thumbnail-img">
      <img :src="thumbnailBase64" />
    </div>
    <div class="thumbnail-text">
      {{ $t('classroom.modules.screenThumbnail.successNotice') }}
    </div>
  </div>
</template>

<script>
import { nativeApi } from 'utils/electronIpc'
import { getScreenshotPath } from 'utils/settings'
import screenshot from 'utils/screenshot'
let timer
export default {
  data() {
    return {
      waiting: false,
      thumbnailBase64: null
    }
  },
  mounted() {
    // 监听从顶部发来的截屏事件
    this.$bus.$on('screenThumbnail', () => {
      this.handleScreenshot()
    })
  },
  methods: {
    /**
     * 处理截屏事件
     */
    async handleScreenshot() {
      if (this.waiting) return
      this.waiting = true
      const res = await screenshot()
      this.thumbnailBase64 = res.thumbnail
      timer && clearTimeout(timer)
      timer = setTimeout(() => {
        this.thumbnailBase64 = null
        this.waiting = false
      }, 3000)
    },
    /**
     * 点击缩略图进入截屏目录
     */
    async handleScreenshotPath() {
      const path = await getScreenshotPath()
      nativeApi.openDirBySystem(path)
    }
  },
  beforeDestroy() {
    this.$bus.$off('screenThumbnail')
  }
}
</script>

<style scoped lang="scss">
.thumbnail-wrapper {
  width: 160px;
  height: 122px;
  border-radius: 6px;
  background: rgba(15, 25, 42, 0.88);
  cursor: pointer;
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1008;
  overflow: hidden;
  .thumbnail-img {
    width: 160px;
    height: 96px;
    img {
      display: block;
      width: 160px;
      height: 96px;
      border-top-left-radius: 6px;
      border-top-right-radius: 6px;
    }
  }
  .thumbnail-text {
    height: 26px;
    line-height: 26px;
    text-align: center;
    font-size: 12px;
    font-family: 'SFProRounded-Medium', 'SFProRounded';
    font-weight: 500;
    color: #ffffff;
  }
}
</style>
