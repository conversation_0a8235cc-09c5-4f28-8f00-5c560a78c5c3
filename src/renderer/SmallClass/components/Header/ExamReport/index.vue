<!-- 小班查看报告弹窗 -->
<template>
  <div class="common-container" v-if="showReportIframe || showtoast">
    <div v-if="showReportIframe" class="report-container">
      <div class="container con">
        <iframe id="examReportContent" :src="reportUrl" @onload="handleIframeLoad" />
        <div class="ignore-badge-close" @click="closeBadgePane"></div>
      </div>
    </div>
    <div v-if="showtoast" class="toast-container">
      <div class="container con">
        <div class="warning-icon"></div>
        <div class="text">
          {{ $t('classroom.smallClass.examReportTips.msg') }}
        </div>
        <div class="button" @click="closeToast">
          {{ $t('classroom.smallClass.examReportTips.confirm') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import logger from 'utils/logger'
import { getLanguage } from 'utils/language'

export default {
  components: {},
  data() {
    return {
      commonOptions: this.$store.state.smallClass.baseData.commonOption,
      showReportIframe: false, // 是否展示报告弹窗
      showtoast: false, // 是否展示toast
      reportUrl: '', // 报告url
      useInfo: window.localStorage.getItem('userInfo')
    }
  },

  mounted() {
    this.$bus.$on('handleOpenExamReport', async examReportInfo => {
      // 判断是否可查看报告
      // 不能查看报告弹窗
      if (!examReportInfo?.studentReportUrl) {
        this.showtoast = true
      } else {
        // 显示报告弹窗
        const token = this.useInfo ? JSON.parse(this.useInfo).unifiedAccessToken : ''
        this.showReportIframe = true
        const language = await getLanguage()
        this.reportUrl = `${examReportInfo.studentReportUrl}&platform=3&language=${language}&classId=${this.commonOptions.classId}&studentId=${this.commonOptions.stuId}&token=${token}&from=live`
        window.thinkApi.ipc.send('test:url', this.reportUrl)
      }
    })
  },
  methods: {
    closeBadgePane() {
      this.showReportIframe = false
    },
    closeToast() {
      this.showtoast = false
    },
    handleIframeLoad() {
      logger.send({
        content: {
          msg: '加载H5成功-考试报告',
          reportUrl: this.reportUrl
        }
      })
    }
  },
  beforeDestroy() {
    this.$bus.$off('handleOpenExamReport')
  }
}
</script>
<style scoped lang="scss">
.common-container {
  position: fixed;
  left: 0px;
  top: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 1005;
  background: rgba(0, 0, 0, 0.6);
  .container {
    position: absolute;
    left: 0px;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
  }
}
.report-container {
  .con {
    width: 80%;
    height: 80%;
    position: absolute;
    iframe {
      width: 100%;
      height: 100%;
      border: none;
      position: relative;
      border-radius: 16px;
    }
    .ignore-badge-close {
      width: 34px;
      height: 34px;
      background-repeat: no-repeat;
      background-size: cover;
      background-image: url('img/icon_close.png');
      position: absolute;
      right: 10px;
      top: 10px;
      cursor: pointer;
    }
  }
}
.toast-container {
  .con {
    width: 426px;
    height: 164px;
    background: #ffffff;
    border-radius: 10px;
    .warning-icon {
      width: 80px;
      height: 61px;
      background-repeat: no-repeat;
      background-size: cover;
      background-image: url('img/img_warning.png');
      position: absolute;
      right: calc(50% - 30px);
      top: -38px;
    }
    .text {
      margin-top: 43px;
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      color: #172b4d;
      line-height: 19px;
    }
    .button {
      width: 322px;
      height: 48px;
      line-height: 48px;
      background: #fff3dd;
      border-radius: 24px;
      margin: 0 auto;
      text-align: center;
      font-weight: 600;
      color: #ffaa0d;
      margin-top: 20px;
      cursor: pointer;
      font-size: 16px;
    }
  }
}
</style>
