<template>
  <a-modal
    v-model="isShowModal"
    :width="width"
    :maskClosable="false"
    :centered="true"
    :keyboard="false"
    :closable="false"
    :footer="null"
    :dialogClass="classes"
    :destroyOnClose="destroyOnClose"
    :zIndex="zIndex"
  >
    <div class="common-model-wrapper">
      <div class="top-wrapper">
        <div class="title">{{ title }}</div>
        <div class="sub-title">{{ subTitle }}</div>
      </div>
      <div class="content-wrapper" v-if="$slots.default"><slot></slot></div>
      <div class="bottom-wrapper" v-if="showFooter">
        <a-button
          v-if="showLeftBtn"
          type="primary"
          shape="round"
          size="large"
          :loading="leftLoading"
          class="btn left-btn"
          @click="leftOperation()"
        >
          {{ leftBtnText }}
        </a-button>
        <a-button
          v-if="showRightBtn"
          type="primary"
          shape="round"
          size="large"
          :disabled="disabled"
          :loading="rightLoading"
          class="btn right-btn"
          @click="rightOperation()"
        >
          {{ rightBtnText }}
        </a-button>
      </div>
    </div>
  </a-modal>
</template>
<script>
export default {
  props: {
    width: {
      type: Number,
      default: 343
    },
    title: {
      type: String,
      default: ''
    },
    isShowModal: {
      type: Boolean,
      default: false
    },
    subTitle: {
      type: String,
      default: ''
    },
    leftBtnText: {
      type: String,
      default: ''
    },
    rightBtnText: {
      type: String,
      default: ''
    },
    showLeftBtn: {
      type: Boolean,
      default: true
    },
    showRightBtn: {
      type: Boolean,
      default: true
    },
    dialogClass: {
      type: String || Array,
      default: ''
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    destroyOnClose: {
      type: Boolean,
      default: false
    },
    zIndex: {
      type: Number,
      default: 1000
    },
    disabled: {
      type: Boolean,
      default: false
    },
    leftLoading: {
      type: Boolean,
      default: false
    },
    rightLoading: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    classes() {
      // @log-ignore
      let dialogClass = this.dialogClass
      if (Array.isArray(this.dialogClass)) {
        dialogClass = this.dialogClass.join(' ')
      }
      return `common-model-cls ${dialogClass}`
    }
  },
  methods: {
    leftOperation() {
      this.$emit('leftBtnOperation')
    },
    rightOperation() {
      this.$emit('rightBtnOperation')
    }
  }
}
</script>
<style lang="scss">
.common-model-cls {
  .ant-modal-body {
    padding: 0;
  }
  .ant-modal-content {
    border-radius: 16px;
  }

  .common-model-wrapper {
    padding: 37px 16px 16px;

    .top-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-bottom: 36px;
      .title {
        margin-bottom: 10px;
        font-size: 18px;
        color: #172b4d;
      }
      .sub-title {
        padding: 0 36px;
        text-align: center;
        color: #a2aab8;
        font-size: 14px;
      }
    }
    .bottom-wrapper {
      display: flex;
      justify-content: space-between;
      .btn {
        width: 147px;
        height: 48px;
        font-size: 16px;
      }
      .left-btn {
        background: #fff3dc;
        color: #ffaa0a;
      }
    }
    .content-wrapper {
      padding: 0 0 20px 0;
    }
  }
}
</style>
