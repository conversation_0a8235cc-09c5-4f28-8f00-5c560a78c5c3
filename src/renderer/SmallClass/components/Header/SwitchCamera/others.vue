<template>
  <div class="flex justify-between gap-[8px] items-center" data-name="switch-camera">
    <span class="text" data-name="switch-camera-text">
      {{ $t('liveroomDisableOthersVideoMenu') }}
    </span>
    <a-switch
      v-model="cameraOthersStatus"
      size="small"
      @change="handleOthersCameraSwitch"
      data-name="switch-camera-btn"
    />
  </div>
</template>

<script>
import { useCameraStatus } from '../../../hooks/useCameraStatus'
import { defineComponent } from '@vue/composition-api'
export default defineComponent({
  name: 'SwitchOtherVideo',
  setup() {
    const { handleOthersCameraSwitch, cameraOthersStatus } = useCameraStatus()
    return {
      handleOthersCameraSwitch,
      cameraOthersStatus
    }
  }
})
</script>
<style scoped lang="scss">
.text {
  color: #fff;
  font-size: 14px;
  white-space: nowrap;
}
::v-deep .ant-switch:not(.ant-switch-checked) {
  background-color: #202020;
}
</style>
