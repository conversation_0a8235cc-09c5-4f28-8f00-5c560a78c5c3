<template>
  <div class="flex justify-between" data-name="switch-camera">
    <span class="text" data-name="switch-camera-text">
      {{ $t('classroom.smallClass.camera.buttonName') }}
    </span>
    <a-switch
      v-model="cameraStatus"
      size="small"
      @change="handleCameraSwitch"
      data-name="switch-camera-btn"
    />
  </div>
</template>

<script>
import { useCameraStatus } from '../../../hooks/useCameraStatus'
import { defineComponent } from '@vue/composition-api'
export default defineComponent({
  name: 'SwitchVideo',
  setup() {
    const { handleCameraSwitch, cameraStatus } = useCameraStatus()
    return {
      handleCameraSwitch,
      cameraStatus
    }
  }
})
</script>
<style scoped lang="scss">
.text {
  color: #fff;
  font-size: 14px;
  white-space: nowrap;
}
::v-deep .ant-switch:not(.ant-switch-checked) {
  background-color: #202020;
}
</style>
