<template>
  <div class="header-container flex justify-between items-center">
    <p class="plan-name font-normal flex-1">{{ baseData.planInfo.name }}</p>
    <div class="right-tools flex items-center">
      <Coins v-if="!baseData.commonOption.isAudition" />
      <div v-if="!baseData.commonOption.isAudition" class="divider"></div>
      <NetworkStatus />
      <Tooltip placement="bottom">
        <template slot="title">
          <span>{{ $t('classroom.smallClass.screenShot.buttonName') }}</span>
        </template>
        <div class="button button-screenShot" @click="handleScreenshot" data-log="课中截图"></div>
      </Tooltip>
      <Tooltip placement="bottom">
        <template slot="title">
          <span>{{ $t('classroom.smallClass.homework.buttonName') }}</span>
        </template>
        <div
          class="button button-assignmentBox"
          @click="handleAssignmentBox"
          data-log="作业盒子"
          data-name="button-assignmentBox"
          v-if="local != 'uk' && !isAudition"
          :class="{ 'message-tip': showNewMessageTip }"
        ></div>
      </Tooltip>
      <Tooltip placement="bottom">
        <template slot="title">
          <span>{{ $t('classroom.smallClass.examReport.buttonName') }}</span>
        </template>
        <div
          v-if="reportBtnVisible && !isAudition"
          class="button button-examReport"
          @click="handleExamReport"
          data-log="课中考试报告"
        ></div>
      </Tooltip>
      <div class="relative">
        <div
          class="button button-more"
          :class="{ 'button-more-hover': isShowMoreTools }"
          @click="handleMore"
          data-log="更多菜单"
          data-name="button-more"
          v-if="!isAudition"
        ></div>
        <div class="more-menu" v-show="isShowMoreTools">
          <div id="switch-carema"><SwitchCamera /></div>
          <div id="switch-other-video"><SwitchOtherVideo /></div>
          <div v-if="hasStickerSwitch" class="flex justify-between" data-name="switch-sticker">
            <span class="text" data-name="switch-sticker-text">
              {{ $t('classroom.smallClass.visualEffect.buttonName') }}
            </span>
            <a-switch
              v-model="arOpen"
              size="small"
              @change="arChange"
              data-name="switch-sticker-btn"
            />
          </div>
          <div @click="handleDeviceTest" data-log="硬件测试">
            {{ $t('classroom.modules.deviceTest.dialogTitle') }}
          </div>
          <div @click="handleFeedback" data-log="问题反馈">
            {{ $t('classroom.smallClass.feedback.buttonName') }}
          </div>
        </div>
        <div
          v-if="stickerTipToast && !isShowMoreTools"
          class="open-sticker-tip disappear-in-3"
          @animationend="stickerTipToast = false"
        >
          <div class="tip">
            {{ $t('classroom.smallClass.visualEffect.openTip') }}
          </div>
        </div>
      </div>
      <div
        class="button button-exit"
        @click="handleExit(settlementInfo)"
        data-log="课中退出按钮"
      ></div>
    </div>
    <!-- 设备检测 -->
    <DeviceTest />
    <!-- 问题反馈 -->
    <Feedback />
    <!-- 考试报告 -->
    <ExamReport />
    <Model
      dialogClass="sticker-forbid got-info"
      :isShowModal="showStickerTipModal"
      :rightBtnText="$t('classroom.smallClass.visualEffect.confirm')"
      :title="$t('classroom.smallClass.visualEffect.unableOpen')"
      :subTitle="$t('classroom.smallClass.visualEffect.teacherClose')"
      :zIndex="9999"
      @rightBtnOperation="showStickerTipModal = false"
    />
    <RetryDialog />
  </div>
</template>
<script>
import {
  defineComponent,
  getCurrentInstance,
  ref,
  onMounted,
  onBeforeUnmount,
  computed
} from '@vue/composition-api'
import { Tooltip } from 'ant-design-vue'
import { useExit } from '../../hooks/useExit'
import Coins from './Coins/index.vue'
import NetworkStatus from './NetworkStatus.vue'
import SwitchCamera from './SwitchCamera/index.vue'
import SwitchOtherVideo from './SwitchCamera/others.vue'
import DeviceTest from './DeviceTest/index.vue'
import Feedback from './Feedback/index.vue'
import ExamReport from './ExamReport/index.vue'
import { lookExamReportApi } from 'api/h5exam/index'
import { getLocal } from 'utils/local'
import logger from 'utils/logger'
import { getSettlementStatus } from 'api/growthHandbook'
import { useStickerStatus } from '../../hooks/useStickerStatus'
import Model from 'components/Common/Model'
import RetryDialog from 'components/GrowthHandbook/retryDialog'

export default defineComponent({
  name: 'Header',
  components: {
    Model,
    Coins,
    NetworkStatus,
    SwitchCamera,
    SwitchOtherVideo,
    DeviceTest,
    Feedback,
    ExamReport,
    Tooltip,
    RetryDialog
  },
  setup() {
    const { proxy } = getCurrentInstance()
    const baseData = proxy.$store.state.smallClass.baseData
    const isShowMoreTools = ref(false)
    const settlementInfo = ref({})
    const { handleExit } = useExit(baseData.planInfo)
    const local = ref('us')
    const showNewMessageTip = ref(false) // 作业盒子是否有新批改消息提示
    console.log('看看', window.location.href.split('?'))

    // 形象贴纸管控相关
    const showStickerTipModal = ref(false)
    const stickerTipToast = ref(false)
    const changeStickerTipToast = () => {
      stickerTipToast.value = true
      isShowMoreTools.value = false
    }
    const changeShowStickerTipModal = () => {
      showStickerTipModal.value = !showStickerTipModal.value
    }
    const { arOpen, hasStickerSwitch, forbidVideoSticker, arChange } = useStickerStatus()

    const isAudition = computed(() => {
      return baseData.commonOption.isParent || baseData.commonOption.isAudition
    })
    /**
     * 处理截屏事件
     */
    const handleScreenshot = () => {
      proxy.$bus.$emit('screenThumbnail')
    }
    /**
     * 处理设备检测事件
     */
    const handleDeviceTest = () => {
      proxy.$bus.$emit('deviceTestShow')
    }
    const handleMore = () => {
      isShowMoreTools.value = !isShowMoreTools.value
      stickerTipToast.value = false
    }
    /**
     * 处理作业盒子
     */
    const handleAssignmentBox = () => {
      // 打开作业盒子-clickoutside排除的dom
      const wrapperDom = document.querySelector('.button-assignmentBox')
      proxy.$bus.$emit('handleOpenBox', wrapperDom)
      showNewMessageTip.value = false
    }
    /**
     * 处理考试报告
     */
    const handleExamReport = async () => {
      await checkIsShowReportBtn()
      proxy.$bus.$emit('handleOpenExamReport', examReportInfo.value)
    }
    /**
     * 处理问题反馈
     */
    const handleFeedback = () => {
      proxy.$bus.$emit('handleOpenFeedback', true)
    }
    /**
     * 点击其他区域关闭更多弹窗
     */
    const clickOutsideHandler = event => {
      const target = event.target
      const dataName = target.getAttribute('data-name')
      const notAllowCloseArr = [
        'switch-camera',
        'switch-camera-text',
        'switch-camera-btn',
        'switch-sticker',
        'switch-sticker-text',
        'switch-sticker-btn',
        'button-more'
      ]
      if (!notAllowCloseArr.includes(dataName)) {
        isShowMoreTools.value = false
      }
    }
    /**
     * 判断是否展示考试报告入口
     */
    const reportBtnVisible = ref(false)
    const examReportInfo = ref({})
    const checkIsShowReportBtn = async () => {
      const res = await lookExamReportApi({
        planId: baseData.planInfo.id, // 课次id
        platform: '3' // pc端
      })
      if (!res || res.code != 0) return
      examReportInfo.value = res.data
      reportBtnVisible.value = res.data?.showReportEnter === '1' // '1': 展示 '0':不展示
    }
    // 获取下课结算页弹窗状态
    const getClassOverStatus = async () => {
      const res = await getSettlementStatus({ planId: baseData.planInfo.id })
      if (res && res.code === 0) {
        settlementInfo.value = res.data
      } else {
        this.$bus.$emit('showRetryModal', {
          callback: this.getClassOverStatus,
          code: res.code
        })
      }
      settlementInfo.value.isAudition = isAudition.value
    }

    onMounted(async () => {
      document.addEventListener('click', clickOutsideHandler)
      proxy.$bus.$on('smallClassShowMessageTip', () => {
        logger.send({
          tag: 'student.Interact',
          content: {
            msg: '显示作业批改消息提示',
            interactType: 'Wall'
          }
        })
        showNewMessageTip.value = true
      })
      proxy.$bus.$on('showNotAllowStickerTipModal', changeShowStickerTipModal)
      proxy.$bus.$on('showAllowStickerTipToast', changeStickerTipToast)
      local.value = await getLocal()
      checkIsShowReportBtn() // 判断是否展示考试报告按钮
      getClassOverStatus()
    })
    onBeforeUnmount(() => {
      document.removeEventListener('click', clickOutsideHandler)
      proxy.$bus.$off('showNotAllowStickerTipModal', changeShowStickerTipModal)
      proxy.$bus.$off('showAllowStickerTipToast', changeStickerTipToast)
    })
    return {
      baseData,
      isAudition,
      stickerTipToast,
      changeStickerTipToast,
      showStickerTipModal,
      changeShowStickerTipModal,
      arOpen,
      forbidVideoSticker,
      hasStickerSwitch,
      arChange,
      isShowMoreTools,
      settlementInfo,
      reportBtnVisible,
      showNewMessageTip,
      handleExit,
      handleScreenshot,
      handleDeviceTest,
      handleMore,
      handleAssignmentBox,
      handleExamReport,
      handleFeedback
    }
  }
})
</script>
<style lang="less" scoped>
.header-container {
  width: 100%;
  height: 100%;
  .plan-name {
    height: 100%;
    color: #fff;
    line-height: 44px;
    margin: 0 20px 0 12px;
    font-size: 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .right-tools {
    margin-right: 12px;
    .divider {
      width: 1px;
      height: 20px;
      border: 1px solid #525252;
      margin-left: 20px;
    }
    .button {
      width: 30px;
      height: 30px;
      background-size: cover;
      margin-left: 20px;
      cursor: pointer;
    }
    .button-screenShot {
      background-image: url('./images/icon-screenshot.png');
      &:hover {
        background-image: url('./images/icon-screenshot-hover.png');
      }
    }
    .button-assignmentBox {
      background-image: url('./images/icon-assignmentBox.png');
      &:hover {
        background-image: url('./images/icon-assignmentBox-hover.png');
      }
    }
    .message-tip {
      position: relative;
      &:after {
        position: absolute;
        content: '';
        display: block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #ff4b4b;
        right: 0;
        top: 0;
      }
    }
    .button-examReport {
      background-image: url('./images/icon-examReport.png');
      &:hover {
        background-image: url('./images/icon-examReport-hover.png');
      }
    }
    .button-exit {
      background-image: url('./images/icon-exit.png');
      &:hover {
        background-image: url('./images/icon-exit-hover.png');
      }
    }
    .button-more {
      background-image: url('./images/icon-more.png');
      position: relative;
      &:hover {
        background-image: url('./images/icon-more-hover.png');
      }
    }
    .button-more-hover {
      background-image: url('./images/icon-more-hover.png');
    }
    .more-menu,
    .open-sticker-tip {
      position: absolute;
      right: -10px;
      top: 50px;
      background: #1a1a1a;
      border-radius: 8px;
      z-index: 999;
      color: #fff;
      & > div {
        padding: 12px 0;
        border-radius: 4px;
        color: #fff;
        font-size: 14px;
        cursor: pointer;
      }
      & > div:not(:last-child) {
        border-bottom: solid 1px rgba(255, 255, 255, 0.1);
      }
      &::before {
        position: absolute;
        top: -6px;
        right: 20px;
        content: '';
        width: 8px;
        height: 6px;
        background-image: url('./images/arrow-up.png');
        background-size: cover;
      }
    }
    .more-menu {
      min-width: 150px;
      padding: 4px 12px;
    }
    .open-sticker-tip {
      width: 260px;
      padding: 16px;
      .tip {
        position: relative;
        padding: 0 0 0 32px;
        &::before {
          position: absolute;
          top: 3px;
          left: 0px;
          content: '';
          width: 24px;
          height: 24px;
          background-image: url('./images/icon-info.png');
          background-size: cover;
        }
      }
    }
  }
}
.sticker-forbid {
  .ant-btn[disabled],
  .ant-btn-primary[disabled] {
    color: #fff;
    &::before {
      background: rgba(255, 255, 255, 0.7);
      display: block;
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
    }
  }
}
::v-deep .got-info {
  .bottom-wrapper {
    justify-content: center;
  }
  .left-btn {
    display: none;
  }
}
@keyframes disappear {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.disappear-in-3 {
  animation-name: disappear;
  animation-duration: 3s;
}
</style>
