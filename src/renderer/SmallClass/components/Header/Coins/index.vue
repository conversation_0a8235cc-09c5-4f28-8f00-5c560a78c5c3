<template>
  <div class="coins-container">
    <div class="coins coin-opration" id="coin-position" @click.prevent.stop="openBadgePane($event)">
      <span :class="['icon-coins', { 'add-coin': addCoin }, 'coin-opration']"></span>
      <div v-if="!addCoin" class="coin-num">
        <div class="img-num" v-for="(i, index) in String(planIdCoin).split('')" :key="index">
          <img :src="numImgArr[i]" class="coin-opration" />
        </div>
      </div>
      <CountTo
        v-else
        :startNum="startCoin"
        :endNum="endCoin"
        @end="countEnd"
        :delayTime="delayTime"
      />
      <div class="coin-detail" v-if="isShowCoinDetail" data-name="coin-detail">
        <div class="coins-blank">
          <div class="coins-curlesson-coins">
            <div class="tip">
              {{ $t('classroom.smallClass.coins.currentLessonCoinsTips') }}
            </div>
            <div class="content">
              <span class="icon-coins"></span>
              <span v-if="coinDatas" class="number">{{ coinDatas.planIdCoin }}</span>
            </div>
          </div>
          <div class="divider"></div>
          <div class="coins-total-coins">
            <div class="tip">
              {{ $t('classroom.smallClass.coins.totalCoinsTips') }}
            </div>
            <div class="content">
              <span class="icon-coins"></span>
              <span v-if="coinDatas" class="number">{{ coinDatas.totalCoin }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import _debounce from 'lodash/debounce'
import { studentCoinAndMedal } from 'api/classroom/index'
import CountTo from './CountTo/index.vue'
import qs from 'querystringify'
import zero from './imgs/0.png'
import one from './imgs/1.png'
import two from './imgs/2.png'
import three from './imgs/3.png'
import four from './imgs/4.png'
import five from './imgs/5.png'
import six from './imgs/6.png'
import seven from './imgs/7.png'
import eight from './imgs/8.png'
import nine from './imgs/9.png'
export default {
  components: {
    CountTo
  },
  data() {
    return {
      numImgArr: [zero, one, two, three, four, five, six, seven, eight, nine],
      coinDatas: null,
      planIdCoin: 0,
      level: 0,
      addCoin: false,
      startCoin: 0,
      endCoin: 0,
      delayTime: 1,
      currentEvent: null,
      isShowCoinDetail: false
    }
  },
  computed: {
    goldCoins() {
      return this.$store.state.smallClass.goldCoins
    },
    options() {
      return this.$store.state.smallClass.baseData.commonOption
    },
    ircStatus() {
      // irc 链接状态
      return this.$store.state.smallClass.ircStatus
    }
  },

  mounted() {
    // 初始化勋章金币数量
    this.initMedalCoins()
    this.$bus.$on('updateAchievement', (type, num) => {
      console.log(num)
      this.initMedalCoins()
    })
    // 另外飞金币的时候不能进行窗口的点击事件
    this.$bus.$on('addCoin', (isPlayAnimation, coin, isTurnoverNum = true) => {
      if (isPlayAnimation) {
        this.dealAddCoin(coin, 1)
      } else if (isTurnoverNum) {
        this.dealAddCoin(coin, 0)
      } else {
        this.initMedalCoins()
      }
    })
    /**
     * 点击其他区域关闭更多弹窗
     */
    document.addEventListener('click', () => {
      this.isShowCoinDetail = false
    })
  },
  methods: {
    async countEnd() {
      await this.initMedalCoins()
      this.addCoin = false
      this.checkRun(this.currentEvent)
      this.currentEvent = null
    },
    async checkRun(event) {
      if (!event) return
      const query = qs.parse(window.location.search)
      const res = await studentCoinAndMedal({
        planId: query.planId
      })
      if (res && res.code == 0 && !this.addCoin) {
        this.coinDatas = res.data
        if (this.planIdCoin < res.data.planIdCoin) {
          this.planIdCoin = this.coinDatas.planIdCoin || ''
        }
        this.level = this.coinDatas.medalNum
      }
    },
    dealAddCoin(coin, delayTime = 1) {
      if (!this.addCoin) {
        this.addCoin = true
        this.startCoin = +this.planIdCoin
        this.endCoin = this.startCoin + coin
        this.delayTime = delayTime
        delayTime && this.$bus.$emit('coinFly')
      } else {
        this.currentEvent = {
          coin,
          delayTime
        }
      }
      this.planIdCoin = +this.planIdCoin + coin
    },
    openBadgePane: _debounce(async function(event) {
      if (this.addCoin) return
      console.log(this.ircStatus, 'this.ircStatus')
      if (!this.ircStatus) {
        return
      }
      if (event.target.className.includes('coin-opration')) {
        this.isShowCoinDetail = !this.isShowCoinDetail
        if (this.isShowCoinDetail) {
          await this.initMedalCoins()
        }
      }
    }, 300),
    async initMedalCoins() {
      const query = qs.parse(window.location.search)
      const res = await studentCoinAndMedal({
        planId: query.planId
      })
      if (res && res.code == 0) {
        this.coinDatas = res.data
        this.planIdCoin = this.coinDatas.planIdCoin || ''
        this.level = this.coinDatas.medalNum
      }
    }
  }
}
</script>
<style scoped lang="scss">
.coins-container {
  padding: 16px 20px;
  background: #1a1a1a;
  border-radius: 8px;
  .coin-opration {
    cursor: pointer;
  }
  .coins {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;
    position: relative;
    color: #fff;
  }
  .coin-num {
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      height: 18px;
      width: 10px;
      border: 0px;
    }
  }
  .icon-coins {
    width: 18px;
    height: 18px;
    margin-right: 6px;
    display: inline-block;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('./imgs/icon-coins.png');
  }
  .coin-detail {
    position: absolute;
    right: -10px;
    top: 50px;
    background: #1a1a1a;
    border-radius: 8px;
    padding: 16px 20px;
    z-index: 999;
    cursor: default;
    &:before {
      position: absolute;
      right: 20px;
      top: -16px;
      content: '';
      width: 0;
      height: 0;
      border-top: 8px solid transparent;
      border-right: 8px solid transparent;
      border-left: 8px solid transparent;
      border-bottom: 8px solid black;
    }
    .tip {
      font-size: 12px;
      font-weight: 400;
      line-height: 14px;
      margin: 0 0 6px 0;
      text-align: center;
      white-space: nowrap;
    }
    .number {
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
    }
    .divider {
      width: 1px;
      height: 44px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 4px;
      margin: 0 20px;
    }
    .coins-blank {
      display: flex;
      box-flex: 1;
    }
    .coins-curlesson-coins {
      min-width: 60px;
    }
    .content {
      align-items: center;
      justify-content: center;
      display: flex;
    }
    .coins-total-coins {
      min-width: 60px;
    }
  }
}
</style>
