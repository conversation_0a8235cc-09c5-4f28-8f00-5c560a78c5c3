<template>
  <div v-if="showCoinsBadge" class="coins-badge">
    <section>
      <div class="coins">
        <div class="coins-curlesson-coins">
          <div class="topTip">
            <div class="tip">
              {{ $t('classroom.smallClass.coins.currentLessonCoinsTips') }}
            </div>
          </div>
          <div class="content">
            <span class="icon"></span>
            <span v-if="coinDatas" class="number">+{{ coinDatas.planIdCoin }}</span>
          </div>
        </div>
        <div class="coins-total-coins">
          <div class="topTip">
            <div class="tip">
              {{ $t('classroom.smallClass.coins.totalCoinsTips') }}
            </div>
          </div>
          <div class="content">
            <span class="icon"></span>
            <span v-if="coinDatas" class="number">{{ coinDatas.totalCoin }}</span>
          </div>
        </div>
      </div>
      <div class="badge">
        <div class="badge-icon">
          <span
            class="badge-item"
            :class="index + 1 <= level ? `level-${index + 1}` : `lock-level-${index + 1}`"
            :style="{ marginRight: index + 1 == level && level != 7 ? '44px' : '' }"
            v-for="(badgeItem, index) in totalBadgeList"
            :key="index"
          >
            <label v-if="index + 1 == level && level != 7"></label>
          </span>
        </div>
        <span class="text">{{ levelText }}</span>
      </div>
      <div class="badge-close" @click="closeBadgePane"></div>
    </section>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      level: 0,
      totalBadgeList: new Array(7),
      currentBadgeList: null,
      showCoinsBadge: false,
      coinDatas: {},
      levelList: ['Level 1', 'Level 2', 'Level 3', 'Level 4', 'Level 5', 'Level 6', 'Level 7']
    }
  },
  computed: {
    levelText() {
      const levelNameList = this.$t('classroom.smallClass.coins.levelNameList')
      if (this.level == 0) {
        return this.$t('classroom.smallClass.coins.levelNotice_0')
      } else if (this.level == 7) {
        return this.$t('classroom.smallClass.coins.levelNotice_7')
      }
      return this.$t('classroom.smallClass.coins.levelNotice_other', {
        levelName: levelNameList[this.level]
      })
    }
  },

  mounted() {
    this.currentBadgeList = new Array(this.level)
    this.$bus.$on('openBadgePane', coinsData => {
      this.coinDatas = coinsData
      this.showCoinsBadge = true
      this.level = this.coinDatas.medalNum
    })
  },
  methods: {
    closeBadgePane() {
      this.showCoinsBadge = false
    }
  },
  beforeDestroy() {
    this.$bus.$off('openBadgePane')
  }
}
</script>
<style scoped lang="scss">
.coins-badge {
  position: fixed;
  left: 0px;
  top: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 1005;
  background: rgba(0, 0, 0, 0.7);
  pointer-events: auto;
  section {
    width: 486px;
    height: 226px;
    background: #fff9ec;
    border-radius: 16px;
    position: absolute;
    left: 0px;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    .coins {
      width: 100%;
      height: 120px;
      padding: 0 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 16px 16px 0px 0px;
      .coins-curlesson-coins,
      .coins-total-coins {
        width: 213px;
        height: 80px;
        background: #ffefd0;
        border-radius: 8px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        padding-top: 22px;
        .topTip {
          display: flex;
          align-items: baseline;
          position: absolute;
          z-index: 999;
          top: 1px;
          .tip {
            position: relative;
            padding: 0px 10px 0;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            font-weight: 500;
            color: #fff;
            line-height: 24px;
            text-align: center;
            // padding-top: 4px;
          }
          .tip::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            text-align: center;
            background: #ffbf14;
            border-bottom: none;
            border-radius: 0px 0px 10px 10px;
            transform: perspective(25px) scale(1, 1) rotateX(-8deg);
            z-index: -1;
          }
        }
        .content {
          display: flex;
          .icon {
            width: 34px;
            height: 38px;
            display: inline-block;
            background-repeat: no-repeat;
            background-size: cover;
            background-image: url('./imgs/right_coin.png');
            margin-right: 8px;
          }
          .number {
            width: 49px;
            height: 38px;
            line-height: 38px;
            font-size: 26px;
            font-weight: 600;
            color: #ff850a;
            vertical-align: text-bottom;
          }
        }
      }
    }
    .badge {
      width: 100%;
      min-height: 106px;
      background-color: orange;
      border-radius: 0 0 16px 16px;
      background-repeat: no-repeat;
      background-size: cover;
      background-image: url('./imgs/badge_bg.png');
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 0 12px;
      .badge-icon {
        display: flex;
        width: 100%;
        justify-content: space-around;
        margin: 14px 0 12px 0;
        .badge-item {
          width: 50px;
          height: 50px;
          display: block;
          background-repeat: no-repeat;
          background-size: cover;
          position: relative;
          &.level-1 {
            background-image: url('~assets/images/live/badges/middle/level-1.png');
          }
          &.level-2 {
            background-image: url('~assets/images/live/badges/middle/level-2.png');
          }
          &.level-3 {
            background-image: url('~assets/images/live/badges/middle/level-3.png');
          }
          &.level-4 {
            background-image: url('~assets/images/live/badges/middle/level-4.png');
          }
          &.level-5 {
            background-image: url('~assets/images/live/badges/middle/level-5.png');
          }
          &.level-6 {
            background-image: url('~assets/images/live/badges/middle/level-6.png');
          }
          &.level-7 {
            background-image: url('~assets/images/live/badges/middle/level-7.png');
          }
          &.lock-level-1 {
            background-image: url('~assets/images/live/badges/middle/lock-level-1.png');
          }
          &.lock-level-2 {
            background-image: url('~assets/images/live/badges/middle/lock-level-2.png');
          }
          &.lock-level-3 {
            background-image: url('~assets/images/live/badges/middle/lock-level-3.png');
          }
          &.lock-level-4 {
            background-image: url('~assets/images/live/badges/middle/lock-level-4.png');
          }
          &.lock-level-5 {
            background-image: url('~assets/images/live/badges/middle/lock-level-5.png');
          }
          &.lock-level-6 {
            background-image: url('~assets/images/live/badges/middle/lock-level-6.png');
          }
          &.lock-level-7 {
            background-image: url('~assets/images/live/badges/middle/lock-level-7.png');
          }
          &.last-child {
            margin-right: 0px;
          }
        }
        label {
          display: block;
          position: absolute;
          width: 33px;
          height: 12px;
          background-image: url('./imgs/progress.png');
          background-repeat: no-repeat;
          background-size: cover;
          border-radius: 18px;
          cursor: not-allowed;
          z-index: 2;
          padding: 0 12px;
          right: -42px;
          top: 20px;
        }
      }
      .text {
        width: 340px;
        font-size: 14px;
        color: #723b03;
        line-height: 16px;
        text-align: center;
        padding-bottom: 8px;
      }
    }
    .badge-close {
      width: 34px;
      height: 34px;
      background-repeat: no-repeat;
      background-size: cover;
      background-image: url('./imgs/icon_close.png');
      position: absolute;
      right: -15px;
      top: -10px;
      cursor: pointer;
    }
  }
}
</style>
