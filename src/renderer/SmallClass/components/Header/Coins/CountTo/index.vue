<template>
  <div class="chartNum">
    <audio src="../audio/get-coins.mp3" class="hide" ref="getCoinsSound"></audio>
    <div class="box-item">
      <li class="number-item" v-for="(item, index) in orderNum" :key="index">
        <span>
          <i ref="numberItem" :class="{ addAnimate: !isShowNum }"></i>
        </span>
      </li>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    startNum: {
      type: Number, // 具体数值
      default() {
        return 0
      }
    },
    endNum: {
      type: Number, // 具体数值
      default() {
        return 0
      }
    },
    // 延迟滚动时间，如果不播放飞金币，只翻转情况，应设置为0
    delayTime: {
      type: Number, // 单位秒
      default() {
        return 1
      }
    }
  },
  data() {
    return {
      orderNum: this.startNum.toString().split(''),
      timerClose: null,
      timer: null,
      animationTime: 1,
      isShowNum: true
    }
  },
  mounted() {
    this.increaseNumber(this.delayTime)
  },
  beforeDestroy() {
    clearTimeout(this.timer)
    clearTimeout(this.timerClose)
  },
  methods: {
    // 定时增长数字
    increaseNumber(delayTime) {
      let self = this
      self.setNumberTransform(true)
      // 若需要飞金币特效，则需要金币结束飞之后再播放翻转数字
      if (delayTime > 0) {
        this.$bus.$on('endCoinFly', () => {
          this.orderNum = this.endNum.toString().split('')
          self.isShowNum = false
          self.$nextTick(() => {
            self.setNumberTransform(false)
            self.$refs?.getCoinsSound?.play()
            // todo 待问一下 为啥有报错，然后声音还能正常播放
            // console.log('看下有啥值', self.$refs.getCoinsSound)
          })
          this.timerClose = setTimeout(() => {
            self.$emit('end')
          }, this.animationTime * 1000)
        })
      } else {
        // 只需要播放翻转数字效果，定时器即可
        this.timer = setTimeout(() => {
          this.orderNum = this.endNum.toString().split('')
          self.isShowNum = false
          self.$nextTick(() => {
            self.setNumberTransform(false)
            this.$refs.getCoinsSound.play()
          })
        }, delayTime * 1000)
        this.timerClose = setTimeout(() => {
          self.$emit('end')
        }, (delayTime + this.animationTime) * 1000)
      }
    },
    // 设置文字滚动
    setNumberTransform(isSkipCompare = false) {
      const numberItems = this.$refs.numberItem // 拿到数字的ref，计算元素数量
      const numberArr = this.orderNum.filter(item => !isNaN(item))
      const startNumberArr = this.startNum
        .toString()
        .split('')
        .filter(item => !isNaN(item))
      // 结合CSS 对数字字符进行滚动,显示订单数量
      for (let index = 0; index < numberItems.length; index++) {
        const elem = numberItems[index]
        let isEqualBefore = 0
        if (!isSkipCompare && startNumberArr[index] && numberArr[index] === startNumberArr[index]) {
          isEqualBefore = 1
        }
        elem.style['background-position-y'] = `-${numberArr[index] * 18 + 180 * isEqualBefore}px`
      }
    }
  }
}
</script>
<style scoped lang="scss">
/*具体值value总量滚动数字设置*/
.box-item {
  position: relative;
  font-size: 54px;
  line-height: 40px;
  text-align: center;
  list-style: none;
  writing-mode: vertical-lr;
  text-orientation: upright;
  /*文字禁止编辑*/
  -moz-user-select: none; /*火狐*/
  -webkit-user-select: none; /*webkit浏览器*/
  -ms-user-select: none; /*IE10*/
  -khtml-user-select: none; /*早期浏览器*/
  user-select: none;
  /* overflow: hidden; */
}
/*滚动数字设置*/
.number-item {
  & > span {
    width: 10px;
    height: 18px;
    display: block;
    overflow: hidden;
    & > i {
      font-style: normal;
      width: 10px;
      height: 180px;
      display: block;
      background: url(../imgs/num-bg.png) repeat-y;
      background-position: 0px 0px;
      background-size: 100% 100%;
    }
    .addAnimate {
      transition: background-position 1s cubic-bezier(0, 0.51, 0, 0.98);
    }
  }
}
</style>
