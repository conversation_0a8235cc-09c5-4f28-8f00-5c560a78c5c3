<template>
  <div class="assignment-container" data-name="assignment-container" id="assignment-container">
    <div v-if="showAssignmentBox" class="box-inner">
      <span class="box-shadow box-shadow-top"></span>
      <div class="box-container">
        <template v-if="loading">
          <a-spin class="loading">
            <a-icon slot="indicator" type="loading" spin />
          </a-spin>
        </template>
        <template v-else>
          <section v-if="boxLists && boxLists.length > 0" id="box-scroll" class="box-scroll">
            <div v-for="box in boxLists" class="box-content" :key="box.index">
              <div
                :class="[
                  'box-item',
                  assignmentStatus(
                    box.newCorrectStatus,
                    isShowIncorrectModify(box) || (!isShowIncorrectModify(box) && box.correctPoint)
                  ).bgClassName
                ]"
              >
                <span class="number">{{ box.index }}</span>
                <div class="img-wrapper" @click="handleBigPicture(box)">
                  <span class="check-big-img"></span>
                  <img
                    v-if="box.photoThumbUrl"
                    :src="
                      box.newCorrectStatus == 0 || box.newCorrectStatus == 3
                        ? box.photoThumbUrl
                        : box.correctThumbUrl
                    "
                    alt=""
                  />
                  <img v-else src="~assets/images/live/assignmentImg_bg.png" alt="" />
                  <div class="answer-status">
                    <span
                      class="icon-feedback"
                      :class="[assignmentStatus(box.newCorrectStatus).feedbackStatus]"
                    ></span>
                    <span :class="assignmentStatus(box.newCorrectStatus).fontStyleStatus">{{
                      assignmentStatus(box.newCorrectStatus).feedbackTitle
                    }}</span>
                  </div>
                </div>
                <div class="box-tips">
                  <!-- 显示金币奖励数 -->
                  <div class="coins-num" v-if="!isShowIncorrectModify(box) && box.correctPoint">
                    <img src="./img/icon_coins.png" />
                    <span>+{{ box.correctPoint }}</span>
                  </div>
                  <a-button
                    v-if="isShowIncorrectModify(box)"
                    type="primary"
                    size="small"
                    shape="round"
                    class="open-photowall"
                    @click="openPhotowall(box)"
                  >
                    <img class="correction-img" src="~assets/images/live/icon_correction.png" />
                    {{ $t('classroom.modules.assignmentBox.checkBigPictures.buttonName') }}
                  </a-button>
                </div>
              </div>
            </div>
          </section>
          <div v-else class="box-content-nodata">
            <img src="./img/icon_empty.png" />
            <div class="title">{{ $t('classroom.modules.assignmentBox.nodataNotice') }}</div>
          </div>
        </template>
      </div>

      <span class="box-shadow box-shadow-bottom"></span>
    </div>
    <!-- 作业盒子查看大图 -->
    <div v-if="showBigPicture">
      <CheckBigPicture
        v-if="checkRefresh"
        ref="showBigPictureRef"
        @closeBigPicture="closeBigPicture"
        @openPhotoWallFormBigPicture="openPhotoWallFormBigPicture"
        :boxMsg="boxMsg"
        :showBigPictureCorrectIcon="showBigPictureCorrectIcon"
        :rightCoin="rightCoin"
        :rewardType="rewardType"
        :isFromClickBox="isFromClickBox"
        :isTakePicture="isTakePicture"
        :isAllowPhotowallRewards="isAllowPhotowallRewards"
      ></CheckBigPicture>
    </div>
    <correctPhotoWall
      ref="correctPhotoWall"
      @destoryCorrectDom="destoryCorrectDom"
      v-if="isShowCorrectPhotoWall"
      :isPhotoWallShow="this.isPhotoWallShow"
      :currentInteractionId="this.currentInteractionId"
    ></correctPhotoWall>
  </div>
</template>
<script>
import CheckBigPicture from './checkBigPictures.vue'
import { queryAssignmentBoxList } from 'api/assignmentBox'
import { getLocal } from 'utils/local'
import correctPhotoWall from './correctPhotoWall.vue'
import logger from 'utils/logger'
import { isOpenTheFunc } from 'utils/initConfig'
export default {
  data() {
    return {
      showAssignmentBox: false,
      showBigPicture: false, // 查看大图
      boxLists: [], // 作业盒子列表
      boxMsg: {}, // 查看当前作业信息
      local: '',
      loading: true,
      showEmpty: false,
      couldCheckImage: false,
      teacher: this.$t('common.MasterTeacher'),
      checkRefresh: true, // 强制刷新子组件
      currentInteractionId: null, // 拍照 +上墙互动id
      showBigPictureCorrectIcon: false, // 大图icon
      photoWallInteractId: null, // 拍照的id
      isPhotoWallShow: false, // 是否是上墙期间
      isShowCorrectPhotoWall: false, // 控制订正
      classType: 2, // 班级类型 0 大班 1伪小班 2 真小班
      rightCoin: 0,
      rewardType: 0,
      isFirstCorrect: false, // 是否是首次提交就批改正确
      isFromClickBox: false, // 在作业盒子中点开大图还是在批改后直接打开
      isTakePicture: false, // 是涂鸦画板还是拍照上墙互动
      isAllowPhotowallRewards: false // 是否允许订正后发放金币奖励
    }
  },
  props: {
    planId: {
      type: Number,
      default: 0
    },
    options: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: {
    CheckBigPicture,
    correctPhotoWall
  },
  methods: {
    // Classroom按钮样式信息
    assignmentStatus(status, isBigBgClassName = false) {
      // @log-ignore
      // newCorrectStatus  0 - 未批改，1 - 正确，2 - 错误  3 - 重新修改后未批改, 5 - 半对半错
      const bgClassName = isBigBgClassName ? 'box-bg-with-tip' : 'box-bg-no-tip'
      const infoMap = {
        0: {
          feedbackStatus: 'feedback-grading',
          feedbackTitle: this.$t('classroom.interactions.photoWall.grading'),
          fontStyleStatus: 'font-style-grading'
        },
        1: {
          feedbackStatus: 'feedback-correct',
          feedbackTitle: this.$t('classroom.interactions.photoWall.correct'),
          fontStyleStatus: 'font-style-correct'
        },
        2: {
          feedbackStatus: 'feedback-incorrect',
          feedbackTitle: this.$t('classroom.interactions.photoWall.incorrect'),
          fontStyleStatus: 'font-style-incorrect'
        },
        3: {
          feedbackStatus: 'feedback-grading',
          feedbackTitle: this.$t('classroom.interactions.photoWall.grading'),
          fontStyleStatus: 'font-style-grading'
        },
        5: {
          feedbackStatus: 'feedback-half-correct',
          feedbackTitle: this.$t('classroom.interactions.photoWall.halfCorrect'),
          fontStyleStatus: 'font-style-half-correct'
        }
      }
      return {
        ...infoMap[status],
        bgClassName: bgClassName
      }
    },
    getOptions() {
      const options = localStorage.getItem('photoWallOptions')
      let photoWallOptions = ''
      try {
        photoWallOptions = JSON.parse(options)
      } catch (error) {
        console.error('作业盒子信令格式化错误', options)
      }
      return photoWallOptions
    },
    // 获取当前互动id
    async getCurrenInteractId() {
      const photoWallOptions = this.getOptions()

      if (photoWallOptions) {
        this.currentInteractionId = photoWallOptions.ircMsg.interactId
        this.sendLogger(`获取当前互动id: ${this.currentInteractionId}`)
      }
    },
    // 判断是否展示订正入口
    isShowIncorrectModify(box) {
      // 不需要参数
      if (
        [2, 5].includes(box.newCorrectStatus) &&
        box.interactId == this.currentInteractionId &&
        this.options.configs.wallCanCorrect
      ) {
        return true
      } else {
        return false
      }
    },
    openPhotoWallFormBigPicture() {
      this.openPhotowall(this.boxMsg)
      // 打开拍照上墙并且关闭查看大图
      this.showBigPicture = false
    },
    closeBox() {
      this.sendLogger(`点击关闭作业盒子`)
      // 作业盒子切换 并且关闭header
      this.hideAssignmentBoxOnly()
    },
    closeBigPicture() {
      this.showBigPicture = false
      this.$bus.$emit('updateAchievement')
    },
    // 订正调起拍照上墙
    openPhotowall(box) {
      this.sendLogger(`订正调起拍照上墙,box:${JSON.stringify(box)}`)
      // // 小班涂鸦
      if (box.tagType == 'graffiti') {
        this.smallClassGraffiti(this.boxMsg)
        this.hideAssignmentBoxOnly()
        return
      }
      this.thinkClass.RtcService.setBeautyOptions('', -1)
      // 延迟调起摄像头, 防止摄像头占用冲突
      this.isShowCorrectPhotoWall = true
      // 隐藏作业盒子
      this.hideAssignmentBoxOnly()
    },
    destoryCorrectDom() {
      this.isShowCorrectPhotoWall = false
    },
    /**
     * 作业盒子显示
     */
    handleOpenAssignmentBox() {
      this.sendLogger(`点击打开作业盒子`)
      console.log('handleOpenAssignmentBox')
      this.showAssignmentBox = !this.showAssignmentBox
      // 取消小红点
      this.$emit('handleHideMessageTip')
      // 作业盒子list
      this.showAssignmentBox && this.getAssignmentBoxList()
    },
    /**
     * 作业盒子列表
     */
    async getAssignmentBoxList() {
      this.sendLogger(`获取作业盒子列表`)
      let res = await queryAssignmentBoxList(this, { planId: this.planId })
      this.loading = false
      if (res.stat == 1) {
        this.boxLists = res.data.list || []
      }
      if (this.boxLists.length <= 0) this.showEmpty = true
      this.sendLogger(`获取作业盒子列表数据, list:${JSON.stringify(this.boxLists)}`)
    },
    /**
     * 查看大图
     */
    handleBigPicture(box) {
      this.sendLogger(`作业盒子点击查看大图`)
      this.showBigPicture = true
      this.isFromClickBox = true
      this.boxMsg = box
      this.boxMsg.isShowIncorrectModify = this.isShowIncorrectModify(box)
    },

    initEvent() {
      // 监听拍照 interactId推送
      this.$bus.$on('photoWallInteractId', interactId => {
        // console.log(interactId, 'photoWallInteractId')
        this.photoWallInteractId = interactId
      })
      // 监听是否是在上墙期间 photoWallShow 上墙期间不展示弹窗
      this.$bus.$on('photoWallShow', photoWallShow => {
        // @log-ignore
        this.isPhotoWallShow = photoWallShow
      })
      // rewardType: 2 (0 老逻辑， 2 提交时  1 批改正确)  rightCoin: 10
      // 监听私聊消息推送-辅导端老师批改完作业的推送
      this.$bus.$on('chats.assignmentCheckedPush', async msg => {
        this.$bus.$emit('smallClassShowMessageTip')
        console.log('[测试bug]辅导端老师批改完作业消息', msg)
        const photoWallOptions = this.getOptions()
        if (photoWallOptions) {
          this.currentInteractionId = photoWallOptions.ircMsg.interactId
        }
        this.sendLogger(
          `辅导端老师批改完作业消息: ${JSON.stringify(msg)},currentInteractionId:${
            this.currentInteractionId
          }`
        )

        const newCorrectStatus = msg.newCorrectStatus || msg.correctStatus
        // 判断是首次就作答正确还是修改后作答正确 第一次答对奖励20金币，订正后才答对奖励10个金币
        const assignmentCheckStatus =
          JSON.parse(localStorage.getItem('assignmentCheckStatus')) || {}
        // 如果没有该curInterIdIsChecked字段或对应的互动id内容，则为首次存储
        this.isFirstCorrect = assignmentCheckStatus[msg.interactId] === undefined
        assignmentCheckStatus[msg.interactId] = this.isFirstCorrect
        localStorage.setItem('assignmentCheckStatus', JSON.stringify(assignmentCheckStatus))
        this.rewardType = msg.rewardType || 0
        // 收到老师批改正确后更新当前金币数, 且奖励机制为1批改正确，或者之前就奖励机制配置为0时（提交和答对都奖励）
        if (newCorrectStatus === 1) {
          if (!msg.rewardType || msg.rewardType === 1) {
            this.rightCoin = msg.rewardType === 1 ? msg.rightCoin : this.isFirstCorrect ? 20 : 10
            // 小班作业盒子弹窗不展示场景，也需要更新金币
            if (this.showAssignmentBox) {
              this.$bus.$emit('updateAchievement') // 更新金币数
            }
          }
          // 更新连对
          this.$bus.$emit('onlyContinuousCorrect', msg)
        }
        // 只弹当前互动的查看大图
        if (msg.interactId != null && this.currentInteractionId == msg.interactId) {
          this.boxMsg.correctUrl = msg.pictureUrl[0] // 取数组第一个元素
          this.boxMsg.newCorrectStatus = newCorrectStatus
          this.boxMsg.tagType = msg.tagType
          this.isTakePicture = msg.tagType === 'interact' || msg.tagType === 'question' // 是否是拍照上墙的私聊消息
          // 动态设置查看大图的宽度
          this.initPictureWidth(this.boxMsg.correctUrl)
          // 是否展示盒子里的订正按钮
          if ([2, 5].includes(newCorrectStatus) && this.options.configs.wallCanCorrect) {
            this.boxMsg.isShowIncorrectModify = true
          }
          // 批改当前互动作业的题目才会弹窗
          // 新增-小班模式的 只有作业盒子收起状态展示大图
          if (!this.showAssignmentBox && this.isTakePicture) {
            this.sendLogger(`作业盒子---互动过程批改完成查看大图`)
            this.isFromClickBox = false
            this.showBigPicture = true
            // 强制刷新子组件
            this.checkRefresh = false
            this.$nextTick(() => {
              this.checkRefresh = true
            })
            // 触发
            if (
              newCorrectStatus === 1 &&
              (this.rewardType === 1 || (this.rewardType === 0 && this.isAllowPhotowallRewards)) &&
              this.rightCoin
            ) {
              this.$bus.$emit('continuousCorrect', {
                answerStat: 1,
                gold: this.rightCoin,
                isAssignmentCorrect: true,
                isNoMedal: true // 不展示勋章
              })
            }
          }
        }

        this.getAssignmentBoxList()
      })
      // 监听整个拍照上墙互动完成
      this.$bus.$on('endCorrectPhotoWall', async ({ photoWallClose }, isHistory) => {
        // console.log('endCorrectPhotoWall')
        this.sendLogger(`收到praise信令,photoWallClose:${photoWallClose},isHistory:${isHistory}`)
        if (photoWallClose) {
          // 如果是重新进入课堂的历史消息, 则不处理
          if (isHistory) {
            return
          }
          console.log('整个拍照上墙结束', this.boxMsg, isHistory)
          // 小班涂鸦的订正
          if (this.boxMsg.tagType == 'graffiti') {
            this.$bus.$emit('closeGraffitiCorrect', true)
            this.$Message.info('Interaction ended')
            this.currentInteractionId = null
          }

          // 只有在订正页面里才弹出
          this.sendLogger(`拍照上墙互动结束,boxMsg${this.boxMsg}`, 'end')
          if (this.isShowCorrectPhotoWall) {
            this.$Message.info('Interaction ended')
          }
          this.isShowCorrectPhotoWall = false
          this.currentInteractionId = null // 重置互动id
          this.showBigPicture = false // 关闭查看大图
          localStorage.removeItem('assignmentCheckStatus') // 清除本次互动批改记录
          localStorage.removeItem('photoWallOptions') // 清除本次互动批改记录
        }
      })
    },
    initPictureWidth(url) {
      // 改变图片的src
      let box_img = new Image()
      box_img.src = url
      box_img.onload = () => {
        this.couldCheckImage = true
      }
    },
    /**
     * 隐藏作业盒子
     */
    hideAssignmentBox() {
      this.showHeader = false
      this.hideAssignmentBoxOnly()
      this.sendLogger(`隐藏作业盒子`)
    },
    hideAssignmentBoxOnly() {
      this.showAssignmentBox = false
    },
    /**
     * 小班涂鸦
     */
    smallClassGraffiti(boxMsg) {
      this.$bus.$emit('room.smallClassGraffiti', boxMsg)
    },
    /**
     * 日志上报
     */
    sendLogger(msg, stage = '') {
      logger.send({
        tag: 'student.Interact',
        content: {
          msg: msg,
          interactType: 'Wall',
          interactId: this.currentInteractionId || '',
          interactStage: stage
        }
      })
    }
  },
  created() {
    this.initEvent()
  },
  async mounted() {
    // 获取整个互动过程 互动id
    this.getCurrenInteractId()
    // 获取local
    this.local = await getLocal()
    // 班级类型
    // 是否允许拍照上墙获得金币奖励
    this.isAllowPhotowallRewards = await isOpenTheFunc('canPhotowallRewards')
  },
  watch: {
    boxMsg: {
      handler(newVal) {
        // console.log(newVal, 'newVal===')
        if (newVal.correctUrl) {
          // 动态设置查看大图的宽度
          this.initPictureWidth(newVal.correctUrl)
        }
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.assignment-container {
  position: relative;
  top: 0;
  right: 0;
  height: 100%;
  .box-inner {
    position: absolute;
    right: 0;
    top: 0;
    width: 224px;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 16px 0 0 16px;
    z-index: 1000;
    pointer-events: auto;
    .box-close {
      cursor: pointer;
      span {
        width: 24px;
        height: 74px;
        position: absolute;
        top: 20px;
        left: -4px;
        background-repeat: no-repeat;
        background-position: 0 0;
        background-size: 100%;
        background-image: url('~assets/images/live/icon-close.png');
      }
    }
    .box-container {
      padding: 0 0;
      height: 100%;
      .loading {
        color: #fff;
        position: absolute;
        left: 50%;
        top: 50%;
        margin-left: -12px;
        margin-top: -12px;
        i {
          font-size: 24px;
          color: #fff;
        }
      }
      .box-content-nodata {
        text-align: center;
        color: rgba(255, 255, 255, 1);
        line-height: 20px;
        font-weight: 400;
        position: absolute;
        top: 35%;
        left: 50%;
        transform: translateX(-50%);
        img {
          width: 94px;
          height: 94px;
          margin-bottom: 8px;
          display: inline-block;
        }
        .title {
          max-width: 185px;
          margin: 0 auto;
        }
      }
      .box-scroll {
        overflow-y: scroll;
        max-height: calc(100vh - 56px);
        height: auto;
      }
      .box-content {
        padding: 0px 24px 16px;
        .box-item {
          width: 176px;
          height: 196px;
          background-repeat: no-repeat;
          background-position: 0 0;
          background-size: 100%;
          position: relative;
          .number {
            width: 29px;
            line-height: 18px;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 2px;
            font-size: 12px;
            font-weight: bold;
            left: 12px;
            top: 26px;
            position: absolute;
            color: #ffffff;
            z-index: 1;
            text-align: center;
          }
          .img-wrapper {
            background: #ccc;
            width: 160px;
            height: 120px;
            position: absolute;
            top: 22px;
            left: 8px;
            cursor: pointer;
            border-radius: 8px 8px 3px 3px;
            img {
              width: 100%;
              height: 120px;
              -o-object-fit: cover;
              object-fit: cover;
            }
            .check-big-img {
              width: 18px;
              height: 18px;
              top: 4px;
              right: 4px;
              background-repeat: no-repeat;
              background-position: 0 0;
              background-size: 100%;
              position: absolute;
              background-image: url('~assets/images/live/icon-big-picture.png');
              cursor: pointer;
            }
            .answer-status {
              width: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              position: absolute;
              bottom: 0;
              left: 0;
              height: 28px;
              background: rgba(0, 0, 0, 0.6);
            }
            .icon-feedback {
              background-repeat: no-repeat;
              background-position: 0 0;
              background-size: 100%;
              display: inline-block;
              width: 14px;
              height: 14px;
              margin-right: 5px;
            }
            .feedback-correct {
              background-image: url('~assets/images/live/icon-correct.png');
            }
            .feedback-half-correct {
              background-image: url('~assets/images/live/icon-half-correct.png');
            }
            .feedback-incorrect {
              background-image: url('~assets/images/live/icon-incorrect.png');
            }
            .feedback-grading {
              background-image: url('~assets/images/live/icon-grading.png');
            }
            .font-style-correct {
              color: #0cbd6c;
            }
            .font-style-half-correct {
              color: #ffd317;
            }
            .font-style-incorrect {
              color: #ff6c29;
            }
            .font-style-grading {
              color: #63b5ff;
            }
          }
          .box-tips {
            max-width: 100%;
            position: absolute;
            bottom: 22px;
            left: 50%;
            transform: translateX(-50%);
            .coins-num {
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .coins-num {
              img {
                width: 24px;
                height: 24px;
                margin-right: 4px;
              }
              span {
                line-height: 19px;
                font-size: 16px;
                color: #ff850a;
                font-weight: bold;
              }
            }
            .open-photowall {
              display: flex;
              justify-content: center;
              align-items: center;
              height: 26px;
              line-height: 26px;
              font-size: 12px;
              min-width: 99px;
              cursor: pointer;
              .correction-img {
                width: 14px;
                height: 14px;
                margin-right: 2px;
              }
            }
          }
          .incorrect-bottom-tip {
            padding: 5px 8px;
            width: 144px;
            height: 80px;
            font-size: 12px;
            color: #fff;
            background: linear-gradient(44deg, #62cdff 0%, #3370ff 100%);
            border-radius: 10px;
            bottom: 0;
            position: absolute;
            right: 0;
          }
        }
      }
    }
    .box-shadow {
      position: absolute;
      width: 100%;
      height: 14px;
      background-repeat: no-repeat;
      background-position: 0 0;
      background-size: 100%;
      z-index: 2;
      display: none;
    }
    .box-shadow-top {
      background-image: url('~assets/images/live/icon-shadow-top.png');
      top: 0px;
    }
    .box-shadow-bottom {
      background-image: url('~assets/images/live/icon-shadow-bottom.png');
      bottom: 0;
    }
  }
  .box-bg-no-tip {
    height: 176px !important;
    background-image: url('~assets/images/live/img-bg-grading.png');
  }
  .box-bg-with-tip {
    background-image: url('~assets/images/live/img-bg-box-assignment.png');
  }
  .box-inner .box-container .box-content:first-child {
    padding-top: 16px;
  }

  ::-webkit-scrollbar {
    width: 4px;
    height: 10px;
    border-radius: 5px;
    -webkit-border-radius: 6px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #fff;
    -webkit-border-radius: 6px;
  }
}
</style>
