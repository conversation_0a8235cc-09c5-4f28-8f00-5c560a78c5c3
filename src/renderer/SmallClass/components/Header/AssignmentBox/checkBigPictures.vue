<template>
  <a-modal
    id="big-image-tag"
    class="small-class"
    :closable="false"
    :maskClosable="false"
    :footer="null"
    :getContainer="getContainer"
    v-model="visibleBigImage"
    :title="$t('classroom.modules.assignmentBox.modalTitle')"
  >
    <div :class="[`content-${boxinfoMap[boxMsg.newCorrectStatus]}`, 'content-header']">
      <span
        class="img-title-top"
        :class="[
          `content-${boxinfoMap[boxMsg.newCorrectStatus]}`,
          `img-title-${boxinfoMap[boxMsg.newCorrectStatus]}`
        ]"
      ></span>
      <span @click="closeBigPicture" class="bigpicture-close"></span>
    </div>
    <div class="picture-content">
      <div class="show-img">
        <img
          v-if="boxMsg.photoUrl || boxMsg.correctUrl"
          :src="
            boxMsg.newCorrectStatus == 0 || boxMsg.newCorrectStatus == 3
              ? boxMsg.photoUrl
              : boxMsg.correctUrl
          "
        />
        <img v-else src="~assets/images/live/assignmnetBigPicture.png" alt="" />
      </div>
    </div>
    <div
      v-if="[2, 5].includes(boxMsg.newCorrectStatus) && boxMsg.isShowIncorrectModify"
      class="reupload picture-reupload"
      @click="openPhotoWall"
    >
      <img src="~assets/images/live/icon_correction.png" />
      {{ $t('classroom.modules.assignmentBox.checkBigPictures.buttonName') }}
    </div>
  </a-modal>
</template>

<script>
import logger from 'utils/logger'

export default {
  data() {
    return {
      visibleBigImage: true,
      boxinfoMap: {
        0: 'grading',
        1: 'correct',
        2: 'incorrect',
        3: 'grading',
        5: 'half-correct'
      },
      isShowCoins: false,
      photoWallOptions: {},
      photoWall: null,
      getContainer: () => {
        return document.getElementById('assignment-container')
      }
    }
  },
  props: {
    boxMsg: {
      type: Object,
      default: () => {
        return {}
      }
    },
    bigPictureWidth: {
      type: String,
      default: '100%'
    },
    isInCurrentInteraction: {
      type: Boolean,
      default: false
    },
    // 作答正确奖励金币数
    rightCoin: {
      type: Number,
      default: 0
    },
    // 奖励机制类型， 0（提交及正确）1 作答正确  2 提交作答时奖励
    rewardType: {
      type: Number,
      default: 0
    },
    // 是否从作业盒子中查看
    isFromClickBox: {
      type: Boolean,
      default: false
    },
    // 是否是拍照上墙互动
    isTakePicture: {
      type: Boolean,
      default: true
    },
    // 是否允许发放奖励
    isAllowPhotowallRewards: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    closeBigPicture() {
      logger.send({
        tag: 'student.Interact',
        content: {
          msg: '点击关闭大图',
          interactType: 'Wall'
        }
      })
      this.$emit('closeBigPicture')
    },
    openPhotoWall() {
      console.log('[测试bug]点击触发')
      logger.send({
        tag: 'student.Interact',
        content: {
          msg: '点击查看练习按钮',
          interactType: 'Wall'
        }
      })
      this.$emit('openPhotoWallFormBigPicture')
      this.closeBigPicture()
    }
  },
  mounted() {
    this.$nextTick(() => {
      const bigImgDom = document.querySelector('#big-image-tag')
      this.$bus.$emit('emitBigImgDom', bigImgDom)
    })
  }
}
</script>
<style lang="scss">
#big-image-tag {
  width: 660px;
  .ant-modal {
    top: 0px;
    padding-bottom: 0px;
  }
  .ant-modal-wrap {
    overflow: hidden;
    right: auto;
    top: auto;
  }
}
.small-class {
  .ant-modal {
    width: 660px !important;
    z-index: 101;
  }
  .ant-modal-wrap {
    left: 50%;
    transform: translateX(-50%);
    bottom: 16px;
  }
}
.big-class {
  .ant-modal {
    width: 720px !important;
  }
  .ant-modal-wrap {
    left: 90px;
    bottom: 66px;
  }
}
</style>
<style lang="scss" scoped>
// 修改modal样式
::v-deep .ant-modal-mask {
  background-color: rgba(0, 0, 0, 0.5);
}
::v-deep .ant-modal-header {
  display: none;
}
::v-deep .ant-modal-content {
  background: transparent;
}
::v-deep .ant-modal {
  .ant-modal-body {
    padding: 0;
  }
  .picture-content {
    border-radius: 0px 0px 10px 10px;
    width: 100%;
    padding: relative;
    padding: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fff;
    .show-img {
      position: relative;
      overflow: hidden;
      border-radius: 8px;
      img {
        max-height: calc(100vh - 160px);
        width: 100%;
        -o-object-fit: contain;
        object-fit: contain;
      }
    }
  }
  .content-header {
    height: 50px;
    border-radius: 10px 10px 0px 0px;
    position: relative;
  }
  .content-correct {
    background: #65d555;
  }
  .content-half-correct {
    background: #ffb234;
  }
  .content-incorrect {
    background: #ff7434;
  }
  .content-grading {
    background: #39a1ff;
  }
  .img-title-top {
    width: 100px;
    height: 22px;
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: contain;
    position: absolute;
    z-index: 888888;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  .img-title-correct {
    background-image: url('~assets/images/live/img-title-correct.png');
  }
  .img-title-half-correct {
    width: 195px;
    background-image: url('~assets/images/live/img-title-half-correct.png');
  }
  .img-title-incorrect {
    width: 124px;
    background-image: url('~assets/images/live/img-title-incorrect.png');
  }
  .img-title-grading {
    background-image: url('~assets/images/live/img-title-grading.png');
  }
  .bigpicture-close {
    width: 22px;
    height: 22px;
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: 100%;
    position: absolute;
    top: 12px;
    right: 12px;
    background-image: url('~assets/images/live/icon_bigpoctrue_close.png');
    cursor: pointer;
  }
  .reupload {
    background: rgba(0, 0, 0, 0.6);
    border-radius: 100px 0px 0px 100px;
    position: absolute;
    top: 66px;
    right: 0px;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  .picture-reupload {
    height: 30px;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    line-height: 30px;
    padding: 0 16px;
    img {
      width: 18px;
      height: 18px;
      margin-right: 6px;
    }
  }
}
.small-class {
  .picture-content {
    min-height: 496px;
  }
  .show-img {
    width: 644px;
  }
}
.big-class {
  .picture-content {
    min-height: 540px;
  }
  .show-img {
    width: 704px;
  }
}
.lottie-player {
  position: absolute;
}
</style>
