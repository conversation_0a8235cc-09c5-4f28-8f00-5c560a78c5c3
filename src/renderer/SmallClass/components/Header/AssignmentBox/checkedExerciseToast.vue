<template>
  <!-- 只用于涂鸦画板 -->
  <div class="checkedExerise" v-if="!isTakePicture">
    <!-- 小班作业盒子引导 -->
    <div class="smallclass-checkedExerise">
      <i class="close" @click="closeTipsPane"></i>
      <div class="content">
        <div class="top">
          <span></span>
          <p>{{ $t('classroom.modules.assignmentBox.checkedExerciseToast.notice') }}</p>
        </div>
        <div class="bottom">
          <div v-if="!couldCheckImage" class="button-view loading">
            {{ $t('common.loading') }}...
          </div>
          <div v-else class="button-view" @click="handleViewExercise">
            {{ $t('classroom.modules.assignmentBox.checkedExerciseToast.buttonName') }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getLocal } from 'utils/local'
import logger from 'utils/logger'

export default {
  data() {
    return {
      visibleBigImage: true,
      local: 'uk',
      assisTeacher: this.$t('common.AssistantTeacher')
    }
  },
  props: {
    couldCheckImage: {
      type: Boolean,
      default: false
    },
    // 是否是拍照上墙互动
    isTakePicture: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    showBigPicture() {
      this.visibleBigImage = true
    },
    handleViewExercise() {
      this.$emit('viewBigPictureDuringInterect')
      this.cancelView()
    },
    cancelView() {
      logger.send({
        tag: 'student.Interact',
        content: {
          msg: '关闭作业盒子视图',
          interactType: 'Wall'
        }
      })
      this.visibleBigImage = false
      this.$emit('cancelViewAssignmentBigPicture')
    },
    closeTipsPane() {
      logger.send({
        tag: 'student.Interact',
        content: {
          msg: '点击x按钮关闭作业盒子视图',
          interactType: 'Wall'
        }
      })
      this.cancelView()
    }
  },
  async mounted() {
    this.local = await getLocal()
    // 如果是拍照上墙订正，直接显示大图
    if (this.isTakePicture) {
      this.handleViewExercise()
    }
  }
}
</script>

<style lang="scss" scoped>
.checkedExerise {
  // position: absolute;
  z-index: 999;
  height: 100%;
}
.smallclass-checkedExerise {
  width: 255px;
  height: 94px;
  background: rgba(15, 25, 42, 0.9);
  border-radius: 10px;
  position: absolute;
  right: 175px;
  bottom: 12px;
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
  .close {
    width: 26px;
    height: 26px;
    display: block;
    position: absolute;
    background-position: 0 0;
    background-size: cover;
    background-image: url('./img/check_close.png');
    right: -10px;
    top: -10px;
    cursor: pointer;
  }
  .content {
    .top {
      width: 253px;
      height: 54px;
      border-radius: 10px 10px 0px 0px;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        width: 34px;
        height: 34px;
        display: block;
        background-repeat: no-repeat;
        background-position: 0;
        background-size: 100%;
        background-image: url('./img/icon_homework.png');
      }
      p {
        font-size: 12px;
        width: 200px;
        margin-left: 4px;
        color: #dee2e7;
        line-height: 14px;
        font-weight: 500;
        // margin-bottom: 24px;
      }
    }
    .bottom {
      width: 253px;
      height: 40px;
      background: #0f192a;
      font-size: 14px;
      font-weight: 500;
      border-radius: 0px 0px 10px 10px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .button-view {
    color: #ffcf1b;
  }
  .loading {
    // background: #aeb1b6;
    color: #fff;
  }
}
// 修改modal样式
::v-deep .ant-modal-mask {
  background-color: rgba(0, 0, 0, 0.5);
}
::v-deep .ant-modal-header {
  display: none;
}
::v-deep .ant-modal-content {
  border-radius: 16px;
}
::v-deep .ant-modal {
  width: 360px;
  height: 200px;
  top: 50%;
  margin-top: -100px;
  .ant-modal-body {
    width: 360px;
    height: 200px;
    padding: 0;
  }
  .button-view {
    width: 310px;
    height: 48px;
    background: linear-gradient(45deg, rgba(255, 213, 24, 1) 0%, rgba(255, 170, 10, 1) 100%);
    border-radius: 24px;
    text-align: center;
    line-height: 48px;
    color: #fff;
    font-size: 16px;
    margin: 0 auto;
    cursor: pointer;
  }
  .loading {
    background: #aeb1b6;
  }
}
</style>
