<template>
  <div
    class="countDownBox"
    v-drag="{
      dragWrapSelector: '#interactionController'
    }"
  >
    <img class="countDownIcon" src="./countDownIcon.png" alt="" />
    <template v-for="(item, index) in this.countdown">
      <div class="numBox" v-if="index != 2" :key="'numBox-' + index">{{ item }}</div>
      <div class="colon" v-else :key="'colon-' + index">:</div>
    </template>
    <audio src="./audio/pop.mp3" ref="countDownSound"></audio>
  </div>
</template>

<script>
import drag from '@thinkacademy/vitas-utils/drag'
export default {
  props: {
    time: {
      type: Number,
      required: true,
      default: 0
    },
    format: {
      type: String,
      default: 'mm:ss'
    }
  },
  directives: {
    drag
  },
  data() {
    return {
      remainingTime: Math.ceil(this.time / 1000) >= 0 ? Math.ceil(this.time / 1000) : 0
    }
  },
  computed: {
    // @log-ignore
    countdown() {
      const minutes = Math.floor(this.remainingTime / 60)
      const seconds = this.remainingTime % 60
      let format = this.format
      format = format.replace('mm', minutes.toString().padStart(2, '0'))
      format = format.replace('ss', seconds.toString().padStart(2, '0'))
      return format
    }
  },
  mounted() {
    console.log('this.leftTime 123', this.time)
    this.timer = setInterval(() => {
      // @log-ignore
      if (this.remainingTime > 0) {
        this.remainingTime--
      } else {
        this.remainingTime = 0
        this.$refs.countDownSound.play()
        clearInterval(this.timer)
        this.$emit('complete')
      }
    }, 1000)
  },
  beforeDestroy() {
    clearInterval(this.timer)
  }
}
</script>
<style lang="scss" scoped>
.countDownBox {
  background: rgba(15, 25, 42, 0.7);
  border-radius: 8px;
  backdrop-filter: blur(4px);
  display: flex;
  padding: 5px 10px;
  align-items: center;
  position: absolute;
  bottom: 1px;
  left: 16px;
  cursor: move;

  .countDownIcon {
    width: 26px;
    height: 30px;
    margin-right: 6px;
  }
  .numBox {
    width: 30px;
    height: 30px;
    background: rgba(147, 157, 175, 0.3);
    border-radius: 4px;
    font-size: 26px;
    font-family: SFProRounded-Bold, SFProRounded;
    font-weight: bold;
    color: #ffffff;
    line-height: 28px;
    margin-right: 2px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .colon {
    font-size: 26px;
    font-family: SFProRounded-Bold, SFProRounded;
    font-weight: bold;
    color: #ffffff;
    margin-right: 4px;
    margin-left: 2px;
  }
}
</style>
