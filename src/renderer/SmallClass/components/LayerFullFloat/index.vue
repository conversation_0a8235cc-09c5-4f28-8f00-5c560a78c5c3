<template>
  <div class="full-float-container">
    <!-- 拍一拍提醒，在互动上层 -->
    <NudgeRemind style="z-index: 96" />
    <!-- 作业盒子z-index: -1使飞金币效果在其上面 -->
    <AssignmentBox
      ref="assignmentBoxRef"
      :plan-id="baseData.planInfo.id"
      :options="baseData.commonOption"
      v-clickoutside="clickBoxOutside"
      style="z-index: -1"
    ></AssignmentBox>
    <!-- chatbox -->
    <ChatBox
      class="pointer-events-auto absolute bottom-[0.16rem]"
      :style="{ right: chatBoxDomRight }"
      style="z-index: -2"
    ></ChatBox>
    <GameInteraction
      v-if="isShowGameInteraction"
      :options="gameInteractionOptions"
      @closeGame="isShowGameInteraction = false"
      style="z-index: 97"
    />
    <CoinsBadge style="z-index: 96" />
    <FlyCoins style="z-index: 98" />
    <UploadSuccessDialog style="z-index: 98" />
    <LiveKickout style="z-index: 99"></LiveKickout>
    <template v-for="interaction in interactions">
      <component
        :ref="interaction.name"
        :is="interaction.componentName"
        :key="interaction.name"
        :layout="interaction.layout"
        v-if="interaction.isShow"
        :options="interaction.options"
        @close="interactionClose"
        style="pointer-events: none"
      ></component>
    </template>
    <OpenMicTip></OpenMicTip>
  </div>
</template>

<script>
import {
  onMounted,
  onBeforeUnmount,
  defineComponent,
  getCurrentInstance,
  ref,
  nextTick,
  reactive
} from '@vue/composition-api'
import CoinsBadge from '../Header/Coins/CoinsBadge/index.vue'
import AssignmentBox from '../Header/AssignmentBox/index.vue'
import Clickoutside from 'utils/clickoutside'
import ChatBox from '../ChatBox/index.vue'
import GameInteraction from '../../interactions/GameForCourseware'
import FlyCoins from '../Header/Coins/FlyCoins/index.vue'
import UploadSuccessDialog from './UploadSuccessDialog/index.vue'
import LiveKickout from '../liveKickout/index.vue'
import TakePicture from '../../interactions/TakePicture/index.vue'
import RandomCall from '../../interactions/RandomCall'
import OpenMicTip from './OpenMicTip/index.vue'
import NudgeRemind from '../LayerFloat/NudgeRemind/index.vue'
import { getCloudConfig } from 'utils/initConfig'

import { clearScreenShotTimer, setIntervalScreenShot } from '../../hooks/useInteractScreenShot'

export default defineComponent({
  name: 'FullFloatLayer',
  components: {
    AssignmentBox,
    ChatBox,
    GameInteraction,
    CoinsBadge,
    FlyCoins,
    UploadSuccessDialog,
    LiveKickout,
    TakePicture,
    RandomCall,
    OpenMicTip,
    NudgeRemind
  },
  directives: {
    Clickoutside
  },
  setup() {
    const { proxy } = getCurrentInstance()
    const baseData = proxy.$store.state.smallClass.baseData
    const assignmentBoxRef = ref(null)
    const assignmentEntryDom = ref(null)
    const checkBigPictureDom = ref(null)
    const isShowGameInteraction = ref(false)
    const gameInteractionOptions = ref({})
    const clickBoxOutside = e => {
      // 打开作业盒子-clickoutside排除的dom
      // 排除的dom 1.打开查看作业盒子入口的dom 2.查看大图的dom
      const targetDom = e.target
      const isExcludeDom =
        (assignmentEntryDom.value && assignmentEntryDom.value.contains(targetDom)) ||
        (checkBigPictureDom.value && checkBigPictureDom.value.contains(targetDom))
      if (isExcludeDom) {
        return
      }
      // 隐藏作业盒子
      nextTick(() => {
        assignmentBoxRef.value.hideAssignmentBoxOnly()
      })
    }
    const listenerEvents = () => {
      proxy.$bus.$on('handleOpenBox', wrapperDom => {
        assignmentEntryDom.value = wrapperDom
        nextTick(() => {
          assignmentBoxRef.value.handleOpenAssignmentBox()
        })
      })
      // 查看大图dom
      proxy.$bus.$on('emitBigImgDom', wrapperDom => {
        checkBigPictureDom.value = wrapperDom
      })
      let game_config = {}
      const cloudConfig = getCloudConfig()
      if (cloudConfig && cloudConfig.configs) {
        for (let i = 0; i < cloudConfig.configs.length; i++) {
          const element = cloudConfig.configs[i]
          if (element.configKey === 'hw_game_config') {
            game_config = JSON.parse(element.configValue)
            break
          }
        }
      }
      console.info('游戏配置云控', game_config)
      proxy.$bus.$on('gameInteract', noticeContent => {
        console.log('发布游戏gameInteract', noticeContent)
        gameInteractionOptions.value = {
          roomMessage: {
            roomInfo: {
              ...proxy.$store.state.smallClass.baseData
            }
          },
          ircMsg: noticeContent
        }
        if (noticeContent.jsString && game_config.closeLoadGame !== 1) {
          proxy.$bus.$emit('coursewarePlayGame', gameInteractionOptions.value)
        } else {
          if (noticeContent.pub) {
            isShowGameInteraction.value = true
          } else {
            isShowGameInteraction.value = false
          }
        }
      })
    }
    const chatBoxDomRight = ref('0rem')
    const openScreenShot = ['take_picture']

    onMounted(() => {
      listenerEvents()
      // 获取 #tools-chatbox dom 在屏幕上的位置
      const chatBoxDom = document.getElementById('tools-chatbox')
      if (chatBoxDom) {
        const chatBoxDomRect = chatBoxDom.getBoundingClientRect()
        // 获取屏幕的宽
        const screenWidth = document.body.clientWidth
        chatBoxDomRight.value = (screenWidth - chatBoxDomRect.left - 16) / 100 + 'rem'
        console.log('chatBoxDomRight', screenWidth, chatBoxDomRect.left, chatBoxDomRight)
      }
    })
    onBeforeUnmount(() => {
      proxy.$bus.$off('handleOpenBox')
      proxy.$bus.$off('emitBigImgDom')
      proxy.$bus.$off('gameInteract')
    })

    const interactions = reactive([
      {
        name: 'TakePicture', // 拍照上墙
        componentName: 'TakePicture',
        layout: 'full',
        isShow: false,
        keys: ['take_picture'],
        options: {
          roomMessage: {
            roomInfo: {
              ...proxy.$store.state.smallClass.baseData
            }
          },
          ircMsg: ''
        }
      },
      {
        name: 'RandomCall',
        componentName: 'RandomCall',
        layout: 'full',
        isShow: false,
        keys: ['small_random_call'],
        options: {
          roomMessage: {
            roomInfo: {
              ...proxy.$store.state.smallClass.baseData
            }
          },
          ircMsg: ''
        }
      }
    ])
    const noRepeatTrigger = ['small_random_call', 'speedyHand', 'distribute_coins', 'redPacket']

    const handleOpenInteraction = (interaction, options) => {
      interaction.isShow = true
      interaction.options.ircMsg = options.noticeContent
      interaction.options.isHistory = options.isHistory
      interaction.options['sendTime'] = options.sendTime
      nextTick(() => {
        if (proxy.$refs[interaction.name] && proxy.$refs[interaction.name][0].receiveMessage) {
          proxy.$refs[interaction.name][0].receiveMessage(options.noticeContent)
        }
      })
      if (openScreenShot.includes(options.key)) {
        const planId = proxy.$store.state.smallClass.baseData?.planInfo?.id
        setIntervalScreenShot(planId, options?.noticeContent?.interactId, options?.key)
      }
    }
    proxy.$bus.$on('take_picture', options => {
      // @log-ignore
      handleInteractions(options)
    })
    proxy.$bus.$on('small_random_call', options => {
      // @log-ignore
      handleInteractions(options)
    })

    function handleInteractions(options) {
      // @log-ignore
      const { key, noticeContent, isTrigger } = options
      if (noRepeatTrigger.includes(key) && isTrigger) {
        console.warn(`不恢复的${key}互动，不触发'handleInteractions'函数`)
        return
      }
      const { pub, open, publishTopic } = noticeContent
      const interaction = findInteraction(key)
      if (pub || open || publishTopic) {
        proxy.$store.dispatch('smallClass/updateInteractionStatus', {
          interactionName: key,
          interactionStatus: true
        })
        // 如果当前有同一类型互动且不是同一个，则先销毁上一个再打开新的互动
        if (interaction.isShow) {
          if (noticeContent.interactId !== interaction.interactId) {
            interaction.isShow = false
            nextTick(() => {
              handleOpenInteraction(interaction, options)
            })
          } else {
            if (proxy.$refs[interaction.name] && proxy.$refs[interaction.name][0].receiveMessage) {
              proxy.$refs[interaction.name][0].receiveMessage(options.noticeContent)
            }
          }
        } else {
          handleOpenInteraction(interaction, options)
        }
        interaction.interactId = noticeContent.interactId
      } else if (
        pub === false ||
        open === false ||
        pub === 0 ||
        open === 0 ||
        publishTopic === false // publishTopic 是答题板私有控制，答题板的关闭，是由端上的倒计时决定的
      ) {
        proxy.$store.dispatch('smallClass/updateInteractionStatus', {
          interactionName: key,
          interactionStatus: false
        })
        // 有些互动在收到停止作答和结束互动信令时还需要例如提交答案等操作，因此需要内部处理
        if (proxy.$refs[interaction.name] && proxy.$refs[interaction.name][0]?.destroyInteraction) {
          proxy.$refs[interaction.name][0]?.destroyInteraction(noticeContent)
          clearScreenShotTimer()
          return
        }
        clearScreenShotTimer()
        interaction.isShow = false
      }
    }

    function findInteraction(key) {
      // @log-ignore
      const interaction = interactions.find(item => item.keys.includes(key))
      return interaction
    }
    function interactionClose(key) {
      const interaction = findInteraction(key)
      interaction.isShow = false
    }
    onBeforeUnmount(() => {
      clearScreenShotTimer()
      proxy.$bus.$off('take_picture')
      proxy.$bus.$off('small_random_call')
    })

    return {
      baseData,
      assignmentBoxRef,
      isShowGameInteraction,
      gameInteractionOptions,
      clickBoxOutside,
      chatBoxDomRight,
      interactions,
      interactionClose
    }
  }
})
</script>
