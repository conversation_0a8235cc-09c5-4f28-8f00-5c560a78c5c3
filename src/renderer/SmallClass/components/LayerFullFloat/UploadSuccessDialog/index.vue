<template>
  <div class="upload-success-container" v-if="showDialog">
    <div class="container w-[3.2rem] h-[1.65rem]" :class="{ 'h-[1.15rem]': !data.coins }">
      <div class="bg-img w-[2.4rem] h-[1.8rem] top-[-1.16rem]"></div>
      <div class="content w-full h-[0.9rem] mt-[0.75rem]" :class="{ 'mt-[0.25rem]': !data.coins }">
        <p class="title text-large" :class="{ 'mt-[0.39rem]': !data.coins }">{{ data.tips }}</p>
        <div class="coins-container flex justify-center items-center" v-if="data.coins">
          <div class="coins w-[0.32rem] h-[0.32rem] mr-[0.08rem]"></div>
          <span class="icon-number text-big">+{{ data.coins }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      data: {},
      showDialog: false
    }
  },
  mounted() {
    this.$bus.$on('uploadSuccess', data => {
      this.data = data
      this.showDialog = true
      setTimeout(() => {
        this.showDialog = false
      }, 3000)
    })
  },
  beforeMount() {
    this.$bus.$off('uploadSuccess')
  }
}
</script>
<style scoped lang="less">
.upload-success-container {
  width: 100%;
  height: 100%;
  top: 0;
  position: fixed;
  .container {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    box-sizing: border-box;
    background: linear-gradient(180deg, #fff0d6 0%, #ffffff 100%);
    border-radius: 12px;
    .content {
      background: url('~assets/images/icon_upload_success_bg.png');
      background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
    }
    p {
      font-weight: bold;
      color: #172b4d;
      line-height: 19px;
      text-align: center;
    }
    .bg-img {
      background: url('~assets/images/icon_upload_success.png');
      background-size: 100% 100%;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }
    .coins-container {
      flex: 1;
    }
    .coins {
      background: url('./icon-coin.png');
      background-size: 100% 100%;
    }
    .icon-number {
      font-weight: bold;
      color: #ff8a3f;
      line-height: 26px;
    }
  }
}
</style>
