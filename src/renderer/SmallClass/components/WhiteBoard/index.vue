<template>
  <div id="white-board-wrapper">
    <white-board-canvas ref="WhiteBoard" />
    <white-board-tools ref="WhiteBoardTools" />
    <!-- 展示画笔 -->
    <div class="authorize-pen" v-if="isAuthorized">
      <img
        v-for="item in penTools"
        :key="item.key"
        :src="item.icon"
        class="eraser"
        @click="selectTool(item.key)"
        :class="`${item.key} ${selectedKey === item.key ? 'select-item' : ''}`"
      />
    </div>
  </div>
</template>
<script>
import logger from 'utils/logger'
import WhiteBoard from '@thinkacademy/new-white-board'
import '@thinkacademy/new-white-board/lib/index/style.css' // 样式文件单独引入
import WhiteBoardTools from '@thinkacademy/white-board-tools'
import '@thinkacademy/white-board-tools/lib/index/style.css' // 样式文件单独引入
import { getHistoryMessage } from 'core/getHistoryMsg'
import { getCanvasScale } from 'utils/canvasScale'
import { fetchIsAuthorizedUserList } from 'api/classroom/index'
import _debounce from 'lodash/debounce'
import { formatBinaryData, longDataToNumber } from 'utils/classwareCanvasUtil'
let BINARY_DATA_CACHE = {} // 涂鸦消息数据缓存

export default {
  name: 'CoursewareWhiteBoard',
  components: {
    WhiteBoardCanvas: WhiteBoard.WhiteBoardCanvas,
    WhiteBoardTools
  },
  data() {
    return {
      baseData: this.$store.state.smallClass.baseData,
      pageId: 0,
      useInfo: window.localStorage.getItem('userInfo'), // TODO: remove from localStorage, use props instead
      isBanned: false,
      currentThickness: 4, // 初始化笔迹大小
      currentLineDashed: false, // 是否带有笔锋
      eraserSize: 50, // 橡皮擦直径
      isAuthorized: false, // 是否被授权
      selectedKey: 'red-pen', // 当前选中的工具
      containerWidth: 0, // 涂鸦区域初始宽高
      containerHeight: 0,
      isInit: false, // 涂鸦是否初始化过
      penTools: [
        {
          key: 'yellow-pen',
          icon: require('@/assets/images/smallClass/yellow-pen.png')
        },
        {
          key: 'red-pen',
          icon: require('@/assets/images/smallClass/red-pen.png')
        },
        {
          key: 'eraser',
          icon: require('@/assets/images/smallClass/eraser.png')
        }
      ],
      historyList: [], // 多人历史消息
      currentDbKey: [], // 当前课件dbkey
      canvasScale: 2
    }
  },
  watch: {
    // 只有自己的状态更改为被授权才更改成默认第一支画笔
    isAuthorized: {
      handler(val) {
        this.sendLogger(`授权涂鸦 ${val}`)
        if (val) {
          // 如果学生设置不可操作尺规setWBToolsStatus(false)，此时尺规sdk内部设置this.canv.style.pointerEvents = "none",当学生被授权画笔可涂鸦时使用橡皮擦除时涂层有问题
          // 因此增加第二个参数为当学生授权涂鸦时修改pointerEvents
          this.$refs.WhiteBoardTools.setWBToolsStatus(false, true)
          this.selectTool('yellow-pen') // 设置面板默认选中第一支颜色笔
        } else {
          this.$refs.WhiteBoardTools.setWBToolsStatus(false) // 设置学生权限不可操作尺规
        }
      }
    }
  },
  async mounted() {
    window.addEventListener('resize', this.setBoardAdaptation)
    this.setBoardAdaptation()
    this.listenerSignalService()
    this.$refs.WhiteBoardTools?.handleMenuEnable(false) // 隐藏原生尺规菜单
    this.canvasScale = await getCanvasScale()
    this.initBoard()
  },
  beforeDestroy() {
    this.removeListenerSignalService()
    if (this.$refs.WhiteBoard) {
      this.$refs.WhiteBoard.uninit()
    }
  },
  methods: {
    /**
     * 初始化涂鸦
     */
    initBoard() {
      const coursewareDom = document.getElementById('white-board-wrapper')
      const coursewareWidth = coursewareDom.offsetWidth
      const coursewareHeight = coursewareDom.offsetHeight
      // 初始化标识
      this.isInit = true
      // 获取当前画布大小并传到canvas中
      const params = {
        canvas: {
          penType: 'Bspline',
          strokeType: 'none',
          scale: this.canvasScale,
          pageChangeReport: false,
          sendRoomCanvasMessage: this.sendRoomCanvasMessage.bind(this),
          getHistoryMessage: this.getHistoryMessage.bind(this),
          enableKeyboardDelete: true,
          enableFittingShape: false // 笔记自动变为直线关闭
        },
        common: {
          liveId: String(this.baseData.planInfo.id),
          role: 'student',
          dataVersion: '1',
          courseId: '',
          userId: this.baseData.stuInfo.id + '',
          userName: this.baseData.stuInfo.nickName, // 用于展示的用户昵称
          screenWidth: coursewareWidth, // 屏幕宽度
          screenHeight: coursewareHeight, // 屏幕高度 Init
          roomIds: [this.baseData.commonOption.roomlist[0]],
          fastFrequency: 2000,
          slowFrequency: 3000,
          serverTimestamp: +new Date() + this.baseData.commonOption.timeOffset
        },
        accessControl: {
          showMenu: false,
          mode: 'itsAndCanvas',
          isBanned: this.isBanned,
          enableHistoryMessage: true,
          showCursor: true,
          enableRetrySend: true,
          enableLogSend: false,
          enableFittingShape: false // 笔记自动变为直线关闭
        }
      }
      // console.log('涂鸦初始化参数', params)
      this.sendLogger(`小班涂鸦初始化参数params: ${JSON.stringify(params)}`)
      if (this.$refs.WhiteBoard) {
        this.$refs.WhiteBoard.uninit()
        this.$refs.WhiteBoard.init(params)
        this.$refs.WhiteBoard.handleMouse('default')
        let mainBoardHandWritting = this.$refs.WhiteBoard.getMainBoardHandWritting()
        let pluginManager = this.$refs.WhiteBoard.getPluginManager()
        let whiteboard = this.$refs.WhiteBoard
        // 初始化尺规工具
        this.$refs.WhiteBoardTools.init(whiteboard, mainBoardHandWritting, pluginManager)
        // 消息注册 11: 直尺，12:三角板 30度，13:圆规，14:三角板 45度， 15:量角器，1000: 工具缩放大小
        const registerData = [11, 12, 13, 14, 15, 1000]
        registerData.forEach(key => {
          if (key === 1000) {
            pluginManager.registerEvent(
              'WhiteboardResize',
              key,
              this.toolsMessageHandler('WhiteboardResize', key)
            )
          } else {
            pluginManager.registerEvent(
              'ReceiveBinaryData',
              key,
              this.toolsMessageHandler('ReceiveBinaryData', key)
            )
          }
        })
        this.$refs.WhiteBoardTools.setWBToolsStatus(false) // 设置学生权限不可操作尺规
      }
    },
    // 处理尺规的同步，拉取历史数据同步， 翻页同步， 正常同步
    toolsMessageHandler(actionType, toolType) {
      // @log-ignore
      return msg => {
        // @log-ignore
        this.$refs.WhiteBoardTools.receiveBinaryData(actionType, toolType, this.pageId, msg)
      }
    },
    initCatalogueInfo(dbKey) {
      // @log-ignore
      let courseInfo = [
        {
          index: 0,
          isHide: 0,
          pageId: this.pageId,
          title: '标题',
          type: 'course'
        }
      ]
      try {
        // 设置当前学生的dbkey
        const stuDbKey = `${this.useInfo ? JSON.parse(this.useInfo).uid : ''}_${dbKey}`
        this.$refs.WhiteBoard.handleCatalogueChange(courseInfo)
        this.$refs.WhiteBoard.handleResetImComingDbkey(stuDbKey)
        this.$refs.WhiteBoard.handlePageChange(String(this.pageId))
        this.$refs.WhiteBoardTools.handlePageChange(String(this.pageId))
      } catch (err) {
        console.error('initCatalogueInfo JSON.parse异常捕获', err)
      }
    },
    sendRoomCanvasMessage(roomId, canvasMessage) {
      // @log-ignore
      this.thinkClass.SignalService.sendRoomBinMessage(
        [this.baseData.commonOption.roomlist[0]],
        canvasMessage.dbKey,
        canvasMessage.keyMsgId,
        canvasMessage.content
      )
      // console.log('sendRoomCanvasMessage', canvasMessage)
      this.sendLogger(`sendRoomCanvasMessage: ${canvasMessage.keyMsgId} ${canvasMessage.dbKey}`)
    },
    async getHistoryMessage(data) {
      const { dbKey } = data['info'][0]
      const teachdbkey = dbKey
        .split('_')
        .splice(1)
        .join('_')
      let userPromise = []
      // console.log('获取用户base', this.baseData, dbKey)
      userPromise.push(
        getHistoryMessage({
          dbkey: teachdbkey,
          appId: this.baseData.configs.ircAk,
          sk: this.baseData.configs.ircSk,
          businessId: 3,
          liveId: this.baseData.planInfo.id,
          ircApiHost: this.baseData.configs.ircApiHost
        })
      )
      // 重新拉取最新被授权学生列表接口
      const allAuthorizedUserList = await this.fetchIsAuthorizedUserInfo({
        planId: Number(this.baseData.planInfo.id)
      })
      this.$store.dispatch('smallClass/updateIsAuthorizedUserList', allAuthorizedUserList)

      const isAuthorizedUserList = this.$store.state.smallClass.isAuthorizedUserList
      // console.log('isAuthorizedUserList', isAuthorizedUserList)
      // 获取该页（相同dbkey）被授权学员列表
      const curPageKeyUserList = isAuthorizedUserList.find(e => e.pageKey === teachdbkey)

      if (curPageKeyUserList && curPageKeyUserList.userIdList) {
        curPageKeyUserList.userIdList.forEach(stu => {
          const studbkey = `${stu}_${teachdbkey}`
          // // console.log('翻页', this.baseData.configs.ircApiHost)
          userPromise.push(
            getHistoryMessage({
              dbkey: studbkey,
              appId: this.baseData.configs.ircAk,
              sk: this.baseData.configs.ircSk,
              businessId: 3,
              liveId: this.baseData.planInfo.id,
              ircApiHost: this.baseData.configs.ircApiHost
            })
          )
        })
      }
      // console.log('userPromise', userPromise)
      Promise.all(userPromise)
        .then(resAll => {
          // @log-ignore
          // console.log('resAll', resAll)
          let msgs = resAll.flat()
          try {
            this.$refs.WhiteBoard &&
              this.$refs.WhiteBoard.handleRecoverHistoryMessage([{ content: msgs }])
          } catch (e) {
            console.error('handleRecoverHistoryMessage', e)
          }
        })
        .catch(err => {
          console.error('获取历史涂鸦失败', err)
        })
    },
    // 处理接收到的涂鸦消息
    handleRoomCanvasMessage(res) {
      // @log-ignore
      const binData = longDataToNumber(formatBinaryData(res.content))
      // console.log('涂鸦全量消息', binData)
      const key = `${binData.pageId}_${binData.msgId}_${binData.dataCreateTimestamp}` // 唯一key转换string
      if (BINARY_DATA_CACHE[key]) return // 重复消息过滤
      BINARY_DATA_CACHE[key] = 1 // 记录缓存
      this.$refs.WhiteBoard && this.$refs.WhiteBoard.handleRoomCanvasMessage(res)
    },
    // 发送涂鸦消息回调
    onSendRoomBinMessageResp(res) {
      // @log-ignore
      if (res.code === 0) {
        this.$refs.WhiteBoard &&
          this.$refs.WhiteBoard.handleSendMessageSuccess(res.dbKey, res.keyMsgId)
      } else {
        this.sendLogger(`发送涂鸦消息回调: ${res}`, 'error')
        this.$refs.WhiteBoard &&
          this.$refs.WhiteBoard.handleSendMessageError(res.code, res.msg, res.dbKey, res.keyMsgId)
      }
    },
    // 处理涂鸦历史消息
    onGetRoomHistoryBinMessageNotice(res) {
      // 获取所有页被授权学员列表
      this.sendLogger(`处理拉取到的涂鸦历史消息`)
      const isAuthorizedUserList = this.$store.state.smallClass.isAuthorizedUserList
      // 获取该页（相同dbkey）被授权学员列表
      const curPageKeyUserList = isAuthorizedUserList.find(e => e.pageKey === this.currentDbKey)

      if (curPageKeyUserList && curPageKeyUserList.userIdList) {
        this.historyList.push(res)
        if (this.historyList.length === curPageKeyUserList.userIdList.length + 1) {
          let newData = this.historyList.flat()
          newData.sort((a, b) => a.msgId - b.msgId)
          // console.log('newData2', newData)
          newData.forEach((element, index) => {
            newData[index].content = Uint8Array.from(element.content)
          })
          // console.log('newData', newData)
          try {
            this.$refs.WhiteBoard &&
              this.$refs.WhiteBoard.handleRecoverHistoryMessage([{ content: newData }])
          } catch (e) {
            this.sendLogger(`涂鸦sdk报错 ${e}`, 'error')
          }
          this.historyList = []
        }
      } else {
        const newData = res
        if (Array.isArray(newData)) {
          newData.forEach((element, index) => {
            newData[index].content = Uint8Array.from(element.content)
          })
          // console.log('newData1', newData)

          // 加try-catch 为了解决涂鸦sdk报错问题,他们暂时不给解决
          try {
            this.$refs.WhiteBoard &&
              this.$refs.WhiteBoard.handleRecoverHistoryMessage([{ content: newData }])
          } catch (e) {
            console.error('涂鸦sdk报错', e)
          }
        } else {
          console.error('涂鸦历史消息格式错误')
        }
      }
    },
    // 获取被授权学员列表
    async fetchIsAuthorizedUserInfo(params) {
      try {
        const res = await fetchIsAuthorizedUserList(params)
        return res.code === 0 ? res.data.pageKeyList : []
      } catch (e) {
        console.error('获取授权学员列表报错', e)
        return []
      }
    },
    /**
     * 切换课件ID
     */
    changePageId(id, currentCourseWareData) {
      // @log-ignore
      this.pageId = id
      const { specificLiveKey, courseWareId, pageId } = currentCourseWareData
      if (!specificLiveKey || !courseWareId || !pageId) {
        console.error('课件信息错误', currentCourseWareData)
        return
      }
      const dbKey = `${specificLiveKey}_${courseWareId}_${pageId}`
      this.initCatalogueInfo(dbKey)
    },
    // 接收到学生上下台以及授权指令时判断自己授权状态
    setIsAuthorizedStatus(noticeContent) {
      console.log('授权涂鸦', noticeContent)
      try {
        const userId = this.useInfo ? JSON.parse(this.useInfo).uid : ''
        const userInfo = noticeContent.data.find(item => item.userId === userId)
        if (userInfo && userInfo.isAuthorize === 1) {
          // 记录授权状态
          this.isAuthorized = true
        } else {
          this.isAuthorized = false
          this.$refs.WhiteBoard.handleMouse('default')
        }
      } catch (err) {
        this.sendLogger(`JSON.parse异常捕获: ${err}`, 'error')
      }
    },
    // 涂鸦区域缩放
    setBoardAdaptation: _debounce(function() {
      const whiteBoardDom = document.getElementById('white-board-wrapper')
      if (this.isInit) {
        // 如果初始化过,后续再出发则是重置尺寸
        this.$refs.WhiteBoard &&
          this.$refs.WhiteBoard.handleResizeCanvas(
            whiteBoardDom.offsetWidth,
            whiteBoardDom.offsetHeight,
            true
          )
      }
    }, 300),
    /**
     * 授权涂鸦选择画笔或橡皮擦
     */
    selectTool(type) {
      this.selectedKey = type
      if (type === 'yellow-pen' || type === 'red-pen') {
        if (this.$refs.WhiteBoard) {
          this.$refs.WhiteBoard.setPenStyle({
            color: type === 'yellow-pen' ? '#FFD82D' : '#FF503F',
            thickness: this.currentThickness, // 笔记大小
            lineDashed: this.currentLineDashed // 是否带笔锋
          })
          this.$refs.WhiteBoard.handlePen()
          this.sendLogger(`授权涂鸦选择画笔 ${type}`)
        } else {
          this.sendLogger('授权涂鸦选择画笔失败，未获得涂鸦元素WhiteBoard')
        }
      } else {
        if (this.$refs.WhiteBoard) {
          this.$refs.WhiteBoard.setEraserStyle(this.eraserSize) // 设置橡皮大小
          this.$refs.WhiteBoard.handleEraser()
          this.sendLogger(`授权涂鸦选择橡皮，大小${this.eraserSize}`)
        } else {
          this.sendLogger('授权涂鸦选择橡皮失败，未获得涂鸦元素WhiteBoard')
        }
      }
    },
    // 监听信令消息
    listenerSignalService() {
      this.thinkClass.SignalService.on('onRecvRoomBinMessageNotice', this.handleRoomCanvasMessage)
      this.thinkClass.SignalService.on('onSendRoomBinMessageResp', this.onSendRoomBinMessageResp)
      // 监听信令历史消息
      this.thinkClass.SignalService.on(
        'onGetRoomHistoryBinMessageNotice',
        this.onGetRoomHistoryBinMessageNotice
      )
      this.$bus.$on('changePageId', (id, currentCourseWareData) => {
        this.changePageId(id, currentCourseWareData)
      })
      this.$bus.$on('setIsAuthorizedStatus', noticeContent => {
        this.setIsAuthorizedStatus(noticeContent)
      })
    },
    // 移除监听信令消息
    removeListenerSignalService() {
      this.thinkClass.SignalService.off('onRecvRoomBinMessageNotice', this.handleRoomCanvasMessage)
      this.thinkClass.SignalService.off('onSendRoomBinMessageResp', this.onSendRoomBinMessageResp)
      this.thinkClass.SignalService.off(
        'onGetRoomHistoryBinMessageNotice',
        this.onGetRoomHistoryBinMessageNotice
      )
      this.$bus.$off('changePageId')
      this.$bus.$off('setIsAuthorizedStatus')
    },
    sendLogger(msg, level = 'info') {
      logger.send({
        tag: 'smallClass-griffiti',
        content: {
          msg: msg
        },
        level
      })
    }
  }
}
</script>

<style lang="less" scoped>
#white-board-wrapper {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;

  /deep/.canvas-shape,
  /deep/.canvas-main,
  /deep/.canvas-temp {
    width: 100% !important;
    height: 100% !important;
  }

  .authorize-pen {
    position: fixed;
    bottom: 0;
    left: calc(50% - 104px);
    width: 208px;
    height: 60px;
    background: #1a1a1a;
    border-radius: 100px;
    border: 2px solid rgba(255, 255, 255, 0.4);
    z-index: 999;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: auto;
    img {
      width: 25px;
      height: 50px;
      bottom: -22px;
      position: relative;
      cursor: pointer;
    }

    img:nth-child(2) {
      margin: 0 30px;
    }

    .select-item {
      bottom: -6px;
    }
  }
}

.container {
  z-index: 66; // 涂鸦z-index
}
</style>
