.ignore-kickout-modal {
  p {
    font-size: 16px;
    font-weight: 500;
    color: #172b4d;
    line-height: 21px;
    text-align: center;
    margin-bottom: 20px;
  }

  // 修改modal样式
  .ant-modal-mask {
    background-color: rgba(0, 0, 0, 0.4);
    z-index: 3000; // 高于全局悬浮层
  }
  .ant-modal-wrap {
    z-index: 3000; // 高于全局悬浮层
  }

  .ant-modal-header {
    display: none;
  }

  .ant-modal-content {
    border-radius: 16px;
    padding: 40px 10px 20px;
  }

  .ant-modal {
    // height: 200px;
    top: 50%;
    margin-top: -150px;

    .ant-modal-body {
      height: 100px;
      display: flex;
      align-items: center;
      flex-direction: column;
      padding: 0;
    }

    .ant-btn.ant-btn-primary {
      width: 220px;
      height: 40px;
    }

    .time-out {
      margin-left: 5px;
    }
  }
}