<!-- 视图层 -->
<template>
  <div class="kickout">
    <a-modal
      class="ignore-kickout-modal"
      :width="420"
      :closable="false"
      :maskClosable="false"
      :footer="null"
      v-model="liveKickout"
    >
      <p>{{ $t('classroom.modules.liveKickout.notice') }}</p>
      <a-button type="primary" shape="round" size="large" @click.stop="handleClose">
        {{ $t('common.confirm') }} <span class="time-out">({{ timeCount }}s)</span>
      </a-button>
    </a-modal>
  </div>
</template>

<script>
import { route_href } from 'utils/routeUtil'

export default {
  name: 'LiveKickout',
  props: {
    options: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      timer: null,
      liveKickout: false,
      timeCount: 10
    }
  },
  created() {},
  mounted() {
    // 函数名不能和变量名相同
    this.$bus.$on('live-kickout', this.liveKickoutFun)
  },
  methods: {
    liveKickoutFun(ircMessage) {
      console.log(ircMessage, 'ircMessage')
      if (ircMessage) {
        this.$bus.$emit('liveQuit')
        this.liveKickout = true
        this.timer = setInterval(() => {
          if (this.timeCount > 0) {
            this.timeCount--
          }
        }, 1000)
      }
    },
    handleClose() {
      this.liveKickout = false
      const curBackUrl = window.location.href.split('backUrl=')[1] || '/home'
      route_href('/#' + curBackUrl)
    }
  },
  watch: {
    timeCount: {
      handler(newVal) {
        if (newVal == 0) {
          this.handleClose()
        }
      }
    }
  },
  beforeDestroy() {
    clearInterval(this.timer)
    this.$bus.$off('live-kickout', this.liveKickoutFun)
  }
}
</script>
<style lang="scss">
@import './app.scss';
</style>
