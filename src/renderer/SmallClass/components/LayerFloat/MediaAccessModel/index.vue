<template>
  <Model
    dialogClass="ignore-mediaSecurityAccess"
    :isShowModal="showModal"
    :type="type"
    :leftBtnText="$t('classroom.modules.mediaSecurityAccess.cancel')"
    :rightBtnText="$t('classroom.modules.mediaSecurityAccess.confirm')"
    :title="titleTip"
    :subTitle="subTitleTip"
    :zIndex="9999"
    @leftBtnOperation="cancelAccess"
    @rightBtnOperation="confirmAccess"
  />
</template>

<script>
import { nativeApi, utilsApi } from 'utils/electronIpc'
import Model from '../../Model.vue'
import { i18n } from 'locale'
import logger from 'utils/logger'
export default {
  data() {
    return {
      showModal: false,
      type: 'default',
      titleMsg: '',
      subTitleMsg: '',
      cameraAccess: 'granted', // 默认摄像头已授权
      microphoneAccess: 'granted' // 默认摄像头已授权
    }
  },
  i18n,
  components: {
    Model
  },
  computed: {
    titleTip() {
      return this.titleMsg
    },
    subTitleTip() {
      return this.subTitleMsg
    }
  },
  mounted() {
    this.$bus.$on('showMediaSecurityAccess', type => {
      this.type = type
      this.checkAccess()
    })
  },
  beforeDestroy() {
    this.$bus.$off('showMediaSecurityAccess')
  },
  methods: {
    /**
     * 检查权限
     */
    async checkAccess() {
      if (!utilsApi.isMacOS()) return
      this.cameraAccess = await nativeApi.getCameraAccessStatus()
      this.microphoneAccess = await nativeApi.getMicAccessStatus()
      this.subTitleMsg = this.$t('classroom.modules.mediaSecurityAccess.tip')
      const handler = {
        camera: () => {
          if (this.cameraAccess !== 'granted') {
            this.titleMsg = this.$t('classroom.modules.mediaSecurityAccess.camera')
            this.showModal = true
            logger.send({
              tag: 'access',
              content: {
                msg: '无摄像头权限弹窗',
                cameraAccess: this.cameraAccess,
                type: this.type
              }
            })
          }
        },
        microphone: () => {
          if (this.microphoneAccess !== 'granted') {
            this.titleMsg = this.$t('classroom.modules.mediaSecurityAccess.microphone')
            this.showModal = true
            logger.send({
              tag: 'access',
              content: {
                msg: '无麦克风权限弹窗',
                microphoneAccess: this.microphoneAccess,
                type: this.type
              }
            })
          }
        },
        default: () => {
          // 优先判断 摄像头和麦克风 同时无权限。 因为 文案显示不一样
          if (this.cameraAccess !== 'granted' && this.microphoneAccess !== 'granted') {
            this.titleMsg = this.$t('classroom.modules.mediaSecurityAccess.deniedAccess')
            this.showModal = true
            logger.send({
              tag: 'access',
              content: {
                msg: '无麦克风和摄像头权限弹窗',
                cameraAccess: this.cameraAccess,
                microphoneAccess: this.microphoneAccess,
                type: this.type
              }
            })
            return
          }
          if (this.cameraAccess !== 'granted') {
            this.titleMsg = this.$t('classroom.modules.mediaSecurityAccess.camera')
            this.showModal = true
            logger.send({
              tag: 'access',
              content: {
                msg: '无摄像头权限弹窗',
                cameraAccess: this.cameraAccess,
                type: this.type
              }
            })
            return
          }
          if (this.microphoneAccess !== 'granted') {
            this.titleMsg = this.$t('classroom.modules.mediaSecurityAccess.microphone')
            this.showModal = true
            logger.send({
              tag: 'access',
              content: {
                msg: '无麦克风权限弹窗',
                microphoneAccess: this.microphoneAccess,
                type: this.type
              }
            })
            return
          }
        }
      }
      return handler[this.type]()
    },
    /**
     * 取消授权
     */
    cancelAccess() {
      logger.send({
        tag: 'access',
        content: {
          msg: '取消授权'
        }
      })
      this.showModal = false
    },
    /**
     * 允许授权
     */
    async confirmAccess() {
      if (this.type === 'default') {
        if (this.cameraAccess !== 'granted') {
          return await nativeApi.openPreferences('security', 'camera')
        }
        if (this.microphoneAccess !== 'granted') {
          return await nativeApi.openPreferences('security', 'microphone')
        }
      } else if (this.type === 'camera') {
        if (this.cameraAccess !== 'granted') {
          return await nativeApi.openPreferences('security', 'camera')
        }
      } else if (this.type === 'microphone') {
        if (this.microphoneAccess !== 'granted') {
          return await nativeApi.openPreferences('security', 'microphone')
        }
      }
      logger.send({
        tag: 'access',
        content: {
          msg: '允许授权',
          cameraAccess: this.cameraAccess,
          type: this.type
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ignore-mediaSecurityAccess {
  .bottom-wrapper {
    .btn {
      width: 45%;
    }
  }
}
</style>
