<template>
  <div class="camera-allow-confirm">
    <AllowAccessDialog
      :isAllOnStage="isAllOnStage"
      v-model="visible"
      theme="orange"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <div class="content">
        <div class="icon-camera"></div>
        <div class="notice">
          {{ $t('classroom.smallClass.cameraAllowConfirm.notice') }}
        </div>
      </div>
    </AllowAccessDialog>
  </div>
</template>

<script>
import AllowAccessDialog from '../../AllowAccessDialog'
import logger from 'utils/logger'
export default {
  name: 'CameraAllowConfirm',
  components: {
    AllowAccessDialog
  },
  data() {
    return {
      visible: false,
      timer: null,
      duration: 10 * 1000
    }
  },
  props: {
    isAllOnStage: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    '$store.state.smallClass.cameraStatus': {
      handler(val) {
        if (val) {
          this.closeDialog()
        }
      }
    }
  },
  mounted() {
    this.$bus.$on('showCameraAllowConfirm', this.autoClose)
  },
  methods: {
    handleOk() {
      this.$bus.$emit('openCamera', true)
      this.closeDialog()
      this.sendLogger('学生同意打开摄像头')
    },
    handleCancel() {
      this.closeDialog()
      this.sendLogger('学生拒绝打开摄像头')
    },
    closeDialog() {
      this.visible = false
      this.timer && clearTimeout(this.timer)
    },
    autoClose() {
      this.visible = true
      // 自动关闭
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.closeDialog()
        this.sendLogger('授权打开摄像头提示自动关闭')
      }, this.duration)
    },
    sendLogger(msg) {
      logger.send({
        tag: 'cameraStatus',
        content: {
          msg: msg
        }
      })
    }
  },
  beforeDestroy() {
    this.$bus.$off('showCameraAllowConfirm', this.autoClose)
  }
}
</script>
<style scoped lang="scss">
.camera-allow-confirm {
  position: absolute;
  right: 10px;
  bottom: 10px;
  .content {
    display: flex;
    align-items: center;
  }
  .icon-camera {
    width: 34px;
    height: 34px;
    background: url('./assets/icon-camera.png') no-repeat;
    background-size: cover;
  }
  .notice {
    flex: 1;
    margin-left: 10px;
    line-height: 17px;
    color: #dee2e7;
  }
}
</style>
