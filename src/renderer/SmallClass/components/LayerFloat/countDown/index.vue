<!-- 视图层 -->
<template>
  <CountDown id="count-down" v-if="show" :time="time"></CountDown>
</template>
<script>
import logger from 'utils/logger'
import CountDown from '@/SmallClass/components/countDown/CountDown.vue'
// import { setStylezIndex } from 'utils/util'
export default {
  props: {
    options: {
      type: Object,
      default: null
    }
  },
  components: {
    CountDown
  },
  data() {
    return {
      show: false,
      ircMsg: {},
      // 课件休息剩余时间
      leftTime: 0
    }
  },
  mounted() {
    this.$bus.$on('room.countDown', ircMessage => {
      console.info('收到倒计时消息:', ircMessage)
      this.ircMsg = ircMessage
      const pubStatus = !!ircMessage.pub
      if (pubStatus) {
        this.show = false
        this.$nextTick(() => {
          this.init()
          this.show = true
        })
      }
      if (this.show && !pubStatus) {
        this.leftTime = 0
        this.show = false
      }
    })
  },
  computed: {
    time() {
      console.log('this.leftTime', this.leftTime)
      return this.leftTime * 1000
    }
  },
  methods: {
    /**
     * 初始化倒计时
     */
    init() {
      if (isNaN(this.ircMsg.type)) {
        let currentTime = new Date().getTime()
        this.leftTime =
          this.ircMsg.duration -
          (this.options.nowTime - this.ircMsg.beginTime) -
          parseInt((currentTime - window._requestBasicTime) / 1000)
        console.info(`倒计时init, 剩余时间: ${this.leftTime},duration:${this.ircMsg.duration}`)
      } else {
        this.leftTime = this.ircMsg.duration
        console.info(`倒计时init, 剩余时间: ${this.leftTime},type:${this.ircMsg.type}`)
      }
    },
    /**
     * 日志上报
     */
    sendLogger(msg) {
      logger.send({
        tag: 'countDown',
        content: {
          msg: msg
        }
      })
    }
  },
  beforeDestroy() {
    this.$bus.$off('room.countDown')
  }
}
</script>

<style lang="scss" scoped>
#count-down {
  pointer-events: auto;
  position: absolute;
}
</style>
