<template>
  <div class="microphone-disabled-notice">
    <AllowAccessDialog
      :isAllOnStage="isAllOnStage"
      v-model="visible"
      :showCancelButton="false"
      :okValue="$t('common.gotIt')"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <div class="content">
        <div :class="[showOnOrOff == 'on' ? 'icon-microphone' : 'icon-off-microphone']"></div>
        <div class="notice">
          {{
            showOnOrOff == 'on'
              ? $t('classroom.smallClass.teacherDisabledOnMicrophone.notice')
              : $t('classroom.smallClass.teacherDisabledOffMicrophone.notice')
          }}
        </div>
      </div>
    </AllowAccessDialog>
  </div>
</template>

<script>
import AllowAccessDialog from '../../AllowAccessDialog'
export default {
  name: 'TeacherDisabledMicrophoneNotice',
  components: {
    AllowAccessDialog
  },
  data() {
    return {
      visible: false,
      timer: null,
      duration: 5 * 1000,
      showOnOrOff: 'on'
    }
  },

  props: {
    isAllOnStage: {
      type: Boolean,
      default: false
    }
  },

  mounted() {
    this.$bus.$on('showTeacherDisabledMicrophoneNotice', this.autoClose)
  },
  methods: {
    handleOk() {
      this.closeDialog()
    },
    handleCancel() {
      this.closeDialog()
    },
    closeDialog() {
      this.visible = false
      this.timer && clearTimeout(this.timer)
    },
    autoClose(type) {
      this.visible = true
      this.showOnOrOff = type
      // 自动关闭
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.closeDialog()
      }, this.duration)
    }
  },
  beforeDestroy() {
    this.$bus.$off('showTeacherDisabledMicrophoneNotice', this.autoClose)
  }
}
</script>
<style scoped lang="scss">
.microphone-disabled-notice {
  position: absolute;
  right: 10px;
  bottom: 10px;
  .content {
    display: flex;
    align-items: center;
  }
  .icon-microphone {
    width: 34px;
    height: 34px;
    background: url('./assets/icon-microphone.png') no-repeat;
    background-size: cover;
  }
  .icon-off-microphone {
    width: 34px;
    height: 34px;
    background: url('./assets/icon-off-microphone.png') no-repeat;
    background-size: cover;
  }
  .notice {
    flex: 1;
    margin-left: 10px;
    line-height: 17px;
    color: #dee2e7;
  }
}
</style>
