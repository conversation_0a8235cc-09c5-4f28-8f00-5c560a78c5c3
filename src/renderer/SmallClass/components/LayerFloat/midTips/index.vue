<template>
  <div id="tipWarp" class="tipwarp" v-html="tipBody"></div>
</template>

<script>
export default {
  data() {
    return {
      tipBody: ''
    }
  },
  computed: {
    courseRate() {
      return this.$store.state.smallClass.courseRate
    }
  },
  methods: {
    /**
     * 创建游戏开始tip
     */
    createActiveStartTip(type) {
      const activeStartTipDom = document.createElement('div')
      activeStartTipDom.className = `activeTip ${type}StartTip`
      activeStartTipDom.setAttribute('id', `${type}StartTip`)

      const bannerDom = document.createElement('div')
      bannerDom.className = 'banner'
      const textDom = document.createElement('div')
      textDom.className = 'text'
      textDom.innerText = this.$t(`classroom.interactions.${type}.startTip`)
      bannerDom.appendChild(textDom)
      const sunline1Dom = document.createElement('div')
      sunline1Dom.className = 'sunline1'
      bannerDom.appendChild(sunline1Dom)
      const sunline2Dom = document.createElement('div')
      sunline2Dom.className = 'sunline2'
      bannerDom.appendChild(sunline2Dom)
      activeStartTipDom.appendChild(bannerDom)
      const streamerDom = document.createElement('div')
      streamerDom.className = 'streamer'
      activeStartTipDom.appendChild(streamerDom)
      const upDom = document.createElement('div')
      console.log('this.courseRate', this.courseRate)
      if (this.courseRate == '2') {
        upDom.className = 'up rate2'
      } else {
        upDom.className = 'up'
      }
      activeStartTipDom.appendChild(upDom)
      console.log('activeStartTipDom', activeStartTipDom)
      this.tipBody = activeStartTipDom.outerHTML
    },
    /**
     * 创建游戏结束tip
     */
    createActiveEndTip(type) {
      const activeEndTipDom = document.createElement('div')
      activeEndTipDom.className = `activeTip ${type}EndTip`
      activeEndTipDom.setAttribute('id', `${type}EndTip`)

      const bannerDom = document.createElement('div')
      bannerDom.className = 'banner'
      const textDom = document.createElement('div')
      textDom.className = 'text'
      textDom.innerText = this.$t(`classroom.interactions.${type}.endTip`)
      bannerDom.appendChild(textDom)
      const sunline1Dom = document.createElement('div')
      sunline1Dom.className = 'sunline1'
      bannerDom.appendChild(sunline1Dom)
      const sunline2Dom = document.createElement('div')
      sunline2Dom.className = 'sunline2'
      bannerDom.appendChild(sunline2Dom)
      activeEndTipDom.appendChild(bannerDom)
      const streamerDom = document.createElement('div')
      streamerDom.className = 'streamer'
      activeEndTipDom.appendChild(streamerDom)
      const upDom = document.createElement('div')
      if (this.courseRate == '2') {
        upDom.className = 'up rate2'
      } else {
        upDom.className = 'up'
      }
      activeEndTipDom.appendChild(upDom)
      this.tipBody = activeEndTipDom.outerHTML
    }
  }
}
</script>
<style lang="scss">
.tipwarp {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  width: 100%;

  .activeTip {
    width: 100%;
    height: 100%;
    z-index: 100;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0);
    animation: bgFadeIn 200ms ease-out forwards;
    display: flex;
    justify-content: center;

    .banner {
      width: 100%;
      height: 17.6%;
      position: absolute;
      bottom: 35.1%;
      background-size: 100% 100%;
      display: flex;
      opacity: 0;
      background: url('./activeTip/banner.png') no-repeat center center;
      align-items: center;
      justify-content: center;
      animation-duration: 200ms;
      animation: bannerFadeIn 3s ease-in-out forwards;

      .text {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 44px;
        font-weight: 900;
        color: #ae4d00;
        animation-duration: 200ms;
        opacity: 0;
        animation: textAnimation 3s ease-in-out forwards;
      }

      .sunline1 {
        width: 20.6%;
        height: 100%;
        position: absolute;
        background: url('./activeTip/sunline1.png') no-repeat center center;
        background-size: 100% 100%;
        left: 0;
        opacity: 0;
        animation-duration: 200ms;
        animation: sunline1 3s linear infinite;
      }

      .sunline2 {
        background: url('./activeTip/sunline2.png') no-repeat center center;
        background-size: 100% 100%;
        width: 15%;
        height: 100%;
        position: absolute;
        right: 0;
        opacity: 0;
        animation-duration: 200ms;
        animation: sunline2 3s linear infinite;
      }
    }

    .streamer {
      width: 71%;
      height: 50%;
      position: absolute;
      bottom: 39%;
      opacity: 0;
      transform: scale(0.4);
      animation-duration: 200ms;
      background: url('./activeTip/streamer.png') no-repeat center center;
      background-size: 100% 100%;
      animation: streamerAnimation 3s ease-in-out forwards;
    }

    .up {
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      width: 60%;
      height: 40%;
      position: absolute;
      z-index: -1;
      bottom: 39%;
      text-align: center;
      opacity: 0;
      transform: scale(0.7);
      animation-delay: 200ms;
      animation: upAnimation 3s ease-in-out forwards;
    }

    .up.rate2 {
      height: 50%;
    }
  }

  .classRestStartTip {
    .up {
      background-image: url('./activeTip/classRest/startUp.png');
    }
  }

  .classRestEndTip {
    .up {
      background-image: url('./activeTip/classRest/endUp.png');
    }
  }
}

@keyframes upAnimation {
  0% {
    transform: scale(0.7);
    opacity: 0;
  }

  20% {
    transform: scale(0.7);
    opacity: 0;
  }

  30% {
    transform: scale(1.06);
    opacity: 1;
  }

  35% {
    transform: scale(0.9);
    opacity: 1;
  }

  38% {
    transform: scale(1.05);
    opacity: 1;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }

  90% {
    transform: scale(1);
    opacity: 1;
  }

  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes bgFadeIn {
  to {
    background: rgba(0, 0, 0, 0.4);
  }
}

@keyframes bannerFadeIn {
  0% {
    opacity: 0;
  }

  10% {
    opacity: 1;
  }

  /* 200ms / 3s = 6.67%，四舍五入到 10% */
  90% {
    opacity: 1;
  }

  /* 等待 2.6s */
  100% {
    opacity: 0;
  }

  /* 最后 200ms 将透明度从 1 变为 0 */
}

@keyframes textAnimation {
  0% {
    transform: scale(3);
    opacity: 0;
  }

  10% {
    transform: scale(3);
    opacity: 0;
  }

  20% {
    transform: scale(0.9);
    opacity: 1;
  }

  25% {
    transform: scale(1.05);
    opacity: 1;
  }

  30% {
    transform: scale(1);
    opacity: 1;
  }

  80% {
    transform: scale(1);
    opacity: 1;
  }

  90% {
    transform: scale(3);
    opacity: 0;
  }
}

@keyframes sunline1 {
  33% {
    left: 0;
    opacity: 0;
  }

  40% {
    opacity: 1;
  }

  60% {
    opacity: 1;
  }

  66% {
    left: 100%;
    opacity: 0;
  }

  100% {
    left: 100%;
    opacity: 0;
  }
}

@keyframes sunline2 {
  40% {
    left: 0;
    opacity: 0;
  }

  57% {
    opacity: 1;
  }

  77% {
    opacity: 1;
  }

  83% {
    left: 100%;
    opacity: 0;
  }

  100% {
    left: 100%;
    opacity: 0;
  }
}

@keyframes streamerAnimation {
  0% {
    transform: scale(0.4);
    opacity: 0;
  }

  20% {
    transform: scale(0.4);
    opacity: 0;
  }

  30% {
    transform: scale(1);
    opacity: 1;
  }

  80% {
    transform: scale(1);
    opacity: 1;
  }

  90% {
    transform: scale(1);
    opacity: 0;
  }
}
</style>
