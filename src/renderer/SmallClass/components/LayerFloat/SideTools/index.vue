<template>
  <div id="side-tools" class="flex-col pointer-events-auto " v-show="silderIsVisible">
    <Microphone></Microphone>
    <div
      class="w-[0.48rem] h-[0.48rem] rounded-[0.24rem] bg-cover handsup  cursor-pointer mb-[0.16rem]"
      :class="{ active: isHandsUpActive, close: isHandsUpClose }"
      @click="handleHandsUpSwitch"
    >
      <div class="left">
        <span></span>
      </div>
      <div class="right">
        <span></span>
      </div>
    </div>
    <div
      class="w-[0.48rem] h-[0.48rem] rounded-[0.24rem] bg-cover emoji  cursor-pointer mb-[0.16rem]"
      :class="{ active: isEmojiActived, cooling: isEmojiCooling, close: isEmojiClose }"
      @click="handleEmojiSwitch"
    >
      <div class="emoji-tip w-[1.5rem]" v-if="isEmojiClose && isShowEmojiCloseTip">
        {{ emojiCloseTipText }}
      </div>
      <div class="emoji-tip w-[2.5rem]" v-if="isEmojiCooling && isShowEmojiCoolingTip">
        {{ emojiCoolingTipText }}
      </div>
    </div>
    <div
      class="w-[0.48rem] h-[0.48rem] rounded-[0.24rem] bg-cover cursor-pointer chatbox"
      :class="{ active: isChatBoxOpen }"
      id="tools-chatbox"
      @click="handleChatBoxSwitchOpen"
    >
      <div v-if="isChatBoxHasNewMsg && !isChatBoxOpen" class="chat-new-msg"></div>
    </div>
  </div>
</template>

<script>
import {
  defineComponent,
  ref,
  computed,
  watchEffect,
  getCurrentInstance,
  onMounted,
  onBeforeUnmount
} from '@vue/composition-api'
import { useEmojiStatus } from '../../../hooks/useEmojiStatus.js'
import _debounce from 'lodash/debounce'
import { nativeApi } from 'utils/electronIpc'
import { chatMsgPriority } from '@/LargeClass/base/interaction-handler/interaction-conf'
import { getSchoolCode } from 'utils/local'
import logger from 'utils/logger'
import { useVolume } from '../../../hooks/useVolume'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import Microphone from './components/Microphone/index.vue'
export default defineComponent({
  name: 'FloatLayer',
  components: {
    Microphone
  },
  setup() {
    const isVisible = ref(true)
    const { reduceOtherVolume } = useVolume()
    const { proxy } = getCurrentInstance()
    const multVideoLinkStatus = ref(false) // 多人上台互动状态
    // 监听多人上台举手状态
    proxy.$bus.$on('raiseHandForMultVideoLink', status => {
      multVideoLinkStatus.value = status
    })
    // 进入课中默认静音
    proxy.thinkClass.RtcService.muteLocalAudio(true)
    // 举手
    const isHandsUpActive = ref(false)
    let handsUpTimer = ref(null)
    const isHandsUpClose = computed(() => {
      return proxy.$store.state.smallClass.selfVideoMicLink
    })
    const baseData = computed(() => proxy.$store.state.smallClass.baseData)
    const cameraStatus = computed(() => proxy.$store.state.smallClass.cameraStatus)
    const microphoneStatus = computed(() => proxy.$store.state.smallClass.microphoneStatus)
    const silderIsVisible = computed(() => proxy.$store.state.smallClass.sliderVisible)

    watchEffect(() => {
      if (isHandsUpClose.value) {
        isHandsUpActive.value = false
        clearTimeout(handsUpTimer.value)
      }
    })
    /**
     * 日志上报
     */
    const sendLogger = (msg, stage = '') => {
      logger.send({
        tag: 'student.Interact',
        content: {
          msg: msg,
          interactType: 'stu_handsup',
          interactId: '',
          interactStage: stage
        }
      })
    }
    function handleHandsUpSwitch() {
      if (isHandsUpActive.value) return
      if (isHandsUpClose.value) return
      isHandsUpActive.value = true
      // 举手后发群聊消息通知其他学生
      sendPeerMessage()
      sendLogger('学员举手')
      // 通知local显示举手
      proxy.$bus.$emit('sendRaiseHand', true)
      classLiveSensor.osta_raise_hand()
      handsUpTimer.value = setTimeout(() => {
        isHandsUpActive.value = false
      }, 10000)
    }

    onBeforeUnmount(() => {
      clearTimeout(handsUpTimer)
    })
    // 表情
    const {
      handleEmojiSwitch,
      isEmojiActived,
      isEmojiCooling,
      isShowEmojiCoolingTip,
      emojiCoolingTipText,
      isEmojiClose,
      isShowEmojiCloseTip,
      emojiCloseTipText
    } = useEmojiStatus()
    // chatbox
    onMounted(() => {
      console.log('proxy.$stote', proxy.$store)
      reduceOtherVolume()
    })
    const sendPeerMessage = () => {
      sendPeerMessageToTour()
      if (multVideoLinkStatus.value) {
        sendPeerMessageToTeacher({ type: 125 })
      }
    }
    /**
     * 给主讲老师发送举手消息
     */
    const sendPeerMessageToTeacher = ({ type }) => {
      // 向主讲老师发送消息
      const { stuInfo, configs } = baseData.value

      const content = {
        type: type || 125,
        status: 6,
        stuId: stuInfo.id,
        cameraIsOpen: cameraStatus.value ? 1 : 2, // 摄像头开启状态
        mikeAvailable: microphoneStatus.value ? 1 : 2 // 麦克风开启状态
      }
      window.ChatClient.PeerChatManager.sendPeerMessage(
        [{ nickname: configs.teacherIrcId }],
        JSON.stringify(content),
        chatMsgPriority.notice
      )
    }
    const sendPeerMessageToTour = async () => {
      const schoolCode = await getSchoolCode()
      const deviceInfo = await nativeApi.getDeviceInfo()
      // 向辅导老师发送消息
      const { planId, classId, stuIRCId, stuInfo, teacherInfo, stime, configs } = baseData.value
      const content = {
        type: 160,
        msg: 'raiseHand',
        parameter: {
          schoolCode: schoolCode,
          planId,
          roomId: classId,
          studentId: stuIRCId,
          uid: stuInfo.id,
          teacherId: teacherInfo.id,
          teacherName: teacherInfo.name,
          startTime: stime,
          currenTime: new Date().getTime(),
          device: deviceInfo.platform,
          deviceVersion: deviceInfo.osVersion,
          AppVersion: deviceInfo.appVersion
        }
      }
      window.ChatClient.PeerChatManager.sendPeerMessage(
        [{ nickname: configs.tutorIrcId }],
        JSON.stringify(content),
        chatMsgPriority.notice
      )
    }

    const isChatBoxOpen = computed(() => proxy.$store.getters['smallClass/isShowBox'])
    const isChatBoxHasNewMsg = computed(() => proxy.$store.getters['smallClass/hasNewMsg'])
    function handleChatBoxSwitchOpen() {
      proxy.$store.dispatch('smallClass/updateChatboxIsShowBox', !isChatBoxOpen.value)
    }

    return {
      // 举手
      isHandsUpActive,
      isHandsUpClose,
      handleHandsUpSwitch: _debounce(handleHandsUpSwitch, 300),
      // 表情
      isEmojiActived,
      isEmojiCooling,
      isShowEmojiCoolingTip,
      emojiCoolingTipText,
      isEmojiClose,
      isShowEmojiCloseTip,
      emojiCloseTipText,
      handleEmojiSwitch,
      // chatbox
      isChatBoxOpen,
      isChatBoxHasNewMsg,
      handleChatBoxSwitchOpen,
      silderIsVisible
    }
  }
})
</script>
<style lang="scss" scoped>
@keyframes move {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(90deg);
  }

  100% {
    transform: rotate(180deg);
  }
}
.handsup {
  position: relative;
  background-image: url('./imgs/handsUp.png');

  &:hover {
    background-image: url('./imgs/handsUp-hover.png');
  }

  &.active {
    background-image: url('./imgs/handsUp-hover.png');

    .left {
      width: 50%;
      height: 100%;
      position: absolute;
      left: 0px;
      top: 0;
      overflow: hidden;
    }

    .left span {
      width: 100%;
      height: 100%;
      position: absolute;
      box-sizing: border-box;
      border: 0.02rem solid #ffb013;
      border-width: 0.02rem 0 0.02rem 0.02rem;
      border-radius: 0.5rem 0 0 0.5rem;
      left: 0;
      top: 0;
      transform-origin: 100% 50%;
      transform: rotate(0deg);
      animation: move 5s 5s linear;
    }
    .right {
      width: 50%;
      height: 100%;
      position: absolute;
      right: 0px;
      top: 0px;
      overflow: hidden;
    }

    .right span {
      width: 100%;
      height: 100%;
      position: absolute;
      box-sizing: border-box;
      border: 0.02rem solid #ffb013;
      border-width: 0.02rem 0.02rem 0.02rem 0;
      border-radius: 0 0.5rem 0.5rem 0;
      right: 0;
      top: 0px;
      transform-origin: 0 50%;
      transform: rotate(180deg);
      animation: move 5s linear;
    }
  }

  &.close {
    background-image: url('./imgs/handsUp-close.png');
  }
}

.emoji {
  background-image: url('./imgs/emoji.png');

  &:hover {
    background-image: url('./imgs/emoji-hover.png');
  }

  &.active {
    background-image: url('./imgs/emoji-hover.png');
  }

  &.close {
    background-image: url('./imgs/emoji-close.png');
  }

  &.cooling {
    background-image: url('./imgs/emoji-cooling.png');
  }

  .emoji-tip {
    padding: 0.1rem 0.16rem;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 0.04rem;
    position: relative;
    left: -0.14rem;
    top: 50%;
    transform: translateY(-50%) translateX(-100%);
    font-size: 0.14rem;
    font-family: Montserrat-Regular, Montserrat;
    font-weight: 400;
    color: #ffffff;
    text-align: center;

    &::after {
      content: '';
      width: 0;
      height: 0;
      border-top: 0.06rem solid transparent;
      border-bottom: 0.06rem solid transparent;
      border-left: 0.06rem solid rgba(0, 0, 0, 0.7);
      position: absolute;
      right: -0.03rem;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}

.chatbox {
  background-image: url('./imgs/chatbox.png');

  &:hover {
    background-image: url('./imgs/chatbox-hover.png');
  }

  &.active {
    background-image: url('./imgs/chatbox-hover.png');
  }

  position: relative;

  .chat-new-msg {
    position: absolute;
    width: 0.08rem;
    height: 0.08rem;
    background: #ff4b4b;
    top: 0.02rem;
    right: 0.02rem;
    border-radius: 50%;
  }
}
</style>
