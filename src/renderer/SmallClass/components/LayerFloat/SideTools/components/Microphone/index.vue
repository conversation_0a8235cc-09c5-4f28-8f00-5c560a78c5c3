<template>
  <div>
    <div
      class="w-[0.48rem] h-[0.48rem] rounded-[0.24rem] bg-cover mic cursor-pointer mb-[0.16rem]"
      v-if="!isShowClick"
      :class="{ mute: showMuteIcon, active: microphoneStatus }"
      @mousedown="mouseDownEvent"
    ></div>
    <div
      v-else
      class="w-[0.48rem] h-[0.48rem] rounded-[0.24rem] bg-cover btn-mic cursor-pointer mb-[0.16rem]"
      :class="{ active: microphoneStatus }"
      @click="handleClickMicrophone"
    >
      <div v-if="microphoneStatus" class="mic-wave" :style="micVolumeStyle"></div>
    </div>
  </div>
</template>

<script>
import { defineComponent, watch, getCurrentInstance } from '@vue/composition-api'
import _debounce from 'lodash/debounce'
import logger from 'utils/logger'
import { useMicroPhoneStatus } from '@/SmallClass/hooks/useMicroPhoneStatus.js'
export default defineComponent({
  name: 'Microphone',
  setup() {
    const { proxy } = getCurrentInstance()
    // 进入课中默认静音
    proxy.$store.dispatch('smallClass/updateMicrophoneStatus', false)
    proxy.thinkClass.RtcService.muteLocalAudio(true)
    /**
     * 日志上报
     */
    const sendLogger = (msg, stage = '') => {
      logger.send({
        tag: 'student.Interact',
        content: {
          msg: msg,
          interactType: 'stu_handsup',
          interactId: '',
          interactStage: stage
        }
      })
    }

    // 麦克风
    const {
      handleClickMicrophone,
      microphoneStatus,
      currentVolume,
      updateMicBoxStyle,
      micVolumeStyle,
      showMuteIcon,
      mouseDownEvent,
      isShowClick
    } = useMicroPhoneStatus()
    watch(
      () => currentVolume.value,
      newValue => {
        updateMicBoxStyle(newValue)
      },
      {
        immediate: true
      }
    )
    return {
      // 麦克风
      showMuteIcon,
      mouseDownEvent,
      handleClickMicrophone,
      microphoneStatus,
      micVolumeStyle,
      isShowClick
    }
  }
})
</script>
<style lang="scss" scoped>
.btn-mic {
  background-image: url('../../imgs/mic-close.png');
  position: relative;
  &.active {
    background-image: url('../../imgs/mic-open.png');
  }
  .mic-wave {
    width: 0.24rem;
    height: 0.24rem;
    position: absolute;
    left: 50%;
    bottom: 50%;
    transform: translate(-50%, 0.12rem);
    overflow: hidden;
    background-image: url('../../imgs/mic-green.png');
    background-position: bottom;
    background-size: 100%;
    transition: all 0.6s;
  }
}
.mic {
  background-image: url('../../imgs/speak.png');
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  &.mute {
    cursor: not-allowed;
    background-image: url('../../imgs/mute-speak.png');
  }
  &.active {
    background-image: url('../../imgs/speaking.png');
  }
}
.animate-wave {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 76px;
  height: 76px;
  border-radius: 50%;
  z-index: -1;
}
@keyframes opac {
  0% {
    width: 48px;
    height: 48px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  // 50% {
  //   width: 50%;
  //   height: 50%;
  //   top: 50%;
  //   left: 50%;
  //   transform: translate(-50%, -50%);
  // }

  100% {
    width: 100%;
    height: 100%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.animate-wave * {
  position: absolute;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  animation: opac 2s infinite;
}

.animate-wave .w2 {
  animation-delay: 0.5s;
}
.animate-wave .w3 {
  animation-delay: 1s;
}
.animate-wave .w4 {
  animation-delay: 1.5s;
}

@keyframes move {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(90deg);
  }

  100% {
    transform: rotate(180deg);
  }
}
</style>
