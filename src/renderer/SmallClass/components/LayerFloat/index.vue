<template>
  <div id="floatLayerController">
    <ScreenThumbnail class="pointer-events-auto" style="z-index: 92;"></ScreenThumbnail>
    <Tools
      v-if="!baseData.commonOption.isAudition"
      class="absolute right-[0.16rem] bottom-[0.16rem]"
      style="z-index: 96;"
    ></Tools>
    <CameraAllowConfirm
      class="pointer-events-auto absolute right-[1rem] bottom-[0.1rem]"
      style="z-index: 98; "
    ></CameraAllowConfirm>
    <MediaAccessModel class="pointer-events-auto" style="z-index: 99;"></MediaAccessModel>
    <EmojiPane
      class="pointer-events-auto bottom-0 left-[50%] translate-x-[-50%]"
      style="z-index: 95; position: absolute"
    >
    </EmojiPane>
    <Atmosephere style="z-index: 100; position: absolute;"></Atmosephere>
    <CountDown style="z-index: 93;" ref="CountDown" :options="baseData.commonOption" />
    <SpeakGuide
      v-if="!isAudition && !isParent && isShowSpeakGuide"
      style="z-index: 93;     
      position: absolute;
      bottom: 16px;
      left: 50%;
      transform: translateX(-50%)"
      @close="closeSpeakGuideHandle"
    ></SpeakGuide>
    <SpeakIng
      v-if="currentTipType === tipTypeMap.SPEAKING"
      style="z-index: 93;     
      position: absolute;
      bottom: 16px;
      left: 50%;
      transform: translateX(-50%)"
    ></SpeakIng>
    <SpeakTimeShort
      v-if="currentTipType === tipTypeMap.SPEAKING_SHORT"
      style="z-index: 93;     
      position: absolute;
      bottom: 50%;
      left: 50%;
      transform: translateX(-50%)"
    ></SpeakTimeShort>
    <SpeakMuteToast
      v-if="currentTipType === tipTypeMap.SPEAK_TYPE_CHANGE"
      :msg="showAudioMuteText"
      style="z-index: 93;     
      position: absolute;
      bottom: 50%;
      left: 50%;
      transform: translateX(-50%)"
    ></SpeakMuteToast>
    <midTips v-if="showTip" style="z-index: 93;" ref="midtipsRef"></midTips>
  </div>
</template>

<script>
import {
  defineComponent,
  getCurrentInstance,
  computed,
  onMounted,
  onBeforeUnmount,
  ref,
  nextTick
} from '@vue/composition-api'
import ScreenThumbnail from '../Header/ScreenThumbnail/index.vue'
import CameraAllowConfirm from './CameraAllowConfirm/index.vue'
import MediaAccessModel from './MediaAccessModel/index.vue'
import Tools from './SideTools/index.vue'
import EmojiPane from './EmojiPane/index.vue'
import Atmosephere from './Atmosephere/index.vue'
import CountDown from './countDown/index.vue'
import SpeakGuide from './SpeakGuide/index.vue'
import SpeakIng from './SpeakIng/index.vue'
import SpeakTimeShort from './SpeakTimeShort/index.vue'
import SpeakMuteToast from './SpeakMuteToast/index.vue'
import midTips from './midTips/index.vue'
import { useMicroPhoneTip } from '@/SmallClass/hooks/useMicroPhoneTip'
export default defineComponent({
  name: 'FloatLayer',
  components: {
    ScreenThumbnail,
    Tools,
    CameraAllowConfirm,
    MediaAccessModel,
    EmojiPane,
    Atmosephere,
    CountDown,
    SpeakGuide,
    SpeakIng,
    SpeakTimeShort,
    SpeakMuteToast,
    midTips
  },
  setup() {
    const { proxy } = getCurrentInstance()
    const baseData = computed(() => proxy.$store.state.smallClass.baseData)

    const isParent = computed(() => baseData.value.commonOption.isParent)
    const isAudition = computed(() => baseData.value.commonOption.isAudition && !isParent.value)
    const {
      isShowSpeakGuide,
      closeSpeakGuideHandle,
      showAudioMuteText,
      tipTypeMap,
      currentTipType
    } = useMicroPhoneTip()

    const midtipsRef = ref(null)
    const showTip = ref(false)
    onMounted(() => {
      proxy.$bus.$on('activeTip', handleActiveTip)
    })
    onBeforeUnmount(() => {
      proxy.$bus.$off('activeTip', handleActiveTip)
    })

    function handleActiveTip(pub, type) {
      console.log('展示横幅', pub, type)
      showTip.value = true
      setTimeout(() => {
        showTip.value = false
      }, 4200)
      nextTick(() => {
        if (pub) {
          midtipsRef.value.createActiveStartTip(type)
        } else {
          midtipsRef.value.createActiveEndTip(type)
        }
      })
    }
    return {
      baseData,
      isShowSpeakGuide,
      showAudioMuteText,
      currentTipType,
      tipTypeMap,
      closeSpeakGuideHandle,
      isAudition,
      isParent,
      midtipsRef,
      showTip
    }
  }
})
</script>
