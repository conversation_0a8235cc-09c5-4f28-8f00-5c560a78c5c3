<!-- 一个引导模块 -->
<template>
  <div class="speak-guide">
    <div class="speak-guide__content">
      <img class="speak-guide__content_img" src="./Keyboard.png" alt="" srcset="" />
      <div class="speak-guide__content__msg">
        {{ $t('liveroomMicFirstTipAlertTitlePC') }}
      </div>
    </div>
    <div class="speak-guide__btn" @click="close">
      {{ $t('liveroomMicFirstTipAlertDoneTitle') }}
    </div>
  </div>
</template>
<script>
import { defineComponent } from '@vue/composition-api'
import logger from 'utils/logger'
export default defineComponent({
  name: 'SpeakGuide',
  methods: {
    close() {
      logger.send({
        tag: 'speakingGuide',
        level: 'info',
        content: {
          msg: '点击我知道了'
        }
      })
      this.$emit('close')
    }
  }
})
</script>

<style scoped lang="scss">
.speak-guide {
  pointer-events: auto;
  width: 480px;
  height: 188px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  .speak-guide__content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 5px;
  }
  .speak-guide__content_img {
    width: 300px;
    height: 80px;
  }
  .speak-guide__content__msg {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 20px;
    width: 425px;
    text-align: center;
  }
  .speak-guide__btn {
    width: 140px;
    height: 36px;
    border-radius: 18px;
    border: 1px solid #ffffff;
    font-size: 14px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #ffffff;
    line-height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
</style>
