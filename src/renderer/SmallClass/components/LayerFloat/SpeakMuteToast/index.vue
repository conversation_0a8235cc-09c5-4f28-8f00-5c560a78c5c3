<!-- 一个引导模块 -->
<template>
  <div class="speak-mute-toast">
    {{ msg }}
  </div>
</template>
<script>
import { defineComponent } from '@vue/composition-api'
export default defineComponent({
  name: 'SpeakMuteToast',
  props: {
    msg: {
      type: String,
      required: true
    }
  }
})
</script>

<style scoped lang="scss">
.speak-mute-toast {
  width: 480px;
  min-height: 68px;
  padding: 24px 40px;
  font-size: 16px;
  text-align: center;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #ffffff;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
