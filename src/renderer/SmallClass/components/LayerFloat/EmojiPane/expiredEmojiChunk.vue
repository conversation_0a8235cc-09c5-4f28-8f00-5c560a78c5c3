<template>
  <div class="emojiPane">
    <div class="emojiGroup-overShow">
      <span class="overShow-emoji">
        <img :src="imgSrc" />
        <span></span>
      </span>
      <p class="overShow-tips top">
        {{ $t('classroom.smallClass.dynamicEmoji[0]') }}
      </p>
      <p class="overShow-tips">{{ $t('classroom.smallClass.dynamicEmoji[1]') }}</p>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ExpiredEmojiChunk',
  props: {
    imgSrc: {
      type: String,
      default: ''
    }
  }
}
</script>
<style scoped lang="scss">
.emojiPane {
  margin: 0 4px;
  .emojiGroup-overShow {
    width: 282px;
    height: 188px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('./imgs/over-show.png');
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 15px;
    .overShow-emoji {
      width: 60px;
      height: 60px;
      position: relative;
      img {
        width: 60px;
        height: 60px;
        display: inline-block;
      }
      span {
        width: 30px;
        height: 30px;
        background-repeat: no-repeat;
        background-size: cover;
        background-image: url('./imgs/lock.png');
        position: absolute;
        bottom: -10px;
        right: -10px;
      }
    }
    .overShow-tips {
      font-size: 14px;
      color: #fff;
      display: block;
      text-align: center;
    }
    .top {
      margin-top: 40px;
    }
  }
}
</style>
