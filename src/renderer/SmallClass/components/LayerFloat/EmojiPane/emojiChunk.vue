<template>
  <div class="emojiPane">
    <div class="emojiLine" :key="idx" v-for="idx in lineNum">
      <template v-for="(singleEmoji, index) in list">
        <div
          :class="['emoji-group-item', emojiSize]"
          :key="index"
          v-if="index < perLineEmojiNum * idx && index >= perLineEmojiNum * (idx - 1)"
        >
          <Emoticon
            v-if="isLocal"
            :name="singleEmoji.name"
            :type="singleEmoji.type"
            :enableHover="true"
            :hoverWidth="hoverWidth"
            :hoverHeight="hoverHeight"
            :width="width"
            :height="height"
            @handleClick="clickEmoji(singleEmoji, index)"
          ></Emoticon>
          <div v-else class="dynamic-emoji" @click="clickEmoji(singleEmoji, index)">
            <img :src="singleEmoji.emojiPicture" alt="" />
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
<script>
import Emoticon from 'components/Common/Emoticon'
export default {
  name: 'emojiChunk',
  components: {
    Emoticon
  },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    lineNum: {
      type: Number,
      default: 3
    },
    isLocal: {
      type: Boolean,
      default: true
    },
    width: {
      type: Number,
      default: 40
    },
    height: {
      type: Number,
      default: 40
    },
    hoverWidth: {
      type: Number,
      default: 45
    },
    hoverHeight: {
      type: Number,
      default: 45
    }
  },
  data() {
    return {}
  },
  computed: {
    perLineEmojiNum() {
      // @log-ignore
      return Math.ceil(this.list.length / this.lineNum)
    },
    emojiSize() {
      // @log-ignore
      return `emojiSize${this.lineNum}`
    }
  },
  methods: {
    clickEmoji(params, index) {
      this.$emit('handleClick', params, index)
    }
  }
}
</script>
<style scoped lang="scss">
.emojiPane {
  border: #0e7fe6 4px solid;
  border-radius: 15px;
  display: inline-block;
  margin: 0 4px;
  padding: 8px 10px;
  .emojiLine {
    display: flex;
    .emoji-group-item {
      display: flex;
      justify-content: center;
      align-items: center;
      .dynamic-emoji {
        img {
          display: block;
          width: 70px;
          height: 70px;
          pointer-events: none;
        }
      }
    }
    .emojiSize2 {
      width: 90px;
      height: 82px;
    }
    .emojiSize3 {
      width: 58px;
      height: 54.4px;
    }
  }
}
</style>
