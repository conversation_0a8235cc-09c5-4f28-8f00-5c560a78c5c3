<template>
  <div
    v-clickoutside="closeEmojiPane"
    v-if="showEmojiPane"
    class="emojis-pane-container"
    :class="allOnStage ? 'allOnStage-pane-container' : ''"
  >
    <section class="emojis-nav">
      <ul>
        <li
          class="emoji-nav-content"
          :class="emojiNav.emojiPackageId == currentOrderId ? 'active' : ''"
          v-for="(emojiNav, key) in getDynamicEmolist"
          :key="emojiNav.emojiPackageId"
          @click="selectedEmoji(emojiNav.emojiPackageId, key)"
        >
          <img v-if="emojiNav.isLocal" src="~assets/images/live/icon_emoji_native.png" alt="" />
          <img v-else :src="emojiNav.picture" alt="" />
        </li>
      </ul>
    </section>
    <!-- 表情列表 -->
    <section class="emojis-list">
      <div class="emoji-scroll-wrapper" ref="scrollWrapper">
        <template v-for="emojiGroup in getDynamicEmolist">
          <expiredEmojiChunk
            :imgSrc="emojiGroup.picture"
            :key="emojiGroup.emojiPackageId"
            v-if="emojiGroup.isOver == true && emojiGroup.overShow == true"
          ></expiredEmojiChunk>
          <emojiChunk
            class="emoji-show-list"
            v-else
            :list="emojiGroup.content"
            :key="emojiGroup.emojiPackageId + 1"
            :lineNum="emojiGroup.isLocal == true ? 3 : 2"
            :isLocal="emojiGroup.isLocal"
            @handleClick="sendEmoji(arguments, emojiGroup)"
          >
          </emojiChunk>
        </template>
      </div>
    </section>
  </div>
</template>

<script>
import emojiChunk from './emojiChunk'
import expiredEmojiChunk from './expiredEmojiChunk'
import { getUserInfo } from 'utils/userInfo'
import { sendEmojiBurryPoint } from 'utils/emojiUtils'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import { getLottieEmojiLists } from 'utils/emojiUtils'

export default {
  name: 'EmojiPane',
  components: {
    emojiChunk,
    expiredEmojiChunk
  },
  data() {
    return {
      showEmojiPane: false,
      nickName: '',
      avatar: '',
      currentOrderId: 0 // 表情包emojiPackageId
    }
  },
  props: {
    allOnStage: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    getDynamicEmolist() {
      // @log-ignore
      return this.$store.state.smallClass.dynamicEmojiLists
    },
    options() {
      return this.$store.state.smallClass.baseData.commonOption
    },
    packageId() {
      return this.$store.state.smallClass.baseData.planInfo.packageId
    }
  },
  async mounted() {
    this.$bus.$on('openEmojiPane', state => {
      if (state) {
        this.openEmojiPane()
      } else {
        this.closeEmojiPane()
      }
    })
    this.getUserInfo()
    // 动态表情包初始化
    await getLottieEmojiLists().then(allEmojiList => {
      // @log-ignore
      this.$store.dispatch('smallClass/updateDynamicEmojiLists', allEmojiList)
    })
  },
  beforeDestroy() {
    this.$bus.$off('openEmojiPane')
  },
  methods: {
    // 获取用户信息
    async getUserInfo() {
      const { nickName, avatar } = await getUserInfo()
      this.nickName = nickName
      this.avatar = avatar
    },
    sendEmoji([params, key], emojiGroup) {
      // 发群聊消息，其他学员展示用
      let opts
      if (params.type == 1) {
        // 本地表情
        opts = this.emojiIrcConfig('send_emoji', params)
      } else {
        // type 2 动态表情-静态图 type 3 动态表情lottie图
        // 同一个ircType 根据不同类型处理不同逻辑 区分lottie的json和图片
        const type = params.lottieUrl.endsWith('.json') ? 2 : 3
        params = Object.assign(params, {
          type,
          name: params.emojiName
        })
        // 动态表情
        opts = this.emojiIrcConfig('animation_emoji', params)
        opts.content.data.resource = {
          emojiName: params.emojiName,
          emojiId: params.emojiId,
          emojiPicture: params.emojiPicture,
          lottieUrl: params.lottieUrl
        }
      }
      this.thinkClass.SignalService && this.thinkClass.SignalService.sendRoomMessage(opts)
      // 记录表情点击次数
      this.$emit('handleSendEmoji')
      // 广播表情发送消息, 本地表情回显使用
      this.$bus.$emit('sendEmoji', params)
      // 关闭表情面板
      this.closeEmojiPane()
      // 表情发送埋点
      sendEmojiBurryPoint(this.options, params, emojiGroup, key, this.allOnStage, this.packageId)
      classLiveSensor.osta_cb_send_msg({
        type: 4,
        contentType: 'emoji',
        msg: `${params.name}`
      })
    },
    // 表情irc消息配置
    emojiIrcConfig(emojitype, params) {
      return {
        roomList: this.options.roomlist,
        content: {
          ircType: emojitype,
          data: {
            name: params.name,
            type: params.type
          },
          from: {
            username: this.nickName,
            path: this.avatar
          }
        },
        chatMsgPriority: 99
      }
    },
    closeEmojiPane() {
      this.showEmojiPane = false
      this.currentOrderId = 0
    },
    openEmojiPane() {
      this.showEmojiPane = true
    },
    selectedEmoji(emojiPackageId, key) {
      this.currentOrderId = emojiPackageId
      const scrollDom = document.getElementsByClassName('emoji-show-list')[key]
      if (scrollDom) {
        scrollDom.scrollIntoView({
          inline: 'center'
        })
      }
    }
  }
}
</script>
<style scoped lang="scss">
.emojis-pane-container {
  width: 763px;
  height: 256px;
  background: #0056a4;
  border-radius: 20px 20px 0 0;
  position: relative;
  overflow: hidden;
  .emojis-nav {
    width: 100%;
    height: 56px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('./imgs/emoji-nav.png');
    ul {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      padding: 4px 8px 0 8px;
    }
    .emoji-nav-content {
      width: 44px;
      height: 44px;
      display: flex;
      background-repeat: no-repeat;
      background-size: cover;
      background-image: url('./imgs/emoji-nav-normal.png');
      margin-right: 16px;
      justify-content: center;
      padding-top: 5px;
      img {
        display: block;
        width: 30px;
        height: 30px;
      }
      &.active {
        background-image: url('./imgs/emoji-nav-selected.png');
      }
    }
  }
  .emojis-list {
    padding: 6px;
    display: flex;
    .emoji-scroll-wrapper {
      display: flex;
      // display: -webkit-box;
      align-items: center;
      flex-direction: row;
      -webkit-overflow-scrolling: touch;
      overflow: hidden;
      white-space: nowrap;
      scroll-behavior: smooth;
      &::-webkit-scrollbar {
        display: none;
      }
    }
  }
}
//  多人上台
.allOnStage-pane-container {
  .emojis-list {
    width: 854px;
  }
}
</style>
