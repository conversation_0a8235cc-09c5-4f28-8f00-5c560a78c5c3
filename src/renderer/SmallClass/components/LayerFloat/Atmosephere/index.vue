<template>
  <div v-if="isShowPlay" class="w-full pointer-events-auto top-[-0.5px] atmosephereBox">
    <canvas class="w-full h-full" id="atmosepherePAG"></canvas>
  </div>
</template>

<script>
import {
  defineComponent,
  onBeforeUnmount,
  getCurrentInstance,
  ref,
  nextTick
} from '@vue/composition-api'
import logger from 'utils/logger'
export default defineComponent({
  name: 'FloatLayer',
  setup() {
    const { proxy } = getCurrentInstance()
    let pagView = null
    let audioEl = null
    const isShowPlay = ref(false)
    proxy.$bus.$on('Ambiance_Tools', id => {
      if (pagView && pagView.isPlaying) {
        if (audioEl) {
          audioEl.pause()
          audioEl = null
        }
        pagView.stop()
        sendLogger('未播放完成点击播放其他效果', { id }, 'warn')
      }
      isShowPlay.value = true
      nextTick(() => {
        playAnimation(id)
      })
    })
    const playAnimation = async fileName => {
      if (!window.PAG) {
        window.PAG = await window.libpag.PAGInit()
      }
      sendLogger('开始播放氛围效果', { fileName })
      const url = `./pagfiles/${fileName}.pag`
      const buffer = await fetch(url).then(response => response.arrayBuffer())
      const pagFile = await window.PAG.PAGFile.load(buffer)

      // Set the width from the PAGFile.
      const canvas = document.getElementById('atmosepherePAG')
      const styleDeclaration = window.getComputedStyle(canvas, null)
      canvas.width = Number(styleDeclaration.width.replace('px', ''))
      canvas.height = Number(styleDeclaration.height.replace('px', ''))

      // canvas.width = pagFile.width()
      // canvas.height = pagFile.height()
      pagView = await window.PAG.PAGView.init(pagFile, canvas, { useScale: false })

      // Get audio data.
      const audioBytes = pagFile.audioBytes()
      if (audioBytes.byteLength > 0) {
        audioEl = document.createElement('audio')
        audioEl.preload = 'auto'
        const blob = new Blob([audioBytes], { type: 'audio/mp3' })
        audioEl.src = URL.createObjectURL(blob)
        pagView.addListener('onAnimationStart', () => {
          audioEl.play()
        })
        pagView.addListener('onAnimationEnd', () => {
          isShowPlay.value = false
          sendLogger('氛围效果播放完毕')
        })
        pagView.addListener('onAnimationCancel', () => {
          if (audioEl) {
            audioEl.pause()
            audioEl = null
          }
          sendLogger('取消播放氛围效果', { fileName })
        })
      }

      pagView.setRepeatCount(1)

      await pagView.play()
    }
    const destroyPagView = () => {
      if (audioEl) {
        audioEl.pause()
        audioEl = null
      }
    }
    const sendLogger = (msg, params = {}, level = 'info') => {
      logger.send({
        tag: 'PAG',
        level,
        content: {
          msg,
          params
        }
      })
    }

    onBeforeUnmount(() => {
      destroyPagView()
    })
    return {
      isShowPlay
    }
  }
})
</script>
<style scoped>
.atmosephereBox {
  height: calc(100% + 1px);
}
</style>
