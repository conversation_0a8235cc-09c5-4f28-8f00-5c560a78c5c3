<template>
  <div class="teacher-on-stage" :class="classTypeName">
    <div
      class="teacher-on-stage-module"
      :class="[this.windowStatusClassName]"
      :style="[styleAttribute]"
    >
      <div class="teacher-on-stage-video" :class="{ 'teacher-off': !teacherVideoStatus }">
        <div
          id="teacherOnStageVideo"
          :class="{ teacherOnStageVideoPostion: teacherVideoStatus }"
        ></div>
      </div>
      <div class="private-chat-title" v-if="isPrivateChat">
        {{ $t('classroom.modules.teacherOnStage.privateChat') }}
      </div>
      <div class="teacher-info">
        <div class="name">{{ teacherName }}</div>
        <div class="microphone-wrapper">
          <img src="../imgs/icon-micro-open.png" v-if="audioStatus" />
          <img src="../imgs/icon-micro-close.png" v-else />
        </div>
      </div>
      <div v-if="teacherOffline" class="video-error">
        <div class="wrapper">
          <div class="notice">
            <p>{{ $t('classroom.modules.teacherOnStage.notice[0]') }}</p>
            <p>{{ $t('classroom.modules.teacherOnStage.notice[1]') }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import _debounce from 'lodash/debounce'
import logger from 'utils/logger'
export default {
  data() {
    return {
      windowStatus: 'normal', // normal: 正常 maximize: 最大化
      originXRatio: 0, // 上麦视图左上角X坐标相对于课件X坐标的比例
      originYRatio: 0, // 上麦视图左上角Y坐标相对于课件Y坐标的比例
      WRatio: 0, // 上麦视图宽度相对于课件宽度比例
      HRatio: 0, // 上麦视图高度相对于课件高度比例
      teacherName: '',
      isPrivateChat: false, // 是否在私聊
      styleAttribute: {
        left: 0,
        top: 0,
        width: 0,
        height: 0
      },
      classType: this.options.commonOption.classType
    }
  },
  props: {
    options: {
      type: Object,
      default: null
    }
  },
  computed: {
    // 老师视频状态
    teacherVideoStatus() {
      return this.$store.state.smallClass.teacherMsg.videoStatus
    },
    // 老师上台状态
    onStageStatus() {
      return this.$store.state.smallClass.teacherMsg.onStageStatus
    },
    teacherOffline() {
      return !this.$store.state.smallClass.teacherMsg.inClass
    },
    audioStatus() {
      return this.$store.state.smallClass.teacherMsg.audioStatus
    },
    volume() {
      return this.$store.state.smallClass.teacherMsg.volume
    },
    canCreateVideo() {
      // 已有视频流且开启老师上台
      return this.teacherVideoStatus && this.onStageStatus
    },
    classTypeName() {
      return this.classType == 2 ? 'small-class' : 'large-class'
    },
    windowStatusClassName() {
      return this.windowStatus
    }
  },
  mounted() {
    this.listenerEvent()
  },
  watch: {
    canCreateVideo: {
      handler(val) {
        if (val) {
          this.createTeacherVideo()
        } else {
          this.removeTeacherVideo()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 更新老师列表
    updateTeacherMsg(msg) {
      // @log-ignore
      this.$store.dispatch('smallClass/updateTeacherMsg', msg)
    },

    /**
     * 监听Vue事件
     */
    listenerEvent() {
      window.addEventListener('resize', _debounce(this.updateWindowPosition, 500))
    },

    handlerMessage(message) {
      const { pub, originXRatio, originYRatio, WRatio, HRatio, teacherName } = message
      console.log(message, 'messagemessage')
      if (this.onStageStatus != pub) {
        // 如果更改当前开启关闭状态就通知teacherVideo
        this.updateTeacherMsg({
          onStageStatus: pub
        })
      }
      if (pub) {
        this.originXRatio = originXRatio
        this.originYRatio = originYRatio
        this.WRatio = WRatio
        this.HRatio = HRatio
        this.teacherName = teacherName
        // this.thinkClass.RtcService.resizeRender(this.teacherUid)
        this.$nextTick(() => {
          this.updateWindowStatus(message.isMaximized ? 'maximize' : 'normal')
          this.updateWindowPosition()
        })

        logger.send({
          tag: 'userTrack',
          content: {
            msg: '老师上台'
          }
        })
      }
    },
    updateWindowStatus(status) {
      this.windowStatus = status
    },
    updateWindowPosition() {
      const boundingClientRectController = document
        .getElementById('stageLayer')
        .getBoundingClientRect()
      let coursewareWidth = boundingClientRectController.width
      let coursewareHeight = boundingClientRectController.height
      this.styleAttribute.left = coursewareWidth * this.originXRatio + 'px'
      this.styleAttribute.top = coursewareHeight * this.originYRatio + 'px'
      this.styleAttribute.width = coursewareWidth * this.WRatio + 'px'
      this.styleAttribute.height = coursewareHeight * this.HRatio + 'px'
    },
    createTeacherVideo() {
      this.$nextTick(() => {
        this.thinkClass.RtcService.createTeacherVideo('teacherOnStageVideo')
      })
    },
    removeTeacherVideo() {
      this.$nextTick(() => {
        if (document.getElementById('teacherOnStageVideo')) {
          document.getElementById('teacherOnStageVideo').innerHTML = ''
        }
      })
    },
    // 开始函数
    receiveMessage(message) {
      this.isPrivateChat = message?.privateChat || false
      this.handlerMessage(message)
    },
    // 结束函数
    destroyInteraction(message) {
      this.receiveMessage(message)
      window.removeEventListener('resize', _debounce(this.updateWindowPosition, 500))
      this.removeTeacherVideo()
      this.$emit('close', 'teacher_video_mic')
    }
  }
}
</script>

<style lang="scss" scoped>
.teacher-on-stage {
  &.large-class {
    position: absolute;
    top: 44px;
  }
}
.teacher-on-stage-module {
  position: absolute;
  z-index: 999;
  overflow: hidden;
  &.maximize .teacher-on-stage-video.teacher-off {
    background: url('./imgs/img-bg.png') no-repeat center;
    background-size: cover;
  }
  &.maximize .teacher-info {
    border-radius: 0px;
  }
  .teacher-on-stage-video {
    width: 100%;
    height: 100%;
    background: #000;
    &.teacher-off {
      background: url('./imgs/on-stage.png') no-repeat center;
      background-size: cover;
    }
  }
  .private-chat-title {
    position: absolute;
    left: 5px;
    top: 5px;
    border-radius: 4px;
    color: #fff;
    padding: 3px 5px 3px 18px;
    background: #ff5353 url('./imgs/chat.png') no-repeat 5px center;
    background-size: 10px 10px;
  }
  .teacher-info {
    position: absolute;
    left: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    right: 0;
    height: 26px;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
    border-radius: 0px 0px 4px 4px;
    .name {
      margin-left: 9px;
      line-height: 26px;
      font-size: 12px;
      color: #f4f6fa;
      overflow: hidden;
    }
    .microphone-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 16px;
      height: 16px;
      margin: 0 9px 0 0;
    }
  }
  .video-error {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #3367ff;
    .wrapper {
      position: relative;
      width: 100%;
      height: 160px;
      background: url('./imgs/bg-error.png') no-repeat center;
      background-size: 178px 160px;
      display: flex;
      justify-content: center;
      align-items: center;
      .notice {
        position: absolute;
        bottom: 0;
      }
      p {
        text-align: center;
        color: #fff;
        line-height: 14px;
        font-size: 12px;
      }
    }
  }
}
.teacherOnStageVideoPostion {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}
</style>
