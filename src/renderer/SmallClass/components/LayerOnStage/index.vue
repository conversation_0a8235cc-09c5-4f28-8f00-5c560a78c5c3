<template>
  <div id="stageLayer">
    <template v-for="interaction in interactions">
      <component
        :ref="interaction.name"
        :is="interaction.componentName"
        :key="interaction.name"
        v-if="interaction.isShow"
        :options="interaction.options"
        @close="interactionClose"
      ></component>
    </template>
  </div>
</template>

<script>
import {
  defineComponent,
  onMounted,
  onBeforeUnmount,
  nextTick,
  getCurrentInstance,
  reactive
} from '@vue/composition-api'
import TeacherOnStage from './teacherOnStage/index.vue'
import StudentOnStage from './multVideoLink/index.vue'
export default defineComponent({
  name: 'OnStageLayer',
  components: {
    TeacherOnStage,
    StudentOnStage
  },
  setup() {
    const { proxy } = getCurrentInstance()
    const interactions = reactive([
      {
        name: 'TeacherOnStage',
        componentName: 'TeacherOnStage',
        layout: 'full',
        isShow: false,
        keys: ['teacher_video_mic', 'graffiti_board_video'],
        options: {
          ...proxy.$store.state.smallClass.baseData,
          ircMsg: '',
          isHistory: false
        }
      },
      {
        name: 'StudentOnStage',
        componentName: 'StudentOnStage',
        layout: 'full',
        isShow: false,
        keys: ['mult_video_mic'],
        options: {
          ...proxy.$store.state.smallClass.baseData,
          ircMsg: '',
          isHistory: false
        }
      }
    ])

    onMounted(() => {
      // 接收emit
      proxy.$bus.$on('onStage', options => {
        const { key, noticeContent, isHistory, sendTime } = options
        const { pub, open, publishTopic } = noticeContent
        // console.log('options1111', options)
        const interaction = findInteraction(key)
        if (pub || open || publishTopic) {
          proxy.$store.dispatch('smallClass/updateInteractionStatus', {
            interactionName: key,
            interactionStatus: true
          })
          interaction.isShow = true
          interaction.options.ircMsg = noticeContent
          interaction.options.isHistory = isHistory
          interaction.options['sendTime'] = sendTime
          // 如果当前同一类型互动是打开状态，则先销毁上一个再打开新的互动
          if (interaction.isShow && interaction.id !== noticeContent.interactId) {
            interaction.isShow = false
            interaction.id = noticeContent.interactId
            nextTick(() => {
              handleOpenInteraction(interaction, options)
            })
          } else {
            handleOpenInteraction(interaction, options)
          }
        } else if (
          pub === false ||
          open === false ||
          pub === 0 ||
          open === 0 ||
          publishTopic === false // publishTopic 是答题板私有控制，答题板的关闭，是由端上的倒计时决定的
        ) {
          proxy.$store.dispatch('smallClass/updateInteractionStatus', {
            interactionName: key,
            interactionStatus: false
          })
          // 有些互动在收到停止作答和结束互动信令时还需要例如提交答案等操作，因此需要内部处理
          if (
            proxy.$refs[interaction.name] &&
            proxy.$refs[interaction.name][0]?.destroyInteraction
          ) {
            proxy.$refs[interaction.name][0]?.destroyInteraction(noticeContent)
            return
          }
          interaction.isShow = false
        }
      })
    })
    const handleOpenInteraction = (interaction, options) => {
      interaction.isShow = true
      interaction.options.ircMsg = options.noticeContent
      interaction.options.isHistory = options.isHistory
      nextTick(() => {
        if (proxy.$refs[interaction.name] && proxy.$refs[interaction.name][0].receiveMessage) {
          proxy.$refs[interaction.name][0].receiveMessage(options.noticeContent)
        }
      })
    }
    function findInteraction(key) {
      // @log-ignore
      const interaction = interactions.find(item => item.keys.includes(key))
      return interaction
    }
    function interactionClose(key) {
      const interaction = findInteraction(key)
      interaction.isShow = false
    }
    onBeforeUnmount(() => {
      proxy.$bus.$off('onStage')
    })
    return {
      interactions,
      interactionClose
    }
  }
})
</script>
