<template>
  <div>
    <div
      class="student-item"
      v-for="item in data"
      :key="item.userId"
      :style="{
        left: pptratio.width * item.originXRatio + 'px',
        top: pptratio.height * item.originYRatio + 'px',
        width: pptratio.width * item.WRatio + 'px',
        height: pptratio.height * item.HRatio + 'px',
        paddingTop: ((pptratio.width * item.WRatio) / 308) * 30 + 'px',
        paddingRight: ((pptratio.width * item.WRatio) / 308) * 10 + 'px',
        paddingBottom: ((pptratio.width * item.WRatio) / 308) * 10 + 'px',
        paddingLeft: ((pptratio.width * item.WRatio) / 308) * 10 + 'px'
      }"
    >
      <div
        v-if="item.videoFrame"
        class="decorate-frame"
        :style="{
          zIndex: 1000,
          backgroundImage: 'url(' + item.videoFrame + ')',
          backgroundSize: `${pptratio.width * item.WRatio}px ${pptratio.height * item.HRatio}px`
        }"
      ></div>
      <div class="item-block" :class="{ 'default-item': !item.videoFrame }">
        <div
          v-show="item.cameraIsOpen == 1"
          class="stu-video"
          :id="'mult-video-' + item.userId"
        ></div>
        <div v-if="item.cameraIsOpen == 2" class="stu-img">
          <div
            v-if="item?.prop?.avatarFrame?.resourceUrl"
            class="decorate-frame"
            :style="
              'background: url(' + item?.prop?.avatarFrame?.resourceUrl + ') no-repeat center'
            "
          ></div>
          <img :src="item.avatar" alt="" />
        </div>
        <lottie-player
          v-if="msgType === 4 && urlMap[item.addCoin] && isShowCoins"
          autoplay
          mode="normal"
          :src="urlMap[item.addCoin]"
          class="lottie-player"
        />
        <!-- 授权涂鸦标识 -->
        <LevelIcon
          class="level-icon"
          v-if="item.levelId > 0"
          :levelId="item.levelId"
          :subLevel="item.subLevel"
        />

        <div class="bottom-bar">
          <p class="stu-name word-ellipsis">
            {{ item.nickName }}
          </p>
          <div class="authgraffiti" v-if="item.isAuthorize"></div>

          <!-- <div class="microphone-wrapper">
            <img src="../imgs/icon-micro-open.png" v-if="itemIsOpenMicrophone(item)" />
            <img src="../imgs/icon-micro-close.png" v-else />
          </div> -->
        </div>
        <div class="medal-wrapper" v-show="item.correctCount">
          <PairIcon :correctCount="item.correctCount" />
        </div>
        <div class="emoji-wrapper" v-if="item.emoticonName">
          <EmoticonMessage
            :willAutoClear="true"
            :name="item.emoticonName"
            :type="item.emoticonType"
            :userId="item.userId"
            :width="70"
            :height="70"
            :emojiId="item.emojiId"
            :lottieUrl="item.lottieUrl"
            @clearEmoticon="handleClearEmoticon"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PairIcon from '../../VideoGroup/StudentTool/PairIcon'
import LevelIcon from '../../VideoGroup/StudentTool/LevelIcon/index.vue'

import EmoticonMessage from 'components/Common/EmoticonMessage'
import _debounce from 'lodash/debounce'
import logger from 'utils/logger'
import '@lottiefiles/lottie-player'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
export default {
  components: {
    PairIcon,
    LevelIcon,
    EmoticonMessage
  },
  props: {
    options: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      stuId: this.options.stuInfo.id,
      urlMap: {
        5: './lottiefiles/coins/coins-add-5/data.json',
        10: './lottiefiles/coins/coins-add-10/data.json'
      },
      // irc消息
      ircMsg: {},
      stuList: [],
      diffInfo: {},
      pptratio: {
        width: '',
        height: ''
      },
      boundingClientRect: {},
      data: [],
      isShowCoins: false,
      lastInteraction: '', // 避免监听和消除互动打两次埋点
      highEncoderConfig: {
        // 高分辨率
        width: 320,
        height: 240,
        bitrate: 120,
        frameRate: 10
      },
      lowEncoderConfig: {
        // 低分辨率
        bitrate: 80,
        frameRate: 10,
        width: 160,
        height: 120
      },
      msgType: null // 1、开始互动，学生举手。2、学生上台3、学生下台4、主讲给上麦学生添加金币 5、主讲滑动回显窗口。
    }
  },
  watch: {
    stuList: {
      /**
       * 监听上下台消息
       */
      handler(newVal, oldVal) {
        console.log('0704 监听上下台消息')
        // 如果是历史消息，拉去别人的
        if (this.options.isHistory) {
          this.upDownClass(newVal, oldVal)
        }
        switch (this.msgType) {
          case 2:
          case 3:
          case 6: {
            // 6表示授权/取消授权涂鸦
            if (this.options.isHistory) {
              return
            }
            console.log('multVideoLink-mounted', 'wathc', this.ircMsg, this.options.isHistory)
            this.upDownClass(newVal, oldVal)
            console.log('datas', this.data)
            break
          }
          case 4: {
            // 主讲给上麦学生添加金币
            // 得到奖励的同学
            const list = newVal.filter(item => item.addCoin !== 0)
            // 清除之前的所得记录
            this.data.forEach(item => (item.addCoin = 0))
            list.forEach(element => {
              this.data.forEach(item => {
                if (element.userId === item.userId) {
                  item.addCoin = element.addCoin
                }
              })
              if (element.userId == this.stuId) {
                // 广播更新金币
                this.$bus.$emit('updateAchievement', 'add', element.addCoin)
              }
            })
            // 展示动效
            this.isShowCoins = true
            let timer = null
            if (timer) {
              return
            }
            timer = setTimeout(() => {
              // 清除动效
              this.isShowCoins = false
              clearTimeout(timer)
              timer = null
            }, 4000)
            break
          }
          case 5:
            // 主讲滑动回显窗口
            this.dragVideoView(newVal, oldVal)
            this.$forceUpdate()
            break
          default:
            break
        }
      },
      deep: true
    },
    // 监听台上学员变化
    data: {
      handler(newVal) {
        console.log('multVideoLink-data', newVal)
        const userList = []
        newVal.forEach(item => {
          userList.push(item.userId)
        })
        this.$store.dispatch('smallClass/updateVideoMicLinkUsers', userList)
      }
    },
    // 监听摄像头状态
    cameraStatus: {
      handler(newVal) {
        this.handleSwitchCameraStatus(newVal)
      },
      immediate: true
    },
    microphoneStatus: {
      handler(newVal) {
        this.listenUpdateMicrophoneStatus(newVal)
      },
      immediate: true
    }
  },
  computed: {
    // 麦克风开启状态
    microphoneStatus() {
      return this.$store.state.smallClass.microphoneStatus
    },
    // 摄像头开启状态
    cameraStatus() {
      return this.$store.state.smallClass.cameraStatus
    },
    remoteAudioStatus() {
      return this.$store.state.smallClass.remoteAudioStatus
    },
    // 判断该上台学员的麦克风状态
    itemIsOpenMicrophone() {
      return function(item) {
        return this.stuId == item.userId ? this.microphoneStatus : item.micIsOpen == 1
      }
    }
  },
  created() {
    this.bindEvent()
  },
  mounted() {
    logger.send({
      tag: 'userTrack',
      content: {
        msg: '小班-多人上台'
      }
    })
    this.$bus.$emit('multVideoLinkStatus', {
      pub: true,
      status: 1
    })
    this.$bus.$emit('raiseHandSendMessageToTeacher', {
      type: 127
    })
    this.$bus.$emit('raiseHandForMultVideoLink', true)

    this.$nextTick(() => {
      this.getSize()
      window.addEventListener('resize', _debounce(this.getSize, 500))
    })
  },
  beforeDestroy() {
    this.setVideoEncoderConfiguration(false)
    this.$bus.$emit('multVideoLinkStatus', {
      pub: false
    })
    this.$bus.$emit('raiseHandForMultVideoLink', false)
    // 互动结束前, 所有人下台消息
    this.data.forEach(item => {
      if (item.userId == this.stuId) {
        this.$bus.$emit('raiseHandDisabled', false)
      }
      this.$bus.$emit('multVideoLinkStatus', {
        pub: true,
        status: 3,
        stuId: item.userId
      })
      this.offStageRecoverAudio(item.userId)
    })
    this.upDownClass([], this.data)
    this.unbindEvent()
  },
  methods: {
    offStageRecoverAudio(stuId) {
      // 关闭互听时，不会禁止多人上台的学生，所以下台后要再次进行静音
      if (!this.remoteAudioStatus) {
        this.thinkClass.RtcService.muteRemoteAudio(stuId, true)
      }
    },
    setVideoEncoderConfiguration(isOnStage) {
      let config
      if (isOnStage) {
        config = this.highEncoderConfig
      } else {
        config = this.lowEncoderConfig
      }
      this.thinkClass?.RtcService?.setVideoEncoderConfiguration(config)
    },
    /**
     * 获取 上台 / 下台 / 全部 学生list
     */
    getUpDownStuObj(newVal, oldVal) {
      // 2、学生上台3、学生下台
      let allList = new Map()
      const arr = [...oldVal, ...newVal]
      arr.forEach(element => {
        allList.set(element.userId, element)
      })
      const oldUserList = oldVal.map(item => item.userId) || []
      const newUserList = newVal.map(item => item.userId) || []
      const newListMap = new Set(newUserList)
      const oldListMap = new Set(oldUserList)
      // 需下台学生
      const downstu = oldUserList.filter(item => !newListMap.has(item))
      // 需要上台的学生
      const upstu = newUserList.filter(item => !oldListMap.has(item))
      return {
        downstu,
        upstu,
        allList
      }
    },
    /**
     * 操作 上台 下台
     */
    upDownClass(newVal, oldVal, isDestroy = false) {
      console.log('upDownClass', newVal)
      const upDownStuObj = this.getUpDownStuObj(newVal, oldVal)
      // 下台
      upDownStuObj.downstu.forEach(item => upDownStuObj.allList.delete(item))
      // const allStuList = Object.values(this.strMapToObj(upDownStuObj.allList) || []) || []
      upDownStuObj.downstu.forEach(i => {
        let delIndex = null
        this.data.forEach((item, index) => {
          if (i == item.userId) {
            delIndex = index
          }
        })
        delIndex != null && this.data.splice(delIndex, 1)
      })
      upDownStuObj.upstu.forEach(i => {
        this.data.length <= 4 && this.data.push(upDownStuObj.allList.get(i))
      })
      this.data.forEach(item => {
        if (this.stuId == item.userId) {
          if (this.cameraStatus) {
            item.cameraIsOpen = 1
          } else {
            item.cameraIsOpen = 2
          }
        }
        this.$store.state.smallClass.studentList.forEach(stu => {
          if (stu.stuId == item.userId) {
            item.micIsOpen = stu.microphoneStatus ? 1 : 2
            item.levelId = stu.prop.levelId
            item.subLevel = stu.prop.subLevel
            item.prop = stu.prop || {}
            item.videoFrame = stu.prop?.videoFrame?.resourceUrl
            item.correctCount = stu?.correctCount || 0
            if (this.stuId != item.userId) {
              item.cameraIsOpen = !stu.mutedVideoStatus ? 1 : 2
            }
          }
        })
      })
      // 授权涂鸦时不会走上台逻辑，但需要变更isAuthorize字段状态
      newVal.forEach(i => {
        this.data.map(item => {
          if (i.userId === item.userId) {
            item.isAuthorize = i.isAuthorize
            if (i.isAuthorize) {
              logger.send({
                tag: 'userTrack',
                content: {
                  msg: '小班-多人上台-授权涂鸦'
                }
              })
            }
          }
          return item
        })
      })
      this.$nextTick(() => {
        console.log('multVideoLink-upstu', upDownStuObj.upstu)
        console.log('multVideoLink-downstu', upDownStuObj.downstu)
        // 上台视频显示逻辑
        if (!isDestroy) {
          upDownStuObj.upstu.forEach(item => {
            this.$bus.$emit('multVideoLinkStatus', {
              pub: true,
              status: 2,
              stuId: item
            })
            // 更新学员上台状态
            this.updateStudentList(item, {
              multVideoLinkStatus: true
            })
            const videoId = `mult-video-${item}`
            if (item == this.stuId) {
              classLiveSensor.osta_ia_on_stage(this.ircMsg?.interactId, true)
              this.setVideoEncoderConfiguration(true)
              this.$bus.$emit('raiseHandDisabled', true)
              // 自己视频显示
              this.setupLocalVideo(videoId)
              // 本地未开启麦克风，则提示打开麦克风
              console.info('多人上台触发上台')
              this.$store.dispatch('smallClass/updateSelfVideoMicLink', true)
            } else {
              // 视频拉流
              setTimeout(() => {
                this.setupRemoteVideo(item, videoId)
                // 音频拉流
                this.muteRemoteAudio(item, false)

                // 音频加大
                this.$bus.$emit('onlink_adjust_volume', 'up', item)
              }, 100)
            }
          })
        }
        // 下台视频回显区逻辑恢复
        upDownStuObj.downstu.forEach(item => {
          if (item == this.stuId) {
            this.setVideoEncoderConfiguration(false)
            this.$bus.$emit('raiseHandDisabled', false)
            this.$store.dispatch('smallClass/updateSelfVideoMicLink', false)
            if (this.lastInteraction != this.ircMsg?.interactId) {
              classLiveSensor.osta_ia_on_stage(this.ircMsg?.interactId, false)
              this.lastInteraction = this.ircMsg?.interactId
            }
          } else {
            // 降低
            this.$bus.$emit('onlink_adjust_volume', 'down', item)
          }
          // 销毁远端视图
          this.destroyRemoteVideo(item)
          this.$bus.$emit('multVideoLinkStatus', {
            pub: true,
            status: 3,
            stuId: item
          })
          this.offStageRecoverAudio(item)
          this.updateStudentList(item, {
            multVideoLinkStatus: false
          })
        })
      })
    },
    /**
     * 接收消息
     */
    receiveMessage(noticeContent) {
      console.log('multVideoLink-receiveMessage', noticeContent)
      this.msgType = noticeContent.status
      this.stuList = noticeContent.data || []
      this.$bus.$emit('setIsAuthorizedStatus', noticeContent)
    },
    // 重置学生窗口位置
    dragVideoView(newVal) {
      newVal.forEach(newValItem => {
        console.log('multVideoLink-dragVideoView', newValItem, this.data)
        this.data.forEach((dataItem, dataIndex) => {
          if (dataItem.userId == newValItem.userId) {
            this.data[dataIndex].originYRatio = newValItem.originYRatio
            this.data[dataIndex].originXRatio = newValItem.originXRatio
          }
        })
      })
      return newVal
    },
    /**
     * 更新学生字段值
     */
    updateStudentVal(uid, key, val) {
      // @log-ignore
      this.data.forEach(item => {
        if (item.userId == uid) {
          item[key] = val
          this.$forceUpdate() // TODO 强制更新,性能问题
        }
      })
    },
    /**
     * map to obj
     */
    strMapToObj(strMap) {
      let obj = Object.create(null)
      for (let [k, v] of strMap) {
        obj[k] = v
      }
      return obj
    },
    /**
     * 获取容器宽度
     */
    getSize() {
      this.boundingClientRectController = document
        .getElementById('stageLayer')
        .getBoundingClientRect()
      this.pptratio.width = this.boundingClientRectController.width
      this.pptratio.height = this.boundingClientRectController.height
    },
    /**
     * 设置本地视频视图
     * @param {String} id
     */
    setupLocalVideo(id) {
      this.thinkClass.RtcService.createLocalVideo(id)
    },
    /**
     * 销毁远端视频视图
     */
    destroyRemoteVideo(uid) {
      console.log('0704 销毁远端视频视图')
      this.thinkClass.RtcService.destroyRemoteVideo(uid)
    },
    /**
     * 设置远端视频视图
     * @param {*} uid uid
     * @param {*} id id
     */
    setupRemoteVideo(uid, id) {
      this.thinkClass.RtcService.createRemoteVideo(uid, id)
    },
    /**
     * 启用/禁用远端音频
     * @param {*} uid
     * @param {*} mute
     */
    muteRemoteAudio(uid, mute) {
      this.thinkClass.RtcService.muteRemoteAudio(uid, mute)
    },
    /**
     * 绑定事件
     */
    bindEvent() {
      this.thinkClass.RtcService.on('remoteVideoStateChanged', this.listenRemoteVideoStateChanged)
      this.thinkClass.RtcService.on('remoteAudioStateChanged', this.listenRemoteAudioStateChanged)
      this.thinkClass.RtcService.on(
        'groupAudioVolumeIndication',
        this.listenGroupAudioVolumeIndication
      )
      this.$bus.$on('sendEmoji', this.listenLocalEmoticonMessage)
      this.thinkClass.SignalService.on('onRecvRoomMessage', this.listenOnRecvRoomMessage)
    },
    /**
     * 解绑事件
     */
    unbindEvent() {
      this.thinkClass.RtcService.off('remoteVideoStateChanged', this.listenRemoteVideoStateChanged)
      this.thinkClass.RtcService.off('remoteAudioStateChanged', this.listenRemoteAudioStateChanged)
      this.thinkClass.RtcService.off(
        'groupAudioVolumeIndication',
        this.listenGroupAudioVolumeIndication
      )
      this.$bus.$off('sendEmoji', this.listenLocalEmoticonMessage)
      this.thinkClass.SignalService.off('onRecvRoomMessage', this.listenOnRecvRoomMessage)
    },
    /**
     * 监听远端视频流状态变化
     */
    listenRemoteVideoStateChanged(uid, state, reason) {
      console.log('multVideoLink-remoteVideoStateChanged-1', uid, state, reason)
      this.data.forEach(item => {
        if (uid == item.userId) {
          if (state == 2) {
            if (
              document.getElementById(`mult-video-${uid}`) &&
              !document.getElementById(`mult-video-${uid}`).innerHTML
            ) {
              setTimeout(() => {
                this.setupRemoteVideo(uid, `mult-video-${uid}`)
              }, 150)
            }
            item.cameraIsOpen = 1
          }
          if (reason == 3 || reason == 5 || reason == 7) {
            item.cameraIsOpen = 2
          } else {
            item.cameraIsOpen = 1
          }
        }
      })
    },
    /**
     * 监听远端音频状态变化
     */
    listenRemoteAudioStateChanged(uid, state, reason) {
      // 远端禁用音频
      if (reason === 5) {
        this.updateStudentVal(uid, 'micIsOpen', 2)
      }
      // 远端启用音频、音频正常播放
      if (reason === 6 || state === 2) {
        this.updateStudentVal(uid, 'micIsOpen', 1)
      }
    },
    /**
     * 监听说话者音量
     */
    listenGroupAudioVolumeIndication(speakers) {
      // @log-ignore
      speakers.forEach(item => {
        this.updateStudentVal(item.uid == 0 ? this.stuId : item.uid, 'volume', item.volume)
      })
    },
    /**
     * 监听本地麦克风开关变化
     */
    listenUpdateMicrophoneStatus(status) {
      this.$bus.$emit('raiseHandSendMessageToTeacher', {
        type: 127
      })
      this.updateStudentVal(this.stuId, 'micIsOpen', status ? 1 : 2)
    },
    /**
     * 监听本地视频开关变化
     */
    listenUpdateCameraStatus(status) {
      this.handleSwitchCameraStatus(status)
      this.$bus.$emit('raiseHandSendMessageToTeacher', {
        type: 127
      })
    },
    /**
     * 监听本地表情消息
     */
    listenLocalEmoticonMessage(params) {
      this.handleEmoticon(this.stuId, params)
    },
    /**
     * 监听群聊消息
     */
    listenOnRecvRoomMessage(res) {
      const { content, fromUserInfo } = res
      console.log(content)
      const { ircType, data } = content
      const commonMsg = {
        name: data?.name,
        type: data?.type
      }
      // 处理表情消息
      if (ircType == 'send_emoji') {
        this.handleEmoticon(fromUserInfo.psId, commonMsg)
      } else if (ircType == 'animation_emoji') {
        this.handleEmoticon(fromUserInfo.psId, {
          ...commonMsg,
          ...data.resource
        })
      }
    },
    /**
     * 处理表情显示逻辑
     */
    handleEmoticon(stuId, params) {
      const setEmojiList = {
        emoticonName: params.name,
        emoticonType: params.type, // 默认本地图片表情
        lottieUrl: params.lottieUrl,
        emojiId: params.emojiId
      }
      this.patchDealEmoji(stuId, setEmojiList)
    },
    /**
     * 批量处理表情模块
     */
    patchDealEmoji(stuId, clearEmojiList) {
      for (let type in clearEmojiList) {
        this.updateStudentVal(stuId, type, clearEmojiList[type])
      }
    },
    /**
     * 处理清空表情消息
     */
    handleClearEmoticon(userId) {
      const clearEmojiList = {
        emoticonName: '',
        emoticonType: 1, // 默认本地图片表情
        lottieUrl: '',
        emojiId: ''
      }
      this.patchDealEmoji(userId, clearEmojiList)
    },
    /**
     * 销毁互动
     */
    destroyInteraction(noticeContent) {
      this.$bus.$emit('setIsAuthorizedStatus', noticeContent)
      console.log('0704 销毁互动-------多人上台1', noticeContent, this.data)
      // 互动组件销毁之后，执行下所有学生下台的逻辑
      this.upDownClass(noticeContent.data, this.data, true)
      this.$emit('close', 'mult_video_mic')
    },
    /**
     * 多人上台支持开关摄像头操作
     */
    handleSwitchCameraStatus(status) {
      this.data.forEach(item => {
        if (this.stuId == item.userId) {
          if (status) {
            item.cameraIsOpen = 1
          } else {
            item.cameraIsOpen = 2
          }
        }
      })
    },
    updateStudentList(uid, data) {
      this.$store.dispatch('smallClass/updateStudentList', {
        uid,
        data: data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.student-item {
  background-size: cover;
  position: absolute;
  z-index: 101;
  overflow: hidden;
  .item-block {
    position: relative;
    border-radius: 10px;
    height: 100%;
    background: url('~assets/images/smallClass/black-lines-bg.png');
    &.default-item {
      border-radius: 10px;
      border: 0.04rem solid rgb(216, 216, 216, 0.9);
    }
  }
}
.stu-name {
  color: #fff;
  height: 26px;
  line-height: 26px;
  padding-left: 8px;
  margin-right: 8px;
  border-radius: 6px;
}
.word-ellipsis {
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.stu-video {
  position: absolute;
  width: 100%;
  height: 100%;
  color: #fff;
  overflow: hidden;
  border-radius: 10px;
}
.stu-img {
  width: 96px;
  height: 96px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
  }
  .decorate-frame {
    background-size: 95px 95px !important;
  }
}
.authgraffiti {
  width: 16px;
  height: 16px;
  margin-right: 11px;
  background: url('~assets/images/smallClass/icon-auth-pen-new.png') center center no-repeat;
  background-size: 100% auto;
  margin-left: auto;
}
.bottom-bar {
  position: absolute;
  width: 100%;
  height: 32px;
  bottom: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
  border-radius: 0 0 10px 10px;
}
.microphone-wrapper {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  z-index: 102;
  width: 16px;
  height: 16px;
  margin-right: 11px;
}
.medal-wrapper {
  position: absolute;
  left: 5px;
  top: 5px;
}
.level-icon {
  right: 6px;
  top: 6px;
}
.raise-hand-wrapper {
  position: absolute;
  right: 4px;
  top: 4px;
  z-index: 10;
}
.emoji-wrapper {
  // 表情区域
  position: absolute;
  background: rgba(0, 0, 0, 0.4);
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  border-radius: 10px;
}
.decorate-frame {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}
</style>
