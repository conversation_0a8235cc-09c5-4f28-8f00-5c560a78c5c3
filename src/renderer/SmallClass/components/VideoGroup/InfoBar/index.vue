<template>
  <!-- 下方信息栏 -->
  <div
    class="absolute inset-x-0 bottom-0 px-[0.04rem] pt-[0.08rem] pb-[0.04rem] bg-gradient-to-b from-transparent to-black flex justify-between z-20"
  >
    <!-- 私聊显示 -->
    <template v-if="showType === showBarType.privateChat">
      <div class="leading-[0.26rem] text-middle text-name whitespace-nowrap helpName">
        <i class="w-[0.12rem] h-[0.12rem] inline-block ml-[0.02rem] bg-no-repeat"></i
        >{{ $t('common.helpOthers') }}
      </div>
    </template>
    <!-- 老师上台信息 -->
    <template v-else-if="showType === showBarType.onStage">
      <div class="text-small text-name whitespace-nowrap helpName">
        {{ $t('common.onStage') }}
      </div>
    </template>
    <!-- 名字显示 -->
    <template v-else-if="showType === showBarType.nameInfo">
      <div class="text-small text-name flex-grow truncate">{{ name }}</div>
      <div class="w-[0.18rem] h-[0.18rem] flex-shrink-0" v-if="showMicrophone">
        <DynamicMic :microphoneStatus="microphoneStatus" />
      </div>
    </template>
  </div>
</template>

<script>
import DynamicMic from './DynamicMic.vue'
import { showBarType } from '../constant.js'
import { defineComponent } from '@vue/composition-api'
export default defineComponent({
  name: 'InfoBar',
  components: {
    DynamicMic
  },
  data() {
    return {
      showBarType
    }
  },
  props: {
    showType: {
      type: String,
      default: showBarType.nameInfo
    },
    name: {
      type: String,
      default: ''
    },
    volume: {
      type: Number,
      default: 0
    },
    microphoneStatus: {
      type: Boolean,
      default: false
    },
    showMicrophone: {
      type: Boolean,
      default: false
    }
  }
})
</script>

<style scoped lang="scss">
@keyframes scroll_show {
  0% {
    transform-origin: left;
    transform: scale3d(1, 1, 1) translateX(100%);
  }
  100% {
    transform-origin: left;
    transform: scale3d(1, 1, 1) translateX(-100%);
  }
}
.helpName {
  white-space: nowrap;
  animation: scroll_show 10s infinite linear;
  display: flex;
  align-items: center;
  i {
    width: 0.12rem;
    height: 0.12rem;
    display: inline-block;
    margin: 0 0.02rem 0 0;
    background: url('~assets/images/chat/chat-01.png') no-repeat left center;
  }
}
</style>
