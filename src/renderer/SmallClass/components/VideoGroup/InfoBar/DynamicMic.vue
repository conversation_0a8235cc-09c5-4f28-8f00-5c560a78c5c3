<template>
  <div class="relative h-full" ref="micBox">
    <template v-if="microphoneStatus">
      <div class="absolute h-full inset-0 bg-cover bg-top z-0 bg-green-mic"></div>
      <div
        class="absolute inset-0 bg-cover bg-bottom z-10 bg-green-mic transition duration-80 ease-in-out"
        :style="micBoxStyle"
      ></div>
    </template>
    <template v-else>
      <div class="absolute h-full inset-0 bg-cover bg-top z-0 bg-off-mic"></div>
    </template>
  </div>
</template>

<script>
import { defineComponent, watch, ref, nextTick } from '@vue/composition-api'

export default defineComponent({
  name: 'DynamicMic',
  props: {
    volume: {
      type: Number,
      default: 225
    },
    microphoneStatus: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const micBox = ref(null)

    const micBoxStyle = ref({
      height: '0px',
      top: '0px'
    })
    const updateMicBoxStyle = volume => {
      // @log-ignore
      nextTick(() => {
        const boxElement = micBox.value
        if (!boxElement) {
          console.warn("Could not find 'mic-box' element.")
          return
        }
        const boxHeight = boxElement.clientHeight
        const height = parseInt(boxHeight * (volume / 225))
        const fontSize = parseInt(document.querySelector('html').style.fontSize)
        const heightRem = (height / fontSize).toFixed(2)
        const topRem = (boxHeight - height) / fontSize
        micBoxStyle.value = {
          height: `${heightRem}rem`,
          top: `${topRem}rem`
        }
      })
    }
    watch(
      () => props.volume,
      newValue => {
        updateMicBoxStyle(newValue)
      },
      {
        immediate: true
      }
    )

    return {
      micBox,
      micBoxStyle
    }
  }
})
</script>

<style>
.dynamic-mic-white {
  will-change: clip;
}
</style>
