<template>
  <!-- 老师视频窗口 -->
  <div
    class="flex-shrink-0 relative w-[1.76rem] h-[1.32rem] overflow-hidden bg-cover bg-no-teacher teacher-video-wrap"
  >
    <!-- 老师视频 -->
    <div v-show="renderTeacherVideo" id="teacherVideoContainer" class="h-full w-full"></div>
    <!-- 老师上台 -->
    <div
      v-if="teacherOnStageStatus"
      class="absolute left-0 top-0 z-10 h-full w-full bg-teacher-onstage bg-cover bg-no-repeat"
    ></div>
    <!-- 下方信息栏 -->
    <InfoBar
      :showType="infoType"
      :name="name"
      :microphoneStatus="teacherMicrophoneStatus"
      :showMicrophone="teacherInclass"
    ></InfoBar>
  </div>
</template>

<script>
import {
  defineComponent,
  watch,
  computed,
  getCurrentInstance,
  nextTick
} from '@vue/composition-api'
import InfoBar from './InfoBar/index.vue'
import { showBarType } from './constant'

export default defineComponent({
  name: 'TeacherVideo',
  components: {
    InfoBar
  },
  setup() {
    const { proxy } = getCurrentInstance()
    const { thinkClass, $store: store } = proxy
    const teacherMsg = store.state.smallClass.teacherMsg
    const name = teacherMsg.nickName
    const teacherMicrophoneStatus = computed(() => teacherMsg.audioStatus)
    const teacherVideoStatus = computed(() => teacherMsg.videoStatus)
    const teacherOnStageStatus = computed(() => teacherMsg.onStageStatus)
    const teacherOnPrivateChatStatus = computed(() => teacherMsg.privateChatStatus)
    const teacherInclass = computed(() => teacherMsg.inClass)
    const renderTeacherVideo = computed(() => {
      return teacherVideoStatus.value && !teacherOnStageStatus.value
    })
    const infoType = computed(() => {
      if (teacherOnPrivateChatStatus.value) {
        return showBarType.privateChat
      } else if (teacherOnStageStatus.value) {
        return showBarType.onStage
      } else {
        return showBarType.nameInfo
      }
    })

    watch(
      () => renderTeacherVideo.value,
      newValue => {
        // console.log('renderTeacherVideo changed:', newValue)
        if (newValue) {
          nextTick(() => {
            document.getElementById('teacherVideoContainer').innerHTML = ''
            thinkClass.RtcService.createTeacherVideo('teacherVideoContainer')
          })
        } else {
          const ele = document.getElementById('teacherVideoContainer')
          if (ele) {
            ele.innerHTML = ''
          }
        }
      },
      { immediate: true }
    )

    return {
      teacherMicrophoneStatus,
      teacherOnPrivateChatStatus,
      teacherOnStageStatus,
      renderTeacherVideo,
      teacherInclass,
      name,
      infoType
    }
  }
})
</script>
