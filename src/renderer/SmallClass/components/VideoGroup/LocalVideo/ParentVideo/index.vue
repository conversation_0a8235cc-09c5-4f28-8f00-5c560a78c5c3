<template>
  <!-- 如果有自己孩子远端流 -->
  <RemoteVideoItem
    v-if="myChildInfo && myChildInfo.inClass"
    :key="myChildInfo.userId"
    :remoteStuInfo="myChildInfo"
    :hideRemoteVideo="false"
    :remoteAudioStatus="true"
  />
  <!-- 孩子未进入教室 -->
  <OfflineVideo v-else :name="nickName"></OfflineVideo>
</template>

<script>
import { defineComponent, computed, getCurrentInstance } from '@vue/composition-api'
import RemoteVideoItem from '../../RemoteVideo/RemoteVideoItem.vue'
import OfflineVideo from './OfflineVideo'

export default defineComponent({
  name: 'Video',
  components: {
    OfflineVideo,
    RemoteVideoItem
  },
  setup() {
    const {
      proxy: { $store: store }
    } = getCurrentInstance()

    const baseData = computed(() => store.state.smallClass.baseData)
    const nickName = computed(() => baseData.value.commonOption.nickName)
    const studentList = computed(() => store.state.smallClass.studentList)
    const myChildInfo = computed(() => {
      //家长旁听时，孩子远端信息
      return studentList.value.find(item => item.currentParentsChild)
    })

    return {
      myChildInfo,
      nickName
    }
  }
})
</script>
