<template>
  <div class="relative w-[1.32rem] h-[1.32rem] flex overflow-hidden justify-center">
    <div class="text-middle text-white text-center leading-[1.32rem]">
      {{ $t('classroom.smallClass.videoGroup.localVideoAudition.offline') }}
    </div>
    <InfoBar :showType="showBarType.nameInfo" :name="name"></InfoBar>
  </div>
</template>
<script>
import InfoBar from '../../InfoBar'
import { showBarType } from '../../constant'
import { defineComponent } from '@vue/composition-api'

export default defineComponent({
  name: 'OffLineVideo',
  props: {
    name: {
      type: String,
      default: ''
    }
  },
  components: {
    InfoBar
  },
  setup() {
    return {
      showBarType
    }
  }
})
</script>

<style lang="scss" scoped>
.video-item {
  position: relative;
  width: 103px;
  height: 103px;
  margin-right: 1px;
  overflow: hidden;
}
.video-parent-audition {
  display: flex;
  justify-content: center;
  // align-items: center;
  position: relative;
  .notice {
    margin-top: 40px;
    font-size: 14px;
    color: #ffffff;
    text-align: center;
  }
  .name {
    position: absolute;
    background: #ffcf1b;
    border-radius: 5px;
    bottom: 2px;
    padding: 4px;
    width: 100%;
    font-size: 12px;
    color: #172b4d;
    overflow: hidden;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
}
</style>
