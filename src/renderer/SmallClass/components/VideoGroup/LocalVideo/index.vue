<template>
  <div class="flex-shrink-0">
    <!-- 家长旁听 -->
    <template v-if="isParent">
      <ParentVideo />
    </template>
    <!-- 学生旁听 -->
    <template v-else-if="isAudition">
      <AuditionVideo />
    </template>
    <!-- 学生本人 -->
    <template v-else>
      <StudentVideo ref="LocalVideo" />
    </template>
  </div>
</template>
<script>
import { defineComponent, computed, getCurrentInstance } from '@vue/composition-api'
import StudentVideo from './StudentVideo.vue'
import ParentVideo from './ParentVideo'
import AuditionVideo from './AuditionVideo'

export default defineComponent({
  name: 'LocalVideo',
  components: {
    StudentVideo,
    ParentVideo,
    AuditionVideo
  },
  setup() {
    const {
      proxy: { thinkClass, $store: store, $bus: bus }
    } = getCurrentInstance()

    const baseData = computed(() => store.state.smallClass.baseData)
    const isParent = computed(() => baseData.value.commonOption.isParent)
    const nickName = computed(() => baseData.value.commonOption.nickName)
    const isAudition = computed(() => baseData.value.commonOption.isAudition && !isParent.value)
    const selfId = computed(() => baseData.value.commonOption.stuId)
    const studentList = computed(() => store.state.smallClass.studentList)
    const teacherId = computed(() => baseData.value.configs.rtcConfig.teacherUid)

    const chatAudioChange = noticeContent => {
      // 被私聊学员收到老师私聊信令后学员端默认仅拉取老师音频，在被私聊期间不响应互听开关信令;其它非被私聊学员在收到老师私聊其它学员信令后停止拉取老师音频和被私聊学员音频，在老师私聊其它学员期间响应互听开关信令（但特别注意的是，即使互听打开也不能拉取正在私聊学员的音频）;在私聊结束后老师端需要补充下发一次当前互听开关状态，保证被私聊的学员互听状态及时同步到老师设置的状态（这里也实现了在互听开启时其它非被私聊学员端会重新拉取被私聊学员的音频）；学员端收到私聊结束信令后恢复拉取老师音频。
      if (noticeContent.pub) {
        store.dispatch('smallClass/updatePrivateChatInfo', noticeContent)
        if (selfId.value == noticeContent.userId) {
          studentList.value.forEach(item => {
            // @log-ignore
            if (item.userId != noticeContent.userId) {
              thinkClass.RtcService.muteRemoteAudio(item.userId, true)
            }
          })
          console.info('私聊触发上台')
          store.dispatch('smallClass/updateSelfVideoMicLink', true)
        } else {
          thinkClass.RtcService.muteRemoteAudio(teacherId.value, true)
          thinkClass.RtcService.muteRemoteAudio(noticeContent.userId, true)
        }
      } else {
        store.dispatch('smallClass/updatePrivateChatInfo', {})
        if (selfId.value == noticeContent.userId) {
          store.dispatch('smallClass/updateSelfVideoMicLink', false)
          studentList.value.forEach(item => {
            // @log-ignore
            if (item.userId != noticeContent.userId) {
              thinkClass.RtcService.muteRemoteAudio(item.userId, false)
            }
          })
        } else {
          thinkClass.RtcService.muteRemoteAudio(teacherId.value, false)
          thinkClass.RtcService.muteRemoteAudio(noticeContent.userId, false)
        }
      }
    }
    // todo 抽到哪里?
    bus.$on('changeAudioStatus', chatAudioChange)
    // 拍照上墙时候会触发
    // todo 移到拍照上墙
    bus.$on('cameraStatus', status => {
      thinkClass.RtcService.muteLocalVideo(status)
    })
    return {
      isParent,
      nickName,
      isAudition
    }
  }
})
</script>
