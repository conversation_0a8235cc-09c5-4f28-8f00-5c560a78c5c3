<template>
  <!-- 学生视频窗口 -->
  <div class="relative w-[1.32rem] h-[1.32rem] overflow-hidden bg-contain bg-student-bg">
    <!-- 学生视频 -->
    <div v-show="renderVideo" id="video-group-local" class="h-full w-full"></div>
    <!-- 头像 -->
    <div
      v-if="!renderVideo"
      class="flex justify-center w-full h-full bg-contain bg-no-repeat items-center avatar-back"
    >
      <div
        v-if="self?.prop?.avatarFrame?.resourceUrl && !self.multVideoLinkStatus"
        class="avatar-frame"
        :style="'background: url(' + self?.prop?.avatarFrame?.resourceUrl + ') no-repeat center'"
      ></div>
      <img
        v-if="!self.multVideoLinkStatus"
        class="relative top-[cacl(50% - 0.22rem)] rounded-full avatar"
        :src="self.avatar"
      />
      <img v-else class="w-[0.54rem] h-[0.62rem]" src="../images//bg-mult-video-link.png" />
    </div>
    <!-- 下方信息栏 -->
    <InfoBar
      :showType="infoType"
      :name="self.stuName"
      :microphoneStatus="microphoneStatus"
      :showMicrophone="isAllOnStage"
    ></InfoBar>
    <StudentTool :id="self.userId" :isAllOnStage="isAllOnStage"></StudentTool>
  </div>
</template>

<script>
import {
  defineComponent,
  watch,
  computed,
  getCurrentInstance,
  nextTick
} from '@vue/composition-api'
import InfoBar from '../InfoBar'
import StudentTool from '../StudentTool'
import { showBarType } from '../constant'
import logger from 'utils/logger'

export default defineComponent({
  name: 'Video',
  components: {
    InfoBar,
    StudentTool
  },
  props: {
    isAllOnStage: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    const {
      proxy: { thinkClass, $store: store, $bus: bus }
    } = getCurrentInstance()

    const microphoneStatus = computed(() => store.state.smallClass.microphoneStatus)
    const cameraStatus = computed(() => store.state.smallClass.cameraStatus)
    const wallRandomCallStatus = computed(() => store.state.smallClass.wallRandomCallStatus)
    const baseData = computed(() => store.state.smallClass.baseData)
    const studentList = computed(() => store.state.smallClass.studentList)
    const self = computed(() =>
      studentList.value.find(item => item.userId == baseData.value.stuInfo.id)
    )
    const renderVideo = computed(() => {
      return (
        self.value.inClass &&
        !wallRandomCallStatus.value &&
        cameraStatus.value &&
        !self.value.multVideoLinkStatus
      )
    })
    const reportRtcStatus = (options = {}) => {
      bus.$emit('rtcStatusChange', options)
    }
    // 更新学生列表
    const updateStudentList = (data = {}) => {
      store.dispatch('smallClass/updateStudentList', {
        uid: self.value.userId,
        data
      })
    }
    /**
     * 日志上报
     */
    const sendLogger = (msg, level = 'info') => {
      logger.send({
        tag: 'videoGroup',
        level,
        content: {
          msg: msg
        }
      })
    }
    const infoType = computed(() => {
      return showBarType.nameInfo
    })
    // 监听进入离开教室和相机状态
    watch(
      () => [self.value.inClass, cameraStatus.value],
      (inClass, status) => {
        const displayVideo = inClass && status

        reportRtcStatus({
          displayVideo,
          micIsOpen: microphoneStatus.value
        })

        sendLogger(`本地视频是否在教室: ${inClass}, 开关状态: ${status}`)
      },
      { immediate: true }
    )
    // 监听是否可以创建视频
    watch(
      () => renderVideo.value,
      value => {
        if (value) {
          nextTick(() => {
            const localVideoElement = document.getElementById('video-group-local')
            if (localVideoElement) {
              thinkClass.RtcService.createLocalVideo('video-group-local')
            } else {
              sendLogger(`本地视频dom不存在`, 'error')
            }
          })
        } else {
          const ele = document.getElementById('video-group-local')
          if (ele) {
            ele.innerHTML = ''
          }
        }
      },
      {
        immediate: true
      }
    )
    watch(
      () => microphoneStatus.value,
      status => {
        reportRtcStatus({
          displayVideo: cameraStatus.value,
          micIsOpen: status
        })
        sendLogger(`本地音频开关状态, status: ${status}`)
      }
    )

    /**
     * 监听Vue事件
     */
    const listenerEvent = () => {
      // @log-ignore
      // 监听获取本地视频开关状态事件
      // todo 重写
      bus.$on('getLocalDisplayVideoStatus', callback => {
        callback && callback(cameraStatus.value)
      })
      // 监听金币激励勋章变化
      bus.$on('chats.correctSelfMedalData', data => {
        updateStudentList({
          level: data.level,
          correctCount: data.correctCount
        })
      })
      // 监听表情发送通知
      bus.$on('sendEmoji', params => {
        let emojiStatus = {
          emoticonName: params.name, // 表情名称
          emoticonType: params.type // 表情类型 1 本地表情 2 lottie表情
        }
        if (params.type == 2 || params.type == 3) {
          emojiStatus = Object.assign(emojiStatus, {
            lottieUrl: params.lottieUrl, // lottie表情url,
            emojiId: params.emojiId // lottie 表情id
          })
        }
        updateStudentList(emojiStatus)
      })
      // 监听本地举手状态发送通知
      bus.$on('sendRaiseHand', status => {
        updateStudentList({
          raiseHandStatus: status
        })
      })
    }
    const init = () => {
      listenerEvent()
    }
    init()
    return {
      self,
      renderVideo,
      infoType,
      microphoneStatus
    }
  }
})
</script>
<style lang="scss" scoped>
.avatar {
  width: 45px;
  height: 45px;
}
.avatar-back {
  .avatar-frame {
    width: 70px;
    height: 70px;
    position: absolute;
    background-size: 70px 70px !important;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
  }
}
</style>
