<template>
  <div
    class="relative remote-video-wrap w-[1.32rem] h-[1.32rem]"
    :class="{
      'remote-audio-border': isShowVolumeBorder && isAllOnStage
    }"
  >
    <!-- 学生视频窗口 -->
    <div
      :id="`remote-observe-${stuInfo.userId}`"
      :data-id="stuInfo.userId"
      class="relative w-full h-full overflow-hidden bg-contain "
      :class="{
        'bg-student-bg': !isAllOnStage
      }"
    >
      <!-- 学生视频 -->
      <div v-show="renderVideo" :id="'remote-' + stuInfo.userId" class="h-full w-full"></div>
      <div
        v-if="stuInfo.microphoneStatus && !isAllOnStage"
        class="absolute top-0 right-0 speaking-icon"
      >
        <img src="../images/speaking.png" alt="" />
      </div>
      <!-- 头像 -->
      <div
        v-if="!renderVideo"
        class="flex justify-center w-full h-full bg-contain bg-no-repeat items-center avatar-back"
      >
        <div
          v-if="stuInfo?.prop?.avatarFrame?.resourceUrl && !stuInfo.multVideoLinkStatus"
          class="avatar-frame"
          :style="
            'background: url(' + stuInfo?.prop?.avatarFrame?.resourceUrl + ') no-repeat center'
          "
        ></div>
        <img
          v-if="!stuInfo.multVideoLinkStatus"
          class="relative top-[cacl(50% - 0.22rem)] rounded-full w-[0.56rem] h-[0.56rem]"
          :src="stuInfo.avatar"
        />
        <img v-else class="w-[0.54rem] h-[0.62rem]" src="../images//bg-mult-video-link.png" />
      </div>
      <!-- 下方信息栏 -->
      <InfoBar
        :showType="infoType"
        :name="stuInfo.stuName"
        :microphoneStatus="stuInfo.microphoneStatus"
        :showMicrophone="isAllOnStage && remoteAudioStatus"
      ></InfoBar>
      <StudentTool :id="stuInfo.userId" :isAllOnStage="isAllOnStage"></StudentTool>
    </div>
    <!-- <div class="video-switch-wrapper" v-if="isAllOnStage">
      <div class="video-switch-container">
        <div class="hide-video-button" v-if="stuInfo.displayVideo" @click="handleRemoteVideoStatus">
          {{ $t('classroom.smallClass.videoGroup.hide') }}
        </div>
        <div
          class="show-video-button"
          v-if="!stuInfo.displayVideo"
          @click="handleRemoteVideoStatus"
        >
          {{ $t('classroom.smallClass.videoGroup.show') }}
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import {
  defineComponent,
  toRefs,
  computed,
  getCurrentInstance,
  watch,
  nextTick,
  onBeforeUnmount,
  onMounted,
  ref
} from '@vue/composition-api'
import InfoBar from '../InfoBar'
import StudentTool from '../StudentTool'
import { showBarType } from '../constant'

export default defineComponent({
  name: 'Video',
  components: {
    InfoBar,
    StudentTool
  },
  props: {
    remoteStuInfo: {
      type: Object,
      default: null
    },
    hideRemoteVideo: {
      type: Boolean,
      default: null
    },
    remoteAudioStatus: {
      type: Boolean,
      default: false
    },
    isAllOnStage: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const {
      proxy: { thinkClass, $bus: bus, $store: store }
    } = getCurrentInstance()

    const stuInfo = computed(() => props.remoteStuInfo)
    const {
      isIntersecting,
      mutedVideoStatus,
      multVideoLinkStatus,
      cameraStatus,
      userId,
      displayVideo,
      volume
    } = toRefs(stuInfo.value)
    const renderVideo = computed(() => {
      return (
        isIntersecting.value &&
        !mutedVideoStatus.value &&
        !multVideoLinkStatus.value &&
        cameraStatus.value &&
        displayVideo.value &&
        !props.hideRemoteVideo
      )
    })
    const infoType = computed(() => {
      return showBarType.nameInfo
    })

    watch(
      () => renderVideo.value,
      value => {
        if (value) {
          nextTick(() => {
            const remoteElement = document.getElementById(`remote-${userId.value}`)
            if (remoteElement && remoteElement.innerHTML) {
              console.warn(`当前已创建学生视频，禁止重复触发,uid：${userId.value}`)
              return
            }

            thinkClass.RtcService.createRemoteVideo(userId.value, `remote-${userId.value}`)
          })
        } else {
          if (multVideoLinkStatus.value) {
            return
          }
          nextTick(() => {
            const remoteElement = document.getElementById(`remote-${userId.value}`)
            if (remoteElement) {
              remoteElement.innerHTML = ''
            }
          })
        }
      },
      { immediate: true }
    )
    const isShowVolumeBorder = ref(false)
    const sleep = time => {
      // @log-ignore
      return new Promise(resolve => setTimeout(resolve, time))
    }
    // 全员上台用户音量大于100（声网回调音量值）时，显示为绿色边框，表示用户正在发言
    watch(
      () => volume.value,
      value => {
        if (value >= 100) {
          isShowVolumeBorder.value = true
          sleep(1000).then(() => {
            // @log-ignore
            isShowVolumeBorder.value = false
          })
        }
      },
      { immediate: true }
    )
    const triggerToggleObserver = (uid, status) => {
      bus.$emit('toggleObserver', uid, status)
    }
    onMounted(() => {
      triggerToggleObserver(userId.value, true)
    })
    onBeforeUnmount(() => {
      triggerToggleObserver(userId.value, false)
    })

    const handleRemoteVideoStatus = () => {
      // 更新远端视频显示状态
      store.dispatch('smallClass/updateStudentList', {
        uid: userId.value,
        data: { displayVideo: !displayVideo.value }
      })
    }
    return {
      renderVideo,
      stuInfo,
      infoType,
      handleRemoteVideoStatus,
      isAllOnStage: props.isAllOnStage,
      isShowVolumeBorder
    }
  }
})
</script>
<style lang="scss" scoped>
.remote-video-wrap {
  // 视频开关区域
  .video-switch-wrapper {
    display: none;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 20;
    .video-switch-container {
      font-size: 12px;
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      .hide-video-button,
      .show-video-button {
        width: 82px;
        height: 30px;
        line-height: 26px;
        text-align: center;
        border-radius: 16px;
        color: #fff;
        border: 2px solid rgba(255, 255, 255, 0.4);
        cursor: pointer;
      }
      .hide-video-button {
        background: #ffaa0a;
      }
      .show-video-button {
        background: #02ca8a;
      }
    }
  }
  &:hover {
    .video-switch-wrapper {
      display: block;
    }
  }
}
.remote-audio-border {
  border: 4px solid #5ac68f !important;
}
.avatar-back {
  .avatar-frame {
    width: 70px;
    height: 70px;
    position: absolute;
    background-size: 70px 70px !important;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
  }
}
.speaking-icon {
  margin-top: 2px;
  margin-right: 2px;
  width: 20px;
  height: 20px;
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.6);
}
</style>
