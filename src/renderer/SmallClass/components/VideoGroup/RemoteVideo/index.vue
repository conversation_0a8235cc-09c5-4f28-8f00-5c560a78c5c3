<template>
  <div
    class="relative overflow-hidden flex items-center flex-grow"
    ref="showBox"
    @mouseenter="enterVideoGroup"
    @mouseleave="leaveVideoGroup"
  >
    <!-- 背景框 -->
    <div class="absolute flex overflow-hidden w-full" v-show="!canScroll">
      <FillRemoteVIdeoItem class="mr-[2px] last:mr-0" v-for="item in 8" :key="item" />
    </div>
    <!-- 左箭头 -->
    <div
      v-show="arrowShow.pre"
      class="w-[0.16rem] h-[0.36rem] absolute bg-black/40 left-[4px] z-10 rounded-[0.02rem] flex justify-center items-center cursor-pointer"
      @click="pageChange('pre')"
    >
      <img class="w-[0.08rem] h-[0.14rem]" src="../images/left-arrow.png" />
    </div>

    <div class="relative flex overflow-hidden remote-video-wrapper w-full" ref="scroll">
      <div class="flex" ref="allRemoteBox">
        <RemoteVideoItem
          class="mr-[2px] last:mr-0"
          v-for="item in inClassStudentList"
          :key="item.userId"
          :remoteStuInfo="item"
          :remoteAudioStatus="remoteAudioStatus"
          :hideRemoteVideo="hideRemoteVideo"
        />
      </div>
    </div>
    <!-- 右箭头 -->
    <div
      v-show="arrowShow.next"
      class="w-[0.16rem] h-[0.36rem] absolute bg-black/40 right-[4px] z-10 rounded-[0.02rem] flex justify-center items-center cursor-pointer"
      @click="pageChange('next')"
    >
      <img class="w-[0.08rem] h-[0.14rem]" src="../images/right-arrow.png" />
    </div>
  </div>
</template>
<script>
import {
  defineComponent,
  computed,
  ref,
  reactive,
  getCurrentInstance,
  nextTick,
  onMounted,
  onBeforeUnmount
} from '@vue/composition-api'
import InfoBar from '../InfoBar'
import RemoteVideoItem from './RemoteVideoItem.vue'
import FillRemoteVIdeoItem from './FillRemoteVIdeoItem'
import BScroll from '@better-scroll/core'
import MouseWheel from '@better-scroll/mouse-wheel'
export default defineComponent({
  name: 'Video',
  components: {
    InfoBar,
    RemoteVideoItem,
    FillRemoteVIdeoItem
  },
  setup() {
    const {
      proxy: { $store: store, $bus: bus, $refs: refs }
    } = getCurrentInstance()

    const wallRandomCallStatus = computed(() => store.state.smallClass.wallRandomCallStatus)
    const studentList = computed(() => store.state.smallClass.studentList)
    const inClassStudentList = computed(() => {
      return studentList.value.filter(item => item.inClass && !item.isSelf)
    })
    const canScroll = computed(() => {
      return inClassStudentList.value.length > 7
    })
    const isExaminationStatus = ref(false)
    // 监听课中考试状态：考试时其他学员的视频调整为头像状态，结束后恢复
    bus.$on('setExaminationStatus', data => {
      isExaminationStatus.value = data
    })
    const hideRemoteVideo = computed(
      () => wallRandomCallStatus.value || isExaminationStatus.value || isFocusMode.value
    )
    const remoteAudioStatus = computed(() => store.state.smallClass.remoteAudioStatus)
    let intersectionObserver = null
    const updateStudentList = (uid, data) => {
      // @log-ignore
      store.dispatch('smallClass/updateStudentList', {
        uid,
        data
      })
    }
    const initObserver = () => {
      intersectionObserver = new IntersectionObserver(
        entries => {
          for (let v of entries) {
            const uid = v.target.dataset.id
            // 更新学员可见状态
            updateStudentList(uid, {
              isIntersecting: v.isIntersecting
            })
          }
        },
        {
          root: document.querySelectorAll('.remote-video-wrapper')[0],
          threshold: 0
        }
      )
    }
    const toggleObserver = (uid, isObserve) => {
      const ele = document.querySelector(`#remote-observe-${uid}`)
      if (isObserve) {
        ele && intersectionObserver.observe(ele)
      } else {
        ele && intersectionObserver.unobserve(ele)
      }
      nextTick(() => {
        bs.refresh()
        if (isInVideoGroup) {
          showArrowHandle()
        }
      })
    }
    bus.$on('toggleObserver', toggleObserver)
    let bs = null
    BScroll.use(MouseWheel)
    initObserver()

    const getPageSize = () => {
      const remoteVideos = refs.allRemoteBox.children
      if (remoteVideos.length > 0) {
        const remoteVideoRect = remoteVideos[0].getBoundingClientRect()
        const boxtRect = refs.showBox.getBoundingClientRect()
        const pageSize = Math.floor(boxtRect.width / remoteVideoRect.width)
        return pageSize
      } else {
        return false
      }
    }
    const firstIntersectingEleIndex = () => {
      let index = 0
      for (let i = 0; i < inClassStudentList.value.length; i++) {
        if (inClassStudentList.value[i].isIntersecting) {
          index = i
          break
        }
      }
      return index
    }

    const pageChange = dir => {
      const pageSize = getPageSize()
      const currentIndex = firstIntersectingEleIndex()
      const direction = dir == 'pre' ? -1 : 1
      let scrollToEleIndex = currentIndex + direction * pageSize

      if (dir == 'pre' && scrollToEleIndex < 0) {
        scrollToEleIndex = 0
      }
      if (dir == 'next' && scrollToEleIndex > inClassStudentList.value.length - 1) {
        scrollToEleIndex = inClassStudentList.value.length - 1
      }
      const userId = inClassStudentList.value[scrollToEleIndex].userId
      const pageToEle = document.getElementById(`remote-observe-${userId}`)
      bs.scrollToElement(pageToEle)
      showArrowHandle()
    }
    const arrowShow = reactive({
      pre: false,
      next: false
    })
    let isInVideoGroup = false
    const enterVideoGroup = () => {
      isInVideoGroup = true
      showArrowHandle()
    }
    const leaveVideoGroup = () => {
      isInVideoGroup = false
      hideArrowHandle()
    }
    const showArrowHandle = () => {
      const rect = refs.allRemoteBox.getBoundingClientRect()
      const boxRect = refs.showBox.getBoundingClientRect()
      arrowShow.pre = rect.left < boxRect.left
      arrowShow.next = rect.right > boxRect.right + 1
    }
    const hideArrowHandle = () => {
      arrowShow.pre = false
      arrowShow.next = false
    }
    const isFocusMode = ref(false)
    onMounted(() => {
      bs = new BScroll(refs.scroll, {
        scrollX: true,
        scrollY: false,
        mouseWheel: true,
        disableMouse: false,
        disableTouch: false,
        bounce: false,
        throttleTime: 300
      })
      bs.on('mousewheelMove', () => {
        showArrowHandle()
      })

      bus.$on('focus_mode', status => {
        isFocusMode.value = status
      })
    })
    onBeforeUnmount(() => {
      intersectionObserver && intersectionObserver.disconnect()
      bs && bs.destroy()
    })

    return {
      inClassStudentList,
      remoteAudioStatus,
      hideRemoteVideo,
      pageChange,
      arrowShow,

      canScroll,
      enterVideoGroup,
      leaveVideoGroup
    }
  }
})
</script>
