<template>
  <!-- 视频长条 -->
  <div class="flex gap-[2px]">
    <TeacherVideo></TeacherVideo>
    <LocalVideo></LocalVideo>
    <RemoteVideo v-if="!isParent || canShowOtherChild"></RemoteVideo>
  </div>
</template>

<script>
import { defineComponent, getCurrentInstance, computed, onMounted } from '@vue/composition-api'
import TeacherVideo from './TeacherVideo'
import LocalVideo from './LocalVideo'
import RemoteVideo from './RemoteVideo'

import { getCameraStatus, getMicrophoneStatus } from 'utils/mediaAccess'
import { setRtcStatus } from 'api/classroom/index'

export default defineComponent({
  name: 'VideoGroup',
  components: {
    TeacherVideo,
    LocalVideo,
    RemoteVideo
  },
  setup() {
    const {
      proxy: { thinkClass, $store: store, $bus: bus }
    } = getCurrentInstance()
    const commonOption = computed(() => store.state.smallClass.baseData.commonOption)
    const isParent = computed(() => commonOption.value.isParent)
    const canShowOtherChild = computed(() => store.state.smallClass.canShowOtherChild)

    bus.$on('liveQuit', () => {
      thinkClass.RtcService.unpublish()
      thinkClass.RtcService.leaveChannel()
    })

    const sendRtcStatus = async (options = {}) => {
      // 后端status接收参数状态
      // 1:推视频、2:推音频、3:推音视频、0:断流
      let status = -1
      const cameraStatus = await getCameraStatus()
      const microphoneStatus = await getMicrophoneStatus()

      if (cameraStatus) {
        status = 1
      }
      if (microphoneStatus) {
        status = 2
      }
      if (cameraStatus && microphoneStatus) {
        status = 3
      }
      if (status == -1) {
        return
      }
      let params = {
        planId: commonOption.value.planId,
        classId: commonOption.value.classId,
        status: status,
        micPermission: microphoneStatus ? 1 : 2,
        cameraPermission: cameraStatus ? 1 : 2,
        micIsOpen: microphoneStatus ? 1 : 2,
        cameraIsOpen: cameraStatus ? 1 : 2
      }

      if (typeof options.micIsOpen === 'boolean') {
        params['micIsOpen'] = options.micIsOpen ? 1 : 2
      }
      if (typeof options.displayVideo === 'boolean') {
        params['cameraIsOpen'] = options.displayVideo ? 1 : 2
      }

      await setRtcStatus(params)
    }

    bus.$on('rtcStatusChange', sendRtcStatus)
    onMounted(() => {
      sendRtcStatus()
    })
    return {
      canShowOtherChild,
      isParent
    }
  }
})
</script>
