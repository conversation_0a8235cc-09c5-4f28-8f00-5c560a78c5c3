<template>
  <div class="flex items-center">
    <div class="w-[0.24rem] h-[0.24rem] bg-contain z-10" :class="[levelClass]"></div>
    <span
      class="relative w-[0.35rem] h-[0.17rem] t-[0.04rem] pl-[0.1rem] left-[-0.08rem] rounded-r-[0.03rem] text-white leading-[0.17rem] bg-level-color text-[0.12rem]"
      >Lv{{ level }}</span
    >
  </div>
</template>

<script>
export default {
  props: {
    type: {
      type: String,
      default: 'small'
    },
    level: {
      type: Number,
      default: 0
    }
  },
  computed: {
    medalClass() {
      return `medal-${this.type}`
    },
    levelClass() {
      return `bg-level-img-${this.level}`
    }
  }
}
</script>

<style lang="scss" scoped>
@for $i from 1 through 7 {
  .bg-level-img-#{$i} {
    background-image: var(--level-#{$i}-img);
  }
  .bg-level-color {
    background-color: rgb(0, 0, 0, 0.5);
  }
}
</style>
