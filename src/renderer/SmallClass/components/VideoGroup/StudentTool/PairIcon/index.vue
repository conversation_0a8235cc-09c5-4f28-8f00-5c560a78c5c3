<template>
  <div class="flex absolute left-[0.05rem] top-[0.05rem] items-center pair-show">
    <span class="right-icon"></span>
    <span v-if="parsedCorrectCount != 'm'" class="plus-icon"></span>
    <div class="contain-animation" :class="{ 'count-animation': animationFlag }">
      <span
        v-for="(item, index) in parsedCorrectCount.toString().split('')"
        class="bg-contain z-10"
        :class="[correctCountClass(item)]"
        :key="index"
      ></span>
    </div>
  </div>
</template>

<script>
import { cloudConfigValueBySchoolCode } from 'utils/initConfig'
let correctCountMax = ''
cloudConfigValueBySchoolCode('correct_count_max').then(res => {
  correctCountMax = res
})
export default {
  data() {
    return {
      animationFlag: false,
      timer: null
    }
  },
  props: {
    type: {
      type: String,
      default: 'small'
    },
    correctCount: {
      type: Number,
      default: 0
    }
  },
  computed: {
    parsedCorrectCount() {
      if (
        correctCountMax &&
        !isNaN(Number(correctCountMax)) &&
        Number(correctCountMax) > 0 &&
        this.correctCount >= Number(correctCountMax)
      ) {
        return 'm' //-1显示max
      } else {
        return this.correctCount
      }
    }
  },
  watch: {
    parsedCorrectCount(newVal) {
      if (newVal) {
        this.animationFlag = true
        this.clearTimer()
        this.timer = setTimeout(() => {
          this.animationFlag = false
        }, 3300)
      }
    }
  },
  methods: {
    clearTimer() {
      // @log-ignore
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
    },
    correctCountClass(index) {
      // @log-ignore
      return `bg-count-img-${index}`
    }
  },
  beforeDestroy() {
    // @log-ignore
    this.clearTimer()
  }
}
</script>

<style lang="scss" scoped>
@keyframes zoom {
  0% {
    transform: scale(1);
  }
  60% {
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
.pair-show {
  display: inline-flex;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 9px;
  opacity: 0.7;
  border-image: linear-gradient(136deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 1 1;
  padding: 2px 5px 2px;
  .contain-animation {
    display: flex;
  }
  .bg-contain {
    display: inline-block;
  }
  .count-animation {
    animation: zoom 0.3s ease-in-out 3s 1;
  }
  .right-icon {
    background: url('./imgs/right.png') no-repeat;
    background-size: 14px 14px;
    width: 14px;
    height: 14px;
  }
  .plus-icon {
    background: url('./imgs/plus.png') no-repeat;
    background-size: 9px 10px;
    width: 9px;
    height: 10px;
    margin: 0 1px;
  }
}
@for $i from 0 through 9 {
  .bg-count-img-#{$i} {
    background-image: var(--count-#{$i}-img);
    background-repeat: no-repeat;
    height: 12px;
    width: 11px;
    background-position: center;
  }
}
.bg-count-img-m {
  background-image: var(--count-m-img);
  background-repeat: no-repeat;
  height: 12px;
  width: 34px;
  background-position: center;
  margin-left: 1px;
}
.class-onstage-layer {
  z-index: 100;
  .pair-show {
    border-radius: 15px;
    padding: 4px 7px 4px;
    .right-icon {
      background-size: 22px 22px;
      width: 22px;
      height: 22px;
    }
    .plus-icon {
      background-size: 15px 16px;
      width: 15px;
      height: 16px;
      margin: 0 2px;
    }
  }
  @for $i from 0 through 9 {
    .bg-count-img-#{$i} {
      height: 20px;
      width: 18px;
    }
  }
  .bg-count-img-m {
    height: 18px;
    width: 56px;
    margin-left: 3px;
  }
}
</style>
