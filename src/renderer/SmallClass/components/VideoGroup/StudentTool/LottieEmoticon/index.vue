<template>
  <div class="lottie-emoticon-icon">
    <lottie-player
      ref="emoji"
      mode="normal"
      autoplay
      :loop="loopLottie"
      @complete="onLottieComplete"
      :src="lottieUrl"
      :style="emoticonStyle"
    >
    </lottie-player>
  </div>
</template>

<script>
import '@lottiefiles/lottie-player'
export default {
  props: {
    // 表情类型
    // 2: lottie表情
    type: {
      type: Number,
      default: 2
    },
    // 表情宽度
    width: {
      type: Number,
      default: 70
    },
    // 表情高度
    height: {
      type: Number,
      default: 70
    },
    lottieUrl: {
      type: String,
      default: ''
    },
    loopLottie: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    emoticonStyle() {
      return {
        width: `${this.width / 100}rem`,
        height: `${this.height / 100}rem`
      }
    }
  },
  methods: {
    onLottieComplete() {
      this.$emit('animationComplete')
    }
  },
  watch: {
    lottieUrl: function(url) {
      if (url) {
        this.$nextTick(() => this.$refs.emoji.load(url))
      }
    }
  }
}
</script>
