<template>
  <div v-if="stuInfo" class="absolute top-0 left-0 z-10 w-full h-full">
    <LevelIcon
      v-if="stuInfo.prop.levelId != 0 && isAllOnStage"
      :levelId="stuInfo.prop.levelId"
      :subLevel="stuInfo.prop.subLevel"
    ></LevelIcon>
    <div v-show="stuInfo.correctCount && !stuInfo.multVideoLinkStatus">
      <PairIcon :correctCount="stuInfo.correctCount" />
    </div>
    <div class="w-full h-full flex justify-center" v-if="stuInfo.raiseHandStatus">
      <RaiseHandMessage
        :status="stuInfo.raiseHandStatus"
        @updateStatus="hideHand"
        :resource="stuInfo?.prop?.hand"
      />
    </div>
    <div
      :class="[
        'absolute w-full h-full inset-0 m-auto flex items-center justify-center z-10',
        stuInfo.multVideoLinkStatus ? '' : 'bg-black/40'
      ]"
      v-if="stuInfo.emoticonName"
    >
      <EmoticonMessage
        v-show="!stuInfo.multVideoLinkStatus"
        :willAutoClear="true"
        :name="stuInfo.emoticonName"
        :type="stuInfo.emoticonType"
        :emojiId="stuInfo.emojiId"
        :width="80"
        :height="80"
        :lottieUrl="stuInfo.lottieUrl"
        @clearEmoticon="handleClearEmoticon"
      />
    </div>
  </div>
</template>
<script>
import { defineComponent, computed, getCurrentInstance } from '@vue/composition-api'
import PairIcon from './PairIcon/index.vue'
import RaiseHandMessage from './RaiseHandMessage'
import EmoticonMessage from './EmoticonMessage/index.vue'
import LevelIcon from './LevelIcon/index.vue'

export default defineComponent({
  name: 'StudentTool',
  components: {
    PairIcon,
    RaiseHandMessage,
    EmoticonMessage,
    LevelIcon
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    isAllOnStage: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const {
      proxy: { $store: store }
    } = getCurrentInstance()
    const studentList = computed(() => store.state.smallClass.studentList)
    const stuInfo = computed(() => {
      // @log-ignore
      return studentList.value.find(item => item.userId == props.id)
    })
    // 更新学生列表
    const updateStudentList = (data = {}) => {
      store.dispatch('smallClass/updateStudentList', {
        uid: props.id,
        data
      })
    }
    const hideHand = () => {
      updateStudentList({ raiseHandStatus: false })
    }
    /**
     * 清空表情名称
     */
    const handleClearEmoticon = () => {
      let emojiObj = {
        emoticonName: '',
        emoticonType: 1,
        lottieUrl: '',
        emojiId: ''
      }
      updateStudentList(emojiObj)
    }
    return {
      stuInfo,
      hideHand,
      handleClearEmoticon
    }
  }
})
</script>
