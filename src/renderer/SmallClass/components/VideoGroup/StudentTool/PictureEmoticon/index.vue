<template>
  <img
    class="mb-[0.04rem]"
    @mouseover="handleMouseover"
    @mouseleave="handleMouseleave"
    @click.stop="handleClick"
    :src="lottieUrl"
    :style="emoticonStyle"
  />
</template>

<script>
export default {
  props: {
    // 表情类型
    // 1: 本地图片表情
    type: {
      type: Number,
      default: 3
    },
    // 表情符号名称
    // 例: [smile]
    name: {
      type: String,
      default: ''
    },
    // 表情宽度
    width: {
      type: Number,
      default: 30
    },
    // 表情高度
    height: {
      type: Number,
      default: 30
    },
    // hover宽度
    hoverWidth: {
      type: Number,
      default: 34
    },
    // hover高度
    hoverHeight: {
      type: Number,
      default: 34
    },
    // 是否开启hover效果
    enableHover: {
      type: Boolean,
      default: false
    },
    lottieUrl: {
      type: String,
      default: ''
    }
  },
  computed: {
    emoticonStyle() {
      return {
        width: `${this.width / 100}rem`,
        height: `${this.height / 100}rem`
      }
    },
    emotionHoverStyle() {
      return {
        width: `${this.hoverWidth / 100}rem`,
        height: `${this.hoverHeight / 100}rem`
      }
    }
  },
  data() {
    return {
      hoverStatus: false
    }
  },
  methods: {
    handleMouseover() {
      if (!this.enableHover) return
      this.hoverStatus = true
    },
    handleMouseleave() {
      if (!this.enableHover) return
      this.hoverStatus = false
    },
    handleClick() {
      this.$emit('handleClick', {
        type: this.type,
        name: this.name
      })
    }
  }
}
</script>
