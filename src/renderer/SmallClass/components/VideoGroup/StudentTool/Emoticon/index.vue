<template>
  <div
    class="emoticon-icon"
    :style="hoverStatus ? emotionHoverStyle : emoticonStyle"
    :class="[emoticonClass]"
    @mouseover="handleMouseover"
    @mouseleave="handleMouseleave"
    @click.stop="handleClick"
  ></div>
</template>

<script>
import { emoticonConfig } from './config'

export default {
  props: {
    // 表情类型
    // 1: 本地图片表情
    type: {
      type: Number,
      default: 1
    },
    // 表情符号名称
    // 例: [smile]
    name: {
      type: String,
      default: ''
    },
    // 表情宽度
    width: {
      type: Number,
      default: 30
    },
    // 表情高度
    height: {
      type: Number,
      default: 30
    },
    // hover宽度
    hoverWidth: {
      type: Number,
      default: 34
    },
    // hover高度
    hoverHeight: {
      type: Number,
      default: 34
    },
    // 是否开启hover效果
    enableHover: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    emoticonInfo() {
      // @log-ignore
      return this.emoticonConfig[this.name] || {}
    },
    emoticonStyle() {
      // @log-ignore
      return {
        width: `${this.width / 100}rem`,
        height: `${this.height / 100}rem`
      }
    },
    emotionHoverStyle() {
      return {
        width: `${this.hoverWidth / 100}rem`,
        height: `${this.hoverHeight / 100}rem`
      }
    },
    emoticonClass() {
      // @log-ignore
      if (!this.emoticonInfo.localImageName) {
        return ''
      }
      // 本地图片表情类型
      if (this.type == 1) {
        return `emoticon-icon__${this.emoticonInfo.localImageName}`
      }
      return ''
    }
  },
  data() {
    return {
      emoticonConfig,
      hoverStatus: false
    }
  },
  methods: {
    handleMouseover() {
      if (!this.enableHover) return
      this.hoverStatus = true
    },
    handleMouseleave() {
      if (!this.enableHover) return
      this.hoverStatus = false
    },
    handleClick() {
      this.$emit('handleClick', {
        type: this.type,
        name: this.name
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.emoticon-icon {
  width: 0.3rem;
  height: 0.3rem;
  background-size: cover;
  transition: all 0.2s;
  cursor: pointer;
  &__e_smile {
    background-image: url('./images/e_smile.png');
  }
  &__e_pleading {
    background-image: url('./images/e_pleading.png');
  }
  &__e_xd {
    background-image: url('./images/e_xd.png');
  }
  &__e_lol {
    background-image: url('./images/e_lol.png');
  }
  &__e_thinking {
    background-image: url('./images/e_thinking.png');
  }
  &__e_the_rock {
    background-image: url('./images/e_the_rock.png');
  }
  &__e_party {
    background-image: url('./images/e_party.png');
  }
  &__e_quiet {
    background-image: url('./images/e_quiet.png');
  }
  &__e_sad {
    background-image: url('./images/e_sad.png');
  }
  &__e_shame {
    background-image: url('./images/e_shame.png');
  }
  &__e_scream {
    background-image: url('./images/e_scream.png');
  }
  &__e_cry {
    background-image: url('./images/e_cry.png');
  }
  &__e_a {
    background-image: url('./images/e_a.png');
  }
  &__e_b {
    background-image: url('./images/e_b.png');
  }
  &__e_true {
    background-image: url('./images/e_true.png');
  }
  &__e_false {
    background-image: url('./images/e_false.png');
  }
  &__e_surprise {
    background-image: url('./images/e_surprise.png');
  }
  &__e_heart {
    background-image: url('./images/e_heart.png');
  }
  &__e_unicorn {
    background-image: url('./images/e_unicorn.png');
  }
  &__e_ghost {
    background-image: url('./images/e_ghost.png');
  }
  &__e_yeah {
    background-image: url('./images/e_yeah.png');
  }
  &__e_like {
    background-image: url('./images/e_like.png');
  }
  &__e_hands_up {
    background-image: url('./images/e_hands_up.png');
  }
  &__e_clap {
    background-image: url('./images/e_clap.png');
  }
}
</style>
