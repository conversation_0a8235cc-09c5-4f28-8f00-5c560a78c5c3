<template>
  <div>
    <Emoticon
      v-if="show && type == 1"
      :name="emoticonName"
      :type="type"
      :width="width"
      :height="height"
    />
    <LottieEmoticon
      v-if="show && type == 2"
      :name="emoticonName"
      :type="type"
      :lottieUrl="lottieUrl"
      :emojiId="emojiId"
      :width="width"
      :height="height"
      @animationComplete="animationComplete"
      :loopLottie="loopLottie"
    />
    <PictureEmoticon
      v-if="show && type == 3"
      :name="emoticonName"
      :type="type"
      :lottieUrl="lottieUrl"
      :emojiId="emojiId"
      :width="width"
      :height="height"
    />
  </div>
</template>

<script>
import Emoticon from '../Emoticon'
import LottieEmoticon from '../LottieEmoticon'
import PictureEmoticon from '../PictureEmoticon'
export default {
  components: {
    Emoticon,
    LottieEmoticon,
    PictureEmoticon
  },
  props: {
    userId: {
      type: String,
      default: ''
    },
    name: {
      type: String,
      default: ''
    },
    type: {
      type: Number,
      default: 1
    },
    loopLottie: {
      type: Boolean,
      default: false
    },
    width: {
      type: Number,
      default: 50
    },
    height: {
      type: Number,
      default: 50
    },
    duration: {
      type: Number,
      default: 3
    },
    lottieUrl: {
      type: String,
      default: ''
    },
    emojiId: {
      type: Number,
      default: 0
    },
    willAutoClear: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      emoticonName: '',
      timer: null
    }
  },
  computed: {
    show() {
      return !!this.emoticonName
    }
  },
  methods: {
    animationComplete() {
      if (this.willAutoClear) {
        this.emoticonName = ''
        this.$emit('clearEmoticon', this.userId)
      }
    },
    clearTimer() {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
    }
  },
  watch: {
    name: {
      immediate: true,
      handler(newVal) {
        if (!newVal) {
          return
        }
        this.emoticonName = newVal
        this.clearTimer()
        // 处理图片类型表情 图片表情需要 根据传入时间自动消失
        if (this.type === 1 || this.type === 3) {
          this.timer = setTimeout(() => {
            if (this.willAutoClear) {
              this.emoticonName = ''
              this.$emit('clearEmoticon', this.userId)
            }
          }, this.duration * 1000)
        }
      }
    }
  },
  beforeDestroy() {
    this.clearTimer()
  }
}
</script>
