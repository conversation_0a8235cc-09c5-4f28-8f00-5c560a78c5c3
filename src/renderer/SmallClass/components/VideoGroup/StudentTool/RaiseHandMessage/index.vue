<template>
  <div
    v-if="status"
    class="h-[1.17rem] bg-raise-hand-img bg-no-repeat bg-cover absolute bottom-0 raise-enter-active"
  >
    <img class="h-[1.17rem]" :src="resource?.resourceUrl || defaultAvatar" />
  </div>
</template>

<script>
export default {
  props: {
    status: {
      type: Boolean,
      default: false
    },
    userId: {
      type: String,
      default: ''
    },
    duration: {
      type: Number,
      default: 10
    },
    resource: {
      type: Object,
      default: () => {
        return {
          resourceId: '',
          resourceUrl: ''
        }
      }
    }
  },
  computed: {},
  data() {
    return {
      timer: null,
      defaultAvatar: require('./images/icon-raise-hand.png')
    }
  },
  watch: {
    status: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.clearTimer()
          this.timer = setTimeout(() => {
            this.$emit('updateStatus', false, this.userId)
          }, this.duration * 1000)
        }
      }
    }
  },
  methods: {
    clearTimer() {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
    }
  },
  beforeDestroy() {
    this.clearTimer()
  }
}
</script>
<style scoped>
@keyframes axisY {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
.raise-enter-active {
  animation: axisY 0.3s cubic-bezier(0, 0, 0.5, 1.5) forwards;
}
</style>
