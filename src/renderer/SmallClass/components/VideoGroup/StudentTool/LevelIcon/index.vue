<template>
  <div
    class="group flex absolute right-[0.05rem] top-[0.05rem] transition-all duration-[0.5s] ease-linear	 hover:right-[0.76rem] cursor-pointer"
  >
    <span
      class="absolute text-[0.14rem] flex items-center text-bold overflow-hidden left-[0.2rem] w-[0] h-[0.28rem] bg-black transition-width  ease-linear group-hover:w-[0.8rem]"
      :class="classNames"
    >
      <span class="relative left-[0.21rem] whitespace-nowrap"
        >{{ levelText }} <span v-if="levelId != 4">{{ subLevelText }}</span></span
      >
    </span>
    <img :src="levelImg" class="w-[0.28rem] h-[0.28rem]" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      levelTextMap: {
        1: this.$t('classroom.chats.chats.level.wood'),
        2: this.$t('classroom.chats.chats.level.silver'),
        3: this.$t('classroom.chats.chats.level.gold'),
        4: this.$t('classroom.chats.chats.level.diamond')
      },
      subLevelTextMap: {
        1: 'Ⅰ',
        2: 'Ⅱ',
        3: 'Ⅲ',
        4: 'Ⅳ'
      },
      classMap: {
        1: 'level-wood',
        2: 'level-silver',
        3: 'level-gold',
        4: 'level-diamond'
      }
    }
  },
  computed: {
    levelText() {
      return this.levelTextMap[this.levelId]
    },
    subLevelText() {
      return this.subLevelTextMap[this.subLevel]
    },
    classNames() {
      return this.classMap[this.levelId]
    },
    levelImg() {
      if (this.levelId == 1) {
        return require(`../../../../imgs/level/wood-${this.subLevel}.png`)
      } else if (this.levelId == 2) {
        return require(`../../../../imgs/level/silver-${this.subLevel}.png`)
      } else if (this.levelId == 3) {
        return require(`../../../../imgs/level/gold-${this.subLevel}.png`)
      } else if (this.levelId == 4) {
        return require(`../../../../imgs/level/diamond.png`)
      } else {
        return require(`../../../../imgs/level/wood-1.png`)
      }
    }
  },
  props: {
    levelId: {
      type: Number,
      default: 0
    },
    subLevel: {
      type: Number,
      default: 0
    }
  }
}
</script>
<style lang="scss" scoped>
.level-wood {
  color: #956335;
  background: url('../../../../imgs/level/woodText.png') no-repeat;
  background-size: 100% 100%;
}
.level-silver {
  color: #5296dc;
  background: url('../../../../imgs/level/silverText.png') no-repeat;
  background-size: 100% 100%;
}
.level-gold {
  color: #f89701;
  background: url('../../../../imgs/level/goldText.png') no-repeat;
  background-size: 100% 100%;
}
.level-diamond {
  color: #fff;
  background: url('../../../../imgs/level/diamondText.png') no-repeat;
  background-size: 100% 100%;
}
</style>
