<template>
  <div class="msg-level" :class="outClass">
    <div class="msg-level-content" :class="contentClass">
      <img :src="levelImg" />
      <span>{{ levelText }}</span>
      <span v-if="levelId != 4" class="ml-[4px]">{{ subLevelText }}</span>
    </div>
  </div>
</template>
<script>
export default {
  name: 'Level',
  data() {
    return {
      levelTextMap: {
        1: this.$t('classroom.chats.chats.level.wood'),
        2: this.$t('classroom.chats.chats.level.silver'),
        3: this.$t('classroom.chats.chats.level.gold'),
        4: this.$t('classroom.chats.chats.level.diamond')
      },
      subLevelTextMap: {
        1: 'Ⅰ',
        2: 'Ⅱ',
        3: 'Ⅲ',
        4: 'Ⅳ'
      },
      outClassMap: {
        1: 'level-wood-out',
        2: 'level-silver-out',
        3: 'level-gold-out',
        4: 'level-diamond-out'
      },
      contentClassMap: {
        1: 'level-wood-content',
        2: 'level-silver-content',
        3: 'level-gold-content',
        4: 'level-diamond-content'
      }
    }
  },
  props: {
    levelId: {
      type: Number,
      default: 0
    },
    subLevel: {
      type: Number,
      default: 0
    }
  },
  computed: {
    levelText() {
      // @log-ignore
      return this.levelTextMap[this.levelId]
    },
    subLevelText() {
      // @log-ignore
      return this.subLevelTextMap[this.subLevel]
    },
    levelImg() {
      // @log-ignore
      if (this.levelId == 1) {
        return require(`../../imgs/level/wood-${this.subLevel}.png`)
      } else if (this.levelId == 2) {
        return require(`../../imgs/level/silver-${this.subLevel}.png`)
      } else if (this.levelId == 3) {
        return require(`../../imgs/level/gold-${this.subLevel}.png`)
      } else if (this.levelId == 4) {
        return require(`../../imgs/level/diamond.png`)
      } else {
        return require(`../../imgs/level/wood-1.png`)
      }
    },
    outClass() {
      // @log-ignore
      return this.outClassMap[this.levelId]
    },
    contentClass() {
      // @log-ignore
      return this.contentClassMap[this.levelId]
    }
  }
}
</script>
<style lang="scss" scoped>
.msg-level {
  position: relative;
  border-radius: 0px 2px 2px 0px;
  padding: 1px;
  .msg-level-content {
    border-radius: 0px 2px 2px 0px;
    padding: 0px 3px 1px 8px;
    font-size: 12px;
    line-height: 12px;
  }
  img {
    width: 16px;
    height: 16px;
    left: -7px;
    position: absolute;
    transform: translateY(-50%);
    top: 50%;
  }
}
.level-wood-out {
  background: #c88e57;
  .level-wood-content {
    background: #e5ae7c;
    color: #936133;
  }
}
.level-silver-out {
  background: #96c6ff;
  .level-silver-content {
    background: #5296dc;
    color: #fff;
  }
}
.level-gold-out {
  background: #fec701;
  .level-gold-content {
    background: #fee333;
    color: #f89701;
  }
}
.level-diamond-out {
  background: linear-gradient(140deg, rgba(218, 155, 255, 1), rgba(184, 93, 238, 1));
  .level-diamond-content {
    background: linear-gradient(159deg, #ad34d3 0%, #e125df 100%);
    color: #fff;
  }
}
</style>
