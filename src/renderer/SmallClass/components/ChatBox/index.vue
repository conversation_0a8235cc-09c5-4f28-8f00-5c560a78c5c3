<template>
  <div
    v-show="chatBox.isShowBox"
    class="chat-box-container "
    :class="{ 'on-wholeOnsatge': isWholeOnsatge, playBack: isPlayBack }"
    v-drag="{
      dragWrapSelector: '.full-float-container',
      dragSelector: '.chat-box-head',
      draggable: !isWholeOnsatge
    }"
  >
    <div class="chat-box-head" v-if="showHeader">
      <div class="title">{{ $t('classroom.chats.chats.chatbox') }}</div>
      <div class="close" @click="handleIsShowBox">
        <img src="./imgs/icon_close.png" alt="" />
      </div>
    </div>
    <div class="chat-box-content" id="chat-list">
      <ul class="chat-ul">
        <template v-for="(item, index) in chatList">
          <li v-if="item.role === 't' || item.role === 'f'" class="left-li" :key="index">
            <div class="avatarBox">
              <div
                v-if="item.userInfo.avatarFrame"
                class="avatar-box"
                :style="'background: url(' + item.userInfo.avatarFrame + ') no-repeat center'"
              ></div>
              <img :src="item.content.path" />
            </div>
            <div class="msgBox">
              <div class="msg-header">
                <div class="nickname">{{ item.content.name }}</div>
                <div v-if="item.content.to_uid" class="msg-private">
                  【{{ $t('classroom.chats.chats.privateMessage') }}】
                </div>
              </div>
              <div class="msg-box msg-box-teacher" :class="{ 'msg-box-f': item.role === 'f' }">
                {{ item.content.msg }}
              </div>
            </div>
          </li>

          <li
            v-if="item.role === 's' && item.userId != baseData.stuInfo.id"
            class="left-li"
            :key="index"
          >
            <div class="avatarBox">
              <div
                v-if="item.userInfo.avatarFrame"
                class="avatar-box"
                :style="'background: url(' + item.userInfo.avatarFrame + ') no-repeat center'"
              ></div>
              <img :src="item.content.path" />
            </div>
            <div class="msgBox">
              <div class="msg-header">
                <Level
                  v-if="item?.userInfo?.levelId"
                  :levelId="item.userInfo.levelId"
                  :subLevel="item.userInfo.subLevel"
                  class="ml-12"
                ></Level>
                <div class="nickname">
                  {{ item.content.name }}
                </div>
              </div>
              <div
                v-if="!item.content.isNewEmoji"
                class="msg-box"
                :class="{ 'box-back': item.userInfo.messageBoxFrame }"
                :style="
                  item.userInfo.messageBoxFrame
                    ? `border-image-source: url(${item.userInfo.messageBoxFrame});border-image-width: 20px 29px`
                    : ''
                "
              >
                {{ item.content.msg }}
              </div>
              <EmoticonMessage
                v-else
                :willAutoClear="false"
                :name="item.content.msg"
                :type="item.content.emojiType"
                :emojiId="item.content.emojiId"
                :width="40"
                :height="40"
                :lottieUrl="item.content.lottieUrl"
                :loopLottie="true"
                style="margin-left: 5px"
              />
            </div>
          </li>
          <li v-if="item.userId == baseData.stuInfo.id" class="right-li" :key="index">
            <div class="msgBox">
              <div class="msg-header">
                <div
                  v-if="item.content.to_uid && item.content.to_uid === baseData.teacherInfo.id"
                  class="msg-private"
                >
                  【{{ $t('classroom.chats.chats.To') }}
                  {{ $t('classroom.chats.chats.sendToTeacher') }}】
                </div>
                <Level
                  v-if="item?.userInfo?.levelId"
                  :levelId="item.userInfo.levelId"
                  :subLevel="item.userInfo.subLevel"
                ></Level>
                <div class="nickname">{{ item.content.name }}</div>
              </div>
              <div class="msg-wrap">
                <div @click="handleReSend(item)" v-if="item.status === 2" class="msg-error">
                  <img src="./imgs/icon_error.png" />
                </div>
                <div
                  v-if="!item.content.isNewEmoji"
                  class="msg-box msg-box-me"
                  :class="{ 'box-back': item.userInfo.messageBoxFrame }"
                  :style="
                    item.userInfo.messageBoxFrame
                      ? `border-image-source: url(${item.userInfo.messageBoxFrame});border-image-width: 20px 29px`
                      : ''
                  "
                >
                  {{ item.content.msg }}
                </div>
                <EmoticonMessage
                  v-else
                  :willAutoClear="false"
                  :name="item.content.msg"
                  :type="item.content.emojiType"
                  :emojiId="item.content.emojiId"
                  :width="40"
                  :height="40"
                  :lottieUrl="item.content.lottieUrl"
                  :loopLottie="true"
                />
              </div>
            </div>
            <div class="avatarBox">
              <div
                v-if="item.userInfo.avatarFrame"
                class="avatar-box"
                :style="'background: url(' + item.userInfo.avatarFrame + ') no-repeat center'"
              ></div>
              <img :src="item.content.path" />
            </div>
          </li>
          <li v-if="item.role === 'tip'" class="tip" :key="index">
            {{ $t(item.msg) }}
          </li>
          <li v-if="item.role === 'enterTip'" class="enterTipBox" :key="index">
            <div class="enterTip">
              <Level
                class="tipLevel"
                v-if="item.userInfo.levelId"
                :levelId="item.userInfo.levelId"
                :subLevel="item.userInfo.subLevel"
              ></Level>
              <span class="tipName">{{ item.userInfo.name }}&nbsp;</span>
              {{ $t(item.msg) }}
            </div>
          </li>
        </template>
      </ul>
    </div>
    <div class="chat-box-foot">
      <div class="new-meg-tip" v-if="isShowNewMsgTip" @click="handleScrollBottom">
        <i class="icon-more-bottom"></i>
      </div>
      <div v-if="isFrequentlyShow" class="too-frequently">
        {{ $t('classroom.chats.chats.frequently') }}
      </div>
      <div id="memberList" class="memberList" v-show="sendToReactive.isOpen">
        <ul>
          <li
            class="member-li"
            v-for="item in sendToList"
            :key="item.id"
            @click="changeSendToValue(item)"
            :data-log="`选中发送对象${$t(item.name)}`"
          >
            <span
              class="member-name"
              :class="{ 'member-changed': sendToReactive.id === item.id }"
              >{{ $t(item.name) }}</span
            >
            <span class="member-icon" v-if="sendToReactive.id === item.id"></span>
          </li>
        </ul>
      </div>
      <div class="send-to" v-if="isShowFoot">
        {{ $t('classroom.chats.chats.sendTo') }}:
        <div
          class="send-to-click"
          @click="toggleMemberPopup"
          :class="{
            'un-send-to-click':
              isConnecting || !chatBox.isOpenChat || chatBox.isOnlyTeacher || chatBox.isMuted
          }"
        >
          <span
            class="send-to-name"
            :class="{
              'un-send-to-name':
                isConnecting || !chatBox.isOpenChat || chatBox.isOnlyTeacher || chatBox.isMuted
            }"
            >{{ $t(sendToReactive.name) }}</span
          >
          <div
            v-if="chatBox.isOpenChat && !chatBox.isOnlyTeacher && !chatBox.isMuted"
            class="send-to-icon"
          ></div>
        </div>
      </div>
      <div
        v-if="isShowFoot && chatBox.isOpenChat && !isConnecting && !chatBox.isMuted"
        class="input-box"
      >
        <a-textarea
          class="text-area"
          v-model="messageText"
          :maxLength="200"
          :auto-size="{ minRows: 1, maxRows: 3 }"
          :placeholder="$t('classroom.chats.chats.inputPlaceholder')"
          @pressEnter="handleSendMsg"
        />
        <div v-if="isCanSend" class="send-btn" @click="handleSendMsg">
          <img src="./imgs/icon_send.png" />
        </div>
        <div v-else class="send-btn no-send">
          <img src="./imgs/icon_send_forbidden.png" />
        </div>
      </div>
      <div v-if="!chatBox.isOpenChat" class="input-close">
        <div class="input-close-content">
          {{ $t('classroom.chats.chats.chatboxClosed') }}
        </div>
      </div>
      <div v-else-if="chatBox.isMuted" class="input-close">
        <div class="input-close-content">
          {{ $t('classroom.chats.chats.underMute') }}
        </div>
      </div>
      <div v-if="isShowFoot && isConnecting" class="input-close">
        <div class="input-close-content">
          {{ $t('classroom.chats.chats.connecting') }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Level from './Level'
import { mapGetters } from 'vuex'
import logger from 'utils/logger'
import EmoticonMessage from 'components/Common/EmoticonMessage'
import Clickoutside from 'utils/clickoutside'
import { getCloudConfig } from 'utils/initConfig'
import drag from '@thinkacademy/vitas-utils/drag'
const MAX_CHATLIST_LENGTH = 300 // 聊天消息最大展示条数
import * as classLiveSensor from '@/utils/sensorTrack/classLive'

// 发送消息的状态
const SEND_MSG_STATUS = {
  PENDING: 0,
  SUCCESS: 1,
  ERROR: 2
}
const defaultAvatar = require('./imgs/avatar_default.png')
export default {
  components: {
    EmoticonMessage,
    Level
  },
  props: {
    showHeader: {
      default: true,
      type: Boolean
    },
    isWholeOnsatge: {
      default: false,
      type: Boolean
    },
    isShowFoot: {
      default: true,
      type: Boolean
    },
    isPlayBack: {
      default: false,
      type: Boolean
    }
  },
  data() {
    return {
      isShowNewMsgTip: false,
      messageText: '',
      chatList: [],
      isFrequently: false,
      isFrequentlyShow: false,
      isConnecting: true,
      chatMsgPriority: 99,
      sendToReactive: {
        id: '',
        name: 'classroom.chats.chats.sendToAll',
        isOpen: false
      },
      dragEvent: null,
      singleChat: [],
      limitNum: 5, // 消息频率 5s 5条
      limitTime: 5000,
      waitTime: 3000
    }
  },
  directives: {
    Clickoutside,
    drag
  },
  computed: {
    ...mapGetters({
      baseData: 'smallClass/baseData',
      chatBox: 'smallClass/chatBox',
      isShowBox: 'smallClass/isShowBox',
      ircStatus: 'smallClass/ircStatus',
      studentList: 'smallClass/studentList'
    }),
    isCanSend() {
      return this.messageText.trim()
    },
    sendToList() {
      console.log('baseData', this.baseData)
      return [
        { id: '', name: 'classroom.chats.chats.sendToAll' },
        { id: this.baseData.teacherInfo.id, name: 'classroom.chats.chats.sendToTeacher' }
      ]
    }
  },
  watch: {
    ircStatus: {
      handler: function(value) {
        if (value) {
          this.isConnecting = false
        }
      },
      immediate: true
    },
    isShowBox: {
      handler: function(value) {
        if (value) {
          this.handleScrollBottom()
          this.$store.dispatch('smallClass/updateChatboxHasNewMsg', false)
        }
      }
    }
  },
  created() {
    if (!this.isPlayBack) {
      // 网络情况
      this.$bus.$on('chatboxNetStatusChanged', this.onNetStatusChanged)
      // 发出消息回调
      this.$bus.$on('chatGroupMsgRes', this.onSendRoomMessageResponse)
      // 获取历史消息
      this.$bus.$on('chatHistoryMsg', this.onGetRoomHistoryMessageResponse)

      // 监听实时消息
      this.$bus.$on('chatboxMsg', this.onRecvRoomMessage)
      // 监听kv
      this.thinkClass.SignalService.on('onJoinRoomNotice', this.onJoinRoomNotice)
      this.$bus.$on('openchat_chatbox', this.onOpenChatHandle)
      this.$bus.$on('chat_msg_control_chatbox', this.onChatMsgControlHandle)
      this.$bus.$on('private_mute_chat_chatbox', this.onPrivateMutChatHandle)
    }
    document.addEventListener('click', this.handleDocumentClick)
  },
  mounted() {
    // 获取云控配置信息-聊天频率
    const cloudConfig = getCloudConfig()
    if (cloudConfig && cloudConfig.configs) {
      for (let i = 0; i < cloudConfig.configs.length; i++) {
        const element = cloudConfig.configs[i]
        if (element.configKey === 'frequentlyObj') {
          const frequentlyArr = element.configDefaultValue.split(',')
          this.limitNum = frequentlyArr[0] || 5
          this.limitTime = frequentlyArr[1] || 5000
          this.waitTime = frequentlyArr[2] || 3000
          break
        }
      }
    }
    console.log(
      'cloudConfig cloudConfig',
      this.limitNum,
      this.limitTime,
      this.waitTime,
      cloudConfig
    )
    if (this.chatBox.historyList.length) {
      // 获取历史消息
      this.chatList = [...this.chatBox.historyList]
      this.handleScrollBottom()
    }
  },
  // vue2 没有unmounted
  beforeDestroy() {
    if (!this.isPlayBack) {
      // 解除监听
      this.thinkClass.SignalService.off('onJoinRoomNotice', this.onJoinRoomNotice)
      this.$bus.$off('chatboxNetStatusChanged', this.onNetStatusChanged)
      // 发出消息回调
      this.$bus.$off('chatGroupMsgRes', this.onSendRoomMessageResponse)
      // 获取历史消息
      this.$bus.$off('chatHistoryMsg', this.onGetRoomHistoryMessageResponse)

      // 监听实时消息
      this.$bus.$off('chatboxMsg', this.onRecvRoomMessage)
      // 监听kv
      this.$bus.$off('openchat_chatbox', this.onOpenChatHandle)
      this.$bus.$off('chat_msg_control_chatbox', this.onChatMsgControlHandle)
      this.$bus.$off('private_mute_chat_chatbox', this.onPrivateMutChatHandle)
      // 当从全员上台进入到课中的时候,要把全员上台时的isShowBox 改成 false,否则进入课中的时候chatbox是显示状态
      this.$store.dispatch('smallClass/updateChatboxIsShowBox', false)
      const chatListDom = document.getElementById('chat-list')
      chatListDom && chatListDom.removeEventListener('scroll', this.handleOnScroll)
    } else {
      this.$store.dispatch('smallClass/updateChatboxIsShowBox', false)
    }
    document.removeEventListener('click', this.handleDocumentClick)
  },
  methods: {
    handleIsShowBox() {
      if (this.isPlayBack) {
        this.$store.dispatch('smallClass/updateChatboxIsShowBox', true)
        return
      }
      this.$store.dispatch('smallClass/updateChatboxIsShowBox', !this.chatBox.isShowBox)
      this.messageText = ''
      if (this.chatBox.isShowBox) {
        this.handleScrollBottom()
        this.$store.dispatch('smallClass/updateChatboxHasNewMsg', false)
      }
    },
    // 发送msg
    // 限制3秒fa
    handleSendMsg(e) {
      e.preventDefault()
      if (!this.messageText.trim()) return
      if (this.isFrequently) {
        this.isFrequentlyShow = true
        classLiveSensor.osta_cb_input_cooling()
        return
      } else {
        this.isFrequentlyShow = false
      }
      // 判断是否控制发言频率
      const isFrequently = this.handleFrequentlyFn()
      if (isFrequently) {
        this.isFrequently = true
        this.isFrequentlyShow = true
        classLiveSensor.osta_cb_input_cooling()
        setTimeout(() => {
          this.isFrequently = false
          this.isFrequentlyShow = false
        }, this.waitTime)
        return
      }
      const content = {
        type: '130', //类型
        from: 'flv',
        name: this.baseData.stuInfo.nickName,
        msg: this.messageText,
        path: this.baseData.stuInfo.avatar
      }
      if (this.sendToReactive.id) {
        content.type = '139'
        content.to_uid = this.sendToReactive.id
      }
      if (this.chatBox.isOnlyTeacher) {
        content.type = '139'
        content.to_uid = this.baseData.teacherInfo.id
      }

      const res = this.thinkClass.SignalService.sendRoomMessageWithPreMsgId({
        content: content,
        chatMsgPriority: this.chatMsgPriority
      })
      if (res.code === 0) {
        this.chatList.push({
          role: 's',
          content: content,
          status: SEND_MSG_STATUS.PENDING,
          preMsgId: res.preMsgId,
          userId: this.baseData.stuInfo.id,
          userInfo: {
            levelId: this.baseData.stuInfo?.prop?.levelId,
            subLevel: this.baseData.stuInfo?.prop?.subLevel,
            name: this.baseData.stuInfo.nickName,
            avatarFrame: this.baseData.stuInfo?.prop?.avatarFrame?.resourceUrl,
            messageBoxFrame: this.baseData.stuInfo?.prop?.messageBoxFrame?.resourceUrl
          }
        })
        this.singleChat.push(+new Date())
        this.messageText = ''
        this.handleScrollBottom()
        this.sendLogger('学生发送chatbox消息', {
          ...content
        })
        classLiveSensor.osta_cb_send_msg(content)
      } else {
        this.sendLogger(
          '学生发送chatbox消息失败',
          {
            ...res
          },
          'error'
        )
      }
    },
    handleFrequentlyFn() {
      if (
        this.singleChat.length >= this.limitNum &&
        +new Date() - this.singleChat[this.singleChat.length - this.limitNum] < this.limitTime
      ) {
        return true
      } else {
        return false
      }
    },
    // 监听 发送消息回调
    onSendRoomMessageResponse(res) {
      // @log-ignore
      if (res.code !== 0) {
        this.sendLogger(
          '学生发送聊天消息回调失败',
          {
            res
          },
          'error'
        )
      }
      for (let i = this.chatList.length - 1; i > 0; i--) {
        const element = this.chatList[i]
        if (element.preMsgId && element.preMsgId === res.preMsgId) {
          const status = res.code === 0 ? SEND_MSG_STATUS.SUCCESS : SEND_MSG_STATUS.ERROR
          element.status = status

          if (res.code === 0) {
            //成功之后 这里更新store里的historyList
            this.$store.dispatch('smallClass/updateChatBoxHistoryList', element)
          }
          return
        }
      }
    },
    // 消息发送失败重发
    handleReSend(item) {
      const { content } = item
      const res = this.thinkClass.SignalService.sendRoomMessageWithPreMsgId({
        content,
        chatMsgPriority: this.chatMsgPriority
      })
      if (res.code === 0) {
        for (let i = this.chatList.length - 1; i > 0; i--) {
          const element = this.chatList[i]
          if (element.preMsgId && element.preMsgId === item.preMsgId) {
            element.status = SEND_MSG_STATUS.PENDING
            element.preMsgId = res.preMsgId
            return
          }
        }
      }
    },
    // 监听获取历史消息回调
    onGetRoomHistoryMessageResponse(res) {
      if (res.code === 0) {
        // 断网重连之后会有重试机制,需要重新置空
        this.chatList = []
        res.content.forEach(e => {
          try {
            e.content = JSON.parse(e.text)
          } catch (error) {
            console.error('[chatbox]监听获取历史消息回调解析错误', error)
          }
          if (
            (e.content.type && Number(e.content.type) === 130) ||
            (Number(e.content.type) === 139 && e.content.to_uid === this.baseData.stuInfo.id) ||
            (Number(e.content.type) === 139 &&
              e.content.to_uid === this.baseData.teacherInfo.id &&
              Number(e.sender.split('_').pop()) === this.baseData.stuInfo.id)
          ) {
            e.status = SEND_MSG_STATUS.SUCCESS
            if (e.sender.startsWith('t')) {
              e.role = 't'
            } else if (e.sender.startsWith('s')) {
              e.role = 's'
            } else if (e.sender.startsWith('f')) {
              e.role = 'f'
            }
            if (!e.content.path) {
              e.content.path = defaultAvatar
            }
            e.userId = e.sender.split('_').pop()
            e.userInfo = {}
            const userInfo = this.studentList.find(item => item.stuId === e.userId)
            if (userInfo) {
              e.content.name = userInfo.stuName
              e.content.path = userInfo?.avatar || defaultAvatar
              e.userInfo = {
                levelId: userInfo?.prop?.levelId,
                subLevel: userInfo?.prop?.subLevel,
                name: userInfo.stuName,
                avatarFrame: userInfo?.prop?.avatarFrame?.resourceUrl,
                messageBoxFrame: userInfo?.prop?.messageBoxFrame?.resourceUrl
              }
            }
            if (
              this.chatBox.isOnlyTeacher &&
              e.role === 's' &&
              +e.userId !== this.baseData.stuInfo.id
            ) {
              return
            }
            this.chatList.unshift(e)
          }
        })
        // 历史消息最多保留300条,与大班逻辑一致
        if (this.chatList.length > MAX_CHATLIST_LENGTH) {
          const splicStartNum = this.chatList.length - MAX_CHATLIST_LENGTH
          this.chatList = this.chatList.splice(splicStartNum)
        }
        this.chatList.push({
          role: 'enterTip',
          userInfo: {
            levelId: this.baseData.stuInfo?.prop?.levelId,
            subLevel: this.baseData.stuInfo?.prop?.subLevel,
            name: this.baseData.stuInfo.nickName
          },
          msg: 'classroom.chats.chats.msgTip.ENTER_ROOM'
        })
        //  更新store historyList
        this.$store.dispatch('smallClass/updateChatBoxHistoryListInit', [...this.chatList])
        this.handleScrollBottom()
      }
    },
    // 监听kv消息
    onOpenChatHandle(res) {
      const { noticeContent } = res
      // 老师开启、关闭聊天功能
      const bool = noticeContent
      this.$store.dispatch('smallClass/updateChatBoxIsClose', bool)
      this.handleChangeIsStopChat(bool)
      this.sendLogger(`老师${bool ? '开启' : '关闭'} 聊天功能`)
    },
    onChatMsgControlHandle(res) {
      const { noticeContent } = res
      // 老师设置学员仅可看到老师消息
      let isOnlyTeacher = noticeContent.status === 'teacher' ? true : false
      this.$store.dispatch('smallClass/updateChatBoxIsOnlyTeacher', isOnlyTeacher)
      this.handleChangeIsOnlyReadTeacher(isOnlyTeacher)
      this.sendLogger(`老师设置学员仅可看到老师消息 ${isOnlyTeacher}`)
      if (isOnlyTeacher) {
        this.changeSendToValue({
          id: this.baseData.teacherInfo.id,
          name: 'classroom.chats.chats.sendToTeacher'
        })
      }
    },
    onPrivateMutChatHandle(res) {
      const { noticeContent } = res
      // 老师开启、关闭单独禁言
      const stopChat = noticeContent.ids.find(id => id == this.baseData.stuInfo.id)

      if (stopChat) {
        if (!this.chatBox.isMuted) {
          this.$store.dispatch('smallClass/updateChatBoxMutedByTeacher', true)
          this.handleChangeIsMutedByTeacher(true)
          this.sendLogger(`老师开启单独禁言`)
        }
      } else {
        if (this.chatBox.isMuted) {
          this.$store.dispatch('smallClass/updateChatBoxMutedByTeacher', false)
          this.handleChangeIsMutedByTeacher(false)
          this.sendLogger(`老师关闭单独禁言`)
        }
      }
    },
    // 监听网络状态
    onNetStatusChanged(res) {
      // @log-ignore
      if (res.netStatus === 4) {
        this.chatList.push({
          role: 'tip',
          msg: 'classroom.chats.chats.msgTip.CONNECT'
        })
      } else if (res.netStatus === 1 || res.netStatus === 2 || res.netStatus === 5) {
        this.chatList.push({
          role: 'tip',
          msg: 'classroom.chats.chats.msgTip.DISCONNECT'
        })
      }
      this.handleConditionScrollBottom()
    },
    // 监听他人加入房间回调
    onJoinRoomNotice(res) {
      const item = this.studentList.find(item => item.stuIrcId === res.userInfo.nickname)
      if (item) {
        this.chatList.forEach(data => {
          if (data.userId === item.stuId && item?.prop) {
            data.content.name = item.stuName
            data.content.path = item?.avatar || defaultAvatar
            data.userInfo.messageBoxFrame = item?.prop?.messageBoxFrame?.resourceUrl
            data.userInfo.avatarFrame = item?.prop?.avatarFrame?.resourceUrl
            data.userInfo.levelId = item?.prop?.levelId
            data.userInfo.subLevel = item?.prop?.subLevel
          }
        })
        this.chatList.push({
          role: 'enterTip',
          userInfo: {
            levelId: item?.prop?.levelId,
            subLevel: item?.prop?.subLevel,
            name: item.stuName
          },
          msg: 'classroom.chats.chats.msgTip.ENTER_ROOM'
        })
        this.handleConditionScrollBottom()
      }
    },
    // 监听实时消息
    onRecvRoomMessage(res) {
      const { content, fromUserInfo } = res
      if (
        (content && content.type && Number(content.type) === 130) ||
        (Number(content.type) === 139 && content.to_uid === this.baseData.stuInfo.id)
      ) {
        let role = ''
        if (fromUserInfo.nickname.startsWith('t')) {
          role = 't'
        } else if (fromUserInfo.nickname.startsWith('s')) {
          role = 's'
        } else if (fromUserInfo.nickname.startsWith('f')) {
          role = 'f'
        }
        if (!content.path) {
          content.path = defaultAvatar
        }
        if (this.chatList.length >= MAX_CHATLIST_LENGTH) {
          this.chatList.shift()
        }
        if (this.chatBox.isOnlyTeacher && role === 's') {
          return
        }
        const userId = fromUserInfo.nickname.split('_').pop()
        const info = this.studentList.find(item => item.stuId === userId)
        let userInfo = {}
        if (info) {
          userInfo = {
            levelId: info?.prop?.levelId,
            subLevel: info?.prop?.subLevel,
            name: info.nickName,
            avatarFrame: info?.prop?.avatarFrame?.resourceUrl,
            messageBoxFrame: info?.prop?.messageBoxFrame?.resourceUrl
          }
        }
        this.handleSaveList({
          role: role,
          content: content,
          userId,
          userInfo
        })
        const chatListDom = document.getElementById('chat-list')
        const scrollHeight = Math.round(chatListDom.scrollHeight)
        const scrollTop = Math.round(chatListDom.scrollTop)
        const clientHeight = Math.round(chatListDom.clientHeight)
        // 跟移动端统一,如果当前聊天list在底部,添加新消息,滚动到底部,否则就显示[新消息]弹窗

        if (scrollHeight <= scrollTop + clientHeight + 1) {
          this.handleScrollBottom()
        } else {
          chatListDom.addEventListener('scroll', this.handleOnScroll)
          this.isShowNewMsgTip = true
        }

        if (!this.chatBox.isShowBox) {
          this.$store.dispatch('smallClass/updateChatboxHasNewMsg', true)
        }
      }
    },
    // 是否close chatbox
    handleChangeIsStopChat(bool) {
      let tip = ''
      if (bool) {
        tip = {
          role: 'tip',
          msg: 'classroom.chats.chats.teacherOn'
        }
      } else {
        tip = {
          role: 'tip',
          msg: 'classroom.chats.chats.teacherOff'
        }
      }
      this.handleSaveList(tip)
      this.handleConditionScrollBottom()
    },
    // 是否单独禁言
    handleChangeIsMutedByTeacher(bool) {
      // mutedByTeacher: '你已被老师禁言',
      // unmutedByTeacher: '你的禁言状态已被解除',
      // underMute: '禁言中'
      let tip = ''
      if (bool) {
        tip = {
          role: 'tip',
          msg: 'classroom.chats.chats.mutedByTeacher'
        }
      } else {
        tip = {
          role: 'tip',
          msg: 'classroom.chats.chats.unmutedByTeacher'
        }
      }

      this.handleSaveList(tip)
      this.handleConditionScrollBottom()
    },
    // 是否 只看老师
    handleChangeIsOnlyReadTeacher(bool) {
      let tip = ''
      if (bool) {
        tip = {
          role: 'tip',
          msg: 'classroom.chats.chats.onlyteacher'
        }
      } else {
        tip = {
          role: 'tip',
          msg: 'classroom.chats.chats.all'
        }
      }
      this.handleSaveList(tip)
      this.handleConditionScrollBottom()
    },
    // 同步 chatList和historyList
    handleSaveList(item) {
      this.chatList.push(item)
      this.$store.dispatch('smallClass/updateChatBoxHistoryList', item)
    },
    // 滚动到底部
    handleScrollBottom() {
      // @log-ignore
      this.isShowNewMsgTip = false
      this.$nextTick(() => {
        const chatListDom = document.getElementById('chat-list')
        if (chatListDom) {
          const scrollHeight = Math.round(chatListDom.scrollHeight)
          const clientHeight = Math.round(chatListDom.clientHeight)
          chatListDom.scrollTop = scrollHeight - clientHeight + 1
          chatListDom.removeEventListener('scroll', this.handleOnScroll)
        }
      })
    },
    // 如果在底部就滚动,不在底部不滚动
    handleConditionScrollBottom() {
      // @log-ignore
      const chatListDom = document.getElementById('chat-list')
      if (chatListDom) {
        const scrollHeight = Math.round(chatListDom.scrollHeight)
        const scrollTop = Math.round(chatListDom.scrollTop)
        const clientHeight = Math.round(chatListDom.clientHeight)

        if (scrollHeight <= scrollTop + clientHeight + 1) {
          this.handleScrollBottom()
        }
      }
    },
    // 监听滚动事件
    handleOnScroll() {
      // @log-ignore
      const chatListDom = document.getElementById('chat-list')
      if (chatListDom) {
        const scrollHeight = Math.round(chatListDom.scrollHeight)
        const scrollTop = Math.round(chatListDom.scrollTop)
        const clientHeight = Math.round(chatListDom.clientHeight)
        if (scrollHeight <= scrollTop + clientHeight + 1) {
          this.isShowNewMsgTip = false
          chatListDom.removeEventListener('scroll', this.handleOnScroll)
        }
      }
    },
    /**
     * 日志上报
     */
    sendLogger(msg, params, level = 'info') {
      logger.send({
        tag: 'ChatBox',
        content: {
          msg: msg,
          interactType: 'ChatBox',
          params
        },
        level
      })
    },
    /**
     * 播放回放记录
     */
    playBackList(e) {
      // @log-ignore
      try {
        e.content = JSON.parse(e.text)
      } catch (error) {
        console.error(error)
      }
      if (e.sender.startsWith('t')) {
        e.role = 't'
      } else if (e.sender.startsWith('s')) {
        e.role = 's'
      } else if (e.sender.startsWith('f')) {
        e.role = 'f'
      }
      if (!e.content.path) {
        e.content.path = defaultAvatar
      }
      e.userId = e.sender.split('_').pop()
      if (
        (e.content.type && Number(e.content.type) === 130) ||
        (Number(e.content.type) === 139 && e.content.to_uid === this.baseData.stuInfo.id) ||
        (Number(e.content.type) === 139 &&
          e.content.to_uid == this.baseData.teacherInfo.id &&
          e.sender.indexOf(this.baseData.stuInfo.id) > -1)
      ) {
        e.status = SEND_MSG_STATUS.SUCCESS
        e.userInfo = {}
        this.chatList.push(e)
      } else {
        let messageParams = {}
        // 本地表情配置 本地表情类型 1
        const commonOptions = {
          name: e.content.from?.username,
          isMe: false,
          msg: e.content.data?.name,
          isNewEmoji: true,
          path: e.content.from?.path || ''
        }
        if (e.content.ircType == 'send_emoji' && e.content.data.type == 1) {
          messageParams = commonOptions
          this.chatList.push({
            role: 's',
            content: messageParams,
            userId: e.userId,
            userInfo: {}
          })
        } else if (
          e.content.ircType == 'animation_emoji' &&
          (e.content.data.type == 2 || e.content.data.type == 3)
        ) {
          // 动态表情配置 动态表情 图片类型2, lottie类型3
          messageParams = {
            ...commonOptions,
            lottieUrl: e.content.data.resource?.lottieUrl || '',
            emojiId: e.content.data.resource?.emojiId || 0,
            emojiType: e.content.data.type
          }
          this.chatList.push({
            role: 's',
            content: messageParams,
            userId: e.userId,
            userInfo: {}
          })
        }
      }

      const chatListDom = document.getElementById('chat-list')
      const scrollHeight = Math.round(chatListDom.scrollHeight)
      const scrollTop = Math.round(chatListDom.scrollTop)
      const clientHeight = Math.round(chatListDom.clientHeight)

      if (scrollHeight <= scrollTop + clientHeight + 1) {
        this.handleScrollBottom()
      } else {
        chatListDom.addEventListener('scroll', this.handleOnScroll)
        this.isShowNewMsgTip = true
      }
    },
    changeSendToValue(item) {
      this.sendToReactive.id = item.id
      this.sendToReactive.name = item.name
      this.sendToReactive.isOpen = false
    },
    toggleMemberPopup(e) {
      e.stopPropagation()

      if (
        !this.chatBox.isOpenChat ||
        this.chatBox.isOnlyTeacher ||
        this.isConnecting ||
        this.chatBox.isMuted
      ) {
        this.sendToReactive.isOpen = false
        return
      }
      this.sendToReactive.isOpen = !this.sendToReactive.isOpen
    },
    handleDocumentClick(e) {
      // @log-ignore
      if (!this.sendToReactive.isOpen) return
      const memberListDom = document.getElementById('memberList')
      if (!memberListDom.contains(e.target)) {
        this.sendToReactive.isOpen = false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.chat-box-container {
  width: 305px;
  height: 572px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  cursor: default;

  border-radius: 10px;
  .chat-box-head {
    width: 100%;
    height: 40px;
    background: #0f192a;
    text-align: center;
    position: relative;

    .title {
      font-size: 14px;
      font-weight: 600;
      color: #dee2e7;
      line-height: 16px;
      position: absolute;
      top: 12px;
      right: 0;
      bottom: 0;
      left: 0;
      margin: auto;
    }

    .close {
      width: 24px;
      height: 24px;
      overflow: hidden;
      border-radius: 50%;
      cursor: pointer;
      position: absolute;
      top: 8px;
      right: 8px;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .chat-box-content {
    width: 100%;
    flex: 1;
    background: rgba(15, 25, 42, 0.9);
    overflow-y: auto;
    padding: 8px;

    &::-webkit-scrollbar {
      display: none;
    }

    .chat-ul {
      margin: 0;
      padding: 0;

      .left-li,
      .right-li {
        .avatarBox {
          width: 48px;
          height: 48px;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
          img {
            width: 31px;
            height: 31px;
            border-radius: 50%;
            background: #fff;
          }
          .avatar-box {
            width: 48px;
            height: 48px;
            background-size: 48px 48px !important;
            position: absolute;
            left: 0;
            top: 0;
          }
        }
        .msgBox {
          padding-top: 9px;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
        }
        margin-top: 8px;
        position: relative;

        .msg-header {
          display: flex;
          align-items: center;

          .nickname {
            font-size: 12px;
            font-weight: 500;
            color: #ffffff;
            line-height: 14px;
            margin: 5px;
            max-width: 80px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .msg-private {
            font-size: 10px;
            font-family: 'PingFangSC-Medium', 'PingFang SC';
            font-weight: 500;
            flex-shrink: 0;
          }
        }

        .msg-box {
          max-width: 243px;
          background: #fff;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
          color: #172b4d;
          line-height: 14px;
          padding: 8px;
          word-break: break-word; // 一堆字母或者一个很长的英文单词时,强制换行,防止浏览器不知道如何换行
        }
      }

      .left-li {
        display: flex;
        align-items: flex-start;
        .msgBox {
          align-items: flex-start;
          .ml-12 {
            margin-left: 12px;
          }
        }
        .msg-header {
          .msg-private {
            height: 14px;
            color: #10e2e0;
          }
        }

        .msg-box {
          margin-left: 5px;
        }

        .msg-box-teacher {
          background: #3370ff;
          color: #ffffff;
        }

        .msg-box-f {
          background: #02ca8a;
          color: #ffffff;
        }
      }

      .right-li {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: flex-start;

        .msg-header {
          .msg-private {
            color: #10e2e0;
            margin-right: 4px;
          }
        }

        .msg-wrap {
          display: flex;
          margin-right: 5px;
          .msg-error {
            margin-right: 7px;
            width: 20px;
            height: 20px;
            cursor: pointer;
            flex-shrink: 0;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .msg-box-me {
            max-width: 217px;
            background: #ff9f0a;
            color: #fff;
          }
        }
      }

      .tip {
        margin: 30px auto 22px;
        background: rgba(255, 255, 255, 0.12);
        border-radius: 4px;
        padding: 3px 5px;
        font-size: 11px;
        font-weight: 500;
        color: #dee2e7;
        line-height: 13px;
        width: fit-content;
      }
      .enterTipBox {
        text-align: center;
        margin: 10px 0;
        .enterTip {
          font-size: 12px;
          padding: 5px 8px;
          display: inline-flex;
          height: 22px;
          background: rgba(0, 0, 0, 0.4);
          border-radius: 11px;
          align-items: center;
          line-height: 12px;
          color: #fff;
        }
        .tipLevel {
          margin: 0 4px 0 8px;
        }
        .tipName {
          max-width: 80px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .box-back {
        border-image-slice: 40 58 40 58 fill;
        border-style: solid;
        border-image-repeat: stretch;
        border-width: 1px;
        background: transparent !important;
        color: #172b4d !important;
      }
    }
  }

  .chat-box-foot {
    width: 100%;
    min-height: 50px;
    background: #0f192a;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    position: relative;

    // padding: 0px 10px;
    .input-box {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      position: relative;
      width: 100%;
      padding: 10px;

      // border-top: 1px solid rgba(255, 243, 220, 0.5);
      .text-area {
        border: none;
        caret-color: #3370ffff;
        width: 231px;
        height: 34px;
        background: rgba(255, 255, 255, 0.08);
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        color: #dee2e7;
        line-height: 16px;
        flex: 1;

        &::-webkit-scrollbar {
          display: none;
        }

        &::placeholder {
          font-size: 14px;
          font-weight: 500;
          color: #a2aab8;
          line-height: 16px;
        }
      }

      ::v-deep .ant-input {
        resize: none !important;
        height: 34px;
        padding: 8px 9px;
      }

      .ant-input:hover {
        border: none;
        border-right-width: 0;
      }

      .ant-input:focus {
        border: none;
        box-shadow: none !important;
      }

      .send-btn {
        width: 45px;
        height: 34px;
        background: rgba(255, 255, 255, 0.08);
        border-radius: 6px;
        margin-left: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        img {
          width: 12px;
          height: 14px;
        }
      }

      .no-send {
        cursor: no-drop;
      }
    }

    .new-msg {
      position: absolute;
      top: -49px;
      right: 16px;
      display: flex;
      align-items: center;
      cursor: pointer;

      img {
        width: 28px;
        height: 31px;
      }
    }

    .new-meg-tip {
      position: absolute;
      top: -30px;
      width: 40px;
      height: 28px;
      line-height: 18px;
      text-align: center;
      font-size: 12px;
      background: rgba(15, 25, 42, 0.95);
      border-radius: 16px;
      color: #ffaa0a;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-more-bottom {
        display: inline-block;
        width: 10px;
        height: 10px;
        background: url('./imgs/icon-more-bottom.png') no-repeat;
        background-size: cover;
      }
    }

    .too-frequently {
      position: absolute;
      top: -28px;
      height: 23px;
      background: #fff3dc;
      border-radius: 17px;
      font-size: 11px;
      padding: 5px 8px;
      font-weight: 500;
      color: #ffaa0a;
      line-height: 13px;
    }

    .input-close {
      width: 100%;
      box-sizing: border-box;
      height: 54px;
      font-size: 12px;
      font-weight: 600;
      line-height: 14px;
      color: #ffaa0a;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10px;

      .input-close-content {
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.08);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
      }
    }

    .send-to {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      font-size: 12px;
      font-family: 'PingFangSC-Semibold', 'PingFang SC';
      font-weight: 500;
      color: rgba(255, 243, 220, 0.5);
      line-height: 17px;
      padding: 10px;
      padding-bottom: 0px;
      width: 100%;

      .send-to-click {
        cursor: pointer;

        &.un-send-to-click {
          cursor: not-allowed;
        }
      }

      .send-to-name {
        margin: 0 10px;
        font-size: 12px;
        font-family: 'PingFangSC-Semibold', 'PingFang SC';
        font-weight: 500;
        color: #10e2e0;
        line-height: 17px;

        &.un-send-to-name {
          color: #a2aab8;
        }
      }

      .send-to-icon {
        width: 8px;
        height: 8px;
        display: inline-block;
        background-image: url('./imgs/open_icon.png');
        background-repeat: no-repeat;
        background-size: 100%;
      }
    }

    .memberList {
      position: absolute;
      width: 143px;
      height: 68px;
      background: #0f192a;
      border-radius: 6px;
      top: -78px;
      left: 10px;
      overflow: hidden;
      padding: 0px 14px;

      .member-name {
        font-size: 12px;
        font-family: 'SFProRounded-Medium', 'SFProRounded';
        font-weight: 500;
        color: rgba(255, 255, 255, 0.5);
        line-height: 14px;

        &.member-changed {
          font-weight: 600;
          color: #10e2e0;
        }
      }

      .member-li {
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid rgba(255, 243, 220, 0.5);
        padding: 10px 0px;

        &:last-child {
          border-bottom: none;
        }
      }

      .member-icon {
        width: 10px;
        height: 10px;
        background-image: url('./imgs/member_changed_icon_green.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }
  }
}

.on-wholeOnsatge {
  margin-top: 12px;
  height: 100%;
  width: 100%;
  border-radius: 0;

  .chat-box-content {
    background: #ffe7b8;

    &::-webkit-scrollbar {
      width: 5px;
    }

    .chat-ul {
      margin: 0;
      padding: 0;

      .left-li,
      .right-li {
        .msg-header {
          .nickname {
            color: #6b3400;
          }

          .msg-private {
            color: #172b4d;
          }
        }
      }

      .left-li {
        .msg-box {
          color: #172b4d;
          background: #ffffff;
        }

        .msg-box-teacher {
          background: #3370ff;
          color: #ffffff;
        }
      }

      .right-li {
        .msg-wrap {
          .msg-box-me {
            background: #ff9f0a;
            color: #fff;
          }
        }
      }

      .tip {
        background: rgba(255, 243, 220, 0.5);
        color: #172b4d;
      }
    }
  }

  .chat-box-foot {
    background: rgba(244, 108, 8, 0.6);

    .input-box {
      .text-area {
        font-weight: 600;
        color: #fff3dc;

        &::placeholder {
          color: rgba(255, 243, 220, 0.8);
        }
      }

      .send-btn {
        background: #fff3dc;
      }
    }

    .input-close {
      color: #fff3dc;
    }

    .send-to {
      color: rgba(255, 243, 220, 0.5);

      .send-to-name {
        color: #ffffff;
      }

      .send-to-icon {
        background-image: url('./imgs/open_icon_white.png');
        background-repeat: no-repeat;
        background-size: 100%;
      }
    }

    .memberList {
      background: #0f192a;

      .member-name {
        color: rgba(255, 255, 255, 0.5);

        &.member-changed {
          color: rgba(255, 255, 255);
        }
      }

      .member-icon {
        background-image: url('./imgs/member_changed_icon_white.png');
      }
    }
  }
}

.playBack {
  margin-top: 2px;
  background: #1a1a1a;
  width: 100%;
  height: 100%;

  .chat-box-content {
    background: #1a1a1a;

    &::-webkit-scrollbar {
      width: 5px;
    }

    .chat-ul {
      margin: 0;
      padding: 0;

      .left-li,
      .right-li {
        .msg-header {
          .nickname {
            color: #ffffff;
          }

          // .msg-private {
          //   color: #10e2e0;
          // }
        }

        .msg-box {
          max-width: none;
        }
      }

      .left-li {
        .msg-box {
          color: #172b4d;
          background: #dee2e7;
        }

        .msg-box-teacher {
          background: #3370ff;
          color: #ffffff;
        }

        .msg-box-f {
          background: #02ca8a;
          color: #ffffff;
        }

        .msg-private {
          color: #10e2e0;
        }
      }

      .right-li {
        .msg-header {
          .msg-private {
            color: #10e2e0;
          }
        }

        .msg-wrap {
          .msg-box-me {
            background: #ffaa0a;
            color: #172b4d;
          }
        }
      }
    }
  }

  .chat-box-foot {
    background: #1a1a1a;
    min-height: 0px;
    height: 0px;

    .new-meg-tip {
      top: -28px;
    }
  }
}
</style>
