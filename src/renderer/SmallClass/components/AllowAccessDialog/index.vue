<template>
  <div v-if="sVisible" class="dialog-wrapper" :class="[dialogClass, allOnStageClass]">
    <div class="dialog-content">
      <slot></slot>
    </div>
    <div class="dialog-footer" v-if="showOkButton || showCancelButton">
      <div v-if="showCancelButton" class="dialog-button button-cancel" @click="handleCancel">
        {{ cancelValue || $t('common.deny') }}
      </div>
      <div v-if="showOkButton" class="dialog-button button-ok" @click="handleOk">
        {{ okValue || $t('common.agree') }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AllowAccessDialog',
  model: {
    prop: 'visible'
  },
  data() {
    return {
      sVisible: !!this.visible
    }
  },
  props: {
    dialogClass: {
      type: String,
      default: ''
    },
    theme: {
      type: String,
      default: 'green'
    },
    visible: {
      type: Boolean,
      default: false
    },
    okValue: {
      type: String,
      default: ''
    },
    cancelValue: {
      type: String,
      default: ''
    },
    showOkButton: {
      type: Boolean,
      default: true
    },
    showCancelButton: {
      type: Boolean,
      default: true
    },
    isAllOnStage: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    visible: function visible(val) {
      this.sVisible = val
    }
  },
  computed: {
    allOnStageClass() {
      return this.isAllOnStage ? `all-on-stage-wrapper` : ``
    }
  },
  mounted() {},
  methods: {
    handleCancel: function handleCancel(e) {
      this.$emit('cancel', e)
    },
    handleOk: function handleOk(e) {
      this.$emit('ok', e)
    }
  }
}
</script>
<style scoped lang="scss">
.dialog-wrapper {
  width: 253px;
  background: rgba(15, 25, 42, 0.9);
  border-radius: 10px;
  overflow: hidden;
}
.all-on-stage-wrapper {
  width: 360px !important;
  border-radius: 10px 10px 0 0;
}
.dialog-content {
  padding: 18px 16px 24px 16px;
  color: #fff;
  font-size: 12px;
}
.dialog-footer {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}
.dialog-button {
  padding: 0 8px;
  height: 26px;
  background: #2f384b;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  cursor: pointer;
  text-align: center;
  line-height: 26px;
}
.button-cancel {
  color: #a2aab8;
  margin-right: 24px;
}
.button-ok {
  background: #02ca8a;
}
</style>
