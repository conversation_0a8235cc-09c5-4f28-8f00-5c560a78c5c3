<template>
  <div ref="ppt-area" class="ppt-area-container" id="ppt-area-container">
    <ClassSoon v-if="!teacherJoinChannelStatus" />
    <H5Courseware ref="h5CoursewareMain" @changePageId="changePageId"> </H5Courseware>
  </div>
</template>

<script>
import H5Courseware from './H5courseware'
import ClassSoon from './ClassSoon'

export default {
  name: 'CoursewareBoard',
  components: {
    ClassSoon,
    H5Courseware
  },
  computed: {
    // 老师是否在课堂
    teacherJoinChannelStatus() {
      console.log(
        'this.$store.state.smallClass.teacherMsg.inClass',
        this.$store.state.smallClass.teacherMsg.inClass
      )
      return this.$store.state.smallClass.teacherMsg.inClass === true
    }
  },
  methods: {
    /**
     * 切换课件ID
     */
    changePageId(id, currentCourseWareData) {
      // @log-ignore
      this.$nextTick(() => {
        // @log-ignore
        this.$bus.$emit('changePageId', id, currentCourseWareData)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.ppt-area-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .ppt-dialog-main {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;

    .dialog-container {
      position: absolute;
      top: 50%;
      left: 50%;
      margin-top: -60px;
      margin-left: -150px;
      width: 300px;
      height: 120px;
      background-color: white;
      box-shadow: 0px 4px 35px 0px rgba(0, 0, 0, 0.14);
      border-radius: 4px;
      text-align: center;
      padding: 16px;

      p {
        color: rgba(33, 40, 49, 1);
        padding: 16px 0 28px 0;
      }

      .ppt-cancle-button,
      .ppt-download-button {
        background-color: white;
        padding: 5px 20px;
        border-radius: 16px;
        color: black;
      }

      .ppt-download-button {
        background-color: red;
        padding: 5px 20px;
        border-radius: 16px;
        color: #ffffff;
      }
    }
  }

  .media-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .exam-container {
    width: 100%;
    height: 100%;

    iframe {
      width: 100%;
      height: 100%;
      border: none;
      background: white;
      position: relative;
      z-index: 998;
    }
  }
}

.whiteboard-component {
  width: 100%;
  height: 100%;
}

.container {
  z-index: 66; // 涂鸦z-index
}

#toolsContainer {
  z-index: 1;
}
</style>
