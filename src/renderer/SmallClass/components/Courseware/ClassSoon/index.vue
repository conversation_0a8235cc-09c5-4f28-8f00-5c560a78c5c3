<template>
  <div class="wait-teacher">
    <div class="wait-dialog">
      <div class="notice-content">
        {{ $t('classroom.smallClass.coursewareBoard.classSoonNotice') }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {}
  },
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.wait-teacher {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
  background: url('../images/wait-bg.jpg');
  background-size: cover;
  .wait-dialog {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 310px;
    height: 205px;
    padding: 30px 30px 40px 30px;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -150px;
    margin-left: -155px;
    background: url('../images/wait-dialog-bg.png');
    background-size: cover;
  }
  .notice-content {
    font-size: 18px;
    color: #ff850a;
  }
}
</style>
