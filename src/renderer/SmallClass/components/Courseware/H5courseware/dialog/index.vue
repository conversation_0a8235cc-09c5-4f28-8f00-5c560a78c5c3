<template>
  <div class="game-courseware-mask-bg" v-if="visible">
    <div class="dialog-contenter">
      <div class="ne-dialog--body">
        <slot></slot>
      </div>
      <div class="ne-dialog--footer" v-if="$slots.footer">
        <slot name="footer"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NeLoading',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style scoped lang="scss">
.game-courseware-mask-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: url(./imgs/wait-bg.jpg) no-repeat center center;
}
.dialog-contenter {
  position: absolute;
  background: #fff;
  border-radius: 16px;
  text-align: center;
  min-width: 360px;
  min-height: 200px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.ne-dialog--body {
  padding: 20px 10px 10px 20px;
  font-size: 20px;
  font-family: Helvetica, PingFang SC, 'sans-serif', Arial, Verdana, Microsoft YaHei;
  color: rgba(23, 43, 77, 1);
  h3 {
    font-weight: 600;
    font-size: 20px;
    padding: 10px 0 10px 0;
    margin: 0;
  }
  ::v-deep p {
    font-family: Helvetica, PingFang SC, 'sans-serif', Arial, Verdana, Microsoft YaHei;
    line-height: 20px;
    font-size: 16px;
    color: #a2aab8;
  }
}
.ne-dialog--footer {
  position: absolute;
  bottom: 16px;
  left: 16px;
  right: 16px;
  ::v-deep .button {
    background: linear-gradient(45deg, rgba(255, 213, 24, 1) 0%, rgba(255, 170, 10, 1) 100%);
    border-radius: 30px;
    font-weight: 600;
    color: rgba(255, 255, 255, 1);
    line-height: 20px;
    border: 0 none;
    padding: 15px 0px;
    width: 45%;
    outline: 0 none;
    text-align: center;
    cursor: pointer;
    font-size: 18px;
  }
}
</style>
