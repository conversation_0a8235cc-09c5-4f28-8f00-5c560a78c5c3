<template>
  <div class="container">
    <img :src="avatar" class="avatar-image" />
    <!-- effect是一个字符串，如果长度大于0并且是字符串类型，才展示，否则不展示 -->
    <img
      v-if="effect && effect.length > 0 && typeof effect === 'string'"
      :src="effect"
      class="effect-image"
    />
  </div>
</template>

<script>
export default {
  props: {
    avatar: {
      type: String,
      required: true
    },
    effect: {
      type: String,
      required: true
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  width: 54px;
  height: 54px;
}

.container img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  /* 保持图片比例 */
  object-fit: contain;
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}

/* 为不同图片单独设置尺寸 */
.container img:nth-child(1) {
  width: 66%; /* 按需调整 */
  border-radius: 50%; // 新增，设置圆角为 50% 实现圆形
}

.container img:nth-child(2) {
  width: 100%; /* 按需调整 */
}
</style>
