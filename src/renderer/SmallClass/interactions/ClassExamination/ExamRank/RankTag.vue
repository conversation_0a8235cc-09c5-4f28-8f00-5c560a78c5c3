<template>
  <div class="container">
    <div v-if="rank <= 3" class="topRank">
      <img :src="getTopRankImage" :alt="`第${rank}名`" class="rank-image" />
    </div>
    <div v-else class="commonRank">
      <img :src="getCommonRankImage[0]" :alt="`第${rank}名`" class="rank-image" />
      <img :src="getCommonRankImage[1]" :alt="`第${rank}名`" class="rank-image" />
    </div>
  </div>
</template>

<script>
import rank1 from './sources/排名_1.png'
import rank2 from './sources/排名_2.png'
import rank3 from './sources/排名_3.png'
import seleceted_0 from './sources/选中_数字_0.png'
import seleceted_1 from './sources/选中_数字_1.png'
import seleceted_2 from './sources/选中_数字_2.png'
import seleceted_3 from './sources/选中_数字_3.png'
import seleceted_4 from './sources/选中_数字_4.png'
import seleceted_5 from './sources/选中_数字_5.png'
import seleceted_6 from './sources/选中_数字_6.png'
import seleceted_7 from './sources/选中_数字_7.png'
import seleceted_8 from './sources/选中_数字_8.png'
import seleceted_9 from './sources/选中_数字_9.png'
import unseleceted_0 from './sources/未选中_数字_0.png'
import unseleceted_1 from './sources/未选中_数字_1.png'
import unseleceted_2 from './sources/未选中_数字_2.png'
import unseleceted_3 from './sources/未选中_数字_3.png'
import unseleceted_4 from './sources/未选中_数字_4.png'
import unseleceted_5 from './sources/未选中_数字_5.png'
import unseleceted_6 from './sources/未选中_数字_6.png'
import unseleceted_7 from './sources/未选中_数字_7.png'
import unseleceted_8 from './sources/未选中_数字_8.png'
import unseleceted_9 from './sources/未选中_数字_9.png'

export default {
  data: () => ({
    selecetedImages: [
      seleceted_0,
      seleceted_1,
      seleceted_2,
      seleceted_3,
      seleceted_4,
      seleceted_5,
      seleceted_6,
      seleceted_7,
      seleceted_8,
      seleceted_9
    ],
    unSelecetedImages: [
      unseleceted_0,
      unseleceted_1,
      unseleceted_2,
      unseleceted_3,
      unseleceted_4,
      unseleceted_5,
      unseleceted_6,
      unseleceted_7,
      unseleceted_8,
      unseleceted_9
    ]
  }),
  props: {
    rank: {
      type: Number,
      required: true
    },
    isCurrentUser: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    getTopRankImage() {
      const rankImages = {
        1: rank1,
        2: rank2,
        3: rank3
      }
      return rankImages[this.rank]
    },
    getCommonRankImage() {
      let counts = this.getRankImageArray(this.rank)
      if (this.isCurrentUser) {
        return [this.selecetedImages[counts[0]], this.selecetedImages[counts[1]]]
      }
      let resImages = [this.unSelecetedImages[counts[0]], this.unSelecetedImages[counts[1]]]
      return resImages
    }
  },
  methods: {
    getRankImageArray(rank) {
      if (rank < 10) {
        return [0, rank]
      } else {
        return rank
          .toString()
          .split('')
          .map(Number)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: red;
  border: 2px solid transparent;
  color: #994112;
  display: flex;
  justify-content: center;
  align-items: center;
}

.topRank {
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 36px;
    height: 36px;
  }
}

.rank-image {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.commonRank {
  display: flex;
  justify-content: center;
  // 侧轴对齐方式
  align-items: center;

  img {
    height: 16px;
    width: 14px;
  }
}
</style>
