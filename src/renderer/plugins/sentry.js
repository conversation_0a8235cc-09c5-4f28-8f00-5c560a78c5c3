import * as Sentry from '@sentry/electron/renderer'
import { RewriteFrames } from '@sentry/integrations'
import sentryConfigManager from '../utils/sentryConfigManager'

let isInitialized = false

// 获取用户信息并设置上下文
function setUserContext() {
  try {
    const userInfo = localStorage.getItem('userInfo')
    if (!userInfo) return false

    const user = JSON.parse(userInfo)
    if (!user || !user.uid) return false

    Sentry.configureScope(scope => {
      scope.setTag('uid', user.uid)
      scope.setTag('userType', 'student')

      scope.setUser({
        id: user.uid,
        username: user.nickName
      })

      scope.setLevel('info')
    })

    console.log('Sentry用户上下文已设置')
    return true
  } catch (error) {
    console.error('设置Sentry用户上下文失败:', error)
    return false
  }
}

// 异步初始化Sentry
async function initSentry() {
  try {
    console.log('开始初始化渲染进程Sentry...')

    // 获取配置（优先从主进程，降级到云端，最后用默认配置）
    const config = await sentryConfigManager.getConfig()
    console.log('获取到的sentry配置:', config)

    if (!config.enabled) {
      console.log('Sentry已禁用')
      return null
    }

    console.log('=== 渲染进程Sentry初始化 ===', config)
    console.log('使用配置初始化Sentry:', {
      dsn: config.dsn ? config.dsn : '未配置',
      enabled: config.enabled,
      environment: config.environment,
      sampleRate: config.sampleRate
    })

    // 准备集成插件
    const integrations = [
      Sentry.browserTracingIntegration(),
      // 添加RewriteFrames集成来重写堆栈帧路径
      new RewriteFrames({
        // 将app://路径重写为相对路径，匹配sourcemap上传时的路径
        iteratee: frame => {
          if (frame.filename && frame.filename.includes('/static/js/')) {
            const originalFilename = frame.filename
            // 提取文件名部分，例如从 file:///...../static/js/chunk-common.d5dbf5df.js
            // 重写为 app:///static/js/chunk-common.d5dbf5df.js
            const match = frame.filename.match(/\/static\/js\/(.+)$/)
            if (match) {
              frame.filename = `app:///static/js/${match[1]}`
              console.log('🔄 RewriteFrames - 路径重写:', originalFilename, '->', frame.filename)
            }
          }
          return frame
        }
      })
    ]
    // 添加回放集成（如果启用）
    if (config.replaysSessionSampleRate > 0 || config.replaysOnErrorSampleRate > 0) {
      integrations.push(Sentry.replayIntegration())
    }
    if (config.needFeedback) {
      console.log('启用Sentry反馈集成')
      integrations.push(Sentry.feedbackIntegration())
    }
    // 初始化Sentry
    const sentryInstance = Sentry.init({
      dsn: config.dsn,
      environment: process.env.VUE_APP_MODE || 'development',
      release: process.env.VUE_APP_RELEASE_VERSION,
      enabled: config.enabled,
      sampleRate: config.sampleRate,
      attachStacktrace: config.attachStacktrace,
      sendDefaultPii: config.sendDefaultPii,
      debug: false, // 关闭调试，避免控制台错误信息
      maxBreadcrumbs: config.maxBreadcrumbs,
      maxValueLength: config.maxValueLength,
      autoSessionTracking: config.autoSessionTracking,
      beforeSend: config.beforeSend,
      beforeBreadcrumb: config.beforeBreadcrumb,
      // 性能监控配置
      tracesSampleRate: config.tracesSampleRate,
      profilesSampleRate: config.profilesSampleRate,
      // 回放配置
      replaysSessionSampleRate: config.replaysSessionSampleRate,
      replaysOnErrorSampleRate: config.replaysOnErrorSampleRate,
      // 设置 in_app 规则，标记哪些路径是应用代码
      inAppInclude: ['app:///static/js/', 'file:///', '~/static/js/'],

      integrations
    })

    // 尝试设置用户上下文（如果有用户信息的话）
    const hasUserContext = setUserContext()
    if (!hasUserContext) {
      console.log('暂无用户信息，Sentry已初始化但未设置用户上下文')
    }

    // 挂载到window
    if (!window.$Sentry) {
      window.$Sentry = Sentry
    }

    isInitialized = true
    console.log('渲染进程Sentry初始化完成')
    return sentryInstance
  } catch (error) {
    console.error('Sentry初始化失败:', error)
    return null
  }
}

// 启动函数
async function start() {
  try {
    const sentryInstance = await initSentry()
    return sentryInstance
  } catch (error) {
    console.error('Sentry启动失败:', error)
    return null
  }
}

// 立即启动
start()

export { start, isInitialized }
