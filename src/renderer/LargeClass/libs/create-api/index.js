import api from 'api-creater'
import { messageCenter } from '@thinkacademy/live-framework/components/framework/message-center'

const request = api.init({
  baseOptions: {
    withCredentials: true,
    timeout: 10000
  },
  interceptors: {
    request: {
      callback: config => {
        // do something ...
        if (window._requestHeadersData.gradeIds && window._requestHeadersData.subjectIds) {
          config.headers['switch-grade'] = window._requestHeadersData.gradeIds.split(',')[0] || -1
          config.headers['switch-subject'] =
            window._requestHeadersData.subjectIds.split(',')[0] || -1
        }
        config.startTime = new Date().getTime()
        config.interactionId = config.data && config.data.interactionId
        // config.headers['switch-subject'] = config

        return config
      },
      errHandler: err => {
        return Promise.reject(err)
      }
    },
    response: {
      callback: config => {
        // do something ...

        return config
      },
      errHandler: err => {
        return Promise.reject(err)
      }
    }
  },
  next(res) {
    const data = res.data

    if (
      data.code * 1 !== 0 &&
      data.msg !== '请勿重复领取' &&
      data.stat !== 1 &&
      data.msg !== '教师没有开启语音弹幕'
    ) {
      console.log('信息解析失败', data)
      messageCenter.emit('exception', {
        level: 'toast',
        data: {
          message: data.msg || '信息解析失败',
          duration: 2000
        }
      })
    }
    showLog(res)
    return true
  },
  errCatcher(err) {
    console.log(err)
    messageCenter.emit('exception', {
      level: 'toast',
      message: '网络请求错误',
      dom: 'body'
    })
    // $toast({
    //   message: '网络请求错误',
    //   dom: 'body'
    // })
    const { response = {}, config = {} } = err
    const { url, data, method, timeout } = config

    const params = {
      type: 'sys',
      data: { response, url, data, method, timeout }
    }
    messageCenter.emit('logger', params)
  }
})

function showLog(res) {
  const endTime = new Date().getTime()
  const spendTime = endTime * 1 - res.config.startTime * 1
  const extradata = {
    time: spendTime,
    url: res.config.url,
    data: res.config.data
  }
  const params = {
    type: 'show',
    data: {
      eventtype: 'web_api_response_time',
      sno: '1',
      stable: '1',
      interactionid: res.config.interactionId || '',
      logtype: 'api',
      ex: 'null',
      extradata
    }
  }
  // console.log(1,params);
  messageCenter.emit('logger', params)
}

export default request
