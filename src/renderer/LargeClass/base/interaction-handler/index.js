import Vue from 'vue'
import InteractionHandlerBase from '@thinkacademy/live-framework/components/base/interaction-handler/interaction-handler-base'
import { messageCenter } from '@thinkacademy/live-framework/components/framework/message-center'
import { ignoreRoomDataNotices } from 'config/classroom'
import { getRedPacketRainStatus } from 'api/interaction/redPackage'
import { interactionList } from './interaction-conf'
import logger from 'utils/logger'
import { checkCourseWareIdChange } from 'utils/courses'

const errorLog = (msg, params) => {
  logger.send({
    tag: 'irc',
    level: 'error',
    content: {
      msg,
      params
    }
  })
}
export default class InteractionHandler extends InteractionHandlerBase {
  constructor() {
    super()
    this.options = null
    this.isAudition = false
    // 互动列表
    this.interactionList = interactionList
    // 销毁前是否提交
    this.submit = false
    // 辅导视频连麦状态
    this.tutorVideoLinkStatus = false
    // 互动管理
    this.interactionMap = new Map()
  }
  /**
   * @param {*} params
   * @description 处理监听
   */
  eventHandler(params) {
    console.log('interaction-eventHandler', params)
    const { type, data, roomMessage } = params
    this.options = roomMessage
    this.isAudition = roomMessage.roomInfo.commonOption.isAudition
    if (type === 'onRecvRoomDataUpdateNotice') {
      this.handleRoomNotice(data)
    }
  }

  /**
   * @param {*} data irc消息
   * @description 处理notice消息
   */
  handleRoomNotice(data) {
    // @log-ignore
    if (!data.datas) {
      return
    }
    const isHistory = !data.msgId // 是否是历史消息
    data.datas.forEach(async (item, key) => {
      // 旁听身份kv消息忽略处理
      if (this.isAudition && ignoreRoomDataNotices.includes(key)) return
      const noticeValue = JSON.parse(item.value)
      let noticeContent = noticeValue[key] || noticeValue.data
      const sendTime = noticeValue.sendTime
      // 收到上课指令时，校验当前课件是否与接口返回最新课件一致，不一致则弹窗让退出重进
      key === 'classmode' &&
        checkCourseWareIdChange(key, {
          [key]: noticeContent
        })

      // 辅导连麦开启后
      // 阻止: 视频连麦、集体发言、拍照上墙、随机连麦、多人上台
      if (
        this.tutorVideoLinkStatus &&
        (key === 'video_mic' ||
          key === 'speech_interact' ||
          key === 'random_video_mic' ||
          key === 'mult_video_mic')
      ) {
        return
      }
      // 处理课件切换消息
      if (key === 'canvas_switch_courseware') {
        // @log-ignore
        messageCenter.emit('player', {
          type: 'canvasSwitchCourseware',
          data: noticeContent
        })
        return
      }

      console.log('interact测试日志：', '存储列表：', this.interactionMap)
      const keyContent = this.interactionMap.get(key)
      console.log(
        'interact测试日志：',
        '获取对应存储数据：',
        keyContent,
        keyContent?.ircMsg?.sendTime
      )
      console.log('interact测试日志：', '收到的消息', noticeValue, sendTime)

      if (keyContent && sendTime === keyContent.ircMsg.sendTime) {
        console.log('interact测试日志：', '抛弃消息', this.interactionMap)

        // 通过发送时间判断，重复收到相同已经渲染的互动，抛弃处理
        logger.send({
          tag: 'irc',
          level: 'error',
          content: {
            msg: '收到和已存在互动相同的消息'
          }
        })
        return
      }
      const logStr = `收到KV消息(${isHistory ? '历史' : '实时'}) => ${key}`
      if (key !== 'canvas_switch_courseware') {
        logger.send({
          tag: 'irc',
          content: {
            msg: logStr,
            params: noticeContent
          }
        })
      }

      // 倒计时
      if (key === 'countDown') {
        Vue.prototype.$bus.$emit('room.countDown', noticeContent)
        return
      }
      // 送礼物互动弹幕消息交互
      if (key === 'sendGiftBarrage') {
        // 如果礼物互动开启中，接收礼物弹幕消息
        const interaction = this.interactionMap.get('openGift')?.instance
        interaction && interaction.vm.receiveGiftBarrage(noticeContent)
        return
      }
      // 更新投票比例
      if (key === 'vote_data') {
        // 如果投票互动开启中，调用更新投票数据
        const interaction = this.interactionMap.get('vote')?.instance
        interaction && interaction.vm.getVoteStatistics(noticeContent)
        return
      }
      // 处理视频连麦互动
      if (key === 'video_mic') {
        // 如果连麦已开始, 则后续举手和连麦消息直接发送给组件, 防止组件销毁重新渲染, 导致组件数据丢失
        const interaction = this.interactionMap.get('video_mic')?.instance
        if (interaction) {
          interaction.vm.videoLinkMessage(noticeContent)
          if (!noticeContent.open) {
            this.destroy(key)
          }
          return
        }
      }
      // 处理辅导连麦
      if (key === 'video_mic_f') {
        Vue.prototype.$bus.$emit('room.tutorVideoLink', noticeContent, ({ status }) => {
          console.log('tutorVideoLink-status', status)
          this.tutorVideoLinkStatus = status
        })
        return
      }
      // 处理课堂表扬消息
      if (key === 'classroom_praise') {
        if (noticeContent.pub) {
          Vue.prototype.$bus.$emit('room.showPraiseTreasure', noticeContent)
          return
        }
      }
      // 处理定向金币消息
      if (key === 'distribute_coins') {
        if (noticeContent.pub) {
          Vue.prototype.$bus.$emit('room.orientationCoins', noticeContent)
          return
        }
      }
      // 处理订正过程 拍照上墙 整个过程结束
      if (key === 'praise') {
        return Vue.prototype.$bus.$emit('endCorrectPhotoWall', noticeContent, isHistory)
      }

      // 处理小班排行榜
      if (key === 'student_rank') {
        if (noticeContent) {
          Vue.prototype.$bus.$emit('room.smallClassRank', noticeContent)
          return
        }
      }

      // 小班语音控制
      if (key === 'video_bet_student') {
        if (noticeContent.pub) {
          Vue.prototype.$bus.$emit('remoteAudioStatus', true)
        } else {
          Vue.prototype.$bus.$emit('remoteAudioStatus', false)
        }
        return
      }

      // 多人上台, 互动中消息
      if (key === 'mult_video_mic') {
        const interaction = this.interactionMap.get('mult_video_mic')?.instance
        if (interaction && noticeContent.pub && noticeContent.status != 1 && !isHistory) {
          interaction.vm.receiveMessage(noticeContent)
          return
        }
      }

      // 老师上台
      if (key === 'teacher_video_mic') {
        Vue.prototype.$bus.$emit('teacherOnStage', noticeContent)
      }
      // 私聊
      if (key === 'graffiti_board_video') {
        if (noticeContent?.userId != this.options.roomInfo.commonOption.stuId) {
          Vue.prototype.$bus.$emit('teacherOnPrivateChat', noticeContent)
        } else {
          noticeContent.status = 1
          Vue.prototype.$bus.$emit('teacherOnStage', noticeContent)
        }
        Vue.prototype.$bus.$emit('changeAudioStatus', noticeContent)
      }

      // 课中考试消息
      if (key === 'class_examination') {
        Vue.prototype.$bus.$emit('classExamination', noticeContent)
      }
      if (key === 'red_packet_rain') {
        const { pub, action } = noticeContent
        const storeVm = this.interactionMap.get(key)

        if (pub) {
          if (storeVm && action === 'start') {
            errorLog('红包雨-红包雨已经开启')
            return
          }
          if (action === 'play') {
            const interactId = localStorage.getItem('redPacketRainKey')
            const status = localStorage.getItem('redPacketRainStatus')
            if (interactId === noticeContent.interactId && status === 'end') {
              if (storeVm) this.destroy(key)
              errorLog('红包雨-重复互动')
              return
            }
            if (storeVm && (status === '' || status === 'start')) {
              storeVm.instance.vm.play()
              errorLog('红包雨-初始化后开始玩红包雨')
              return
            }
            const nowTime = this.options.roomInfo.commonOption.timeOffset + +new Date()
            console.log(
              'redpacketRainTime',
              this.options.roomInfo.commonOption.timeOffset,
              nowTime,
              noticeValue.sendTime
            )
            if (nowTime > noticeValue.sendTime + 10000) {
              errorLog('红包雨-超时', {
                timeOffset: this.options.roomInfo.commonOption.timeOffset,
                localTime: +new Date(),
                sendTime: noticeValue.sendTime
              })
              return
            }
            const hasReport = await getRedPacketRainStatus(this, {
              interactId: noticeContent.interactId,
              planId: this.options.roomInfo.commonOption.planId,
              userId: this.options.roomInfo.commonOption.stuId
            })
            if (hasReport) {
              errorLog('红包雨-已参与')
              return
            }
          }
        }
      }


      // 处理互动消息
      // publishTopic 是答题板私有控制，答题板的关闭，是由端上的倒计时决定的。
      if (interactionList[key]) {
        const { pub, open, publishTopic } = noticeContent
        // 处理其它互动
        if (pub || open || publishTopic) {
          // 课堂表扬榜
          if (key === 'class_praise_list' && noticeContent.isUpdate) {
            if (isHistory) {
              // 如果是自定义榜单历史恢复，不需要再执行一次 update_praise_list
              await this.render({ key, noticeContent, isHistory, sendTime })
            } else {
              Vue.prototype.$bus.$emit('update_praise_list', noticeContent)
            }
            return
          }
          // 保证相同类型互动不重复开启
          const keyContent = this.interactionMap.get(key)
          if (keyContent) {
            // 由于新互动开启的销毁，课件互动题不进行提交
            this.destroy(key, false)
          }
          this.render({ key, noticeContent, isHistory, sendTime })
        } else if (
          pub === false ||
          open === false ||
          pub === 0 ||
          open === 0 ||
          publishTopic === false
        ) {
          this.destroy(key)
        }
      }
    })
  }
  /**
   * 互动添加额外的参数
   */
  addExtraTimeParams(noticeValue) {
    this.interactionContent.sendTime =
      noticeValue.sendTime < window._requestBasicEnterServerTime * 1000
        ? window._requestBasicEnterServerTime * 1000 +
        (new Date().getTime() - window._requestBasicTime)
        : noticeValue.sendTime
    this.interactionContent.getIrcLocalTime = new Date().getTime()
  }

  /**
   * @description 渲染互动
   */
  async render({ key, isHistory, noticeContent, sendTime }) {
    const interactionComponent = this.interactionList[key]
    if (interactionComponent) {
      let params = {
        roomMessage: this.options,
        ircMsg: noticeContent,
        isHistory
      }
      const Interaction = await interactionComponent()
      const interactionVM = new Interaction.default(params)
      // 互动存储
      this.interactionMap.set(key, {
        instance: interactionVM,
        ircMsg: {
          ...noticeContent,
          sendTime
        }
      })
    } else {
      console.error('未实现这种互动')
    }
  }

  /**
   * @description 销毁互动
   */
  destroy(key, isNeedSubmit = true) {
    const interactionObj = this.interactionMap.get(key)
    if (!interactionObj) return

    const interaction = interactionObj.instance
    const ircMsg = interactionObj.ircMsg
    /**
     * 如果后续还有互动需要由互动层自主关闭
     * 请在各自的互动里添加 [ destroyInteraction ] 私有方法进行销毁互动
     */
    this.interactionMap.delete(key)
    if (interaction.vm.destroyInteraction) {
      interaction.vm.destroyInteraction({
        ircMsg,
        isNeedSubmit
      })
      return
    }
    interaction.destroy({
      options: this.options,
      submit: this.submit,
      ircMsg
    })
  }
}
