/**
 * @description 互动注册中心
 * https://wiki.zhiyinlou.com/pages/viewpage.action?pageId=31030405
 **/

/**
 * @description 互动配置文件
 */
const interactionList = {
  redPacket: () => import('components/Classroom/CommonInteractions/redPacket/index.js'), // 红包
  red_packet_rain: () => import('components/Classroom/CommonInteractions/redPacketRain/index.js'), // 红包雨
  openGift: () => import('components/Classroom/CommonInteractions/gift/index.js'), // 礼物
  vote: () => import('components/Classroom/CommonInteractions/vote/index.js'), // 投票
  classRest: () => import('components/Classroom/CommonInteractions/classRest/index.js'),
  video_mic: () => import('components/Classroom/CommonInteractions/videoLink/index.js'), // 视频连卖
  random_video_mic: () =>
    import('components/Classroom/CommonInteractions/randomVideoLink/index.js'), // 随机点名连麦
  mult_video_mic: () => import('components/Classroom/CommonInteractions/multVideoLink/index.js'), // 多人上台
  interact: () => import('components/Classroom/CommonInteractions/coursewareBoard/index.js'), // h5课件互动题
  game_interact: () => import('components/Classroom/CommonInteractions/gameCourseware/index.js'), // 游戏课件
  speech_interact: () =>
    import('components/Classroom/CommonInteractions/collectiveSpeech/index.js'), // 集体发言
  take_picture: () => import('components/Classroom/CommonInteractions/photoWall/index.js'), // 拍照上墙
  fill_blank: () => import('components/Classroom/CommonInteractions/fillBlanks/index.js'), // 填空
  random_call: () => import('components/Classroom/CommonInteractions/randomCall/index.js'), // 随机点名
  graffiti_board: () => import('components/Classroom/CommonInteractions/graffiti/index.js'), // 涂鸦
  class_praise_list: () =>
    import('components/Classroom/CommonInteractions/classPraiseList/index.js'), // 课堂表扬榜
}

const chatMsgPriority = {
  topic: 0,
  notice: 1,
  privMsg: 99
}

const exceptionStartInteractionLeanplum = ['classRest']
const exceptionShowInteractionLeanplum = ['classRest', 'game_interact', 'speech_interact']
const exceptionEndInteractionLeanplum = ['classRest']

export {
  chatMsgPriority,
  interactionList,
  exceptionStartInteractionLeanplum,
  exceptionShowInteractionLeanplum,
  exceptionEndInteractionLeanplum
}
