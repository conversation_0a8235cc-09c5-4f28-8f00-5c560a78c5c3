<template>
  <div ref="ppt-area" class="ppt-area-container" id="ppt-area-container">
    <div v-if="showCanvas && !isInClass" class="wait-teacher">
      <div class="wait-dialog">
        <div class="notice-content">
          {{ $t('classroom.largeClass.coursewareBoard.classSoonNotice') }}
        </div>
      </div>
    </div>

    <H5Courseware
      ref="h5CoursewareMian"
      :ircconfig="ircconfig"
      @courseWareReady="courseWareReady"
      @changePageId="changePageId"
      @clear="clear"
      :options="options"
      @getCoursewareRate="getCoursewareRate"
    >
    </H5Courseware>

    <white-board-canvas ref="WhiteBoard" v-if="showCanvas" />
    <white-board-tools ref="WhiteBoardTools" v-if="showCanvas" />

    <!-- h5考试exam -->
    <div class="exam-container" ref="exam-wrapper" v-if="showEaxmIframe">
      <iframe @onload="handleIframeLoad" id="class-examination" :src="examUrl" />
    </div>
  </div>
</template>
<script>
import H5Courseware from '../h5courseware'
import { getRatioScale } from 'utils/util'
import _get from 'lodash/get'
import _debounce from 'lodash/debounce'
import bindEventMixin from '@/mixins/h5BindEvent.js'
import SignalService from '@/LargeClass/base/signal-service'
import WhiteBoard from '@thinkacademy/new-white-board'
import '@thinkacademy/new-white-board/lib/index/style.css' // 样式文件单独引入
import WhiteBoardTools from '@thinkacademy/white-board-tools'
import '@thinkacademy/white-board-tools/lib/index/style.css' // 样式文件单独引入
import logger from 'utils/logger'
import { getCanvasScale } from 'utils/canvasScale'
import store from '@/store'
import { getHistoryMessage } from 'core/getHistoryMsg'
import { getLanguage } from 'utils/language'
export default {
  name: 'ppt-area',
  components: {
    H5Courseware,
    WhiteBoardCanvas: WhiteBoard.WhiteBoardCanvas,
    WhiteBoardTools
  },
  props: ['ircconfig', 'isInClass', 'options', 'configs'],
  data() {
    return {
      pageId: 0,
      showCanvas: false,
      examUrl: '',
      showEaxmIframe: false, // 是否展示考试弹窗
      useInfo: window.localStorage.getItem('userInfo'),
      isBanned: false,
      currentThickness: 4, // 初始化笔迹大小
      currentLineDashed: false, // 是否带有笔锋
      eraserSize: 50, // 橡皮擦直径
      containerWidth: 0, // 涂鸦区域初始宽高
      containerHeight: 0,
      currentCourseWareData: {},
      currentDbKey: '',
      canvasScale: 2,
      coursewareRate: 1 // 默认4:3
    }
  },
  mixins: [bindEventMixin],
  async mounted() {
    this.canvasScale = await getCanvasScale()
    this.$nextTick(() => {
      this.init()
      window.addEventListener('resize', this.setCanvasScale)
      this.bindEvent()
    })
  },
  destroy() {
    this.$bus.$off('classExamination')
    window.removeEventListener('resize', this.setCanvasScale)
  },
  beforeDestroy() {
    if (this.$refs.WhiteBoard) {
      this.$refs.WhiteBoard.uninit()
    }
  },
  watch: {
    // 课件加载完成后初始化涂鸦
    showCanvas(val) {
      if (val) {
        this.$nextTick(() => {
          this.initBoard()
        })
      }
    }
  },
  methods: {
    init() {
      this.listenerEvent()
    },
    /**
     * 初始化涂鸦
     */
    initBoard() {
      // 获取当前画布大小并传到canvas中
      this.getCurrentCanvasSize()
      const params = {
        canvas: {
          penType: 'Bspline',
          strokeType: 'none',
          scale: this.canvasScale,
          pageChangeReport: false,
          sendRoomCanvasMessage: this.sendRoomCanvasMessage.bind(this),
          getHistoryMessage: this.getHistoryMessage.bind(this),
          enableKeyboardDelete: true,
          enableFittingShape: false // 笔记自动变为直线关闭
        },
        common: {
          liveId: String(this.options.planInfo.id),
          role: 'student',
          dataVersion: '1',
          courseId: '',
          userId: this.options.stuId + '',
          userName: this.options.nickName, // 用于展示的用户昵称
          screenWidth: this.containerWidth, // 屏幕宽度
          screenHeight: this.containerHeight, // 屏幕高度 Init
          roomIds: [this.options.roomlist[0]],
          fastFrequency: 2000,
          slowFrequency: 3000,
          serverTimestamp: +new Date() + this.options.timeOffset
        },
        accessControl: {
          showMenu: false,
          mode: 'itsAndCanvas',
          isBanned: this.isBanned,
          enableHistoryMessage: true,
          showCursor: true,
          enableRetrySend: true,
          enableLogSend: false,
          enableFittingShape: false // 笔记自动变为直线关闭
        }
      }
      console.log('涂鸦初始化参数', params)
      this.sendLogger(`大班涂鸦初始化参数params: ${JSON.stringify(params)}`)
      if (this.$refs.WhiteBoard) {
        this.$refs.WhiteBoard.uninit()
        this.$refs.WhiteBoard.init(params)
        this.$refs.WhiteBoard.handleMouse('default')
        let mainBoardHandWritting = this.$refs.WhiteBoard.getMainBoardHandWritting()
        let pluginManager = this.$refs.WhiteBoard.getPluginManager()
        let whiteboard = this.$refs.WhiteBoard
        // 初始化尺规工具
        this.$refs.WhiteBoardTools.handleMenuEnable(false) // 隐藏原生尺规菜单
        this.$refs.WhiteBoardTools.init(whiteboard, mainBoardHandWritting, pluginManager)
        // 消息注册 11: 直尺，12:三角板 30度，13:圆规，14:三角板 45度， 15:量角器，1000: 工具缩放大小
        const registerData = [11, 12, 13, 14, 15, 1000]
        registerData.forEach(key => {
          if (key === 1000) {
            pluginManager.registerEvent(
              'WhiteboardResize',
              key,
              this.toolsMessageHandler('WhiteboardResize', key)
            )
          } else {
            pluginManager.registerEvent(
              'ReceiveBinaryData',
              key,
              this.toolsMessageHandler('ReceiveBinaryData', key)
            )
          }
        })
        this.$refs.WhiteBoardTools.setWBToolsStatus(false) // 设置学生权限不可操作尺规
        if (this.currentDbKey) {
          this.initCatalogueInfo(this.currentDbKey)
        }
      }
    },
    // 处理尺规的同步，拉取历史数据同步， 翻页同步， 正常同步
    toolsMessageHandler(actionType, toolType) {
      return msg => {
        this.$refs.WhiteBoardTools.receiveBinaryData(actionType, toolType, this.pageId, msg)
      }
    },
    initCatalogueInfo(dbKey) {
      let courseInfo = [
        {
          index: 0,
          isHide: 0,
          pageId: this.pageId,
          title: '标题',
          type: 'course'
        }
      ]
      try {
        // 拉取老师涂鸦数据
        if (this.$refs.WhiteBoard) {
          this.$refs.WhiteBoard.handleCatalogueChange(courseInfo)
          this.$refs.WhiteBoard.handleResetImComingDbkey(dbKey)
          this.$refs.WhiteBoard.handlePageChange(String(this.pageId))
          this.$refs.WhiteBoardTools.handlePageChange(String(this.pageId))
        }
      } catch (err) {
        console.error('initCatalogueInfo中JSON.parse异常捕获', err)
      }
    },
    sendRoomCanvasMessage(roomId, canvasMessage) {
      // @log-ignore
      const ChatClient = window.ChatClient
      ChatClient.RoomChatManager.sendRoomBinMessage(
        [this.options.roomlist[0]],
        canvasMessage.dbKey,
        canvasMessage.keyMsgId,
        canvasMessage.content
      )
      console.log('sendRoomCanvasMessage', canvasMessage)
      this.sendLogger(`sendRoomCanvasMessage: ${JSON.stringify(canvasMessage)}`)
    },
    // 发送拉取历史数据请求
    getHistoryMessage(data) {
      const { dbKey } = data['info'][0]
      console.log('大班课获取历史消息', this.configs, this.ircconfig, this.options)
      this.sendLogger(`发送消息申请拉取数据dbkey: ${dbKey}`)
      getHistoryMessage({
        dbkey: dbKey,
        appId: this.configs.ircAk,
        sk: this.configs.ircSk,
        businessId: 3,
        liveId: this.options.planInfo.id,
        ircApiHost: this.configs.ircApiHost
      })
        .then(res => {
          try {
            this.$refs.WhiteBoard &&
              this.$refs.WhiteBoard.handleRecoverHistoryMessage([{ content: res }])
          } catch (e) {
            console.error(e)
            this.sendLogger(`涂鸦sdk报错 ${e}`)
          }
        })
        .catch(err => {
          console.log('获取历史消息err', err)
        })
    },
    // 处理接收到的涂鸦消息
    handleRoomCanvasMessage(res) {
      // @log-ignore
      // this.sendLogger(`处理接收到的实时涂鸦消息: ${res.dbKey}, ${res.msgId}}`)
      this.$refs.WhiteBoard && this.$refs.WhiteBoard.handleRoomCanvasMessage(res)
    },
    // 发送涂鸦消息回调
    onSendRoomBinMessageResp(res) {
      if (res.code === 0) {
        this.$refs.WhiteBoard &&
          this.$refs.WhiteBoard.handleSendMessageSuccess(res.dbKey, res.keyMsgId)
      } else {
        this.$refs.WhiteBoard &&
          this.$refs.WhiteBoard.handleSendMessageError(res.code, res.msg, res.dbKey, res.keyMsgId)
      }
    },
    // 处理涂鸦历史消息
    onGetRoomHistoryBinMessageNotice(res) {
      this.sendLogger(`处理拉取到的涂鸦历史消息`)
      const newData = res
      if (Array.isArray(newData)) {
        newData.forEach((element, index) => {
          newData[index].content = Uint8Array.from(element.content)
        })
        console.log('newData1', newData)

        // 加try-catch 为了解决涂鸦sdk报错问题,他们暂时不给解决
        try {
          this.$refs.WhiteBoard &&
            this.$refs.WhiteBoard.handleRecoverHistoryMessage([{ content: newData }])
        } catch (e) {
          console.error('涂鸦sdk报错', e)
        }
      } else {
        console.error('涂鸦历史消息格式错误')
      }
    },
    // 监听开始考试
    listenerEvent() {
      this.$bus.$on('classExamination', async examinationInfo => {
        logger.send({
          tag: 'student.Interact',
          content: {
            msg: '收到课中考试互动',
            examinationInfo
          }
        })
        console.log('课中考试', examinationInfo)
        // pub: true代表考试相关互动，false结束考试互动
        if (!examinationInfo.pub) {
          // 主讲端关闭考试窗口，则学生端直接关闭iframe不需要通知h5
          this.showEaxmIframe = false
          this.examUrl = '' // 关闭iframe后清空url，再次打开考试后重新加载新的url
          this.$bus.$emit('setExaminationStatus', false)
        } else {
          this.$bus.$emit('setExaminationStatus', true) // 开始考试时将其他学生摄像头变为头像
          const timeOffset = this.options.timeOffset // 获取服务器与本机时间差值
          const localTime = +new Date() // 本机时间
          const localRealTime = parseInt((localTime + timeOffset) / 1000)

          // 判断学生是否迟到(精确到秒)  isLate: 0 未迟到，1迟到。服务器当前时间 - 考试考试时间 >1分钟为迟到
          const isLate = localRealTime - examinationInfo.beginTime - 60
          // 剩余考试时间 = 考试开始时间 + 考试总时长 - 当前时间
          let remainSeconds = examinationInfo.beginTime + examinationInfo.totaltime - localRealTime

          const token = this.useInfo ? JSON.parse(this.useInfo).unifiedAccessToken : ''
          // status=1: 开始考试
          // 开始考试时如果未迟到，则剩余考试时间=总时长
          if (examinationInfo.status === 1 && isLate <= 0) {
            remainSeconds = examinationInfo.totaltime
          }
          if (!this.examUrl) {
            // 判断examUrl是否有值是为了防止status变化时，examUrl会变化
            // 课堂相关信息 传source=pcLargeClass目的：大班报告按钮会被导航条遮挡，需要h5调整
            const classInfo = `from=live&source=pcLargeClass&classId=${this.options.classId}&studentId=${this.options.stuId}&token=${token}`
            // 考试相关信息 completed=1提交作答，0未提交
            const testInfo = `duration=${examinationInfo.totaltime}&completed=${
              examinationInfo.status === 3 ? 1 : 0
            }&isLate=${isLate > 0 ? 1 : 0}&remainSeconds=${remainSeconds}`
            // iframe拼接url参数  platform=3表示pc端
            const language = await getLanguage()
            this.examUrl = `${examinationInfo.examUrl}&platform=3&language=${language}&${classInfo}&${testInfo}`
            window.thinkApi.ipc.send('test:url', this.examUrl)
          }
          this.showEaxmIframe = true // 打开iframe答题弹窗
          // status=2: 更改考试时间
          if (examinationInfo.status === 2) {
            // 传递获取剩余考试时间
            this.$nextTick(() => {
              document.getElementById('class-examination').contentWindow.postMessage(
                {
                  type: 'updateRemainSecondsTo',
                  data: {
                    remainSeconds: remainSeconds
                  }
                },
                '*'
              )
            })
          }
          // status=3: 老师收卷结束考试
          else if (examinationInfo.status === 3) {
            this.$nextTick(() => {
              document.getElementById('class-examination').contentWindow.postMessage(
                {
                  type: 'completeExam'
                },
                '*'
              )
            })
          }
        }
      })
    },
    // 监听学生交卷时，发送给教师端
    sendSubmitEaxmToTeacher() {
      const opts = {
        roomlist: this.options.roomlist,
        content: {
          type: 128, // 学生交卷后给主讲发送消息
          name: this.options.nickName,
          msg: '',
          submit: true
        },
        chatMsgPriority: 99 // 私聊类型
      }
      if (this.options.classType !== 2) {
        // 大班发送消息
        SignalService.sendRoomMessage(opts)
      } else {
        // 真小班发送消息
        this.thinkClass.SignalService.sendRoomMessage(opts)
      }
    },

    /**
     * 课件准备妥当
     */
    courseWareReady(flag) {
      this.showCanvas = flag
      if (flag) {
        this.$nextTick(() => {
          this.setCanvasScale()
        })
      }
      logger.send({
        tag: 'init',
        content: {
          msg: `课件准备状态${flag}`
        }
      })
    },
    // 获取课件比例
    getCoursewareRate(rate) {
      this.coursewareRate = rate
      this.setCanvasScale()
    },

    /**
     * 设置涂鸦层缩放
     * 为什么要缩放，因为课件的比例是4:3, 要保证涂鸦完全在课件区域
     */
    setCanvasScale: _debounce(function() {
      if (!this.showCanvas) return
      // 这里增加setTimeout是为了防止本次缩放的dom还没渲染完成，导致获取的课件宽高是上次的宽高
      setTimeout(() => {
        this.getCurrentCanvasSize()
        this.$refs.WhiteBoard &&
          this.$refs.WhiteBoard.handleResizeCanvas(this.containerWidth, this.containerHeight, true)
      }, 0)
    }, 300),
    // 获取当前课件区域的宽高
    getCurrentCanvasSize() {
      const container = document.getElementById('ppt-area-container')
      // 获取当前课件区域的宽高
      this.containerWidth = Math.round(
        Number(window.getComputedStyle(container).width.replace('px', ''))
      )
      // 当窗口缩放拉长时，课件高度没有占据全屏，上下会留白，导致获取的canvas高度和位置不对，因此通过4:3的比例获取课件区域实际高度，并获取距顶部距离
      // 总高度
      let height = Math.round(Number(window.getComputedStyle(container).height.replace('px', '')))
      // 通过宽度计算正确高度
      const heightFor16b9 = (this.containerWidth * 9) / 16 // 如果是16: 9的课件计算16: 9的高
      this.containerHeight = (this.containerWidth * 3) / 4
      // 课件区域距顶部距离 =（总高度 - 课件高度）/ 2
      let marginTop = (height - this.containerHeight) / 2
      let marginLeft = 0
      // 如果是16:9的课件则判断课件的计算基准，如果宽高比小于16:9则宽为基准，如果大于16:9则以高为基准，但涂鸦区域的计算一直是4:3
      if ((this.coursewareRate != 2 && marginTop < 0) || height < heightFor16b9) {
        console.log('通过高度计算宽度')
        // 缩放时宽:高 = 4:3 由于宽度太长，导致高度会超出了屏幕大小，因此需要以高度为基准，计算宽度
        let width = (height * 4) / 3
        this.containerHeight = height
        marginLeft = (this.containerWidth - width) / 2
        marginTop = 0
        this.containerWidth = width
      }
      this.$nextTick(() => {
        // 设置canvas距顶部距离
        const wrapperDom = document.querySelector('.ppt-area-container .container')
        wrapperDom.style.top = `${marginTop}px`
        wrapperDom.style.left = `${marginLeft}px`
        // 缩放时设置涂鸦画板位置
        const photoWallDom = document.querySelector('.showPhotoWall')
        if (photoWallDom) {
          photoWallDom.style.top = `${marginTop}px`
          photoWallDom.style.left = `${marginLeft}px`
          photoWallDom.style.width = `${this.containerWidth}px`
          photoWallDom.style.height = `${this.containerHeight}px`
        }
        // 缩放时设置黑板大小
        const blackBoardImgDom = document.querySelector('.blackBoardImg')
        if (blackBoardImgDom) {
          blackBoardImgDom.style.top = `${marginTop}px`
          blackBoardImgDom.style.left = `${marginLeft}px`
          blackBoardImgDom.style.width = `${this.containerWidth}px`
          blackBoardImgDom.style.height = `${this.containerHeight}px`
        }
      })
      console.log('获取当前课件区域的宽高', this.containerWidth, this.containerHeight)
      this.sendLogger(
        `获取当前课件区域的宽高(宽：${this.containerWidth}, 高：${this.containerHeight}）`
      )
    },
    clear() {},
    changePageId(id, currentCourseWareData) {
      // @log-ignore
      this.pageId = id
      this.currentCourseWareData = currentCourseWareData
      const { specificLiveKey, courseWareId, pageId } = currentCourseWareData
      if (!specificLiveKey || !courseWareId || !pageId) {
        console.error('课件信息错误', currentCourseWareData)
        return
      }
      const dbKey = `${specificLiveKey}_${courseWareId}_${pageId}`
      this.currentDbKey = dbKey
      this.$nextTick(() => {
        this.initCatalogueInfo(dbKey)
      })
    },
    /**
     * 日志上报
     */
    sendLogger(msg) {
      logger.send({
        tag: 'largeClass-graffiti',
        content: {
          msg: msg
        }
      })
    },
    handleIframeLoad() {
      this.sendLogger(`加载H5成功-大班H5考试,${this.examUrl}`)
    }
  }
}
</script>
<style scoped lang="less">
.ppt-area-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .pointer {
    position: absolute;
    background: rgba(24, 144, 255, 1);
    border-radius: 50%;
    top: 0;
    left: 0;
    z-index: 999;
    display: none;
    width: 14px;
    height: 14px;
    border: 2px solid rgba(255, 255, 255, 0.8);
  }

  .pen-icon {
    position: absolute;
    left: 0;
    top: 0;
    width: 41px;
    z-index: 99;
  }

  .ppt-area-canvas {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: transparent;
    z-index: 9;
  }

  .ppt-area-wrapper {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1024px;
    height: 768px;
    transform: translate(-50%, -50%);
    background-color: transparent;
    z-index: 10;
  }

  .wait-teacher {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 600;
    background: url('../../assets/wait-bg.jpg');
    background-size: cover;

    .wait-dialog {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 310px;
      height: 205px;
      padding: 30px 30px 40px 30px;
      position: absolute;
      top: 50%;
      left: 50%;
      margin-top: -150px;
      margin-left: -155px;
      background: url('../../assets/wait-dialog-bg.png');
      background-size: cover;
    }

    .notice-content {
      font-size: 18px;
      color: #ff850a;
    }
  }

  .ppt-area-canvas-tuxing {
    background-color: transparent;
    z-index: 8;
  }

  .ppt-dialog-main {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;

    .dialog-container {
      position: absolute;
      top: 50%;
      left: 50%;
      margin-top: -60px;
      margin-left: -150px;
      width: 300px;
      height: 120px;
      background-color: white;
      box-shadow: 0px 4px 35px 0px rgba(0, 0, 0, 0.14);
      border-radius: 4px;
      text-align: center;
      padding: 16px;

      p {
        color: rgba(33, 40, 49, 1);
        padding: 16px 0 28px 0;
      }

      .ppt-cancle-button,
      .ppt-download-button {
        background-color: white;
        padding: 5px 20px;
        border-radius: 16px;
        color: black;
      }

      .ppt-download-button {
        background-color: red;
        padding: 5px 20px;
        border-radius: 16px;
        color: #ffffff;
      }
    }
  }

  .media-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .exam-container {
    width: 100%;
    height: 100%;

    iframe {
      width: 100%;
      height: 100%;
      border: none;
      background: white;
      position: relative;
      z-index: 998;
    }
  }

  /deep/.tools-canvas-menu {
    display: none;
  }
}

/*.ppt-area-container::-webkit-scrollbar { width: 10px !important }*/
</style>
