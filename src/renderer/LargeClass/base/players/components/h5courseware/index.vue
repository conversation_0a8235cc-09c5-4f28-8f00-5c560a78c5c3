<template>
  <div class="h5coursewareContainer" :class="{ topZindex: coursewareState === 'error' }">
    <!-- 课件外层loading -->
    <NeLoading :visible="coursewareState === 'loading'" />
    <NeDialog :visible="coursewareState === 'error'">
      <h3>Courseware failed to load</h3>
      <p>Your courseware failed to load.<br />Please click "Reload" to try again</p>
      <div slot="footer">
        <button class="button" @click="reloadCourseWare">Reload</button>
      </div>
    </NeDialog>

    <!-- 黑板 -->
    <img v-show="blackBoardImg" class="blackBoardImg" :src="blackBoardImg" />

    <!-- 图片上墙 -->
    <ShowPhotoWall :options="options" :photoWallImg="photoWallImg" v-show="photoWallImg" />

    <!-- 课件 -->
    <div ref="h5courseware" id="h5CoursewareContent" class="h5courseware"></div>
  </div>
</template>

<script>
import _debounce from 'lodash/debounce'
import { nativeApi } from 'utils/electronIpc'
import CoursewarePlayerFactory from '@thinkacademy/courseware-player'
import { messageCenter } from '@thinkacademy/live-framework/components/framework/message-center'

import { getCoursewareInfo } from 'api/h5courseware'
import NeLoading from './loading'
import NeDialog from './dialog'
import ShowPhotoWall from './showPhotoWall'

import logger from 'utils/logger'
import { getUrlParam } from 'utils/util'
import { checkCourseWare } from 'utils/courses'

export default {
  props: ['ircconfig', 'playback', 'options'],
  data() {
    return {
      coursewareInfo: null,
      coursewarePlayer: null,
      coursewareState: 'loading',
      blackBoardImg: null, // 黑板背景图
      photoWallImg: null, // 图片上墙的图片地址
      localUrl: null,
      coursewareStartLoadTime: 0,
      pageId: 0, // 当前课件页面
      currentCourseWareData: {} // 课件数据
    }
  },
  components: { NeLoading, NeDialog, ShowPhotoWall },
  async created() {
    const planId = getUrlParam('planId')
    // 获取课件本地SERVER信息
    const { CW_SERVER_ADDRESS, CW_WEBROOT } = await nativeApi.getClientInfo()
    const courseware = await getCoursewareInfo(planId * 1)
    if (courseware.code !== 0) {
      this.coursewareState = 'error'
      this.sendLogger(
        `接口返回错误`,
        {
          courseware
        },
        'error'
      )
      return
    }
    if (courseware.data.list.length === 0) {
      this.coursewareState = 'error'
      this.sendLogger(`接口返回课件为空`, 'error')
      return
    }
    courseware.data.list.forEach(coursewareItem => {
      if (coursewareItem.id == 16) {
        this.coursewareInfo = coursewareItem
        this.$emit('getCoursewareRate', this.coursewareInfo.detail.rate)
        localStorage.setItem('coursewareRate', this.coursewareInfo.detail.rate)
      }
    })
    // 没有主讲老师课件
    if (!this.coursewareInfo) {
      this.coursewareState = 'error'
      this.sendLogger(`没有主讲课件`, 'error')
      return
    }
    // 课件没有同步完成
    // 0未压缩，1压缩中，2压缩完成，3压缩失败
    if (this.coursewareInfo.detail.compressState !== 2) {
      this.coursewareState = 'error'
      this.sendLogger(
        `课件没有同步完成,status:${this.coursewareInfo.detail.compressState}`,
        'error'
      )
      return
    }

    const checkCourseWareResult = await checkCourseWare(
      this.coursewareInfo.detail.baseZipUrl,
      this.coursewareInfo.detail.baseZipMd5,
      CW_WEBROOT
    ) // 检查本地课件是否存在
    this.sendLogger('后端返回课件信息', {
      baseZipUrl: this.coursewareInfo.detail.baseZipUrl,
      baseZipMd5: this.coursewareInfo.detail.baseZipMd5
    })
    this.localUrl = checkCourseWareResult
      ? `${CW_SERVER_ADDRESS}${this.coursewareInfo.detail.catalog}/index.html`
      : ''
    this.sendLogger('课件加载地址', {
      localUrl: this.localUrl
    })
    // 初试化课件
    this.initCoursewareParams = {
      role: 'student',
      screenWidth: '100%', // 屏幕宽度
      screenHeight: '100%', // 屏幕高度
      itsId: this.coursewareInfo.detail.id, // 课件ID
      localUrl: this.localUrl,
      remoteUrl: [
        this.coursewareInfo.detail.compressIndexUrl,
        ...this.coursewareInfo.detail.compressIndexUrlSpareList
      ],
      onEvent: this.onEvent.bind(this),
      showPagePercent: 30,
      gameTip: this.$t('classroom.largeClass.coursewareBoard.gameConfig.gameTip'),
      gameToast: this.$t('classroom.largeClass.coursewareBoard.gameConfig.gameToast'),
      getLatestItsMessage: async cb => {
        let data
        try {
          data = data && JSON.parse(data)
          cb(data)
        } catch (e) {
          console.error('error latest message', data)
        }
      },
      getAllStoredData: this.getAllStoredData.bind(this)
    }
    this.$nextTick(() => {
      this.coursewarePlayer = CoursewarePlayerFactory.getPlayer('student', this.$refs.h5courseware)
      this.sendLogger('开始初始化课件', {
        itsId: this.coursewareInfo.detail.id, // 课件ID
        localUrl: this.localUrl,
        remoteUrl: this.initCoursewareParams.remoteUrl
      })
      this.coursewarePlayer.init(this.initCoursewareParams)
    })
    this.coursewareStartLoadTime = new Date().getTime()
  },
  mounted() {
    this.$nextTick(() => {
      // 监听课堂刷新事件,
      this.$bus.$on('liveRefresh', () => {
        this.sendLogger('刷新课件', 'warn')
        // 重新载入课件
        this.reloadCourseWare()
        // this.sendLogger('课中刷新', 'warn')
        // document.location.reload()
      })
      nativeApi.setWindowAble('resize', true) // 启用窗口大小调整能力
      // 0722版本 进入教室后全屏
      nativeApi.setWindowCenter() // 窗口居中
      nativeApi.setWindowAble('maximize', true) // 启用最大化能力
      nativeApi.setWindowAble('fullscreen', true) // 启用全屏能力
      nativeApi.setFullScreen(true) // 进入全屏
    })
  },
  methods: {
    /**
     * 重新加载课件
     */
    reloadCourseWare() {
      this.sendLogger('开始reload课件')
      if (this.coursewarePlayer) {
        this.sendLogger('重新开始初始化课件', 'warn')
        this.coursewarePlayer.init(this.initCoursewareParams)
      }
    },
    /**
     * 监听课件事件
     */
    onEvent(event, data) {
      // @log-ignore
      switch (event) {
        case 'error':
          this.handleError(data)
          break
        case 'loadingProgress': // 课件加载状态
          this.handleLoadingProgress(data)
          break
        case 'statusChange': // loading, loaded
          this.handleStatusChange(data)
          break
        case 'pageChangeToTeacher':
          // 学生端不需要这个时间
          break
        default:
          this.handleDefault(event, data)
          break
      }
    },
    handleDefault(event, data) {
      // @log-ignore
      if (event === 'catalogueChange') {
        this.sendLogger(
          '课件event',
          {
            event: event,
            data: data.pages.length
          },
          'warn'
        )
      } else {
        this.sendLogger(
          '课件event',
          {
            event: event,
            data: data
          },
          'warn'
        )
      }
    },
    // 加载课件错误
    handleError(data) {
      this.coursewareState = 'error'
      this.$emit('courseWareReady', false)
      this.sendLogger(
        '课件加载报错',
        {
          data: data
        },
        'error'
      )
    },
    // 课件加载进度监听(课件加载出来的页数达到某个值，就发出显示课件，不等课件全部加载完成。)
    handleLoadingProgress(data) {
      // @log-ignore
      this.sendLogger('课件加载中', {
        data: data
      })
      if ((data.loaded / data.total) * 100 >= this.initCoursewareParams.showPagePercent) {
        this.$emit('courseWareReady', true)
        this.coursewareState = 'loaded'
      } else {
        this.coursewareState = 'loading'
      }
    },
    // 课件加载状态变化
    handleStatusChange(data) {
      // @log-ignore
      this.sendLogger('课件状态改变', {
        data: data
      })
      this.$bus.$emit('corewareLoadStatus', data)
      if (data.status === 'loaded') {
        this.sendLogger('课件加载成功')
      }
    },

    getAllStoredData(callback) {},

    /**
     * 执行课件翻页操作
     * blackBoardType  0: 正常切换课件   1: 黑板   3: 图片上墙
     */
    handleSwitchCourseware(data) {
      // @log-ignore
      if (!data || !data.currentCourseWare) return
      this.currentCourseWareData = data.currentCourseWare
      const {
        blackBoardType,
        imgUrl,
        jsString,
        photoWallImageArray,
        pageId
      } = data.currentCourseWare
      this.$bus.$emit('photoWallShow', false)
      // 先清空所有的静态图片(黑板背景图、上墙图片)
      this.blackBoardImg = this.photoWallImg = ''
      // 显示黑板
      if (blackBoardType === 1 && imgUrl) {
        logger.send({
          tag: 'courseware',
          content: {
            msg: '显示黑板'
          }
        })
        this.blackBoardImg = imgUrl
      }
      // 图片上墙
      if (blackBoardType === 3 && photoWallImageArray) {
        this.photoWallImg = photoWallImageArray[0]
        this.$bus.$emit('photoWallShow', true) // 告诉作业盒子，现在正在上墙
        logger.send({
          tag: 'courseware',
          content: {
            msg: '图片上墙',
            photoWallImg: this.photoWallImg
          }
        })
      }
      // 正常切换课件
      if (blackBoardType === 0 && jsString) {
        this.coursewarePlayer && this.coursewarePlayer.handleRoomItsMessage(JSON.parse(jsString))
      }
      this.pageId = pageId // 更改当前页码
      this.$emit('changePageId', pageId, this.currentCourseWareData) // 告诉涂鸦层，页码改变了，绘制涂鸦
    },

    /**
     * 课件加载上报日志
     */
    sendLogger(msg = '', param, level = 'info') {
      if (typeof param === 'string') {
        level = param
      }
      logger.send({
        tag: 'coursewareLoad',
        content: {
          msg,
          param
        },
        level
      })
    }
  }
  // watch: {
  //   localUrl: {
  //     handler: (newUrl, oldUrl) => {
  //       // console.log(newUrl, 'newUrlnewUrlnewUrlnewUrlnewUrlnewUrlnewUrlnewUrl')
  //     }
  //   }
  // }
}
</script>

<style lang="scss" scoped>
.h5coursewareContainer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 7;

  &.topZindex {
    z-index: 1000;
  }

  .blackBoardImg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
  }
}

.h5courseware {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: url('~assets/images/live/bg.png');

  iframe {
    background: transparent;
    position: relative;
    z-index: 1;
  }

  ::v-deep .loading-contenter {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 100%;
  }
}

.h5courseware-loading {
  padding-top: 260px;
  z-index: 3;
}

::v-deep .gameDemoTip {
  background: rgba(0, 0, 0, 0.8);
  position: absolute;
  bottom: 10px;
  left: 10px;
  z-index: 100;
  min-height: 30px;
  padding: 15px 15px 15px 30px;
  border-radius: 10px;
  color: #fff;
  justify-content: center;
  align-items: center;
  text-align: center;
  line-height: 20px;
  font-size: 16px;
  font-weight: 500;
  display: none;

  &::before {
    position: absolute;
    left: 10px;
    top: 50%;
    width: 10px;
    height: 10px;
    background: #3370ff;
    content: '';
    transform: translate(0, -50%);
    border-radius: 10px;
  }
}

::v-deep .gameDemoToast {
  background: rgba(15, 25, 42, 0.95);
  position: absolute;
  max-width: 275px;
  top: 50%;
  left: 50%;
  z-index: 100;
  min-height: 30px;
  padding: 22px;
  border-radius: 10px;
  color: #fff;
  justify-content: center;
  align-items: center;
  text-align: center;
  line-height: 20px;
  font-size: 16px;
  transform: translate(-50%, -50%);
  font-weight: 500;
  display: none;
}
</style>
