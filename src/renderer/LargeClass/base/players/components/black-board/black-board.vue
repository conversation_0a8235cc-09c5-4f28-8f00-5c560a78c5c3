<template>
  <div class="black-board" :style="boardStyle" v-show="boardShowState">
    <canvas
      class="ppt-area-canvas ppt-area-canvas-tuxing"
      id="board-tuxing"
      ref="board-tuxing"
      :style="{ transform: `scale(${canvasScale})` }"
    ></canvas>
    <canvas
      class="ppt-area-canvas"
      id="board-canvas"
      ref="board-canvas"
      :style="{ transform: `scale(${canvasScale})` }"
    ></canvas>
    <div class="border" :style="borderStyle"></div>
  </div>
</template>
<script>
import ClasswareCanvas from '@thinkacademy/think-web-tboard'

export default {
  data() {
    return {
      classwareCanvasCtx: null,
      boardStyle: {},
      boardShowState: false,
      borderStyle: {},
      lastData: null,
      canvasScale: 1
    }
  },
  methods: {
    dispatchDrawEvents(type, data, ishistory = false) {
      if (type === 14) this.updateBoardStyle(data)
      if (type === 15) this.showBoard()
      if (type === 16) this.hideBoard()
      this.classwareCanvasCtx.dispatchDrawEvents(type, data, ishistory)
    },
    clearAll() {
      this.classwareCanvasCtx.clear()
      this.canvasScale = 1
    },
    showBoard() {
      this.boardShowState = true
    },
    hideBoard() {
      this.boardShowState = false
    },
    updateBoardStyle(data) {
      const { fillColor, strokeColor, height, width, x, y } = data.canvasInfo
      const boardStyle = {
        background: this.classwareCanvasCtx.getColor(fillColor),
        width: `${width * this.coursewareDom.width}px`,
        height: `${height * this.coursewareDom.height}px`,
        top: `${y * this.coursewareDom.height}px`,
        left: `${x * this.coursewareDom.width}px`
      }
      const borderStyle = {
        border: `6px solid ${this.classwareCanvasCtx.getColor(strokeColor)}`
      }

      this.lastData = data
      this.borderStyle = borderStyle
      this.boardStyle = boardStyle
      this.boardShowState = true
    },
    initCanvasCtx(width, height) {
      const tuxingCanvas = this.$refs['board-tuxing']
      const PenToolsDom = this.$refs['board-canvas']

      this.coursewareDom = {
        width,
        height
      }

      this.classwareCanvasCtx = new ClasswareCanvas({
        CommonGraphDom: tuxingCanvas,
        PenToolsDom,
        getLinePostion: this.getLinePostion
      })

      this.classwareCanvasCtx.initCanvasCtx(width, height)
    },
    refresh(scale = null) {
      console.log('refreshScale', scale)
      if (scale) this.canvasScale = scale
      if (this.boardShowState) {
        this.updateBoardStyle(this.lastData)
      }
    },
    resetCanvasSize(width, height) {
      if (width && height) {
        this.coursewareDom = {
          width,
          height
        }
        this.classwareCanvasCtx.resetCanvasSize(width, height)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.black-board {
  z-index: 10;
  overflow: hidden;
  position: absolute;
  left: 0;
  top: 0;
  box-sizing: border-box;
  .ppt-area-canvas {
    position: absolute;
    left: 0;
    top: 0;
    transform-origin: left top;
  }
  .border {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    box-sizing: border-box;
  }
}
</style>
