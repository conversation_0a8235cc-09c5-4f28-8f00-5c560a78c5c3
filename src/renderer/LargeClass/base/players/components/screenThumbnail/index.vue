<template>
  <!-- 由于更换了截屏按钮的位置，截屏的缩略图更改到课件区域显示 -->
  <div v-if="thumbnailBase64" class="thumbnail-wrapper" @click.stop="handleScreenshotPath">
    <div class="thumbnail-img">
      <img :src="thumbnailBase64" />
    </div>
    <div class="thumbnail-text">
      {{ $t('classroom.modules.screenThumbnail.successNotice') }}
    </div>
  </div>
</template>

<script>
import { nativeApi } from 'utils/electronIpc'
import { getScreenshotPath } from 'utils/settings'
let timer
export default {
  data() {
    return {
      thumbnailBase64: null
    }
  },
  mounted() {
    // 监听从顶部发来的截屏事件
    this.$bus.$on('screenThumbnail', thumbnailBase64 => {
      this.thumbnailBase64 = thumbnailBase64
      timer && clearTimeout(timer)
      timer = setTimeout(() => {
        this.thumbnailBase64 = null
      }, 3000)
    })
  },
  methods: {
    /**
     * 点击缩略图进入截屏目录
     */
    async handleScreenshotPath() {
      const path = await getScreenshotPath()
      nativeApi.openDirBySystem(path)
    }
  },
  beforeDestroy() {
    this.$bus.$off('screenThumbnail')
  }
}
</script>

<style scoped lang="scss">
.thumbnail-wrapper {
  position: fixed;
  z-index: 9999;
  right: 30%;
  top: 50px;
  width: 160px;
  height: 124px;
  border-radius: 6px;
  background: rgba(15, 25, 42, 0.88);
  .thumbnail-img {
    width: 160px;
    height: 88px;
    img {
      display: block;
      width: 160px;
      height: 88px;
      border-top-left-radius: 6px;
      border-top-right-radius: 6px;
    }
  }
  .thumbnail-text {
    height: 26px;
    line-height: 26px;
    text-align: center;
    font-size: 12px;
    font-family: 'SFProRounded-Medium', 'SFProRounded';
    font-weight: 500;
    color: #ffffff;
  }
}
</style>
