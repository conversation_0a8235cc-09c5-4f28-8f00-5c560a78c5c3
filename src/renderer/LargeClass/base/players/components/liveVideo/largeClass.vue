<template>
  <div class="live-video-wrapper">
    <div
      v-show="teacherVideoStatus"
      class="live-video-container"
      id="liveVideoContainer"
      ref="liveVideoContainer"
    ></div>
    <div class="live-bg" v-show="!teacherVideoStatus"></div>
    <div class="microphone-container" v-if="audioMuteStatus">
      <MicrophoneStatus />
    </div>
    <div v-if="teacherOnStageStatus" class="teacher-on-stage">
      <!-- <div class="wrapper">
        <div class="teacher-avatar">
          <img :src="teacherAvatar" />
        </div>
        <div class="stage-tips">On Stage</div>
      </div> -->
      <div class="stageTips">{{ $t('common.onStage') }}</div>
    </div>
  </div>
</template>

<script>
import RtcClass from './rtcClass'
import logger from 'utils/logger'
import MicrophoneStatus from './MicrophoneStatus'
import { RtcSensor } from 'utils/sensorEvent'

export default {
  name: 'liveVideo',
  components: {
    MicrophoneStatus
  },
  props: {
    options: {
      type: Object,
      default: () => {
        return {}
      }
    },
    rtcConfig: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    const { avatar: teacherAvatar } = this.options.teacherInfo
    return {
      audioMuteStatus: false, // 音频静音状态, true: 静音 false: 无静音
      teacherAvatar, // 老师头像
      teacherOnStageStatus: false, // 老师上台状态
      teacherVideoStatus: true, //视频开启状态
      teacherRtcSensor: null
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.rtcClass = new RtcClass({
        rtcConfig: this.rtcConfig
      })
      this.initTeacherRtcChannel()
      this.bindEvent()
    },

    /**
     * 事件绑定
     */
    bindEvent() {
      // 监听主讲音频启用/禁用事件
      this.$bus.$on('player.muteTeacherChannelAudio', status => {
        this.rtcClass.muteTeacherChannelAudio(status)
      })
      this.$bus.$on('stageLeavelChannel', () => {
        this.rtcClass.destroyVideo()
        console.log('remotechange--teacherstage')
        this.teacherVideoStatus = false
      })
      this.$bus.$on('teacherOnStageStatus', status => {
        this.handleTeacherOnStage(status)
      })
    },

    /**
     * 初始化主讲RTC频道
     */
    async initTeacherRtcChannel() {
      const teacherRtcChannel = await this.rtcClass.createTeacherRtcChannel()
      this.sendLogger('创建主讲rtc频道')
      // 监听远端视频流状态变化
      teacherRtcChannel.on('remoteVideoStateChanged', (uid, state, reason) => {
        // 接收到远端视频流首帧
        if (uid == this.rtcConfig.teacherVideoUid && state == 1) {
          this.sendLogger(
            `接收到主讲视频首帧, uid:${uid}, ${this.rtcConfig.teacherVideoUid} ${this.rtcConfig.teacherAudioUid} ${state} ${reason}`
          )
        }
        console.log(
          'remoteChange-remoteVideoStateChanged',
          uid,
          this.rtcConfig.teacherVideoUid,
          this.rtcConfig.teacherAudioUid,
          state,
          reason
        )
        // 判断远端用户为主讲端
        if (uid == this.rtcConfig.teacherVideoUid) {
          // 远端禁用视频
          if (reason === 5) {
            this.teacherVideoStatus = false
          }
          // 远端启用视频
          if (reason === 6) {
            this.teacherVideoStatus = true
          }
        }
      })

      // 监听远端音频流状态变化
      teacherRtcChannel.on('remoteAudioStateChanged', (uid, state, reason) => {
        // 判断远端用户为主讲端
        if (uid == this.rtcConfig.teacherAudioUid) {
          // 老师音频正常播放状态
          if (state === 2) {
            this.audioMuteStatus = false
          }
          // 主讲静音
          if (state === 0 && reason === 5) {
            this.audioMuteStatus = true
            this.sendLogger(`主讲端静音, uid: ${this.rtcConfig.teacherAudioUid}`)
          }
          // 主讲解除静音
          if (state === 2 && reason === 6) {
            this.audioMuteStatus = false
            this.sendLogger(`主讲端解除静音, uid: ${this.rtcConfig.teacherAudioUid}`)
          }
        }
      })

      // 监听远端用户加入频道
      teacherRtcChannel.on('remoteJoinChannel', uid => {
        this.sendLogger(`老师频道远端用户加入回调触发, uid: ${uid}`, {
          teacherAudioUid: this.rtcConfig.teacherAudioUid,
          teacherVideoUid: this.rtcConfig.teacherVideoUid
        })
        console.log('remoteChange--join', uid)

        if (uid == this.rtcConfig.teacherAudioUid) {
          // 主讲端进入频道
          this.$emit('updateLiveState', 1)
          this.sendLogger(`主讲端音频加入频道`)
        }
        if (uid == this.rtcConfig.teacherVideoUid) {
          this.teacherVideoStatus = true
          this.rtcClass.createVideo('liveVideoContainer')
          this.sendLogger(`主讲视频加入频道`)
        }
      })

      // 监听远端用户离开频道
      teacherRtcChannel.on('remoteLeaveChannel', uid => {
        console.log('remoteChange--leavel', uid)

        if (uid == this.rtcConfig.teacherAudioUid) {
          // 隐藏静音图标
          this.audioMuteStatus = false
          // 主讲离开频道
          this.$emit('updateLiveState', 0)
          this.sendLogger(`主讲端音频离开频道, uid: ${this.rtcConfig.teacherAudioUid}`)
        }
        if (uid == this.rtcConfig.teacherVideoUid) {
          this.teacherVideoStatus = false
          // 销毁主讲教师视频
          this.rtcClass.destroyVideo()
          this.sendLogger(`主讲视频端离开频道, uid: ${this.rtcConfig.teacherVideoUid}`)
        }
      })

      // 监听RTC状态
      teacherRtcChannel.on('rtcStats', stats => {
        // @log-ignore
        this.$bus.$emit('teacherRtcChannelStats', stats)
      })

      // 监听主讲频道网络连接状态
      teacherRtcChannel.on('connectionStateChanged', state => {
        console.log('[hw_rtc_join_room]主讲频道网络状态触发方法触发,code:', state)
        this.sendLogger(`主讲频道网络状态触发方法触发,code: ${state}`)
        if (state == 5) {
          this.teacherRtcSensor.rtcSensorPush({ result: 'fail', errorType: '连接失败' })
        } else if (state == 4) {
          this.teacherRtcSensor.isFirstJoinChannel = false
          this.teacherRtcSensor.rtcSensorPush({ result: 'start' })
        }
      })
      // 监听本地加入主讲频道成功
      teacherRtcChannel.on('localJoinChannel', () => {
        console.log('[hw_rtc_join_room]主讲频道本地加入成功')
        this.sendLogger(`本地加入主讲频道成功`)
        this.teacherRtcSensor.rtcSensorPush({ result: 'success' })
      })
      // 监听本地重连加入主讲频道
      teacherRtcChannel.on('rejoinChannelSuccess', () => {
        console.log('[hw_rtc_join_room]主讲频道重连本地加入成功')
        this.sendLogger(`本地重连加入主讲频道成功`)
        this.teacherRtcSensor.rtcSensorPush({ result: 'success' })
      })
      this.teacherRtcSensor = new RtcSensor() // 主讲频道神策埋点方法初始化
      console.log('[hw_rtc_join_room]加入主讲频道触发start')
      this.teacherRtcSensor.rtcSensorPush({ result: 'start' })
      const joinRes = teacherRtcChannel.joinChannel({
        autoSubscribeAudio: true,
        autoSubscribeVideo: true,
        publishLocalAudio: false,
        publishLocalVideo: false
      })
      // 添加加入频道日志
      if (joinRes == 0) {
        this.sendLogger(`调用加入主讲频道成功`)
      } else {
        console.log('[hw_rtc_join_room]调用加入主讲频道方法触发fail')
        this.sendLogger(`调用加入主讲频道失败,code:${joinRes}`, {}, 'error')
        this.teacherRtcSensor.rtcSensorPush({ result: 'fail', errorType: '调用加入房间接口失败' })
      }
    },

    /**
     * 处理老师上台逻辑
     */
    handleTeacherOnStage(status) {
      this.teacherOnStageStatus = status
      if (status) {
        this.rtcClass.destroyVideo()
        this.teacherVideoStatus = false
      } else {
        this.rtcClass.createVideo('liveVideoContainer')
        this.teacherVideoStatus = true
      }
    },

    /**
     * 日志上报
     */
    sendLogger(msg, parmas = {}, level = 'info') {
      logger.send({
        tag: 'liveVideo',
        level,
        content: {
          msg,
          parmas
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.live-video-wrapper {
  position: absolute;
  right: 0;
  top: 0;
  width: 27.5%;
  height: calc(246 / 739 * 100vh);
  .live-video-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    box-sizing: border-box;
    background: url('../../assets/no-camera.png') center;
    background-size: cover;
  }
  .live-bg {
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    box-sizing: border-box;
    background: url('../../assets/no-camera.png') center;
    background-size: cover;
    border-radius: 16px 16px 4px 4px;
  }
  .microphone-container {
    position: absolute;
    left: 4px;
    bottom: 4px;
    z-index: 1;
  }
  .teacher-on-stage {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
    width: 100%;
    height: 100%;
    background: url('../../assets/onstage.png') center;
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items: center;
    .wrapper {
      margin-top: -10px;
    }
    .teacher-avatar {
      width: 76px;
      height: 76px;
      border: 3px solid rgba(255, 255, 255, 0.2);
      border-radius: 38px;
      overflow: hidden;
      background: #fff;
      img {
        width: 70px;
        height: 70px;
      }
    }
    .stage-tips {
      position: relative;
      z-index: 1;
      width: 58px;
      height: 18px;
      margin: -11px 0 0 9px;
      line-height: 16px;
      background: linear-gradient(45deg, #62cdff 0%, #3370ff 100%);
      border-radius: 4px;
      border: 1px solid rgba(255, 255, 255, 0.5);
      color: #f3f7ff;
      font-size: 10px;
      text-align: center;
    }
  }
}
// 主讲视频LOGO水印
::v-deep.live-video-container {
  > div::after {
    content: ' ';
    position: absolute;
    left: 10px;
    top: 10px;
    z-index: 1;
    display: block;
    width: 24px;
    height: 30px;
    background: url('~assets/images/logo-video-mark.png') no-repeat;
    background-size: cover;
  }
}
.stageTips {
  bottom: 0px;
  position: absolute;
  background: #3370ff;
  border-radius: 4px;
  margin: 4px;
  left: 0px;
  right: 0px;
  color: #fff;
  padding: 4px 0 4px 10px;
}
</style>
