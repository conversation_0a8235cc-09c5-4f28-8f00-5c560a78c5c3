import { getDefaultAudioPlaybackDevice } from 'utils/deviceSettings'

export default class {
  constructor(opts = {}) {
    // @log-ignore
    this.rtcConfig = opts.rtcConfig
    this.teacherVideoUid = Number(this.rtcConfig.teacherVideoUid)
    this.teacherAudioUid = Number(this.rtcConfig.teacherAudioUid)
  }

  /**
   * 创建主讲RTC频道
   */
  async createTeacherRtcChannel() {
    const rtcEngine = window.RTC_COMMON.rtcEngine
    // 创建主讲教师频道
    const teacherRtcChannel = rtcEngine.createChannel()

    // 停止发送本地音视频流
    teacherRtcChannel.muteLocalAudioStream(true)
    teacherRtcChannel.muteLocalVideoStream(true)
    // 加入主讲教师频道
    // teacherRtcChannel.joinChannel()

    this.teacherRtcChannel = teacherRtcChannel
    window.RTC_COMMON.teacherRtcChannel = teacherRtcChannel
    return teacherRtcChannel
  }

  /**
   * 创建主讲视频
   * @param {String} element
   */
  createVideo(element) {
    this.teacherRtcChannel.setupRemoteVideo(this.teacherVideoUid, document.getElementById(element))
  }

  /**
   * 销毁主讲视频
   */
  destroyVideo() {
    this.teacherRtcChannel.destroyRemoteVideo(
      this.teacherVideoUid,
      document.getElementById(this.teacherVideoUid)
    )
  }

  /**
   * 启用/禁用主讲频道音频
   * @param {Boolean} mute
   */
  muteTeacherChannelAudio(mute) {
    this.teacherRtcChannel.muteAllRemoteAudioStreams(mute)
  }
}
