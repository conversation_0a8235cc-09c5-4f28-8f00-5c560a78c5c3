import Vue from 'vue'
import app from './rtc-live.vue'
import PlayerBase from '@thinkacademy/live-framework/components/base/players/player-base.js'
import { i18n } from 'locale'
import logger from 'utils/logger'

export default class RTCWebLive extends PlayerBase {
  // 1、把直播的video初始化
  // 2、把功能条初始化
  constructor(options = {}) {
    super(options)
    this.initPlayer(options.dom)
  }

  createVuePlayer(app) {
    const Constructor = Vue.extend(app)
    const propsData = this.createPlayerProps()
    const vm = new Constructor({
      i18n,
      propsData: propsData
    })
    vm.$mount()
    return vm
  }

  /**
   *
   * @param {*} dom
   * @description 初始化播放器
   */
  initPlayer(dom) {
    this.vm = this.createVuePlayer(app)
    this.render(dom, this.vm)
  }

  eventHandler(params) {
    console.log('rtc-live-eventHandler', params)
    const { type, data } = params

    const pptVm = this.vm.$refs.ppt
    const h5CourseWareVm = pptVm.$children[0]

    // 实时信令消息
    if (type === 'onRecvRoomBinMessageNotice') {
      pptVm.handleRoomCanvasMessage(data)
    }

    // 信令历史消息
    if (type === 'onGetRoomHistoryBinMessageNotice') {
      pptVm.onGetRoomHistoryBinMessageNotice(data)
    }

    // const courseVm = pptVm.$children[0]

    // console.log('lifeipptVm', this.vm, pptVm, courseVm, data)

    //  切换课件
    if (type === 'canvasSwitchCourseware') {
      h5CourseWareVm.handleSwitchCourseware(data)
      // courseVm.canvasSwitchCourseware(data)
    }
  }

  /**
   * 日志上报
   */
  sendLogger(msg, params) {
    logger.send({
      tag: 'rtc-live',
      content: {
        msg: msg,
        params: params
      }
    })
  }
}
