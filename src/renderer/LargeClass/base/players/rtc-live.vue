<template>
  <div class="live-main">
    <div class="live-ppt">
      <PptArea
        :options="commonOption"
        ref="ppt"
        :configs="configs"
        :ircconfig="ircconfig"
        :isInClass="isInClass"
      />
    </div>
    <MediaSecurityAccess :visible="true" />
    <LargeClassLiveVideo
      v-if="classType == 0 || classType == 1"
      :options="commonOption"
      :rtcConfig="configs.rtcConfig"
      @updateLiveState="updateLiveState"
    />
    <ScreenThumbnail />
  </div>
</template>

<script>
import PptArea from './components/ppt'
import MediaSecurityAccess from 'components/Common/MediaSecurityAccess'
import { i18n, i18nLocaleUpdate } from 'locale'
import LargeClassLiveVideo from './components/liveVideo/largeClass'
import ScreenThumbnail from './components/screenThumbnail'
import errorMsg from '@/mixins/useErrorMsg.js'
export default {
  components: {
    PptArea,
    MediaSecurityAccess,
    LargeClassLiveVideo,
    ScreenThumbnail
  },
  i18n,
  props: {
    configs: {
      type: Object,
      default: () => {
        return {}
      }
    },
    extraInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    commonOption: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  mixins: [errorMsg],
  data() {
    return {
      classType: this.commonOption.classType,
      ircconfig: null,
      liveState: 0, // 0: 离开频道 1: 进入频道
      videoErrorShowedMap: {},
      audioErrorShowedMap: {},
    }
  },
  computed: {
    isInClass() {
      return this.liveState === 1
    }
  },
  mounted() {
    this.ircconfig = this.configs
    this.addRtcListener()
    i18nLocaleUpdate()
  },
  methods: {
    /**
     * 更新状态
     */
    updateLiveState(state) {
      console.log('rtc-live-updateLiveState', state)
      this.liveState = state
    },
    /**
     * rtc 监听
     */
    addRtcListener() {
      // 监听本地视频变化
      window.RTC_COMMON.rtcEngine.on('localVideoStateChanged', (state, err) => {
        if(this.videoErrorShowedMap[err]) return
        this.videoErrorShowedMap[err] = true
        this.showNotification(this.VideoErrorMap, err)
      })
      // 监听本地音频变化
      window.RTC_COMMON.rtcEngine.on('localAudioStateChanged', (state, err) => {
        if(this.audioErrorShowedMap[err]) return
        this.audioErrorShowedMap[err] = true
        this.showNotification(this.AudioErrorMap, err)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.live-main {
  position: relative;
  width: 100%;
  height: 100%;
  .live-ppt {
    position: absolute;
    width: calc(72.5% - 2px);
    height: 100%;
    bottom: 0;
    overflow: hidden;
  }
}
</style>
