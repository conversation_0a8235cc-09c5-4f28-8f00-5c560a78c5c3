import ExceptionBase from '@thinkacademy/live-framework/components/base/exception-handler/exception-base.js'
import { isFunction } from '@thinkacademy/live-framework/libs/utils/is'
import { getLocalConfig } from 'locale'
import { route_href } from 'utils/routeUtil'
export default class LiveExceptionHandler extends ExceptionBase {
  isShow = false
  eventHandler(params = {}) {
    const { level, data } = params
    if (isFunction(this[level])) {
      this[level](data)
    }
  }

  /**
   * 网络异常提示
   */
  networkError(data = {}) {
    if (data.netStatus != 3) {
      if (data.netStatus != 4) {
        this.showNotice()
      } else {
        this.closeNotice()
      }
    }
  }
  /**
   * 调度服务连接回调
   */
   onSDKProvisionStatusNotice(data = {}){
    if (data.status != 0) {
      this.showNotice()
    } else {
      this.closeNotice()
    }
   }
  // 开启错误弹框
  async showNotice() {
    if (this.isShow) return
    this.isShow = true
    const localConfig = await getLocalConfig()
    Vue.prototype.$Notification.open({
      key: 'networkErrorTip',
      description: h => {
        const networkErrorConfig = localConfig.classroom.modules.networkError
        return (
          <div>
            <i class="netError"></i>
            <div class="description">
              {networkErrorConfig.notice[0]}
              <br />
              {networkErrorConfig.notice[1]}
            </div>
          </div>
        )
      },
      class: 'notification-network-error',
      placement: 'bottomRight',
      bottom: '0px',
      onClose: null,
      btn: h => {
        return (
          <a-button class="ant-btn-primary" shape="round">
            {localConfig.common.exit}
          </a-button>
        )
      },
      getContainer: () => {
        return document.getElementById('interactionFullPage')
      },
      onClick: () => {
        route_href("/")
      },
      closeIcon: () => {
        return
      },
      duration: null
    })
    document.getElementById('interactionFullPage').style.zIndex = '1001'
  }
  // 关闭错误弹框
  closeNotice() {
    this.isShow = false
    Vue.prototype.$Notification.destroy()
    document.getElementById('interactionFullPage').removeAttribute('style')
  }
  
  /*
   * toast类提示
   */
  // toast(data = {}) {}

  // confirm(data = {}) {}

  // newConfirm(data = {}) {}

  // notify(data) {}

  // resultToast(data = {}) {}
}
