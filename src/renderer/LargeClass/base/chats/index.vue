<template>
  <div v-show="isShowChat" :class="diffClassStyle">
    <div class="chat-container">
      <!-- 监听的需要暴露出去的数据，需要自己用一个才能监听到变化 勿删 -->
      <span v-show="false">{{ peopleOnline }}{{ selfChatStatus }}</span>
      <div class="content-box">
        <PrivateChat
          v-show="privateMessageStatus"
          :privateMessages="privateMessages"
          @handleSetPrivateMessage="handleSetPrivateMessage"
          @closePrivateChat="handleClosePrivateChat"
          :privateMessageStatus="privateMessageStatus"
          :options="options"
        ></PrivateChat>
        <GroupChat
          v-show="!privateMessageStatus"
          :inputStatus="ChatClass.inputStatus"
          :messages="messages"
          :lockTeacherMessage="ChatClass.lockTeacherMessage"
          :planId="options.planId"
          :options="options"
          :privateMessageStatus="privateMessageStatus"
          :packageId="options.planInfo.packageId"
          @handleSetMessage="handleSetMessage"
          :lottieEmojiLists="lottieEmojiLists"
        ></GroupChat>
      </div>
    </div>
  </div>
</template>

<script>
import { messageCenter } from '@thinkacademy/live-framework/components/framework/message-center'

import { replaceExpressName, msgUserType } from './chat-conf.js'
import { isClient } from '@thinkacademy/live-framework/libs/utils/is'
import { updateOnlineNum } from '@thinkacademy/live-framework/libs/utils/clientMethods.js'
import { getLottieEmojiLists } from 'utils/emojiUtils'
import PrivateChat from './components/privateChat.vue'
import logger from 'utils/logger'
import GroupChat from './components/groupChat.vue'

export default {
  components: {
    PrivateChat,
    GroupChat
  },
  props: {
    options: {
      type: Object,
      default() {
        return {
          counselorInfo: {},
          isPlayBack: false,
          planId: ''
        }
      }
    }
  },
  data() {
    const chatsConf = this.$t('classroom.largeClass.chats')
    return {
      msgTip: chatsConf.msgTip,
      smallClassSwitchType: 'video', // 小班模式默认类型
      // 私聊消息状态
      privateMessageStatus: false,
      lottieEmojiLists: [], // lottie表情包组
      // 视频回显状态
      // 视频回显显示时: 金币显示缩略状态
      // 视频回显隐藏时: 金币显示常规状态
      teamSwitchStatus: null,
      ChatClass: {
        inputStatus: {
          status: 5,
          text: ''
        }
      }
    }
  },
  computed: {
    // 班级模式
    classType() {
      // 0: 大班 1: 小班
      return this.options.classType
    },
    showFooter() {
      return !this.options.isAudition
    },
    isShowChat() {
      if (this.classType && this.smallClassSwitchType === 'video') {
        return false
      }
      return true
    },
    // 区分大班和小班样式
    diffClassStyle() {
      if (this.classType === 1 || this.classType === 2) {
        return 'small-class-chat-wrapper'
      }
      if (this.classType === 0 && this.teamSwitchStatus === false) {
        return 'chat-wrapper-no-video'
      }
      if (this.classType === 0 && this.teamSwitchStatus === true) {
        return 'chat-wrapper-video'
      }
      return ''
    },
    // 在线人数,更新header
    peopleOnline() {
      const { userList } = this.ChatClass
      const num = userList ? userList.length : 0
      // 因为初次进入直播间时直播组件偶尔加载慢于聊天组件，导致消息广播不同步
      // 所以延迟广播事件
      // setTimeout(() => {
      //   messageCenter.emit('room', {
      //     type: 'onlinePeople',
      //     data: num
      //   })
      // }, 2000)

      if (isClient()) {
        updateOnlineNum({
          onlineNum: num
        })
      }
      return num
    },
    // 群聊消息列表
    messages() {
      const { messages } = this.ChatClass
      const msg = messages || []
      return msg
    },
    // 私聊消息列表
    privateMessages() {
      const { privateMessages } = this.ChatClass
      const msg = privateMessages || []
      return msg
    },
    // 当前input 用户打的字
    inputText() {
      return this.ChatClass.inputStatus.text
    },
    selfChatStatus() {
      console.log('selfChatStatus', this.ChatClass)
      const { status = 5 } = this.ChatClass.inputStatus || {}
      // 禁言中
      if (status === 2) {
        sessionStorage.setItem('stopSpeaking', true)
      } else {
        // 非禁言状态
        sessionStorage.setItem('stopSpeaking', false)
      }
      return status
    }
  },
  mounted() {
    console.log('chat-index', this.options)
    this.$bus.$on('handleLargeNativeEmoji', params => {
      this.handleSetNewSingleEmoji(params)
    })
    this.getTeamSwitchStatus()
    this.bindEvent()
    this.initDynamicEmoji()
    // this.$nextTick(() => {
    //   window.addEventListener('scroll', this.handleOnScroll, true)
    // })
  },
  methods: {
    /**
     * 监听滚动到底部区域
     */
    handleOnScroll() {
      const messagesRefDom = document.getElementById('messagesRef')
      const DISTANCE_GAP = 1 // 滚动高度和scrolltop差距
      const scrollDistance = messagesRefDom.scrollTop + messagesRefDom.clientHeight + DISTANCE_GAP
      // 是否在底部
      if (messagesRefDom.scrollHeight <= scrollDistance) {
        this.ChatClass.hasNewMsg = false
        this.ChatClass.hasPrivateNewMsg = false
      }
    },
    /**
     * 绑定事件
     */
    bindEvent() {
      // 监听视频回显状态变化
      this.$bus.$on('updateTeamSwitchStatus', status => {
        this.teamSwitchStatus = status
      })
      // 监听私聊消息入口切换
      this.$bus.$on('chats.changePrivateMessageStatus', () => {
        this.privateMessageStatus = !this.privateMessageStatus
      })
      // 监听小班模式切换视频和聊天
      this.$bus.$on('switchVideoAndChat', type => {
        logger.send({
          tag: 'action',
          content: {
            msg: '伪小班模式切换视频和聊天'
          }
        })
        this.smallClassSwitchType = type
      })
      // 监听消息范围切换按钮
      this.$bus.$on('handleSwitchMessage', status => {
        this.ChatClass.lockTeacherMessage = status
      })
    },

    /**
     * 获取视频回显状态
     */
    async getTeamSwitchStatus() {
      const status = await window.thinkApi.ipc.invoke(
        'getStoreValue',
        `videoGroupSwitchStatus_${this.options.planId}`
      )

      console.log('getLiveSpecialData', status)
      this.teamSwitchStatus = status === undefined ? true : status
    },
    /**
     * @function
     * @name handleSetMessage
     * @description 设置消息列表
     * @param flag {boolean}
     */
    handleSetMessage(flag, data) {
      if (flag) {
        this.ChatClass.messages = []
        this.ChatClass.userNotInMegBottom = false
        messageCenter.emit('logger', {
          type: 'interactive',
          data: {
            logtype: 'clearScreen',
            isPlayBack: this.options.isPlayBack,
            planId: this.options.planId
          }
        })
        return
      }

      let {
        type,
        name,
        msg,
        evenexc,
        lottieUrl,
        emojiType,
        emojiId,
        isNewEmoji,
        emojiPicture = ''
      } = data
      try {
        msg = replaceExpressName(msg)
      } catch (error) {
        console.error('handleSetMessage replaceExpressName', msg, error)
      }
      this.ChatClass.setMessage({
        type,
        emojiPicture,
        name,
        msg,
        lottieUrl,
        emojiId,
        emojiType,
        isNewEmoji,
        evenexc
      })
    },
    /**
     * 动态表情包初始化
     */
    async initDynamicEmoji() {
      await getLottieEmojiLists().then(allEmojiList => {
        // @log-ignore
        this.lottieEmojiLists = allEmojiList
      })
    },
    /**
     * 设置私聊消息列表
     */
    handleSetPrivateMessage(data) {
      let { type, name, msg } = data
      try {
        msg = replaceExpressName(msg)
      } catch (error) {
        console.error('handleSetPrivateMessage', msg, error)
      }
      this.ChatClass.setPrivateMessage({
        type,
        name,
        msg
      })
    },
    /**
     * @function
     * @name handleInputText
     * @description 设置input展示文本
     */
    handleInputText(newText) {
      this.ChatClass.inputStatus.text = newText
    },
    /**
     * @function
     * @name handleSetNewMsgStatus
     * @description 点击新消息事件处理
     */
    handleSetNewMsgStatus(status) {
      this.ChatClass.hasNewMsg = status
      this.ChatClass.hasPrivateNewMsg = status
    },
    // 聊天表情改版
    handleSetNewSingleEmoji(params) {
      if (this.$refs.chatFooter && this.$refs.chatFooter.handleIn3s()) return
      this.handleSetMessage(false, {
        type: msgUserType.mine,
        name: this.msgTip.SPEECH_MINE,
        msg: params.name,
        emojiType: params.type,
        emojiPicture: params.emojiPicture,
        emojiId: params.emojiId ? params.emojiId : 0,
        lottieUrl: params.lottieUrl ? params.lottieUrl : '',
        isNewEmoji: true
      })
    },
    // 关闭私聊窗口
    handleClosePrivateChat() {
      this.privateMessageStatus = false
    }
  },
  beforeDestroy() {
    this.$bus.$off('handleLargeNativeEmoji')
    window.removeEventListener('scroll', this.handleOnScroll)
  }
}
</script>

<style lang="scss" scoped>
.chat-wrapper-no-video {
  position: absolute;
  right: 0;
  width: 27.5%;
  height: calc(100vh - 280 / 739 * 100vh - 44px);
  top: calc(280 / 739 * 100vh + 44px);
}
.chat-wrapper-video {
  position: absolute;
  right: 0;
  width: 27.5%;
  height: calc(100vh - 490 / 739 * 100vh - 46px);
  top: calc(490 / 739 * 100vh + 46px);
}
.small-class-chat-wrapper {
  position: absolute;
  right: 0;
  width: 27.5%;
  height: calc(100vh - 246 / 739 * 100vh - 97px);
  top: calc(246 / 739 * 100vh + 97px);
  .chat-container {
    .messages-wrap {
      padding: 25px 12px 12px 12px;
    }
  }
}
.chat-container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  color: #ddddde;
  box-sizing: border-box;
  overflow: hidden;
  .chat-header-box {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.2);
    .online-num {
      color: #848488;
    }
  }
  .switch-area {
    position: relative;
    width: 100%;
    padding: 0 12px;
    margin-top: 12px;
    .switch-teacher-icon {
      display: inline-block;
      width: 24px;
      height: 24px;
      background: url('./img/icon-teacher.png');
      background-size: cover;
      vertical-align: middle;
      margin-right: 10px;
    }
    .teacher-only {
      font-size: 14px;
      font-weight: 500;
      color: #172b4d;
    }
  }
  .switch-message {
    position: absolute;
    top: 0;
    right: 12px;
    width: 42px;
    height: 24px;
    background: #e9eaed;
    border-radius: 12px;
    transition: all 0.5s;
    z-index: 1;
    cursor: pointer;
    &.lock {
      background: #ffaa0a;
    }
    .switch-message-button {
      position: absolute;
      left: 2px;
      top: 2px;
      width: 20px;
      height: 20px;
      text-align: center;
      color: #ffffff;
      background: #ffffff;
      border-radius: 50%;
      transition: all 0.5s;
      &.lock {
        left: 20px;
        background: #ffffff;
      }
    }
  }
  .content-box {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #272727;
  }
  .messages-wrap {
    position: relative;
    flex: 1;
    padding: 12px;
    overflow-x: hidden;
    overflow-y: auto;
    box-sizing: border-box;
    &::-webkit-scrollbar {
      display: none;
    }
  }
}
</style>
