<template>
  <div
    v-clickoutside="closeEmojiPane"
    v-if="showEmojiPane"
    class="emojis-pane-container"
    :class="allOnStage ? 'allOnStage-pane-container' : ''"
  >
    <section class="emojis-nav">
      <ul>
        <li
          class="emoji-nav-content"
          :class="emojiNav.emojiPackageId == currentOrderId ? 'active' : ''"
          v-for="(emojiNav, key) in lottieEmojiLists"
          :key="emojiNav.emojiPackageId"
          @click="selectedEmoji(emojiNav.emojiPackageId, key)"
        >
          <img v-if="emojiNav.isLocal" src="~assets/images/live/icon_emoji_native.png" alt="" />
          <img v-else :src="emojiNav.picture" alt="" />
        </li>
      </ul>
    </section>
    <!-- 表情列表 -->
    <section class="emojis-list">
      <div class="emoji-scroll-wrapper" ref="scrollWrapper">
        <div
          v-for="emojiGroup in lottieEmojiLists"
          class="emoji-show-list"
          :class="{
            'native-emoji': emojiGroup.isLocal == true,
            'overshow-style': emojiGroup.isOver == true && emojiGroup.overShow == true
          }"
          :style="synamicWidthStyle(emojiGroup.isLocal, emojiGroup.content.length)"
          :key="emojiGroup.emojiPackageId"
        >
          <!-- 过期展示 -->
          <template v-if="emojiGroup.isOver == true && emojiGroup.overShow == true">
            <div class="emojiGroup-overShow">
              <span class="overShow-emoji">
                <img :src="emojiGroup.picture" />
                <span></span>
              </span>
              <p class="overShow-tips top">
                {{ $t('classroom.smallClass.dynamicEmoji[0]') }}
              </p>
              <p class="overShow-tips">{{ $t('classroom.smallClass.dynamicEmoji[1]') }}</p>
            </div>
          </template>
          <template v-else>
            <div
              class="emoji-group-item"
              v-for="(singleEmoji, key) in emojiGroup.content"
              :key="key"
            >
              <Emoticon
                v-if="emojiGroup.isLocal"
                :name="singleEmoji.name"
                :type="singleEmoji.type"
                :enableHover="true"
                :hoverWidth="45"
                :hoverHeight="45"
                :width="40"
                :height="40"
                @handleClick="handleLocalClick(arguments, emojiGroup, key)"
              />
              <div class="dynamic-emoji" @click="handleLottieClick(singleEmoji, emojiGroup)" v-else>
                <img :src="singleEmoji.emojiPicture" alt="" />
              </div>
            </div>
          </template>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import Emoticon from 'components/Common/Emoticon'
import { getUserInfo } from 'utils/userInfo'
import { sendEmojiBurryPoint } from 'utils/emojiUtils'
import logger from 'utils/logger'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
export default {
  name: 'EmojiPane',
  components: {
    Emoticon
  },
  data() {
    return {
      emoticonList: '',
      nickName: '',
      avatar: '',
      currentOrderId: 0, // 表情包emojiPackageId
      dynamicEmolist: []
    }
  },
  props: {
    options: {
      type: Object,
      default: null
    },
    allOnStage: {
      type: Boolean,
      default: false
    },
    showEmojiPane: {
      type: Boolean,
      default: false
    },
    packageId: {
      type: Number,
      default: 0
    },
    lottieEmojiLists: {
      default: () => [],
      type: Array
    }
  },
  computed: {},
  mounted() {
    this.getUserInfo()
  },
  methods: {
    // 获取用户信息
    async getUserInfo() {
      const { nickName, avatar } = await getUserInfo()
      this.nickName = nickName
      this.avatar = avatar
    },
    // 点击表情
    handleClick(params, emojiGroup, key) {
      this.$bus.$emit('handleLargeNativeEmoji', params)
      this.$emit('closeLargeEmojiPane')
      this.currentOrderId = 0
      // 表情发送埋点
      sendEmojiBurryPoint(this.options, params, emojiGroup, key, this.allOnStage, this.packageId) // 大班-非全员上台
      classLiveSensor.osta_cb_send_msg({
        type: 4,
        contentType: 'emoji',
        msg: `${params.name}`
      })
    },
    // 点击本地表情
    handleLocalClick(params, emojiGroup, key) {
      logger.send({
        tag: 'action',
        content: {
          msg: '学生选择本地表情发送'
        }
      })
      this.handleClick(params[0], emojiGroup, key)
    },
    // 动态表情
    handleLottieClick(params, emojiGroup) {
      logger.send({
        tag: 'action',
        content: {
          msg: '学生选择动态表情发送'
        }
      })
      // type 2 动态表情-静态图 type 3 动态表情lottie图
      // 同一个ircType 根据不同类型处理不同逻辑 区分lottie的json和图片
      const type = params.lottieUrl.endsWith('.json') ? 2 : 3
      const newParams = {
        ...params,
        type,
        name: params.emojiName
      }
      this.handleClick(newParams, emojiGroup)
    },
    // 宽度计算-扩展
    synamicWidthStyle(isLocal, len) {
      // 本地表情宽度
      const localEmojiWidth = 488 // 本地图片表情面板宽度
      const baseLottitePaneWidth = 300 // 基础 6个表情的宽度
      const lottiePaneGap = 70 // 表情面板间距
      // lottie面板表情宽度 计算规则(目前表情六个，后续扩展成2的整数倍 比如 8 10 12 ...)
      const synamicWidth = ((len - 6) / 2) * lottiePaneGap + baseLottitePaneWidth
      return { width: isLocal ? `${localEmojiWidth}px` : `${synamicWidth}px` }
    },
    selectedEmoji(emojiPackageId, key) {
      logger.send({
        tag: 'action',
        content: {
          msg: '学生选择表情类型'
        }
      })
      this.currentOrderId = emojiPackageId
      const scrollDom = document.getElementsByClassName('emoji-show-list')[key]
      if (scrollDom) {
        scrollDom.scrollIntoView({
          inline: 'center'
        })
      }
    },
    // 关闭面板 改变导航定位
    changeNavPos() {
      this.currentOrderId = 0
    }
  }
}
</script>
<style scoped lang="scss">
.emojis-pane-container {
  width: 840px;
  height: 256px;
  background: #0056a4;
  border-radius: 23px;
  overflow: hidden;
  position: absolute;
  left: 50%;
  bottom: 10px;
  margin-left: -420px;
  .emojis-nav {
    width: 100%;
    height: 56px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('./imgs/emoji-nav.png');
    ul {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      padding: 4px 8px 0 8px;
    }
    .emoji-nav-content {
      width: 44px;
      height: 44px;
      display: flex;
      background-repeat: no-repeat;
      background-size: cover;
      background-image: url('./imgs/emoji-nav-normal.png');
      margin-right: 16px;
      padding-top: 5px;
      justify-content: center;
      img {
        display: block;
        width: 30px;
        height: 30px;
      }
      &.active {
        background-image: url('./imgs/emoji-nav-selected.png');
      }
    }
  }

  .emojis-list {
    padding: 6px;
    display: flex;
    width: 840px;
    .emoji-scroll-wrapper {
      display: -webkit-box;
      align-items: center;
      flex-direction: row;
      -webkit-overflow-scrolling: touch;
      overflow: hidden;
      white-space: nowrap;
      scroll-behavior: smooth;
      &::-webkit-scrollbar {
        display: none;
      }
      .emoji-show-list {
        width: 488px;
        height: 188px;
        border: #0e7fe6 4px solid;
        border-radius: 15px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-right: 12px;
        justify-content: space-between;
        padding: 8px 10px;
        .emoji-group-item {
          width: 70px;
          height: 70px;
          display: flex;
          background: transparent;
          border-radius: 6px;
          cursor: pointer;
          .dynamic-emoji {
            img {
              display: block;
              width: 100%;
              height: 100%;
              pointer-events: none;
            }
          }
        }
      }
      .native-emoji {
        width: 488px;
        height: 188px;
        border: #0e7fe6 4px solid;
        display: flex;
        border-radius: 15px;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        padding: 8px 10px;
        margin-right: 12px;
        .emoji-group-item {
          width: 55px;
          height: 50px;
          display: flex;
          justify-content: center;
          align-items: center;
          background: none;
        }
      }
      .overshow-style {
        border: none;
        padding: 0 0;
        .emojiGroup-overShow {
          width: 282px !important;
          height: 188px;
          background-repeat: no-repeat;
          background-size: cover;
          background-image: url('./imgs/over-show.png');
          display: flex;
          flex-direction: column;
          // justify-content: center;
          align-items: center;
          padding-top: 15px;
          .overShow-emoji {
            width: 60px;
            height: 60px;
            position: relative;
            img {
              width: 60px;
              height: 60px;
              display: inline-block;
            }
            span {
              width: 30px;
              height: 30px;
              background-repeat: no-repeat;
              background-size: cover;
              background-image: url('./imgs/lock.png');
              position: absolute;
              bottom: -10px;
              right: -10px;
            }
          }
          .overShow-tips {
            font-size: 14px;
            color: #fff;
            display: block;
            text-align: center;
          }
          .top {
            margin-top: 40px;
          }
        }
      }
    }
  }
  .emoji-item {
    float: left;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 46px;
    height: 46px;
    margin: 0 0 4px 8px;
    &:hover {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
    }
  }
}
//  多人上台
.allOnStage-pane-container {
  .emojis-list {
    width: 854px;
  }
}
</style>
