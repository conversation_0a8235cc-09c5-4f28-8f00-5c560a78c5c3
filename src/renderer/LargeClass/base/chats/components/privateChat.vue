<!-- 辅导老师私聊窗口 -->
<template>
  <div class="private-chat">
    <!-- 聊天头部 -->
    <div class="private-header">
      <div class="header-left">
        <div class="name">{{ options.counselorInfo.name }}({{ assistantTitle }})</div>
      </div>
      <div class="header-right">
        <!-- 使用img icon-close -->
        <img class="btn-close" src="../img/icon-close.png" @click="closePrivateChat" />
      </div>
    </div>
    <!-- 聊天内容 -->
    <div class="message-container" id="message-list" ref="message-list">
      <div v-for="(item, index) in privateMessages" :key="index" class="item-message">
        <div v-if="item.type == 2" class="message-teacher teacher-f">
          <section>
            <span class="name-label name">
              {{ item.name }}
            </span>
          </section>

          <section class="message-content">
            <span class="message" v-html="replaceExpress(item.msg, true).replace(/\n/g, '<br>')">
            </span>
          </section>
        </div>
        <div v-if="item.type === msgUserType.mine" class="message-student">
          <div class="stu-message-content private-stu-message">
            <section>
              <span class="self">
                <span class="name">
                  {{ nickName }}
                </span>
              </span>
            </section>
            <section class="message-content">
              <div class="content">
                <span v-show="item.messageStatus" class="send_error"></span>
                <span class="message" v-html="replaceExpress(item.msg, true)"></span>
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
    <!-- 聊天输入框 -->
    <div class="chat-footer-container">
      <div class="footer-live-box">
        <div class="send-message-box">
          <div class="message-box private-message-box">
            <div class="input-wrap">
              <input
                type="text"
                ref="inputBox"
                :placeholder="$t('classroom.largeClass.chats.inputPlaceholder')"
                v-model="privateInputText"
                autocomplete="off"
                @keydown.enter="handleSetPrivateMessage"
                maxlength="200"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="new-meg-tip" v-show="isShowNewMsgTip" @click="handleNewMsg">
        <i class="icon-more-bottom"></i>
      </div>
    </div>
  </div>
</template>

<script>
import { msgUserType, replaceExpress } from '../chat-conf.js'
import { getUserInfo } from 'utils/userInfo'
import logger from 'utils/logger'
import { sensorEvent } from 'utils/sensorEvent'
import { getConfig } from 'utils/initConfig'
import { getSchoolCode } from 'utils/local'
export default {
  data() {
    return {
      nickName: '', // 当前用户昵称
      msgUserType,
      privateInputText: '',
      isShowNewMsgTip: false,
      assistantTitle: ''
    }
  },
  props: {
    // 私聊消息列表
    privateMessages: {
      default: () => [],
      type: Array
    },
    options: {
      type: Object,
      default() {
        return {}
      }
    },
    // 是否显示私聊窗口
    privateMessageStatus: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    privateMessageStatus: {
      handler: function(val) {
        this.$nextTick(() => {
          this.handleScrollBottom()
        })
      },
      deep: true
    }
  },
  computed: {
    msgTip() {
      return this.$t('classroom.largeClass.chats.msgTip')
    }
  },
  methods: {
    replaceExpress,
    /**
     * 封装埋点事件
     */
    reSensorEvent(eventName, params = {}) {
      sensorEvent(eventName, this.options, params)
    },
    // 当前用户头像
    async getUserAvatar() {
      const { nickName } = await getUserInfo()
      this.nickName = nickName
    },
    // 发送私聊
    /**
     * @function
     * @name handleSetPrivateMessage
     * @description 发送私聊消息
     */
    handleSetPrivateMessage() {
      console.log('发送私聊消息')
      if (!this.privateInputText.length) return
      logger.send({
        tag: 'action',
        content: {
          msg: '发送私聊消息',
          privateInputText: this.privateInputText
        }
      })
      if (this.privateInputText.trim().length > 0) {
        this.inputBox.blur()
        this.$emit('handleSetPrivateMessage', {
          type: msgUserType.mine,
          name: this.msgTip.SPEECH_MINE,
          msg: this.privateInputText.trim()
        })
        this.privateInputText = ''
        this.handleScrollBottom()
        this.reSensorEvent('hw_classroom_tutor_message_reply')
      }
    },
    /**
     * @function
     * @name handleNewMsg
     * @description 点击新消息事件处理
     */
    handleNewMsg() {
      logger.send({
        tag: 'action',
        content: {
          msg: '点击新消息事件'
        }
      })
      this.handleScrollBottom()
    },
    // 关闭私聊窗口
    closePrivateChat() {
      this.$bus.$emit('chats.changePrivateMessageStatus')
    },
    // 滚动到底部
    handleScrollBottom() {
      // @log-ignore
      this.isShowNewMsgTip = false
      this.$nextTick(() => {
        const chatListDom = document.getElementById('message-list')
        const scrollHeight = Math.round(chatListDom.scrollHeight)
        const clientHeight = Math.round(chatListDom.clientHeight)
        chatListDom.scrollTop = scrollHeight - clientHeight + 1
        chatListDom.removeEventListener('scroll', this.handleOnScroll)
      })
    },
    // 监听滚动事件
    handleOnScroll() {
      // @log-ignore
      const chatListDom = document.getElementById('message-list')
      const scrollHeight = Math.round(chatListDom.scrollHeight)
      const scrollTop = Math.round(chatListDom.scrollTop)
      const clientHeight = Math.round(chatListDom.clientHeight)
      if (scrollHeight <= scrollTop + clientHeight + 1) {
        this.isShowNewMsgTip = false
        chatListDom.removeEventListener('scroll', this.handleOnScroll)
      }
    },
    // 监听私聊消息推送
    handleReceivePrivateMessage() {
      const chatListDom = document.getElementById('message-list')
      const scrollHeight = Math.round(chatListDom.scrollHeight)
      const scrollTop = Math.round(chatListDom.scrollTop)
      const clientHeight = Math.round(chatListDom.clientHeight)
      // 跟移动端统一,如果当前聊天list在底部,添加新消息,滚动到底部,否则就显示[新消息]弹窗
      if (scrollHeight <= scrollTop + clientHeight + 1) {
        this.handleScrollBottom()
      } else {
        chatListDom.addEventListener('scroll', this.handleOnScroll)
        this.isShowNewMsgTip = true
      }
    }
  },
  async mounted() {
    this.getUserAvatar()
    this.inputBox = this.$refs.inputBox
    this.handleScrollBottom()
    this.$bus.$on('chats.privateMessagePush', this.handleReceivePrivateMessage)
    const config = await getConfig()
    const schoolCode = await getSchoolCode()
    this.assistantTitle = config?.teacherV2[schoolCode]?.assistantTitle || 'VIP Teacher'
  },
  beforeDestroy() {
    this.$bus.$off('chats.privateMessagePush', this.handleReceivePrivateMessage)
    const chatListDom = document.getElementById('message-list')
    chatListDom && chatListDom.removeEventListener('scroll', this.handleOnScroll)
  }
}
</script>

<style lang="scss" scoped>
.private-chat {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  .private-header {
    height: 34px;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 12px 12px 0px 0px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px;
    width: 100%;
    .header-left {
      display: flex;
      align-items: center;
      flex: 1;
      margin-right: 10px;
      // 超出一行隐藏
      overflow: hidden;
      .name {
        font-size: 14px;
        font-family: SFProRounded-Medium, SFProRounded;
        font-weight: 500;
        color: #ffffff;
        line-height: 20px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        margin-left: 8px;
      }
    }
    .header-right {
      cursor: pointer;
      .btn-close {
        width: 26px;
        height: 26px;
      }
      .anticon {
        font-size: 20px;
        color: #fff;
      }
    }
  }
  .message-container {
    // 通用消息样式
    overflow-y: scroll;
    flex: 1;
    padding-top: 12px;
    .item-message {
      margin-bottom: 10px;
      // 标签型名称
      .name-label {
        display: inline-block;
        height: 18px;
        line-height: 16px;
        border-radius: 3px;
      }
      .message {
        word-break: break-all;
        color: #dee2e7;
      }
    }
    // 学生消息样式
    .message-student {
      .private-stu-message {
        align-items: flex-end;
        flex-direction: column;
        .message-content {
          max-width: 90%;
          span {
            color: #172b4d;
          }
        }
        .message {
          background: #ffaa0a;
          border-radius: 4px;
        }
      }
      .stu-message-content {
        display: flex;
        .message {
          border-radius: 4px;
          font-size: 14px;
          font-weight: 500;
          line-height: 16px;
          padding: 4px 6px;
          word-break: break-all;
        }
        section:nth-child(1) {
          max-width: 90%;
          .self {
            display: flex;
            .name {
              font-size: 14px;
              font-weight: 500;
              color: #a2aab8;
              height: 32px;
              line-height: 32px;
              overflow: hidden;
              word-break: break-all;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              flex: 1;
              margin-right: 16px;
            }
          }
        }
        section:nth-child(2) {
          display: flex;
          flex-direction: column;
          margin-right: 16px;
          .content {
            display: flex;
            justify-content: flex-end;
            .send_error {
              display: inline-block;
              margin-right: 4px;
              width: 20px;
              height: 20px;
              background: url('../img/send_error.png') no-repeat;
              background-size: 100%;
            }
          }
        }
      }
    }
    // 教师消息样式
    .message-teacher {
      overflow: hidden;
      display: flex;
      align-items: flex-start;
      flex-direction: column;
      section:nth-child(1) {
        max-width: 90%;
        .name {
          color: #a2aab8;
          font-size: 14px;
          margin-left: 16px;
          overflow: hidden;
          word-break: break-all;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
      section:nth-child(2) {
        max-width: 90%;
        background: #02ca8a;
        border-radius: 4px;
        margin: -2px 0 0 16px;
        padding: 3px 5px;
        line-height: unset;
      }
      .message-content {
        margin: 8px 0 0 44px;
        line-height: 1.5;
        .private-flag {
          margin-left: 8px;
        }
      }
    }
  }
}
::v-deep .message {
  img {
    width: 24px;
    height: 24px;
    vertical-align: bottom;
  }
}
.chat-footer-container {
  position: relative;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 42px;
  padding: 0 6px;
  margin: 8px 16px;
  font-size: 14px;

  .new-meg-tip {
    position: absolute;
    top: -30px;
    width: 30px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    font-size: 12px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 16px;
    color: #ffaa0a;
    cursor: pointer;
    .icon-more-bottom {
      display: inline-block;
      width: 10px;
      height: 10px;
      background: url('../img/icon-more-bottom.png') no-repeat;
      background-size: cover;
    }
  }
  .footer-live-box {
    width: 100%;
  }
  .send-message-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 36px;
    border-radius: 20px;
    .message-box {
      position: relative;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      border-radius: 20px;
      // 私聊消息输入框
      &.private-message-box {
        width: 100%;
        input {
          padding-left: 15px;
        }
      }
    }
    .input-wrap {
      flex: 1;
      height: 36px;
      line-height: 36px;
      border-radius: 22px;
      background: #040404;
      display: flex;
      align-items: center;
      padding-left: 10px;
      input {
        height: 100%;
        flex: 1;
        color: #dee2e7;
        &::placeholder {
          color: #a2aab8;
          font-weight: 400;
        }
        &.disabled::placeholder {
          color: #dee2e7;
        }
      }
    }
  }
}
</style>
