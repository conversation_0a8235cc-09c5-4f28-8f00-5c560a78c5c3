<template>
  <div class="group-chat">
    <div class="message-container" id="group-chat-list">
      <div v-for="item in messages" :key="item.id" class="item-message">
        <!-- 辅导老师 -->
        <div v-if="item.type === msgUserType.teacher_f" class="message-teacher teacher-f">
          <section>
            <span class="name-label name">
              {{ item.name }}
            </span>
          </section>
          <section class="message-content">
            <span class="message" v-html="replaceExpress(item.msg, true)"></span>
          </section>
        </div>
        <!-- 学生 -->
        <div
          v-if="item.type === msgUserType.stu || item.type === msgUserType.mine"
          class="message-student"
        >
          <div
            class="stu-message-content"
            :class="
              item.isMe || item.type === msgUserType.mine
                ? 'self-chat-content'
                : 'others-chat-content'
            "
          >
            <!-- 头像&昵称 -->
            <section>
              <span v-if="item.type === msgUserType.mine || item.isMe" class="self">
                <span class="name">
                  {{ nickName }}
                </span>
              </span>

              <span v-else class="others">
                <span class="name">
                  {{ item.name }}
                </span>
              </span>
            </section>
            <!-- 消息内容 -->
            <section>
              <div class="content">
                <span v-show="item.messageStatus" class="send_error"></span>
                <div
                  v-if="!item.isNewEmoji"
                  class="message"
                  v-html="replaceExpress(item.msg, true)"
                ></div>
                <EmoticonMessage
                  v-else
                  :willAutoClear="false"
                  :name="item.msg"
                  :type="item.emojiType"
                  :emojiId="item.emojiId"
                  :width="40"
                  :height="40"
                  :lottieUrl="item.lottieUrl"
                  :loopLottie="true"
                />
              </div>
            </section>
          </div>
        </div>
        <div v-if="item.type === msgUserType.system" class="message-system">
          <div class="message-content">
            <span class="message">{{ item.msg }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="chat-footer-wrapper" v-if="showFooter">
      <!-- 学生 -->
      <div class="chat-footer-container">
        <a-badge
          v-if="options.counselorInfo && options.counselorInfo.name"
          :dot="showDot"
          class="private-badge"
          @click="handleTogglePrivateChat"
        >
          <img
            v-if="options.counselorInfo.avatar"
            class="private-avatar"
            :src="options.counselorInfo.avatar"
            alt=""
            srcset=""
          />
        </a-badge>

        <div class="footer-live-box">
          <div class="send-message-box" :class="{ lock: needLoak }">
            <div v-show="needLoak" class="message-box message-box-lock">
              <div class="chatbox-closed">
                {{ inputStatus.text }}
              </div>
            </div>
            <div v-show="!needLoak" class="message-box">
              <div class="input-wrap">
                <input
                  type="text"
                  ref="inputBox"
                  :placeholder="$t('classroom.largeClass.chats.inputPlaceholder')"
                  v-model="inputStatus.text"
                  autocomplete="off"
                  @keydown.enter="handleSendMessage"
                  :class="{ disabled: needLoak }"
                  :disabled="needLoak"
                  maxlength="200"
                />
                <div
                  class="express"
                  :class="showEmojiPane ? 'select-express' : ''"
                  id="emoji-icon"
                  @click="handleShowExpress"
                ></div>
              </div>
            </div>
          </div>
        </div>
        <div class="new-meg-tip" v-show="isShowNewMsgTip" @click="handleNewMsg">
          <i class="icon-more-bottom"></i>
        </div>
        <div class="emoji-container" id="emoji-container-id">
          <LargeEmojiPane
            :showEmojiPane="showEmojiPane"
            v-clickoutside="closeEmojiPane"
            :lottieEmojiLists="lottieEmojiLists"
            :options="options"
            :packageId="packageId"
            @closeLargeEmojiPane="closeLargeEmojiPane"
            ref="emojiPaneRef"
          />
        </div>
        <div v-if="showPrivateToast" class="private-message-container">
          <div class="private-message-toast">
            <div class="message-header">
              <div class="header-left">
                <!-- 头像 -->
                <div class="header-avatar">
                  <img :src="options.counselorInfo.avatar || defaultAvatar" alt="" srcset="" />
                </div>
                <!-- 昵称+职位 -->
                <div class="private-name">
                  <span class="private-name__name">{{ options.counselorInfo.name }}</span>
                  <span class="private-name__position">{{ assistantTitle }}</span>
                </div>
              </div>
            </div>
            <div class="message-body">
              <div
                class="private-message"
                v-html="replaceExpress(privateMsg, true).replace(/\n/g, '<br>')"
              ></div>

              <div class="message-footer-btn">
                <a-button class="btn" type="primary" @click="handleReply">{{
                  $t('classroom.largeClass.chats.reply')
                }}</a-button>
                <a-button class="btn" type="primary" @click="handleGotIt">{{
                  $t('classroom.largeClass.chats.gotIt')
                }}</a-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCursortPosition, msgUserType, replaceExpress } from '../chat-conf'
import LargeEmojiPane from './LargeEmojiPane'
import Clickoutside from 'utils/clickoutside'
import { addClass, removeClass } from 'utils/util'
import { openEmojiBurryPoint } from 'utils/emojiUtils'
import { sensorEvent } from 'utils/sensorEvent'
import logger from 'utils/logger'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import { getUserInfo } from 'utils/userInfo'
import EmoticonMessage from 'components/Common/EmoticonMessage'
import { getConfig } from 'utils/initConfig'
import { getSchoolCode } from 'utils/local'

const defaultAvatar = require('../img/avatar_default.png')

export default {
  components: {
    LargeEmojiPane,
    EmoticonMessage
  },
  props: {
    inputStatus: {
      type: Object,
      default() {
        return {
          status: 5,
          text: this.$t('classroom.largeClass.chats.inputStatusMap')[5]
        }
      }
    },
    isPlayBack: {
      default: false
    },
    planId: {
      default: ''
    },
    allOnStage: false, // 大班不存在全员上台
    lottieEmojiLists: {
      default: () => [],
      type: Array
    },
    options: {
      type: Object,
      default() {
        return {}
      }
    },
    packageId: {
      type: Number,
      default: 0
    },
    // 群聊消息列表
    messages: {
      default: () => [],
      type: Array
    },
    privateMessageStatus: {
      default: false,
      type: Boolean
    }
  },
  directives: {
    Clickoutside
  },
  data() {
    return {
      inputBox: null,
      showEmojiPane: false,
      rightLabel: '',
      showDot: false,
      singleEmoji: {},
      emoticonType: 1, // 1 本地表情 2 动态表情
      emojiId: 0, // 动态表情id
      lottieUrl: '', // 动态表情url
      nickName: '', // 当前用户昵称
      msgUserType,
      showPrivateToast: false,
      privateToastTimer: null,
      privateMsg: '',
      isShowNewMsgTip: false,
      assistantTitle: ''
    }
  },
  computed: {
    needLoak() {
      return this.inputStatus.status != 3 && this.inputStatus.status != 4
    },
    msgTip() {
      return this.$t('classroom.largeClass.chats.msgTip')
    },
    showFooter() {
      return !this.options.isAudition
    }
  },
  watch: {
    needLoak(isNeedLoak) {
      logger.send({
        tag: 'action',
        content: {
          msg: '老师禁言',
          params: {
            isNeedLoak,
            showEmojiPane: this.showEmojiPane
          }
        }
      })
      // 老师禁言 如果学生表情面板打开 -> 关闭
      if (isNeedLoak && this.showEmojiPane) {
        this.closeLargeEmojiPane()
      }
    },
    privateMessageStatus(val) {
      this.$nextTick(() => {
        this.handleScrollBottom()
      })
    }
  },
  async mounted() {
    this.inputBox = this.$refs.inputBox
    this.getUserAvatar()
    this.showDot = false
    this.$bus.$on('chats.privateMessagePush', this.handleReceivePrivateMessage)
    this.$bus.$on('chats.messagePush', this.handleReceiveNewMessage)
    const config = await getConfig()
    const schoolCode = await getSchoolCode()
    this.assistantTitle = config?.teacherV2[schoolCode]?.assistantTitle || 'VIP Teacher'
    this.$nextTick(() => {
      this.handleScrollBottom()
    })
    if (!this.options.counselorInfo.avatar) {
      this.options.counselorInfo.avatar = defaultAvatar
    }
    document.getElementsByTagName('img').forEach(img => {
      img.onerror = this.noImg
    })
  },
  beforeDestroy() {
    this.$bus.$off('chats.privateMessagePush', this.handleReceivePrivateMessage)
    this.$bus.$off('chats.messagePush', this.handleReceiveNewMessage)
    // cancel scroll event
    let chatListDom = document.getElementById('group-chat-list')
    chatListDom && chatListDom.removeEventListener('scroll', this.handleOnScroll)
  },
  methods: {
    noImg(event) {
      console.log('defaultAvatar', defaultAvatar)
      var img = event.srcElement
      img.src = defaultAvatar
      img.onerror = null
    },
    replaceExpress,
    /**
     * 封装埋点事件
     */
    reSensorEvent(eventName, params = {}) {
      sensorEvent(eventName, this.options, params)
    },
    /**
     * @function
     * @name handleShowExpress
     * @description emoji表情显隐切换
     */
    handleShowExpress() {
      logger.send({
        tag: 'action',
        content: {
          msg: '点击表情icon切换显示隐藏',
          privateMessageStatus: this.privateMessageStatus,
          needLoak: this.needLoak
        }
      })
      if (!this.privateMessageStatus && this.needLoak) return
      this.showEmojiPane = !this.showEmojiPane
      if (this.showEmojiPane) {
        // 动态修改表情面板层级
        this.dynamicDealPaneZindex('add')
        // 表情面板打开埋点
        openEmojiBurryPoint(this.options, this.allOnStage, this.packageId)
      } else {
        this.dynamicDealPaneZindex('remove')
      }
    },
    /**
     * 点击表情面板外围关闭面板
     */
    closeEmojiPane(e) {
      // 排除的dom
      const wrapperDom = document.querySelector('#emoji-icon')
      const clickDom = wrapperDom == e.target
      if (clickDom) {
        this.showEmojiPane = true
        return
      }
      logger.send({
        tag: 'action',
        content: {
          msg: '点击外部关闭表情弹窗'
        }
      })
      this.showEmojiPane = false
      // 动态修改表情面板层级
      this.dynamicDealPaneZindex('remove')
      // 修改面板导航
      this.$refs.emojiPaneRef.changeNavPos()
    },
    closeLargeEmojiPane() {
      logger.send({
        tag: 'action',
        content: {
          msg: '关闭表情面板'
        }
      })
      this.showEmojiPane = false
      this.dynamicDealPaneZindex('remove')
    },
    /**
     * 动态修改表情面板层级
     */
    dynamicDealPaneZindex(type) {
      const emojiContainerDom = document.getElementById('emoji-container-id')
      if (type === 'add') {
        addClass(emojiContainerDom, 'emoji-z-index')
      } else if (type === 'remove') {
        removeClass(emojiContainerDom, 'emoji-z-index')
      }
    },
    /**
     * @function
     * @description 选择emoji表情
     * @name handleSelectEmoji
     */
    handleSelectEmoji(word) {
      const { text } = this.inputStatus

      if (text.length + word.length > 100) return

      const pos = getCursortPosition(this.inputBox)
      const newText =
        text.substring(0, pos) + '[e]' + word + '[e]' + text.substring(pos, text.length)

      this.$emit('changeInputText', newText)
      this.inputBox.focus()
    },
    /**
     * @function
     * @name handleSendMessage
     * @description 发送消息
     */
    handleSendMessage() {
      logger.send({
        tag: 'action',
        content: {
          msg: '发送消息',
          inputStatus: this.inputStatus
        }
      })
      const { text, status } = this.inputStatus

      if (!text.length) return

      if (this.handleIn3s()) return

      if (text.trim().length > 0 && status === 3) {
        this.inputStatus.text = ''
        this.$emit('handleSetMessage', false, {
          type: msgUserType.mine,
          name: this.msgTip.SPEECH_MINE,
          msg: text.trim(),
          evenexc: this.rightLabel
        })
        this.reSensorEvent('hw_classroom_chat_send')
        classLiveSensor.osta_cb_send_msg({
          type: msgUserType.mine,
          msg: text.trim()
        })
      }
      this.handleScrollBottom()
      this.inputBox.focus()
    },

    /**
     * @function
     * @name handleIn3s
     * @description 发言在3s限制内判断
     */
    handleIn3s() {
      if (this.inputStatus.status === 4) {
        this.$emit('handleSetMessage', false, {
          type: msgUserType.system,
          name: this.msgTip.SPEECH_SYS,
          msg: this.msgTip.SPEECH_INTERVAL
        })
        return true
      }
      return false
    },
    /**
     * @function
     * @name handleNewMsg
     * @description 点击新消息事件
     */
    handleNewMsg() {
      logger.send({
        tag: 'action',
        content: {
          msg: '点击新消息事件'
        }
      })
      this.handleScrollBottom()
    },
    // 切换辅导私聊窗口
    handleTogglePrivateChat() {
      this.showPrivateToast = false
      this.showDot = false
      if (this.privateToastTimer) clearTimeout(this.privateToastTimer)
      logger.send({
        tag: 'action',
        content: {
          msg: '切换辅导私聊窗口'
        }
      })

      this.$bus.$emit('chats.changePrivateMessageStatus')
    },
    async getUserAvatar() {
      const { nickName } = await getUserInfo()
      this.nickName = nickName
    },
    handleReply() {
      logger.send({
        tag: 'action',
        content: {
          msg: '点击回复按钮'
        }
      })
      this.showPrivateToast = false
      this.showDot = false
      if (this.privateToastTimer) clearTimeout(this.privateToastTimer)
      // 打开辅导私聊
      this.$bus.$emit('chats.changePrivateMessageStatus')
    },
    handleGotIt() {
      logger.send({
        tag: 'action',
        content: {
          msg: '点击知道了按钮'
        }
      })
      // 关闭弹窗
      this.showPrivateToast = false
      this.showDot = false
      if (this.privateToastTimer) clearTimeout(this.privateToastTimer)
    },
    // 滚动到底部
    handleScrollBottom() {
      // @log-ignore
      this.isShowNewMsgTip = false
      this.$nextTick(() => {
        const chatListDom = document.getElementById('group-chat-list')
        if (chatListDom) {
          const scrollHeight = Math.round(chatListDom.scrollHeight)
          const clientHeight = Math.round(chatListDom.clientHeight)
          chatListDom.scrollTop = scrollHeight - clientHeight + 1
          chatListDom.removeEventListener('scroll', this.handleOnScroll)
        }
      })
    },
    // 监听滚动事件
    handleOnScroll() {
      // @log-ignore
      const chatListDom = document.getElementById('group-chat-list')
      if (chatListDom) {
        const scrollHeight = Math.round(chatListDom.scrollHeight)
        const scrollTop = Math.round(chatListDom.scrollTop)
        const clientHeight = Math.round(chatListDom.clientHeight)
        if (scrollHeight <= scrollTop + clientHeight + 1) {
          this.isShowNewMsgTip = false
          chatListDom.removeEventListener('scroll', this.handleOnScroll)
        }
      }
    },
    // 监听私聊消息
    handleReceivePrivateMessage(msg) {
      if (this.privateMessageStatus) return
      logger.send({
        tag: 'action',
        content: {
          msg: '监听大班收到私聊消息',
          message: msg
        }
      })
      this.showPrivateToast = true
      this.showDot = true
      this.privateMsg = msg
      if (this.privateToastTimer) clearTimeout(this.privateToastTimer)
      this.privateToastTimer = setTimeout(() => {
        this.showPrivateToast = false
      }, 10000)
    },
    // 监听新消息
    handleReceiveNewMessage() {
      const chatListDom = document.getElementById('group-chat-list')
      const scrollHeight = Math.round(chatListDom.scrollHeight)
      const scrollTop = Math.round(chatListDom.scrollTop)
      const clientHeight = Math.round(chatListDom.clientHeight)
      // 跟移动端统一,如果当前聊天list在底部,添加新消息,滚动到底部,否则就显示[新消息]弹窗
      if (scrollHeight <= scrollTop + clientHeight + 1) {
        this.handleScrollBottom()
      } else {
        chatListDom.addEventListener('scroll', this.handleOnScroll)
        this.isShowNewMsgTip = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
pre {
  overflow: inherit;
}
.group-chat {
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  height: 100%;
}
.chat-footer-wrapper {
  position: relative;
  margin-bottom: 8px;
}
.chat-footer-container {
  position: relative;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 42px;
  padding: 0 6px;
  font-size: 14px;
  .private-badge {
    width: 36px;
    height: 36px;
    background-color: #2c2c2c;
    padding: 2px;
    border-radius: 50%;
    margin-right: 12px;
    margin-left: 10px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    ::v-deep .ant-badge-dot {
      top: 5px;
      right: 5px;
      box-shadow: none;
    }
    img {
      width: 26px;
      height: 26px;
      border-radius: 50%;
      background-color: #f4f5fa;
    }
  }
  .private-avatar {
    width: 26px;
    height: 26px;
  }
  .new-meg-tip {
    position: absolute;
    top: -30px;
    width: 30px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    font-size: 12px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 16px;
    color: #ffaa0a;
    cursor: pointer;
    .icon-more-bottom {
      display: inline-block;
      width: 10px;
      height: 10px;
      background: url('../img/icon-more-bottom.png') no-repeat;
      background-size: cover;
    }
  }
  .footer-live-box {
    width: 100%;
    margin-right: 16px;
  }
  .send-message-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 36px;
    border-radius: 20px;
    .message-box {
      position: relative;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      border-radius: 20px;
      // 私聊消息输入框
      &.private-message-box {
        width: 100%;
        input {
          padding-left: 15px;
        }
      }
    }
    .message-box-lock {
      background: rgba(0, 0, 0, 0.85);
      border-radius: 22px;
      margin: 0 auto;
    }
    .chatbox-closed {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffaa0a;
      font-size: 14px;
      cursor: default;
    }
    .express {
      margin: 0 8px 0 3px;
      width: 26px;
      height: 26px;
      background-image: url('./LargeEmojiPane/imgs/icon_emoji_normal.png');
      background-size: cover;
      cursor: pointer;
    }
    .select-express {
      background-image: url('./LargeEmojiPane/imgs/icon_emoji_selected.png');
    }
    .input-wrap {
      flex: 1;
      height: 36px;
      line-height: 36px;
      border-radius: 22px;
      background: rgba(255, 255, 255, 0.08);
      display: flex;
      align-items: center;
      padding-left: 10px;
      .quick-fadeback {
        width: 26px;
        height: 26px;
        background-image: url('../img/icon-face-normal.png');
        background-size: cover;
        margin: 0 10px 0 5px;
        cursor: pointer;
        &:hover {
          background-image: url('../img/icon-face-normal-hover.png');
        }
      }
      input {
        height: 100%;
        flex: 1;
        color: #dee2e7;
        &::placeholder {
          color: #a2aab8;
          font-weight: 400;
        }
        &.disabled::placeholder {
          color: #dee2e7;
        }
      }
    }
  }
  .emoji-container {
    position: fixed;
    height: calc(100% - 86px);
    width: 72.5%;
    bottom: 42px;
    z-index: 0;
    overflow: hidden;
    left: 0;
  }
  .emoji-z-index {
    z-index: 1000; // 表情面板要层级高于其他互动
  }
}
::v-deep .express-box .express-item img {
  width: 24px;
  height: 24px;
}

.message-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: scroll;
  // 通用消息样式
  .item-message {
    margin-bottom: 10px;
    // 标签型名称
    .name-label {
      display: inline-block;
      height: 18px;
      line-height: 16px;
      border-radius: 3px;
    }
    .message {
      word-break: break-all;
      color: #dee2e7;
    }
  }
  // 系统消息样式
  .message-system {
    margin: 25px 0 15px 0;
    .message-content {
      margin: 0 auto;
      text-align: center;
      max-width: 268px;
      padding: 3px 8px;
      background: rgba(255, 255, 255, 0.12);
      border-radius: 4px;
      .message {
        margin-left: 0;
        font-size: 12px;
        color: #dee2e7;
      }
    }
  }
  // 学生消息样式
  .message-student {
    .stu-message-content {
      display: flex;
      .message {
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        line-height: 16px;
        padding: 4px 6px;
        word-break: break-all;
      }
      section:nth-child(1) {
        max-width: 90%;
        .self,
        .others {
          .name {
            font-size: 14px;
            font-weight: 500;
            color: #a2aab8;
            height: 32px;
            line-height: 32px;
            overflow: hidden;
            word-break: break-all;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            flex: 1;
          }
        }
      }
      section:nth-child(2) {
        display: flex;
        flex-direction: column;
        margin-right: 16px;
        max-width: 90%;
        .content {
          display: flex;
          .send_error {
            display: inline-block;
            margin-right: 4px;
            width: 20px;
            height: 20px;
            background: url('../img/send_error.png') no-repeat;
            background-size: 100%;
          }
        }
      }
    }
    .self-chat-content {
      flex-direction: column;
      align-items: flex-end;
      .message {
        background: #ffaa0a;
        color: #172b4d;
      }
      section:nth-child(1) {
        margin-right: 16px;
      }
      section:nth-child(2) {
        margin-right: 16px;
      }
    }
    .others-chat-content {
      display: flex;
      align-items: flex-start;
      flex-direction: column;
      .message {
        background: #dee2e7;
        color: #172b4d;
      }
      section:nth-child(1) {
        margin-left: 16px;
      }
      section:nth-child(2) {
        display: flex;
        flex-direction: column;
        margin-right: 0px;
        margin-left: 16px;
      }
    }
  }
  // 教师消息样式
  .message-teacher {
    overflow: hidden;
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    section:nth-child(1) {
      margin-left: 16px;
      max-width: 90%;
      .name {
        color: #a2aab8;
        font-size: 14px;
      }
    }
    section:nth-child(2) {
      max-width: 90%;
      background: #02ca8a;
      border-radius: 4px;
      margin: -2px 0 0 18px;
      padding: 3px 5px;
      line-height: unset;
    }
    .message-content {
      margin: 8px 0 0 44px;
      line-height: 1.5;
    }
  }
}

::v-deep .message {
  img {
    width: 24px;
    height: 24px;
    vertical-align: bottom;
  }
}
.private-message-container {
  position: fixed;
  height: calc(100% - 67px);
  width: 72.5%;
  bottom: 0;
  left: 0;
  z-index: 1000;
}
.private-message-toast {
  position: absolute;
  width: 321px;
  bottom: 8px;
  right: 4px;
  // 利用伪元素实现一个顶角向右的三角形
  &:before {
    content: '';
    position: absolute;
    bottom: 14px;
    right: -8px;
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 8px solid #fff;
  }
  .message-header {
    // 背景图 toast-header.png
    height: 70px;
    background: url('../img/toast-header.png') no-repeat;
    background-size: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 14px;
    .header-left {
      display: flex;
      align-items: center;
      margin-left: 12px;
      max-width: 60%;
      .header-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #f4f5fa;
        overflow: hidden;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 28px;
          height: 28px;
          border-radius: 50%;
        }
      }
      .private-name {
        // 上下布局,居中对齐
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-left: 8px;
        overflow: hidden;
        .private-name__name {
          font-size: 14px;
          font-family: SFProRounded-Bold, SFProRounded;
          font-weight: bold;
          color: #ffffff;
          line-height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .private-name__position {
          font-size: 12px;
          font-family: SFProRounded-Medium, SFProRounded;
          font-weight: 500;
          color: rgba(255, 255, 255, 0.8);
          line-height: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  .message-body {
    width: 321px;
    background: #ffffff;
    padding: 16px;
    border-radius: 0px 0px 12px 12px;
    .private-message {
      font-size: 14px;
      font-family: SFProRounded-Medium, SFProRounded;
      font-weight: 500;
      color: #172b4d;
      line-height: 16px;
      // 最多5行
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 5;
      -webkit-box-orient: vertical;
      min-height: 40px;
    }
    .message-footer-btn {
      margin-top: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .btn {
        width: 120px;
        height: 32px;
        border-radius: 22px;
      }
    }
  }
}
</style>
