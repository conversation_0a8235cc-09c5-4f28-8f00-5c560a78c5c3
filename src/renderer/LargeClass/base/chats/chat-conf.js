/* eslint-disable */
/**
 * @name msgUserType
 * @description 聊天用户身份
 */
const msgUserType = {
  stu: 1, //　学生
  teacher_f: 2, // 辅导老师
  system: 3, // 系统
  mine: 4 // 学生自己
}

/**
 * @name replaceExpress
 * @description 替换表情图片
 * @param {String} text
 */
const replaceExpress = (text, isMegList) => {
  // @log-ignore
  if (isMegList && text == '[e]em_18[e]') return '<i class="get-good-method"></i>'

  // const reg = /\[e\](\d+)\[e\]/g
  // if (reg.test(text)) {
  //   return text.replace(reg, (em, it) => {
  //     return it
  //   })
  // }

  text = escapeHTML(text)
  return (
    text &&
    text.replace(/\[e\](em\_[1-9]{1,1}[0-9]{0,1})\[e\]/g, (em, item) => {
      return `<img src="${require(path + item + '.png')}" />`
    })
  )
}

/**
 * 
 * @param {String} text
 */
function escapeHTML(text) {
  const map = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#039;'
  };

  return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

/**
 * 判断是辅导老师还是主讲老师 还是学生
 * @return 0辅导 1主讲 2学生 3未知
 */
const msgFormType = str => {
  const prefix = str.substr(0, 2)
  if (prefix === 't_') {
    return 1
  }
  if (prefix === 'f_') {
    return 0
  }
  if (prefix === 's_' || str.substr(0, 3) === 'ws_') return 2

  return 3
}
/**
 * irc消息类型
 */
const ircMsgType = {
  CONTINUS_CORRECT: 142, // 连对激励
  NORMAL_ROOM_MESSAGE: 130 // 普通群聊类型
}
/**
 * 发送/接收者类型
 */
const userMsgType = {
  ASSIST_TEACHER: 0, // 辅导老师
  MAIN_TEACHER: 1, // 主讲老师
  STUDENT: 2, // 学生
  OTHERS: 3 // 其他
}
/**
 * @function
 * @name isTeacher
 * @description 是否是老师
 */
const isTeacher = str => {
  return str.substr(0, 2) === 't_' || str.substr(0, 2) === 'f_'
}

/**
 * @function
 * @name isStudent
 * @description 是否是学生
 */
const isStudent = str => {
  return str.substr(0, 2) === 's_' || str.substr(0, 3) === 'ws_'
}

/**
 * @function
 * @name getCursortPosition
 * @description 获取光标位置
 */
const getCursortPosition = ele => {
  let pos = 0
  if (document.selection) {
    ele.focus()
    const rng = document.selection.createRange()
    rng.moveStart('character', -ele.value.length)
    pos = rng.text.length
  } else if (ele.selectionStart || ele.selectionStart == '0') {
    pos = ele.selectionStart
  }

  return pos
}

/**
 * @name replaceExpressName
 * @description 替换表情
 * @param {String} text
 */
const replaceExpressName = text => {
  if (text) {
    return text
      .replace(/\[e\]smiling\[e\]/g, '[e]em_1[e]')
      .replace(/\[e\]grinning\[e\]/g, '[e]em_2[e]')
      .replace(/\[e\]flushe\[e\]/g, '[e]em_3[e]')
      .replace(/\[e\]satisfy\[e\]/g, '[e]em_4[e]')
      .replace(/\[e\]grimacing\[e\]/g, '[e]em_5[e]')
      .replace(/\[e\]winking\[e\]/g, '[e]em_6[e]')
      .replace(/\[e\]sweat\[e\]/g, '[e]em_7[e]')
      .replace(/\[e\]downcast\[e\]/g, '[e]em_8[e]')
      .replace(/\[e\]confounded\[e\]/g, '[e]em_9[e]')
      .replace(/\[e\]disappointed\[e\]/g, '[e]em_10[e]')
      .replace(/\[e\]loudlyCrying\[e\]/g, '[e]em_11[e]')
      .replace(/\[e\]tearsOfJoy\[e\]/g, '[e]em_12[e]')
      .replace(/\[e\]dizzy\[e\]/g, '[e]em_13[e]')
      .replace(/\[e\]pouting\[e\]/g, '[e]em_14[e]')
      .replace(/\[e\]thumbsUp\[e\]/g, '[e]em_15[e]')
      .replace(/\[e\]OK\[e\]/g, '[e]em_16[e]')
      .replace(/\[e\]victory\[e\]/g, '[e]em_17[e]')
  } else {
    return ''
  }
}

/**
 * @name expressList
 * @description emoji表情列表
 */
const path = './icons/'
const expressList = [
  {
    text: 'smiling',
    name: 'em_1'
  },
  {
    text: 'grinning',
    name: 'em_2'
  },
  {
    text: 'flushe',
    name: 'em_3'
  },
  {
    text: 'satisfy',
    name: 'em_4'
  },
  {
    text: 'grimacing',
    name: 'em_5'
  },
  {
    text: 'winking',
    name: 'em_6'
  },
  {
    text: 'sweat',
    name: 'em_7'
  },
  {
    text: 'downcast',
    name: 'em_8'
  },
  {
    text: 'confounded',
    name: 'em_9'
  },
  {
    text: 'disappointed',
    name: 'em_10'
  },
  {
    text: 'loudlyCrying',
    name: 'em_11'
  },
  {
    text: 'tearsOfJoy',
    name: 'em_12'
  },
  {
    text: 'dizzy',
    name: 'em_13'
  },
  {
    text: 'pouting',
    name: 'em_14'
  },
  {
    text: 'thumbsUp',
    name: 'em_15'
  },
  {
    text: 'OK',
    name: 'em_16'
  },
  {
    text: 'victory',
    name: 'em_17'
  }
]

/**
 * @function
 * @name isObj
 * @description 判断是否为对象
 */
const isObj = v => Object.prototype.toString.call(v) === '[object Object]'

/**
 * @function
 * @name messageCut
 * @description 消息截取
 * @param {Array} messages - 消息列表
 */
const messageCut = messages => {
  if (messages.length >= 300) {
    messages = messages.slice(-299)
  }
  return messages
}

/**
 * @name inStreamTime
 * @description 是否在断流期间
 */
const inStreamTime = (arr, currentTime) => {
  if (!Array.isArray(arr) || !arr.length) return false

  return arr.reduce((origin, item) => {
    const { beginTime, endTime } = item
    if (currentTime > endTime) {
      origin += endTime - beginTime
    }
    return origin
  }, 0)
}

/**
 * @description 判断是否开启聊天区
 * @returns 3开启 1关闭
 *
 */
const chatStatus = (data, mode) => {
  // eslint-disable-next-line camelcase
  let { openchat, openchat_f } = data

  // eslint-disable-next-line camelcase
  if (openchat !== undefined || openchat_f !== undefined) {
    openchat = openchat === undefined ? true : openchat
    // eslint-disable-next-line camelcase
    openchat_f = openchat_f === undefined ? true : openchat_f
    // eslint-disable-next-line camelcase
    const status = mode ? openchat : openchat_f

    return status ? 3 : 1
  }
}
const chatMsgPriority = {
  topic: 0,
  notice: 1,
  privMsg: 99
}

export {
  chatMsgPriority,
  msgUserType,
  replaceExpress,
  isTeacher,
  isStudent,
  expressList,
  getCursortPosition,
  replaceExpressName,
  isObj,
  messageCut,
  inStreamTime,
  chatStatus,
  msgFormType,
  ircMsgType,
  userMsgType
}
