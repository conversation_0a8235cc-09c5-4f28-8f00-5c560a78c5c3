import Vue from 'vue'
import request from '@thinkacademy/live-framework/libs/create-api/index.js'
import { messageCenter } from '@thinkacademy/live-framework/components/framework/message-center'
import {
  chatMsgPriority,
  isTeacher,
  msgUserType,
  msgFormType,
  ircMsgType,
  userMsgType
} from './chat-conf'
import SignalService from '@/LargeClass/base/signal-service'
import { queryPeerMessageHistory } from 'api/classroom/index'
import logger from 'utils/logger'
import { getUserInfo } from 'utils/userInfo'

/**
 * @class
 * @name ChatMain
 * @description 聊天
 * @param {Object} options 创建实例需要的参数
 *
 * @example
 * // 创建初始化聊天实例
 * new Chat(options)
 */
export default class ChatMain {
  constructor(options = {}) {
    this.options = options
    this.msgTip = options.msgTip
    this.inputStatusMap = options.inputStatusMap
    this.blurTimer = null
    this.isWindowBlur = false
    const defaultOpt = {
      // 学生是否在房间
      stuInRoom: false,
      // 老师是否在房间
      teaInRoom: false,
      // 锁定只看老师消息
      lockTeacherMessage: false,
      // 有新消息
      hasNewMsg: false,
      // 有新私聊消息
      hasPrivateNewMsg: false,
      userNotInMegBottom: true,
      // 我的名字
      myName: '',
      // 群聊消息列表
      messages: [],
      // 私聊消息列表
      privateMessages: [],
      // 聊天连乘激励
      encourageData: [],
      // 聊天区连乘激励
      encourageStatus: false,
      // 在线用户列表
      userList: [],
      // input的一些状态
      inputStatus: {
        // 开启聊天
        openChat: false,
        // 禁言
        peerMuteChat: false,
        // input展示话术
        text: this.inputStatusMap[5],
        // 1:老师关闭聊天 2:禁言 3:老师开启聊天 4:5s内不能再次发送消息 5:未开启
        status: 5
      },
      // 聊天展示区
      messagesRef: document.getElementById('messagesRef'),
      // 父组件传过来的参数
      parentData: {},

      // 回放的一些参数
      playBackData: {
        // 接口获取到的数据
        originMessages: [],
        // 断流时间
        streamTime: 0,
        // 拦截请求次数
        preventRequestCon: 0,
        // 数据较少次数
        lessDataCon: 0,
        // 上一次聊天最后的时间
        msgEndTime: null
      },
      // 聊天区互动消息数据
      discussInteractionData: {},
      // 是否断线中
      isBrokenLine: true,
      // 发送群聊消息状态
      sendRoomMessageFail: false,
      // 私聊消息发送状态
      sendPrivateMessageFail: false
    }
    const opts = Object.assign(defaultOpt, options)

    this.stuInRoom = opts.stuInRoom
    this.teaInRoom = opts.teaInRoom
    this.lockTeacherMessage = opts.lockTeacherMessage
    this.messages = opts.messages
    this.privateMessages = opts.privateMessages
    this.encourageData = opts.encourageData
    this.userList = opts.userList
    this.inputStatus = opts.inputStatus
    this.parentData = opts.chatOptions
    this.playBackData = opts.playBackData
    this.messagesRef = opts.messagesRef
    this.userNotInMegBottom = opts.userNotInMegBottom
    this.hasNewMsg = opts.hasNewMsg
    this.hasPrivateNewMsg = opts.hasPrivateNewMsg
    this.sendMsgFrom = 'flv'
    this.discussInteractionData = opts.discussInteractionData
    this.isBrokenLine = opts.isBrokenLine
    this.encourageStatus = opts.encourageStatus
    this.sendRoomMessageFail = opts.sendRoomMessageFail
    this.sendPrivateMessageFail = opts.sendPrivateMessageFail
    // 0辅导 1主讲
    this.mode = this.parentData.streamMode

    this.goodMethod = false
    // https://wiki.zhiyinlou.com/pages/viewpage.action?pageId=50460105

    const { nickName } = this.parentData
    this.myName = nickName
    this.chatNum = 0

    console.log('%c进入聊天main.js', 'color: red;')
  }

  /**
   * @name Chat#init
   * @description 初始化
   */
  init() {
    try {
      // 查询私聊消息历史, 辅导id不能为0
      if (this.options.counselorInfo.id) {
        this.initPrivateMessagesHistory()
      }
      const { roomlist, planId, stuId } = this.parentData

      window.onbeforeunload = () => {
        if (!this.stuInRoom) return

        const logData = {
          live_id: planId,
          uid: stuId,
          roomlist,
          loginStatus: 'onbeforeunload'
        }
        messageCenter.emit('logger', {
          type: 'sys',
          data: {
            logData,
            logType: 'irc'
          }
        })
      }
    } catch (e) {
      console.error('%c报错信息', 'color: red;', e.message)
    }
  }
  /**
   * @param {*} data
   * @description 处理kv消息
   */
  onRecvRoomDataUpdateNotice(data) {
    // @log-ignore
    console.log('chat-main-onRecvRoomDataUpdateNotice', data)
    if (!data.datas) {
      return
    }
    data.datas.forEach((item, key) => {
      const noticeValue = JSON.parse(item.value)
      const noticeContent = noticeValue[key]
      // 处理kv消息
      switch (key) {
        // 老师开启、关闭聊天
        case 'openchat':
          this.handleUpdateNoticeByOpenchat(noticeContent)
          break
        // 单独禁言、单独解禁
        case 'peer_mute_chat':
          this.handleUpdateNoticeByPeerMuteChat(noticeContent)
          break
      }
    })
  }

  /**
   * 处理老师开启、关闭聊天消息
   * @param {Object} noticeContent
   */
  handleUpdateNoticeByOpenchat(noticeContent) {
    logger.send({
      tag: 'chatbox',
      content: {
        msg: noticeContent ? '主讲老师开启聊天区' : '主讲老师关闭聊天区'
      }
    })
    if (noticeContent) {
      // 老师开启聊天
      this.inputStatus.openChat = true
      if (this.inputStatus.peerMuteChat) {
        // 设置禁言状态
        this.setInputStatus(2)
        logger.send({
          tag: 'chatbox',
          content: {
            msg: '聊天区开启,但是被单独禁言'
          }
        })
      } else {
        // 老师开启聊天
        this.setInputStatus(3)
        logger.send({
          tag: 'chatbox',
          content: {
            msg: '聊天区开启'
          }
        })
      }
    } else {
      this.inputStatus.openChat = false
      // 老师关闭聊天
      this.setInputStatus(1)
      logger.send({
        tag: 'chatbox',
        content: {
          msg: '聊天区关闭'
        }
      })
    }
  }

  /**
   * 处理单独禁言、解除禁言
   * @param {Object} noticeContent
   */
  handleUpdateNoticeByPeerMuteChat(noticeContent) {
    const { stuId, mute } = noticeContent
    // 判断是否自己
    if (stuId == this.parentData.stuId) {
      this.inputStatus.peerMuteChat = mute
      if (mute) {
        // 禁言
        this.setInputStatus(2)
        // 发送系统消息
        this.setMessage({
          type: msgUserType.system,
          name: this.msgTip.SPEECH_SYS,
          msg: this.msgTip.BAN_SPEECH
        })
        logger.send({
          tag: 'chatbox',
          content: {
            msg: '已被单独禁言'
          }
        })
      } else {
        // 发送系统消息
        this.setMessage({
          type: msgUserType.system,
          name: this.msgTip.SPEECH_SYS,
          msg: this.msgTip.RELIEVE_SPEECH
        })
        // 如果老师开启聊天状态
        if (this.inputStatus.openChat) {
          // 老师开启聊天
          this.setInputStatus(3)
          logger.send({
            tag: 'chatbox',
            content: {
              msg: '解除禁言,聊天区当前开启'
            }
          })
        } else {
          // 老师关闭聊天
          this.setInputStatus(1)
          logger.send({
            tag: 'chatbox',
            content: {
              msg: '解除禁言,聊天区当前关闭'
            }
          })
        }
      }
    }
  }

  /**
   * @param {*} data
   * @description 获取聊天历史消息
   */
  onGetRoomHistoryMessageResponse(data) {
    if (data.code != 0) {
      return
    }
    const messageList = data.content
    if (!messageList || !messageList.length) {
      return
    }
    // 临时消息列表
    let tempMessage = []
    // 消息遍历处理
    messageList.forEach(item => {
      if (item.priority === chatMsgPriority.privMsg) {
        let textMsg
        try {
          textMsg = JSON.parse(item.text)
        } catch (e) {
          console.error(e)
        }
        // 消息参数
        let messageParams = {}
        const _msgFormType = msgFormType(item.sender)
        // 辅导老师消息
        if (_msgFormType === userMsgType.ASSIST_TEACHER) {
          messageParams = {
            type: msgUserType.teacher_f,
            name: textMsg.name,
            msg: textMsg.msg,
            tutor_avatar: textMsg.tutor_avatar
          }
        }
        // 学生消息
        // 连对称号抛出的消息 聊天区域不展示 type 142
        // 新增表情消息
        if (textMsg.ircType == 'send_emoji' || textMsg.ircType == 'animation_emoji') {
          messageParams = {
            type: msgUserType.stu,
            name:
              item.sender === this.options.configs.stuIrcId
                ? this.msgTip.SPEECH_MINE
                : textMsg.from?.username,
            isMe: item.sender === this.options.configs.stuIrcId, // 判断是否自己发送
            msg: textMsg.data.name, // 发送人
            emojiType: textMsg.data.type, // 表情类型
            lottieUrl: textMsg.data.resource?.lottieUrl || '', // lottie url
            emojiId: textMsg.data.resource?.emojiId || 0, // 表情id
            isNewEmoji: true, // 是否是新增表情类型
            avatar: item.sender === this.options.configs.stuIrcId ? '' : textMsg.from?.path
          }
        } else {
          // 原来文本聊天逻辑
          // 学生消息 && 非连对激励消息
          if (
            _msgFormType === userMsgType.STUDENT &&
            Number(textMsg.type) === ircMsgType.NORMAL_ROOM_MESSAGE
          ) {
            messageParams = {
              type: msgUserType.stu,
              name:
                item.sender === this.options.configs.stuIrcId
                  ? this.msgTip.SPEECH_MINE
                  : textMsg.name,
              isMe: item.sender === this.options.configs.stuIrcId, // 判断是否自己发送
              msg: textMsg.msg, // 消息体
              isNewEmoji: false, // 是否是新增表情类型
              avatar: textMsg?.path || ''
            }
          }
        }
        messageParams.id = this.chatNum++
        tempMessage.push(messageParams)
      }
    })
    // 数组颠倒排序
    tempMessage.reverse()
    if (!tempMessage.length) {
      return
    }
    // 消息重新赋值
    this.messages = tempMessage
    this.hasNewMsg = false
    // // 滚动到最新消息处
    // setTimeout(() => {
    //   this.messagesRef.scrollTop = this.messagesRef.scrollHeight
    // }, 20)
  }

  /**
   * 此方法待确认是否有用
   * @param {*} res
   * @description 登出
   */
  onLogoutNotice(res) {
    if (!res.userInfo) return
    const { nickname } = res.userInfo
    let isTeacherFlag
    if (isTeacher(nickname)) {
      isTeacherFlag = true
    } else {
      isTeacherFlag = false
    }

    // const { isTeacherFlag, nickname } = res
    if (isTeacherFlag) {
      this.teaInRoom = false
      messageCenter.emit('interaction', {
        type: 'onStuAndTeaStatus',
        data: {
          stuInRoom: this.stuInRoom,
          teaInRoom: this.teaInRoom
        }
      })
    } else {
      this.userList = this.userList.filter(item => item !== nickname)
    }
  }

  /**
   * @function
   * @name recvPeerMessageT
   * @description 单聊消息T处理
   */
  recvPeerMessageT() {
    this.stuInRoom = false
    messageCenter.emit('interaction', {
      type: 'onStuAndTeaStatus',
      data: {
        stuInRoom: this.stuInRoom,
        teaInRoom: this.teaInRoomx
      }
    })

    this.userList = []

    this.setInputStatus(5)

    this.setMessage({
      type: msgUserType.system,
      name: this.msgTip.SPEECH_SYS,
      msg: this.msgTip.REMOTE_LOGIN
    })

    // 端内加载的时候通知端退出直播间
    Vue.prototype.$bus.$emit('live-kickout', true)
  }

  /**
   * @param {*} res
   * @description 被踢下线
   */
  onKickoutNotice(res) {
    // 重复登录被踢下线
    if (res.code === 301) {
      logger.send({
        tag: 'student.IRCKick'
      })
      logger.send({
        tag: 'ircStatus',
        content: {
          msg: `Kickout: ${JSON.stringify(res)}`
        }
      })
      this.recvPeerMessageT(true)
    }
  }

  /**
   * @param @description 网络未知 0
   */
  serverUnkown() {
    const ITC_STATUS = 0
    Vue.prototype.$bus.$emit('ircConnectStatus', ITC_STATUS)
  }

  /**
   * @param @description 网络不可用 1
   */
  serverUnavailable() {
    const ITC_STATUS = 1
    Vue.prototype.$bus.$emit('ircConnectStatus', ITC_STATUS)
  }

  /**
   * @param @description 服务器连接失败 2
   */
  serverFailed() {
    const ITC_STATUS = 2
    Vue.prototype.$bus.$emit('ircConnectStatus', ITC_STATUS)
    logger.send({
      tag: 'ircStatus',
      content: {
        msg: 'Connection failed'
      }
    })
  }
  /**
   * @description 服务器重连 3
   */
  serverConnection() {
    console.log('%c收到serverConnection 服务器重连', 'color: red;')
    const ITC_STATUS = 3
    Vue.prototype.$bus.$emit('ircConnectStatus', ITC_STATUS)
    this.setMessage({
      type: msgUserType.system,
      name: this.msgTip.SPEECH_SYS,
      msg: this.msgTip.RECONNECT
    })

    this.userList = []
    this.isBrokenLine = true

    // 上报日志
    logger.send({
      tag: 'ircStatus',
      content: {
        msg: 'Connecting'
      }
    })
  }
  /**
   * @description 服务器重连成功 4
   */
  serverConnected() {
    // 销毁重连消息提示
    Vue.prototype.$Notification.destroy()
    document.getElementById('interactionFullPage').removeAttribute('style')
    const ITC_STATUS = 4
    Vue.prototype.$bus.$emit('ircConnectStatus', ITC_STATUS)
    // 上报日志
    logger.send({
      tag: 'ircStatus',
      content: {
        msg: 'Connection successful'
      }
    })
  }
  /**
   * @param @description 服务器断开连接 5
   */
  disconnect() {
    const ITC_STATUS = 5
    Vue.prototype.$bus.$emit('ircConnectStatus', ITC_STATUS)
    logger.send({
      tag: 'ircStatus',
      content: {
        msg: 'Disconnect'
      }
    })
    this.messages.push({
      type: msgUserType.system,
      name: this.msgTip.SPEECH_SYS,
      msg: this.msgTip.CHAT_DISCONNECT
    })
  }

  /**
   *
   * @param {*} res
   * @description 加入房间回调消息
   */
  onJoinRoomResponse(res) {
    console.log('%c收到加入聊天室成功', 'color: red;', res)

    if (res.code != 0) return

    this.isBrokenLine = false
    // 发送系统消息
    this.setMessage({
      type: msgUserType.system,
      name: this.msgTip.SPEECH_SYS,
      msg: this.msgTip.CONNECT
    })

    // 开启聊天
    this.setInputStatus(3)
    this.stuInRoom = true

    messageCenter.emit('interaction', {
      type: 'onStuAndTeaStatus',
      data: { stuInRoom: this.stuInRoom, teaInRoom: this.teaInRoom }
    })
  }

  /**
   * @description 设置inputStatus状态
   */
  setInputStatus(status) {
    this.inputStatus.status = status
    this.inputStatus.text = this.inputStatusMap[status]
    if (status == 1) {
      // 老师关闭聊天标记
      this.inputStatus.openChat = false
    }
    if (status == 2) {
      this.inputStatus.peerMuteChat = true
    }
    if (status == 3) {
      // 老师开启聊天标记
      this.inputStatus.openChat = true
    }
  }
  /**
   *
   * @description 群聊消息回调
   */
  onSendRoomMessageResponse(message) {
    console.log('onSendRoomMessageResponse', message)
    this.sendRoomMessageFail = message.code !== 0
    if (this.sendRoomMessageFail) {
      logger.send({
        tag: 'student.IRCMsgSend',
        content: {
          msg: `SendRoomMessageResponse: ${message.code} ${message.info}`
        }
      })
      this.messages.forEach((item, index) => {
        if (item.type === msgUserType.mine && item.preMsgId === message.preMsgId) {
          Vue.prototype.$set(this.messages[index], 'messageStatus', this.sendRoomMessageFail)
        }
      })
    }
  }
  /**
   *
   * @description 单聊消息回调
   */
  onSendPeerMessageResponse(message) {
    console.log('%c收到单聊消息回调', 'color: red;', message)
    this.sendPrivateMessageFail = message.code !== 0
    if (this.sendPrivateMessageFail) {
      logger.send({
        tag: 'student.IRCMsgSend',
        content: {
          msg: `SendPeerMessageResponse: ${message.code} ${message.info}`
        }
      })
      this.privateMessages.forEach((item, index) => {
        if (item.type === msgUserType.mine && item.preMsgId === message.preMsgId) {
          Vue.prototype.$set(
            this.privateMessages[index],
            'messageStatus',
            this.sendPrivateMessageFail
          )
        }
      })
    }
  }
  /**
   * @function
   * @name ChatMain#onJoinRoomNotice
   * @description 他人进入聊天室回调函数
   */
  onJoinRoomNotice(res) {
    console.log('%c收到别人进入聊天室通知', 'color: red;', res)
    if (!res.userInfo) return

    const { nickname } = res.userInfo

    if (isTeacher(nickname)) {
      this.teaInRoom = true
      messageCenter.emit('interaction', {
        type: 'onStuAndTeaStatus',
        data: { stuInRoom: this.stuInRoom, teaInRoom: this.teaInRoom }
      })
      return
    }

    this.userList.push(res.userInfo.nickname)
  }

  /**
   * 此方法待确认是否有用
   * @name ChatMain#onRecvRoomUserList
   * @description 接收聊天室用户列表回调函数
   */
  onRecvRoomUserList(res) {
    console.log('%c收到到聊天室用户列表', 'color: red;', res)

    this.teaInRoom = false
    messageCenter.emit('interaction', {
      type: 'onStuAndTeaStatus',
      data: {
        stuInRoom: this.stuInRoom,
        teaInRoom: this.teaInRoom
      }
    })

    res.userList.filter(item => {
      const { nickname } = item
      if (!this.userList.includes(nickname)) {
        this.userList.push(nickname)
      }
    })
    // 聊天室昵称列表结束
    if (res.code === 53) {
      for (let i = 0; i < this.userList.length; i++) {
        if (isTeacher(this.userList[i])) {
          this.teaInRoom = true
          messageCenter.emit('interaction', {
            type: 'onStuAndTeaStatus',
            data: {
              stuInRoom: this.stuInRoom,
              teaInRoom: this.teaInRoom
            }
          })
          break
        }
      }
    }
  }
  /**
   * @function
   * @name ChatMain#onLeaveRoomNotice
   * @description 他人离开聊天室回调函数
   */
  onLeaveRoomNotice(res) {
    console.log('%c收到他人离开聊天室通知', 'color: red;', res)
    if (res.userInfo) {
      const { nickname } = res.userInfo
      if (isTeacher(nickname)) {
        this.teaInRoom = false
        messageCenter.emit('interaction', {
          type: 'onStuAndTeaStatus',
          data: {
            stuInRoom: this.stuInRoom,
            teaInRoom: this.teaInRoom
          }
        })
      } else {
        this.userList = this.userList.filter(item => item !== nickname)
      }
    }
  }

  /**
   * @function
   * @name ChatMain#onRecvRoomMessage
   * @description 群聊消息回调函数
   */
  onRecvRoomMessage(res) {
    console.log('%c收到群聊消息', 'color: red;', res)
    const { messagePriority, content, fromUserInfo = {} } = res
    const { nickname = '' } = fromUserInfo
    const _msgFormType = msgFormType(nickname)
    const data = JSON.parse(content)
    // 点赞消息
    Vue.prototype.$bus.$emit('chats.getThumbNumMsg', data)
    messagePriority === chatMsgPriority.privMsg &&
      this.recvRoomMessagePri(data, _msgFormType, nickname)
  }

  /**
   * @function
   * @name recvRoomMessagePri
   * @description 群聊消息的Pri消息处理
   */
  recvRoomMessagePri(data, _msgFormType, nickname = '') {
    const { type, name, msg, tutor_avatar = '' } = data
    // 聊天 -- 连对称号抛出的消息 聊天区域不展示 type 142
    if (data.type == ircMsgType.CONTINUS_CORRECT) {
      // 广播连对称号消息
      Vue.prototype.$bus.$emit('chats.correctMedalData', data)
      return
    }
    let messageParams = {}
    if (type * 1 === ircMsgType.NORMAL_ROOM_MESSAGE) {
      // 过滤掉自己的消息，因为自己的消息已经渲染过了
      if (nickname !== this.parentData.nick || this.options.commonOption.isParent) {
        // 消息参数
        // 辅导老师消息
        if (_msgFormType === userMsgType.ASSIST_TEACHER) {
          messageParams = {
            type: msgUserType.teacher_f, // 判断是否是辅导老师
            name,
            msg,
            tutor_avatar
          }
        }
        // 学生消息
        if (_msgFormType === userMsgType.STUDENT) {
          messageParams = {
            type: msgUserType.stu,
            name,
            msg,
            avatar: data.path || ''
          }
        }
      } else {
        console.warn('过滤掉自己的消息，因为自己的消息已经渲染过了', msg)
      }
    } else {
      // 本地表情配置 本地表情类型 1
      const commonOptions = {
        type: msgUserType.stu,
        name: data.from?.username,
        isMe: false,
        msg: data.data?.name,
        isNewEmoji: true,
        avatar: data.from?.path || ''
      }
      if (data.ircType == 'send_emoji' && data.data.type == 1) {
        messageParams = commonOptions
      } else if (
        data.ircType == 'animation_emoji' &&
        (data.data.type == 2 || data.data.type == 3)
      ) {
        // 动态表情配置 动态表情 图片类型2, lottie类型3
        messageParams = {
          ...commonOptions,
          lottieUrl: data.data.resource?.lottieUrl || '',
          emojiId: data.data.resource?.emojiId || 0,
          emojiType: data.data.type
        }
      }
    }

    this.setMessage(messageParams)
  }

  /**
   * @function
   * @name ChatMain#onRecvPeerMessage
   * @description 接收单聊消息回调函数
   */
  onRecvPeerMessage(res) {
    console.log('%c收到单聊消息', 'color: red;', res)

    const { msgPriority, content, fromUserInfo = {} } = res
    const { nickname = '' } = fromUserInfo
    const _msgFormType = msgFormType(nickname)
    const data = JSON.parse(content)

    msgPriority === chatMsgPriority.privMsg && this.recvPeerMessagePri(data, _msgFormType)
  }

  /**
   * @function
   * @name recvPeerMessagePri
   * @description 单聊消息的Pri消息处理
   */
  recvPeerMessagePri(data) {
    const { type, name, msg, tutor_avatar, suid } = data
    console.log('recvPeerMessagePri', data, this.parentData)
    // 聊天
    if (type == ircMsgType.NORMAL_ROOM_MESSAGE) {
      // 判断是否单聊给自己的
      if (suid == this.parentData.stuId) {
        // 默认功能只有辅导老师才能单聊
        this.setPrivateMessage({
          type: msgUserType.teacher_f,
          name,
          msg,
          isPrivateMsg: true,
          tutor_avatar
        })
        // 广播私聊消息推送通知
        Vue.prototype.$bus.$emit('chats.privateMessagePush', msg)
      }
    }
    // 辅导私聊消息
    if (type == 140) {
      // 辅导老师批改作业通知
      // 敏感词问题 消息题格式需兼容
      const resceiveMsg = data.parameter ? data.parameter : data.msg
      const correctPicrure = JSON.parse(resceiveMsg).correct_picrure_t
      if (!correctPicrure) {
        return
      }
      Vue.prototype.$bus.$emit('chats.assignmentCheckedPush', correctPicrure)
    }
  }

  /**
   * 添加私聊消息
   * @param {*} data
   */
  async setPrivateMessage(data) {
    // 最多300条消息
    if (this.privateMessages.length >= 300) {
      this.privateMessages = this.privateMessages.slice(-299)
    }
    this.privateMessages.push(data)
    if (data.type === msgUserType.mine) {
      // 获取用户信息
      const { avatar } = await getUserInfo()
      const tutorRoomId = this.options.configs.tutorIrcId
      const opts = {
        nickname: tutorRoomId,
        content: this.baseCommonUserInfo(data, avatar),
        chatMsgPriority: chatMsgPriority.privMsg
      }
      const pre = SignalService.sendPeerMessageWithPreMsgId(opts)

      if (pre.code !== 0) {
        console.log('发送单聊消息失败', pre)
      } else {
        this.privateMessages[this.privateMessages.length - 1].preMsgId = pre.preMsgId
      }
    }
  }

  /**
   * @function
   * @name Chat#setMessagejudge
   * @description 添加消息的判断
   * @param {Object} data - 消息
   */
  setMessagejudge(data) {
    /**
     * @description 开启了只看老师&消息为学生
     */
    if (this.lockTeacherMessage && data.type === msgUserType.stu) return

    // 最多300条消息
    if (this.messages.length >= 300) {
      this.messages = this.messages.slice(-299)
    }
    data.id = this.chatNum++
    this.liveJudge(data)
  }
  /**
   * @function
   * @name Chat#liveJudge
   * @description 直播逻辑处理
   * @param {Object} data - 消息
   */
  async liveJudge(data) {
    this.messages.push(data)
    // 广播私聊消息推送通知
    Vue.prototype.$bus.$emit('chats.messagePush')
    // 本地发消息
    if (data.type === msgUserType.mine) {
      let opts
      const { roomlist } = this.parentData
      // 获取用户信息
      const { nickName, avatar } = await getUserInfo()
      if (!data.isNewEmoji) {
        opts = {
          roomlist,
          content: this.baseCommonUserInfo(data, avatar),
          chatMsgPriority: chatMsgPriority.privMsg
        }
      } else {
        const commonIrcConfig = {
          roomlist,
          content: {
            ircType: 'send_emoji',
            data: {
              name: data.msg,
              type: data.emojiType
            },
            from: {
              username: nickName,
              path: avatar
            }
          },
          chatMsgPriority: chatMsgPriority.privMsg
        }
        if (data.emojiType == 1) {
          // 本地表情
          opts = commonIrcConfig
        } else if (data.emojiType == 2 || data.emojiType == 3) {
          // 动态表情 lottie类型 2 图片类型 3
          opts = commonIrcConfig
          opts.content.ircType = 'animation_emoji'
          opts.content.data.resource = {
            emojiName: data.msg,
            emojiId: data.emojiId,
            emojiPicture: data.emojiPicture,
            lottieUrl: data.lottieUrl
          }
        }
      }
      console.log('opts', opts)
      const pre = SignalService.sendRoomMessageWithPreMsgId(opts)
      if (pre && pre.code == 0) {
        this.messages[this.messages.length - 1].preMsgId = pre.preMsgId
      } else {
        console.error('大班群聊消息发送失败', pre)
      }
      console.log('pre', pre)

      // 讨论区互动逻辑start
      const { discussInteractionUrl } = this.parentData
      if (discussInteractionUrl) {
        if (this.discussInteractionData && this.discussInteractionData.pub) {
          // 教师端统计表----在教师端可以出发点赞
          this.sendMegToInterface(
            this.discussInteractionData.interactId,
            data.msg,
            discussInteractionUrl
          )
        }
      }

      // 开启聊天
      if (this.inputStatus.status === 3) {
        // 5s内不能再次发送消息
        this.inputStatus.status = 4

        let timer = null
        let count = 0
        clearInterval(timer)
        timer = setInterval(() => {
          count++
          if (count >= 3) {
            if (this.inputStatus.status === 4) {
              this.inputStatus.status = 3
            }
            clearInterval(timer)
            timer = null
          }
        }, 1000)
      }
    }
  }

  /**
   * @function
   * @name
   * @description 发送消息给接口
   */
  async sendMegToInterface(interactionId, message, discussInteractionUrl) {
    const {
      bizId,
      planId,
      nick,
      psId,
      imgPath,
      stuName,
      classId,
      teamId
      // subjectIds,
      // stuId,
      // gradeIds,
      // courseId
    } = this.parentData
    const res = await request.post(discussInteractionUrl, {
      bizId,
      planId,
      interactionId,
      message,
      stuIrcId: nick,
      psId,
      imgPath,
      name: stuName,
      classId,
      teamId,
      // 1:三分屏的轻互动  2:大班整合语音弹幕   3:大班整合的轻互动
      businessType: 3
    })
    if (!res.stat) {
      messageCenter.emit('exception', {
        level: 'toast',
        data: {
          message: res.msg,
          duration: '1000'
        }
      })
    }
    messageCenter.emit('logger', {
      type: 'interactive',
      data: {
        elem: document.body,
        eventtype: 'live_liveroom',
        stable: 1,
        sno: '100.3',
        logtype: 'submit',
        ex: res.stat === 1 ? 'Y' : 'N',
        interactionId
      }
    })
  }

  /**
   * @function
   * @name Chat#setMessage
   * @param {Object} opts = { type 类型, name 名字, text 话术 }
   * @description 聊天区添加消息方法
   */
  setMessage(opts) {
    this.setMessagejudge(opts)
  }
  /**
   * 基础用户信息
   */
  baseCommonUserInfo(data, avatar) {
    return {
      from: this.sendMsgFrom,
      name: this.myName,
      msg: data.msg,
      type: 130,
      evenexc: data.evenexc,
      path: avatar
    }
  }
  /**
   * @function
   * @name Chat#getBaseTime
   * @description 获取回放开始时间
   */
  getBaseTime() {
    const { gotoClassTime, stime } = this.parentData
    return gotoClassTime || parseInt(stime)
  }
  /**
   * 初始化私聊消息历史
   */
  async initPrivateMessagesHistory() {
    const res = await queryPeerMessageHistory({
      planId: this.parentData.planId,
      tutorId: this.options.counselorInfo.id
    })

    console.log('initPrivateMessagesHistory', res)

    if (!res || res.code != 0) {
      return
    }

    const data = res.data || {}
    const list = data.list || []

    if (!list.length) {
      return
    }

    // 临时消息存储
    let tempMessages = []

    list.forEach(item => {
      const message = JSON.parse(item.message)
      // 学生消息
      if (item.sendTo == msgUserType.stu) {
        tempMessages.push({
          type: msgUserType.mine,
          msg: message.msg,
          name: item.studentId == this.options.stuInfo.id ? this.msgTip.SPEECH_MINE : message.name,
          isPrivateMsg: true
        })
      }
      // 辅导老师消息
      if (item.sendTo == msgUserType.teacher_f) {
        tempMessages.push({
          type: msgUserType.teacher_f,
          msg: message.msg,
          name: message.name,
          tutor_avatar: message.tutor_avatar,
          isPrivateMsg: true
        })
      }
    })

    // 私聊消息显示历史
    this.privateMessages = tempMessages
  }
}
