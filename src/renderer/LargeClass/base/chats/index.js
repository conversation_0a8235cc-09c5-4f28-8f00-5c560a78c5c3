import app from './index.vue'
import Vue from 'vue'
import ChatBase from '@thinkacademy/live-framework/components/base/chats/chat-base'
import ChatMain from './chat-main.js'
import { i18n } from 'locale'
import { getConfig } from 'utils/initConfig'
import { getSchoolCode } from 'utils/local'
export default class Chat extends ChatBase {
  // 1、把直播的video初始化
  // 2、把功能条初始化
  constructor(options = {}) {
    // console.log('chats', options)
    super()
    this.options = options
    this.initChat(options.dom)
  }

  /**
   *
   * @param {*} dom
   * @description 初始化chat
   */
  async initChat(dom) {
    // 初始化
    this.vm = this.createVueChat(app)
    this.render(dom, this.vm)
    const chatsConf = this.vm.$t('classroom.largeClass.chats')
    const config = await getConfig()
    const schoolCode = await getSchoolCode()
    chatsConf.msgTip = this.vm.$t('classroom.largeClass.chats.msgTip')
    chatsConf.msgTip.BAN_SPEECH = this.vm.$t('classroom.largeClass.chats.msgTip.BAN_SPEECH')
    chatsConf.msgTip.RELIEVE_SPEECH = this.vm.$t(
      'classroom.largeClass.chats.msgTip.RELIEVE_SPEECH'
    )
    this.vm.ChatClass = new ChatMain({
      msgTip: chatsConf.msgTip,
      inputStatusMap: this.vm.$t('classroom.largeClass.chats.inputStatusMap'),
      ...this.options,
      sendMsgFrom: 'flv'
    })
  }

  createChatProps() {
    // console.log('chat-----', this.options, this.options.configs.skinType)
    const opts = {
      skinType: this.options.configs.skinType,
      options: this.options.commonOption
    }
    return opts
  }

  createVueChat(app) {
    const Constructor = Vue.extend(app)
    let vm = null
    const props = this.createChatProps()
    vm = new Constructor({
      i18n,
      propsData: props
    })
    vm.$mount()
    this.vm = vm
    return vm
  }

  eventHandler(params) {
    console.log('chat-eventHandler', params, this.options, this.vm)
    const { type, data } = params
    if (this.vm.ChatClass[type]) {
      this.vm.ChatClass[type](data)
    }
  }
}
