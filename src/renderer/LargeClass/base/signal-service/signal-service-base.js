import { ChatResultCode } from './chat-sdk/signal-service-conf.js'
import OnChatClientRes from './chat-sdk/on-chat-client-res.js'
import OnRoomRes from './chat-sdk/on-room-res.js'
import { messageCenter } from '@thinkacademy/live-framework/components/framework/message-center'

let ChatClient = null

/**
 * @description 1、初始化SDK 2、监听ChatClient方法
 */
export default class SignalServiceBase {
  constructor(opts = {}) {
    // @log-ignore
    console.log('irc', opts.ircInitOptions)
    this.options = opts
    this.isLive = opts.isLive
    this.ircInitOptions = opts.ircInitOptions
    this.timeOut = 5
    this.ircTimer = null
    this.inRoom = false // 当前是否在房间中，重连不需要重复加入房间
    this.init()
    this.listener()
  }
  // 登陆开始倒计时
  beginTimer() {
    this.ircTimer = setTimeout(() => {
      messageCenter.emit('exception', {
        level: 'showNotice',
        data: 402
      })
    }, this.timeOut * 1000)
  }
  // 结束倒计时
  endTimer() {
    messageCenter.emit('exception', {
      level: 'closeNotice',
      data: 402
    })
    clearTimeout(this.ircTimer)
    this.ircTimer = null
  }
  // 向日志模块抛出irc日志
  sendLogger(logType, logData = {}) {
    messageCenter.emit('logger', {
      type: 'customirc',
      data: {
        logData: {
          options: this.ircInitOptions,
          ...logData
        },
        logType
      }
    })
  }
  // 向日志模块抛出神策埋点信息
  sensorPush(logType, logData) {
    messageCenter.emit('logger', {
      type: 'sensors',
      data: {
        logData,
        logType
      }
    })
  }
  /**
   * @description 初始化SDK、登录
   */
  init() {
    const {
      roomlist,
      appId,
      appKey,
      nick = '',
      planId = '',
      liveTypeId,
      ircSwitch,
      psId,
      password,
      confService = {},
      logService = {},
      location
    } = this.ircInitOptions
    const liveInfo = {
      nickname: nick, // t_3_54312_1211_0 教师的账号
      // username: 'xes-chat', // 固定
      // realname: 'websocket IRC Client', // 固定
      liveId: planId + '', // 直播的场次
      businessId: liveTypeId, // 业务id
      classId: '0', // 班级id
      location: location || 'China' // China / Thailand / Philippines / America
    }
    this.sendLogger('onLiveInfo', { liveInfo })
    const TalMsgClient = window.TalMsgClient
    const client = new TalMsgClient(appId, 'v3.2.1')
    client.setSdkConfig({
      extra: {
        logLevel: 'warn'
      }
    })
    window.ChatClient = ChatClient = client.getInstance(1)

    // SDK初始化
    const res = ChatClient.init(appId, appKey)
    this.sendLogger('ircinit', {
      code: res
    })

    let strInfo = ''
    // 如果初始化成功
    if (res === ChatResultCode.Success) {
      messageCenter.get('_log')('irc 初始化 success')
      const roomUserMode = ircSwitch === 1 ? 1 : 0
      // 设置直播信息
      const setLiveInfoRes = ChatClient.setLiveInfo(liveInfo)
      this.sendLogger('setLiveInfo', {
        code: setLiveInfoRes
      })
      // 如果设置直播信息成功
      if (setLiveInfoRes == ChatResultCode.Success) {
        const params = {
          confService: {
            hostname: confService.hostname || 'chatconf.msg.xescdn.com',
            url: confService.url || '/chat/v1/getConfig',
            protocol: confService.protocol || 'https'
          },
          logService: {
            hostname: logService.hostname || 'log.xescdn.com',
            url: logService.url || '/log',
            protocol: logService.protocol || 'https'
          }
        }
        // 设置配置信息
        const setSdkRes = ChatClient.setSdkProperties(params)
        this.sendLogger('setSdkProperties', {
          code: setSdkRes,
          params: params
        })
        // 如果配置信息成功
        if (setSdkRes == ChatResultCode.Success) {
          // 登陆时开始倒计时
          this.beginTimer()
          // 去登陆
          const loginRes = ChatClient.loginWithMode(psId, password, roomUserMode, true)
          this.sendLogger('loginWithMode', {
            code: loginRes
          })
        }
      }
    }

    // 初始化上报日志
    messageCenter.emit('logger', {
      type: 'sys',
      data: {
        logData: { msg: 'irc 初始化' + strInfo, liveInfo, res, strInfo },
        logType: 'irc'
      }
    })
  }

  /**
   * @description 消息监听
   */
  listener() {
    if (!ChatClient) throw Error('ChatClient is not exist.')
    this.ChatClientListner()
    this.roomListener()
    this.peerListener()
  }

  /**
   * @description 监听ChatClient消息
   */
  ChatClientListner() {
    const onChatClientRes = new OnChatClientRes(this.options)

    // 监听登录-回调响应
    ChatClient.on('onLoginResponse', res => {
      // 登陆成功后结束倒计时
      this.endTimer()
      const { uid, roomlist, planId = '' } = this.options.ircInitOptions
      const { ircSwitch } = this.options.chatOptions
      const joinMode = ircSwitch === 1 ? 2 : 1
      this.sendLogger('onLoginResponse', {
        code: res.code
      })
      // 如果登陆成功
      if (res.code == ChatResultCode.Success) {
        if (!this.inRoom) {
          // 调用加入房间
          const joinRoomRes = ChatClient.RoomChatManager.joinChatRoomsWithJoinMode(
            roomlist,
            joinMode
          )
          this.sendLogger('joinChatRoomsWithJoinMode', {
            code: joinRoomRes
          })
          // 信令优化
          ChatClient.RoomChatManager.getAllRoomData(roomlist[0])
          // irc是异步登录，登录成功后初始化聊天
          messageCenter.emit('chat', {
            type: 'init',
            data: {
              ...this.options
            }
          })
          messageCenter.emit('logger', {
            type: 'sys',
            data: {
              logData: {
                msg: '登录成功后初始化聊天',
                ...this.options
              },
              logType: 'chat'
            }
          })
          messageCenter.get('_log')('irc login success,开始初始化chat')
        } else {
          console.warn('当前已在irc房间中，无需重复加入')
        }
      }
    })

    // 用户退出-通知，当有用户退出聊天系统时，服务器通知所在同一聊天室的其他用户
    ChatClient.on('onLogoutNotice', res => {
      onChatClientRes.onLogoutNotice(res)
    })

    // 网络状态变更-回调响应
    ChatClient.on('onNetStatusChanged', res => {
      onChatClientRes.onNetStatusChanged(res)
      messageCenter.emit('exception', {
        level: 'onNetStatusChanged',
        data: res
      })
      this.sendLogger('onNetStatusChanged', {
        code: res.netStatus,
        res
      })
    })
    // 调度状态回调
    // 0-成功
    // 1-调度错误
    // 2-配置错误
    // 100 -未知错误
    ChatClient.on('onSDKProvisionStatusNotice', res => {
      messageCenter.emit('exception', {
        level: 'onSDKProvisionStatusNotice',
        data: res
      })
      this.sendLogger('onSDKProvisionStatusNotice', {
        code: res.status,
        res
      })
    })
    // 被顶掉
    ChatClient.on('onKickoutNotice', res => {
      onChatClientRes.onKickoutNotice(res)
      this.sendLogger('onKickoutNotice', {
        res
      })
    })

    ChatClient.on = () => {
      throw Error('不允许继续在ChatClient进行监听事件')
    }
  }

  /**
   * @description 监听room消息
   */
  roomListener() {
    const RoomChatManager = ChatClient.RoomChatManager

    const onRoomRes = new OnRoomRes(this.options)

    RoomChatManager.on('onNetStatusChanged', res => {
      console.log('ChatClient-onNetStatusChanged', res)
      console.log('onNetStatusChanged', res)
      // msgBroadCast.emit('onNetStatusChanged', result)
      onRoomRes.onPlayer(res, 'onNetStatusChanged')
    })
    // 涂鸦消息
    RoomChatManager.on('onRecvRoomBinMessageNotice', res => {
      console.log('ChatClient-onRecvRoomBinMessageNotice', res)
      onRoomRes.onPlayer(res, 'onRecvRoomBinMessageNotice')
    })

    // 涂鸦历史消息
    RoomChatManager.on('onGetRoomHistoryBinMessageNotice', res => {
      console.log('ChatClient-onGetRoomHistoryBinMessageNotice', res)
      onRoomRes.onPlayer(res, 'onGetRoomHistoryBinMessageNotice')
    })

    // 信令优化
    RoomChatManager.on('onRecvRoomDataUpdateNotice', res => {
      console.log('onRecvRoomDataUpdateNotice', res)
      onRoomRes.onInteraction(res, 'onRecvRoomDataUpdateNotice')
    })

    // 信令优化收到历史消息
    RoomChatManager.on('onGetRoomDataResponse', res => {
      onRoomRes.onInteraction(res, 'onGetRoomDataResponse')
    })
    // 群聊消息处理
    RoomChatManager.on('onRecvRoomMessage', res => {
      console.log('ChatClient-onRecvRoomMessage', res)
      this.sendLogger('onRecvRoomMessage', {
        res
      })
      onRoomRes.onInteraction(res, 'onRecvRoomMessage')
      // onRoomRes.onInteraction(res, 'onRecvRoomTopic')
    })

    // 接收聊天室主题
    RoomChatManager.on('onRecvRoomTopic', res => {
      console.log('ChatClient-onRecvRoomTopic', res)
      onRoomRes.onInteraction(res, 'onRecvRoomTopic')
    })

    // 加入聊天室回调
    RoomChatManager.on('onJoinRoomResponse', res => {
      this.inRoom = true
      this.sendLogger('onJoinRoomResponse', {
        code: res.code,
        res
      })
      messageCenter.get('_log')(`chat join room ${JSON.stringify(res)}`)

      const { ircSwitch, roomlist } = this.options.chatOptions
      // 新版irc在线人数逻辑需要主动拉取一下
      if (ircSwitch === 1) {
        RoomChatManager.getRoomUserList(roomlist, 1)
      }

      onRoomRes.onChatMsg(res, 'onJoinRoomResponse')

      // 获取历史消息
      RoomChatManager.getRoomHistoryMessage(roomlist[0], 0)
    })

    // 接收群聊历史消息
    RoomChatManager.on('onGetRoomHistoryMessageResponse', res => {
      console.log('ChatClient-onGetRoomHistoryMessageResponse', res)
      onRoomRes.onChatMsg(res, 'onGetRoomHistoryMessageResponse')
    })

    // 他人进入聊天室通知
    RoomChatManager.on('onJoinRoomNotice', res => {
      console.log('ChatClient-onJoinRoomNotice', res)
      messageCenter.get('_log')(`chat 他人进入聊天室通知 ${JSON.stringify(res)}`)
      onRoomRes.onChatMsg(res, 'onJoinRoomNotice')
    })

    // 接收聊天室用户列表
    RoomChatManager.on('onRecvRoomUserList', res => {
      console.log('ChatClient-onRecvRoomUserList', res)
      onRoomRes.onChatMsg(res, 'onRecvRoomUserList')
    })

    // 他人离开聊天室通知
    RoomChatManager.on('onLeaveRoomNotice', res => {
      console.log('ChatClient-onLeaveRoomNotice', res)
      onRoomRes.onChatMsg(res, 'onLeaveRoomNotice')
    })

    // 发送群聊消息回复
    RoomChatManager.on('onSendRoomMessageResponse', res => {
      console.log('ChatClient-onSendRoomMessageResponse', res)
      onRoomRes.onChatMsg(res, 'onSendRoomMessageResponse')
      this.sendLogger('onSendRoomMessageResponse', {
        code: res.code,
        res
      })
    })

    // 监听在线人数
    RoomChatManager.on('onRoomUserCountNotice', res => {
      console.log('room---', res)
      onRoomRes.onLineNum(res, 'onRoomUserCountNotice')
    })
    // 涂鸦发送事件回调
    RoomChatManager.on('onSendRoomBinMessageResp', res => {
      this.sendLogger('onSendRoomBinMessageResp', {
        code: res.code,
        res
      })
    })
    RoomChatManager.on = () => {
      throw Error('不允许继续在RoomChatManager上监听事件')
    }
  }

  /**
   *
   * @description 监听私聊消息
   */
  peerListener() {
    const PeerChatManager = ChatClient.PeerChatManager
    // const onPeerRes = new OnPeerRes(this.ircInitOptions)
    // 发送单聊消息的回复
    PeerChatManager.on('onSendPeerMessageResponse', res => {
      messageCenter.emit('chat', {
        type: 'onSendPeerMessageResponse',
        data: {
          ...res
        }
      })
      this.sendLogger('onSendPeerMessageResponse', {
        code: res.code,
        res
      })
    })

    // 私聊消息处理
    PeerChatManager.on('onRecvPeerMessage', res => {
      this.sendLogger('onRecvPeerMessage', {
        res
      })
      // onPeerRes.onRecvRoomMessage(res)
      messageCenter.emit('interaction', {
        type: 'onRecvPeerMessage',
        data: {
          ...res
        }
      })
    })

    PeerChatManager.on = () => {
      throw Error('不允许继续在PeerChatManager上监听事件')
    }
  }

  static getRoomHistoryBinMessage(data) {
    const { roomid, key } = data
    ChatClient.RoomChatManager.getRoomHistoryBinMessage(roomid, key, '0', true)
  }

  /**
   *
   * @param {*} params
   * @description 给教师端私发消息
   * @param params.content 消息内容
   * @param params.chatMsgPriority 消息优先级
   * @param params.nickname 昵称
   * @param params.psid 磐石id
   */
  static sendPeerMessage(params = {}) {
    const {
      // 消息内容
      content,
      // 消息优先级
      chatMsgPriority,
      // 昵称
      nickname,
      // 磐石id
      psid
    } = params
    const res = ChatClient.PeerChatManager.sendPeerMessage(
      [{ nickname, psid }],
      JSON.stringify(content),
      chatMsgPriority
    )
    return res
  }

  static sendPeerMessageWithPreMsgId(params = {}) {
    const {
      // 消息内容
      content,
      // 消息优先级
      chatMsgPriority,
      // 昵称
      nickname,
      // 磐石id
      psid
    } = params
    const res = ChatClient.PeerChatManager.sendPeerMessageWithPreMsgId(
      [{ nickname, psid }],
      JSON.stringify(content),
      chatMsgPriority
    )
    return res
  }

  /**
   * @description 给房间发消息
   * @param {*} params.roomlist 房间号
   * @param {*} params.content 消息
   * @param {*} params.privMsg 消息优先级
   */
  static sendRoomMessage(params = {}) {
    const { roomlist, content, chatMsgPriority } = params
    ChatClient.RoomChatManager.sendRoomMessage(roomlist, JSON.stringify(content), chatMsgPriority)
  }

  /**
   * 给房间发消息返回preMsgId
   * @param {Object} params 参数对象
   * @param params.roomList 房间号
   * @param params.content 消息
   * @param params.chatMsgPriority 消息优先级
   */
  static sendRoomMessageWithPreMsgId(params = {}) {
    const { content, roomlist, chatMsgPriority = 99 } = params
    const res = ChatClient.RoomChatManager.sendRoomMessageWithPreMsgId(
      roomlist,
      JSON.stringify(content),
      chatMsgPriority
    )
    console.log('sendRoomMessageWithPreMsgId', res)
    return res
  }

  /**
   *
   */
  static kickout() {
    ChatClient.logout && ChatClient.logout()
  }
}
