import { messageCenter } from '@thinkacademy/live-framework/components/framework/message-center'
export default class OnRoomRes {
  constructor(opts = {}) {
    // @log-ignore
    this.ircInitOptions = opts
  }

  /**
   *
   * @param {*} res
   * @description 收到直播中互动信令
   */
  onInteraction(res, type) {
    if (type === 'onRecvRoomMessage') {
      // 本地调试代码
      if (process.env.NODE_ENV === 'local') {
        res.messagePriority = 1
        res.msgPriority = 1
        const teachertype = 0

        if (teachertype === 0) {
          if (res.fromUserInfo) res.fromUserInfo.nickname = 'f_3_868025_58421_1'
        } else {
          if (res.fromUserInfo) res.fromUserInfo.nickname = 't_3_868025_58421_1'
        }
      }
    }
    // console.log('interaction123', res, type)
    messageCenter.emit('interaction', {
      type,
      data: {
        ...res
      }
    })
  }

  /**
   *
   * @param {*} res
   * @param {*} type
   */
  onPlayer(res, type) {
    const data = res instanceof Array ? res : { ...res }
    messageCenter.emit('player', {
      type,
      data
    })
  }

  /**
   *
   * @param {*} res
   * @param {*} type
   * @description 聊天
   */
  onChatMsg(res, type) {
    // console.log(type, res)
    messageCenter.emit('chat', {
      type,
      data: {
        ...res
      }
    })
  }

  /**
   *
   * @param {*} res
   * @param {*} type
   * @description 在线人数
   */
  onLineNum(res, type) {
    messageCenter.emit('room', {
      type: type,
      data: {
        ...res
      }
    })
  }
}
