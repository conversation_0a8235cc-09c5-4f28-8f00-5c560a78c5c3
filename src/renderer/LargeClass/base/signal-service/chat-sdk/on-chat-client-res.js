import { messageCenter } from '@thinkacademy/live-framework/components/framework/message-center'
import { ChatResultCode, ChatNetStatus } from './signal-service-conf.js'

export default class OnChatClientRes {
  constructor(opts = {}) {
    // @log-ignore
    this.ircInitOptions = opts.ircInitOptions
  }

  /**
   * @function
   * @name InitChat#_onLogoutNotice
   * @description 用户退出-通知，当有用户退出聊天系统时，服务器通知所在同一聊天室的其他用户
   */
  onLogoutNotice(res) {
    messageCenter.emit('chat', {
      type: 'onLogoutNotice',
      data: {
        ...res
      }
    })
  }

  /**
   * @function
   * @name _onNetStatusChanged
   * @description 网络状态变更-回调响应
   */
  onNetStatusChanged(res) {
    // eslint-ignore-nextline
    // console.log('%c网络状态发生改变啦！！！！', 'color: blue', res)
    let strInfo
    switch (res.netStatus) {
      case ChatNetStatus.Unkown || 0:
        strInfo = 'Network unknown'
        messageCenter.emit('chat', {
          type: 'serverUnkown',
          data: {}
        })
        break
      case ChatNetStatus.Unavailable || 1:
        strInfo = 'Network unavailable'
        messageCenter.emit('chat', {
          type: 'serverUnavailable',
          data: {}
        })
        break
      case ChatNetStatus.ServerFailed || 2:
        strInfo = 'Server connection failed'
        messageCenter.emit('chat', {
          type: 'serverFailed',
          data: {}
        })
        break
      case ChatNetStatus.Connecting || 3:
        strInfo = 'Connecting' // 服务器重连

        // 聊天处理服务器重连
        messageCenter.emit('chat', {
          type: 'serverConnection',
          data: {}
        })
        break
      case ChatNetStatus.Connected || 4:
        strInfo = 'Connection successful'
        messageCenter.emit('chat', {
          type: 'serverConnected',
          data: {}
        })
        break
      case ChatNetStatus.DisConnected || 5:
        strInfo = 'Disconnect'
        // 聊天处理服务器重连
        messageCenter.emit('chat', {
          type: 'disconnect',
          data: {}
        })
        // this.handleLeaveRoomSuccess()
        break
    }

    // console.log('irc-------', strInfo)
    const { uid, roomlist, planId = '' } = this.ircInitOptions
    const logData = {
      elem: document.body,
      params: {
        live_id: planId,
        uid: uid,
        roomlist: roomlist,
        loginStatus: res,
        errmsg: strInfo
      }
    }
    messageCenter.emit('logger', {
      type: 'sys',
      data: {
        logData,
        logType: 'irc'
      }
    })
  }

  /**
   *
   * @param {*} res
   * @description 新版sdk踢人监听
   */
  onKickoutNotice(res) {
    if (res.code === ChatResultCode.KickoutRepeat || res.code === ChatResultCode.KickoutRequest) {
      messageCenter.emit('chat', {
        type: 'onKickoutNotice',
        data: {
          ...res
        }
      })
    }
  }
}
