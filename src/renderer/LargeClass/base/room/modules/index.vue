<template>
  <div>
    <CountDown :options="options" />
    <OrientationCoins :options="options" />
    <ContinuousCorrect :options="options" />
    <ClassPraise :options="options" />
    <LiveKickout :options="options" />
    <TutorVideoLink :options="options" :stuInfo="stuInfo" :rtcConfig="rtcConfig" />
    <SmallClassRank :options="options" />
    <DeviceTest />
    <SmallClassGraffitiCorrect />
    <TeacherOnStage :options="options" :rtcConfig="rtcConfig" />
  </div>
</template>

<script>
import CountDown from 'components/Classroom/CommonModules/countDown/index'
import OrientationCoins from 'components/Classroom/CommonModules/orientationCoins/index'
import ContinuousCorrect from 'components/Classroom/CommonModules/continuousCorrect/index'
import ClassPraise from 'components/Classroom/CommonModules/classPraise/index'
import TutorVideoLink from './tutorVideoLink/index'
import SmallClassRank from 'components/Classroom/CommonModules/smallClassRank/index'
import DeviceTest from './deviceTest/index'
import LiveKickout from 'components/Classroom/CommonModules/liveKickout/index'
import SmallClassGraffitiCorrect from 'components/Classroom/CommonModules/smallClassGraffitiCorrect/index'
import TeacherOnStage from 'components/Classroom/CommonModules/teacherOnStage'
export default {
  components: {
    CountDown,
    OrientationCoins,
    ContinuousCorrect,
    ClassPraise,
    TutorVideoLink,
    DeviceTest,
    SmallClassRank,
    LiveKickout,
    SmallClassGraffitiCorrect,
    TeacherOnStage
  },
  props: {
    options: {
      type: Object,
      default: null
    },
    stuInfo: {
      type: Object,
      default: null
    },
    rtcConfig: {
      type: Object,
      default: null
    }
  }
}
</script>
