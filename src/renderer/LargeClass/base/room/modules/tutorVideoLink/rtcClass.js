export default class {
  constructor(opts = {}) {
    // @log-ignore
    this.options = opts.options
    this.rtcConfig = opts.rtcConfig
    this.teacherAudioUid = this.rtcConfig.teacherAudioUid
    this.tutorUid = this.options.counselorInfo.id
    this.teacherRtcChannel = window.RTC_COMMON.teacherRtcChannel || null
    this.classRtcChannel = window.RTC_COMMON.classRtcChannel || null
  }

  /**
   * 辅导连麦开始
   * @param {String} element
   */
  tutorVideoLinkStart(element) {
    this.classRtcChannel.muteRemoteAudioStream(this.tutorUid, false)
    this.classRtcChannel.muteRemoteVideoStream(this.tutorUid, false)
    this.classRtcChannel.setupRemoteVideo(this.tutorUid, document.getElementById(element))
  }

  /**
   * 辅导连麦结束
   */
  tutorVideoLinkEnd() {
    this.classRtcChannel.muteRemoteAudioStream(this.tutorUid, true)
    this.classRtcChannel.muteRemoteVideoStream(this.tutorUid, true)
    this.classRtcChannel.destroyRemoteVideo(this.tutorUid, document.getElementById(this.tutorUid))
  }
}
