<template>
  <div v-if="isShowLink" class="tutor-video-link-wrapper">
    <div class="tutor-video" :class="{ error: isError, normal: !isError }">
      <div v-show="!isError" class="tutor-video-container">
        <div :id="tutorId"></div>
      </div>
      <div v-if="isError" class="icon-error"></div>
    </div>
    <div class="title-bar">
      <div class="dot"></div>
      <div class="title">
        {{ $t('classroom.modules.tutorVideoLink.title') }}
      </div>
    </div>
    <div class="video-link-info">
      <div v-if="isError" class="tutor-video-error">
        <div class="notice">
          {{ $t('classroom.modules.tutorVideoLink.errorMsg')[0] }}<br />
          {{ $t('classroom.modules.tutorVideoLink.errorMsg')[1] }}
        </div>
        <div class="button" @click="handleExit">
          {{ $t('common.exit') }}
        </div>
      </div>
      <div v-else>
        <div class="avatar-info">
          <div class="avatar-tutor" :class="{ 'default-avatar': !tutorAvatarUrl }">
            <img v-if="tutorAvatarUrl" :src="tutorAvatarUrl" />
          </div>
          <div class="icon-vocal-print"></div>
          <div class="avatar-student">
            <img :src="studentAvatarUrl" />
          </div>
        </div>
        <div class="link-time">{{ formatTime }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import RtcClass from './rtcClass'
import logger from 'utils/logger'
import { padZero } from 'utils/util'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'

export default {
  props: {
    options: {
      type: Object,
      default: null
    },
    stuInfo: {
      type: Object,
      default: null
    },
    rtcConfig: {
      type: Object,
      default: null
    }
  },
  computed: {
    tutorId() {
      return `remote-${this.options.counselorInfo.id}`
    },
    tutorAvatarUrl() {
      return this.options.counselorInfo.avatar || ''
    },
    studentAvatarUrl() {
      return this.stuInfo.avatar || ''
    },
    formatTime() {
      const timerCounter = this.timerCounter
      const minute = Math.floor(timerCounter / 60)
      const second = timerCounter % 60
      return `${padZero(minute)}:${padZero(second)}`
    }
  },
  data() {
    return {
      isShowLink: false,
      isError: false,
      isFirstMsg: false, // 是否第一次收到辅导连麦消息
      status: 0, //  1: 预备开启 2: 开启 3: 关闭
      callback: null,
      timerCounter: 0, // 计时
      timer: null,
      highEncoderConfig: {
        // 高分辨率
        width: 320,
        height: 240,
        bitrate: 120,
        frameRate: 10
      },
      lowEncoderConfig: {
        // 低分辨率
        bitrate: 80,
        frameRate: 10,
        width: 160,
        height: 120
      }
    }
  },
  mounted() {
    this.$bus.$on('room.tutorVideoLink', (noticeContent, callback) => {
      console.log('tutorVideoLink-noticeContent', noticeContent)
      this.sendLogger(`收到辅导连麦消息: ${JSON.stringify(noticeContent)}`)
      const { studentId, status } = noticeContent
      if (studentId != this.stuInfo.id) {
        return
      }
      if (status == 1) {
        this.isFirstMsg = true
      }
      this.status = status
      this.callback = callback
      if (this.isFirstMsg && status == 2) {
        window.RTC_COMMON?.classRtcChannel?.setVideoEncoderConfiguration(this.highEncoderConfig)
        this.startLink()
        classLiveSensor.osta_ia_video_link('', 'guiding_teacher', true)
      }
      if (this.isFirstMsg && status == 3) {
        window.RTC_COMMON?.classRtcChannel?.setVideoEncoderConfiguration(this.lowEncoderConfig)
        this.endLink()
        classLiveSensor.osta_ia_video_link('', 'guiding_teacher', false)
      }
    })
  },
  methods: {
    /**
     * 开始连麦
     */
    startLink() {
      this.isShowLink = true
      this.isError = false
      this.timerCounter = 0
      this.rtcClass = new RtcClass({
        options: this.options,
        rtcConfig: this.rtcConfig
      })
      this.addEventListen()
      this.$nextTick(() => {
        this.$bus.$emit('player.muteTeacherChannelAudio', true) // 禁用主讲音频
        this.rtcClass.tutorVideoLinkStart(this.tutorId)
        this.callback &&
          this.callback({
            status: true
          })
        this.startTimerCounter()
        this.sendLogger(`开始辅导连麦`, 'start')
      })
    },

    /**
     * 结束连麦
     */
    endLink() {
      this.$bus.$emit('player.muteTeacherChannelAudio', false) // 启用主讲音频
      this.rtcClass.tutorVideoLinkEnd()
      this.removeEventListen()
      this.startTimerCounter()
      this.isShowLink = false
      this.isError = false
      this.isFirstMsg = false
      this.status = 0
      this.callback &&
        this.callback({
          status: false
        })
      this.callback = null
      this.sendLogger(`结束辅导连麦`, 'end')
    },

    /**
     * 添加RTC事件监听
     */
    addEventListen() {
      this.rtcClass.classRtcChannel.on(
        'remoteVideoStateChanged',
        this.listenRemoteVideoStateChanged
      )
      this.rtcClass.classRtcChannel.on('connectionStateChanged', this.listenConnectionStateChanged)
    },

    /**
     * 移除RTC事件监听
     */
    removeEventListen() {
      this.rtcClass.classRtcChannel.off(
        'remoteVideoStateChanged',
        this.listenRemoteVideoStateChanged
      )
      this.rtcClass.classRtcChannel.off('connectionStateChanged', this.listenConnectionStateChanged)
    },

    /**
     * 监听远端视频流状态变化事件
     * @param {Number} uid 远端uid
     * @param {Number} state 状态
     * @param {Number} reason 原因
     */
    listenRemoteVideoStateChanged(uid, state, reason) {
      const tutorUid = this.options.counselorInfo.id
      if (uid == tutorUid) {
        // 远端用户停止视频推流
        if (state == 0 && reason == 5) {
          this.isError = true
          this.sendLogger('视频显示失败')
        }
        // 远端用户离开频道
        if (state == 0 && reason == 7) {
          this.endLink()
        }
        // 远端流正常播放状态
        if (state == 2) {
          this.isError = false
          this.sendLogger('视频显示成功')
        }
      }
    },

    /**
     * 监听网络连接状态已改变回调
     */
    listenConnectionStateChanged(state, reason) {
      console.log('tutorVideoLink-connectionStateChanged', state, reason)
      // 网络连接断开、重新建立网络连接中、网络连接失败
      if (state === 1 || state === 4 || state === 5) {
        this.isError = true
      }
      // 网络已连接
      if (state === 3) {
        this.isError = false
      }
    },

    /**
     * 主动退出操作
     */
    handleExit() {
      this.endLink()
      this.sendLogger(`学生点击Exit按钮, 主动退出辅导连麦`)
    },

    /**
     * 开始计时器
     */
    startTimerCounter() {
      // @log-ignore
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        ++this.timerCounter
        this.startTimerCounter()
      }, 1000)
    },

    /**
     * 结束计时器
     */
    endTimerCounter() {
      clearTimeout(this.timer)
      this.timer = null
      this.timerCounter = 0
    },

    /**
     * 日志上报
     */
    sendLogger(msg, stage = '') {
      logger.send({
        tag: 'student.Interact',
        content: {
          msg: msg,
          interactType: 'TutorLinkMic',
          interactStage: stage
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.tutor-video-link-wrapper {
  position: absolute;
  right: 0;
  top: 44px;
  z-index: 998;
  width: 27.5%;
  height: calc(100% - 44px);
  background: #1a1a1a;
  overflow: hidden;
  .tutor-video {
    height: calc(246 / 739 * 100vh);
    background-repeat: no-repeat;
    background-size: cover;
    overflow: hidden;
    border-radius: 16px 16px 4px 4px;
    &.normal {
      background-color: #2c2c2c;
    }
    &.error {
      display: flex;
      justify-content: center;
      align-items: center;
      background-image: url('./assets/video-bg.png');
      .icon-error {
        width: 88px;
        height: 88px;
        background: url('./assets/icon-error.png') no-repeat;
        background-size: cover;
      }
    }
  }
  .tutor-video-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
    > div {
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
  }
  .tutor-video-error {
    .notice {
      margin: 120px 0 50px 0;
      text-align: center;
      height: 21px;
      font-size: 18px;
      color: #dee2e7;
    }
    .button {
      width: 110px;
      height: 44px;
      margin: 35px auto;
      background: #ffffff;
      border-radius: 22px;
      color: #ffaa0a;
      font-size: 18px;
      line-height: 44px;
      text-align: center;
      cursor: pointer;
    }
  }
  .title-bar {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 42px;
    background: #2c2c2c;
    color: #02ca8a;
    border-radius: 2px;
    margin-top: 1px;
    .dot {
      width: 8px;
      height: 8px;
      background: #02ca8a;
      border-radius: 4px;
      margin-right: 10px;
    }
    .title {
      font-size: 16px;
    }
  }
  .video-link-info {
    margin: 0 30px;
    .avatar-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 90px 0 30px 0;
    }
    .avatar-tutor,
    .avatar-student {
      width: 78px;
      height: 78px;
      border-width: 3px;
      border-style: solid;
      border-radius: 39px;
      overflow: hidden;
      img {
        display: block;
        width: 72px;
        height: 72px;
      }
    }
    .avatar-tutor {
      border-color: #02ca8a;
      &.default-avatar {
        background: url('./assets/default-avatar.png') no-repeat;
        background-size: cover;
      }
    }
    .icon-vocal-print {
      width: 39px;
      height: 18px;
      background: url('./assets/icon-vocal-print.png') no-repeat;
      background-size: cover;
    }
    .avatar-student {
      border-color: #ffaa0a;
    }
    .link-time {
      position: relative;
      height: 50px;
      background: #2c2c2c;
      border-radius: 25px;
      line-height: 50px;
      text-align: center;
      color: #dee2e7;
      &::after,
      &::before {
        position: absolute;
        top: 14px;
        display: block;
        width: 22px;
        height: 22px;
        content: ' ';
        background-size: cover;
      }
      &::after {
        left: 27px;
        background-image: url('./assets/icon-tel-green.png');
      }
      &::before {
        right: 27px;
        background-image: url('./assets/icon-tel-orange.png');
      }
    }
  }
}
// 主讲视频LOGO水印
::v-deep.tutor-video-container {
  > div::after {
    content: ' ';
    position: absolute;
    left: 10px;
    top: 10px;
    z-index: 1;
    display: block;
    width: 24px;
    height: 30px;
    background: url('~assets/images/logo-video-mark.png') no-repeat;
    background-size: cover;
  }
}
</style>
