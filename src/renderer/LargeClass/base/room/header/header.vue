<template>
  <div class="live-header">
    <div class="wrapper">
      <div class="windows-exit" v-if="platform == 'win'" data-log="win">
        <div class="button button-exit" @click="handleBack" data-log="退出按钮"></div>
      </div>
      <div class="drag-bar" :class="platform"></div>
      <div class="title">
        <span class="plan-name" v-if="planName">
          {{ planName }}
        </span>
      </div>
      <div class="right">
        <div class="operation-button">
          <div v-if="!isPlayBack" class="item">
            <NetworkStatus @update-network-quality="updateNetworkQuality" />
          </div>
          <PrintScreen @click="printScrenClick" />
          <div class="item" v-if="showFeedbackButton">
            <div class="button button-feedback" @click="handleFeedbackShow"></div>
          </div>
          <!-- 查看考试结果入口 -->
          <div class="item" v-if="showExamReportButton">
            <div class="button button-exam" @click="handleExamResult"></div>
          </div>
          <!-- 英分不展示作业盒子 -->
          <div v-if="showHomeworkButton" class="item">
            <div class="button button-homework" @click="handleOpenAssignmentBox">
              <span v-if="haveNewMessage"></span>
            </div>
          </div>
          <div class="item">
            <div class="button button-refresh" @click="handleRefresh" data-log="刷新"></div>
          </div>
          <div v-if="!this.isPlayBack && showTeamSwitch" class="item">
            <div class="button button-more" @click="handleMore"></div>
          </div>
          <div class="item" v-if="platform == 'mac'" data-log="mac">
            <div class="button button-exit" @click="handleBack" data-log="课中退出按钮"></div>
          </div>
        </div>
        <div class="operation-more" v-show="showMore">
          <OnlyTeacherChat :options="options"></OnlyTeacherChat>
          <TeamSwitch v-if="showTeamSwitch" :plan-id="planInfo.id" :options="options" />
          <DeviceTest />
        </div>
      </div>
    </div>
    <div class="components-box">
      <!-- 作业盒子 -->
      <AssignmentBox
        ref="assignmentBoxRef"
        :plan-id="planInfo.id"
        :options="options"
        @hideHeader="hideHeader"
        @handleShowMessageTip="handleShowMessageTip"
        @handleHideMessageTip="handleHideMessageTip"
      ></AssignmentBox>
      <!-- 问题反馈 -->
      <Feedback
        ref="Feedback"
        :options="options"
        :rtcConfig="rtcConfig"
        :downlinkNetworkQuality="downlinkNetworkQuality"
      />
      <!-- 考试报告 -->
      <ExamReport :commonOptions="options" />
    </div>
  </div>
</template>
<script>
import { getPlatform } from 'utils/util'
import NetworkStatus from './NetworkStatus.vue'
import Feedback from './feedback'
import TeamSwitch from './TeamSwitch.vue'
import DeviceTest from './DeviceTest.vue'
import AssignmentBox from 'components/Classroom/CommonModules/assignmentBox/assignmentBox'
import logger from 'utils/logger'
import { getLocal } from 'utils/local'
import { queryReadMessage } from 'api/assignmentBox'
import PrintScreen from './print-screen/printScreen.vue'
import { lookExamReportApi } from 'api/h5exam/index'
import ExamReport from 'components/Classroom/CommonModules/examReportModal/index'
import { sensorEvent } from 'utils/sensorEvent'
import { checkApiDomainHealth, reportTimeInterval } from 'utils/checkNetworkHealth'
import { typeMap } from 'utils/courses'
import qs from 'querystringify'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import { setPlanId } from 'plugins/sensors'
import OnlyTeacherChat from './OnlyTeacherChat.vue'
import _debounce from 'lodash/debounce'
import { route_href } from 'utils/routeUtil'

export default {
  props: {
    options: {
      type: Object,
      default: null
    },
    stuInfo: {
      type: Object,
      default: null
    },
    rtcConfig: {
      type: Object,
      default: null
    },
    planInfo: {
      type: Object,
      default: null
    },
    planName: {
      type: String,
      default: ''
    }
  },
  components: {
    Feedback,
    AssignmentBox,
    NetworkStatus,
    TeamSwitch,
    DeviceTest,
    PrintScreen,
    ExamReport,
    OnlyTeacherChat
  },
  data() {
    return {
      platform: getPlatform(),
      isPlayBack: this.options.isPlayBack,
      showHeader: true,
      showMore: false,
      local: '',
      haveNewMessage: false, // 作业盒子是否有未读消息
      downlinkNetworkQuality: -1, // 声网下行网络质量
      checkRefreshBox: true,
      backUrl: '',
      reportBtnVisible: false, // 是否展示查看报告入口
      checkNetHealthIntervalId: null,
      lessonType: ''
    }
  },
  computed: {
    showTeamSwitch() {
      return !this.options.isAudition
    },
    showHomeworkButton() {
      return this.local != 'uk' && !this.options.isAudition
    },
    showFeedbackButton() {
      return !this.options.isAudition
    },
    showExamReportButton() {
      return !this.options.isAudition && this.reportBtnVisible
    }
  },
  async mounted() {
    this.local = await getLocal()
    this.queryReadMessage()
    this.backUrl = window.location.href.split('backUrl=')[1] || '/home'
    await this.initShowReportBtn() // 判断查看报告是否显示
    // 开启网络健康检测定时
    this.checkNetHealthIntervalId = setInterval(() => {
      checkApiDomainHealth()
    }, reportTimeInterval)
    const query = qs.parse(window.location.search)
    this.lessonType = typeMap[query['lessonType']] || ''
  },
  beforeDestroy() {
    // 销毁网络健康检测定时
    if (this.checkNetHealthIntervalId) {
      clearInterval(this.checkNetHealthIntervalId)
    }
  },
  methods: {
    /**
     * 封装埋点事件
     */
    reSensorEvent(toolName) {
      sensorEvent('hw_classroom_toolbar_click', this.options, {
        tool_name: toolName
      })
    },
    /**
     * 截屏点击事件,埋点专用
     */
    printScrenClick() {
      logger.send({
        tag: 'action',
        content: {
          msg: '学生点击header截屏'
        }
      })
      this.reSensorEvent('截屏')
    },

    /**
     * 处理返回事件
     */
    async handleBack() {
      localStorage.setItem('largeClassTestCoverage', JSON.stringify(window.__coverage__))
      if (this.options.isPlayBack) {
        route_href('/#' + this.backUrl)
        return
      }
      // 只记录直播埋点, 不记录回放
      this.$Modal.confirm({
        class: 'modal-simple apu-header',
        content: this.$t('classroom.modules.header.backConfirm.content'),
        okText: this.$t('common.yes'),
        cancelText: this.$t('common.no'),
        zIndex: 3000,
        onOk: () => {
          // 广播消息: 直播退出
          if (!this.options.isPlayBack) {
            this.$bus.$emit('liveQuit')
            classLiveSensor.sendExitOrRefreshSensor('click_exit_btn', {
              timeOffset: this.options?.timeOffset,
              planInfo: this.planInfo,
              lessonType: this.lessonType,
              interactionStatus: [],
              classTypeName: '大班'
            })
            classLiveSensor.osta_exit_classroom(
              this.options?.timeOffset,
              this.planInfo?.endStampTime
            )
            setPlanId({})
          }
          logger.send({
            tag: 'action',
            content: {
              msg: '学生退出课堂'
            }
          })
          setTimeout(() => {
            route_href('/#' + this.backUrl)
          }, 1000)
        }
      })
    },
    /**
     * 打开问题反馈窗口
     */
    handleFeedbackShow() {
      this.$refs['Feedback'].handleShow()
      this.reSensorEvent('反馈')
      // 取消作业盒子展示
      this.$refs.assignmentBoxRef.hideAssignmentBoxOnly()
    },
    /**
     * 处理刷新事件
     */
    handleRefresh: _debounce(function() {
      logger.send({
        tag: 'action',
        content: {
          msg: '点击header刷新'
        }
      })
      this.reSensorEvent('刷新')
      // 广播消息: 课堂刷新
      this.$bus.$emit('liveRefresh')
      classLiveSensor.sendExitOrRefreshSensor('refresh_livePage', {
        timeOffset: this.options?.timeOffset,
        planInfo: this.planInfo,
        lessonType: this.lessonType,
        interactionStatus: [],
        classTypeName: '大班'
      })
    }, 300),
    /**
     * 处理查看报告事件
     */
    handleExamResult() {
      this.$bus.$emit('handleOpenExamReport', true)
    },
    /**
     * 处理按钮更多事件
     */
    handleMore() {
      logger.send({
        tag: 'action',
        content: {
          msg: '点击更多按钮'
        }
      })
      this.reSensorEvent('小组视频')
      this.showMore = !this.showMore
    },
    /**
     * 作业盒子未读信息提示
     */
    async queryReadMessage() {
      let res = await queryReadMessage(this, { planId: this.planInfo.id })
      if (res?.stat == 1) {
        this.haveNewMessage = res.data.haveNewMessage
      }
    },
    // 作业展示小红点
    handleShowMessageTip() {
      logger.send({
        tag: 'action',
        content: {
          msg: '新的作业盒子消息'
        }
      })
      this.haveNewMessage = true
    },
    // 作业取消小红点
    handleHideMessageTip() {
      logger.send({
        tag: 'action',
        content: {
          msg: '取消作业盒子消息提醒'
        }
      })
      this.haveNewMessage = false
    },
    handleOpenAssignmentBox() {
      logger.send({
        tag: 'action',
        content: {
          msg: '打开作业盒子'
        }
      })
      this.reSensorEvent('作业盒子')
      // 强制刷新子组件
      this.$nextTick(() => {
        this.$refs.assignmentBoxRef.handleOpenAssignmentBox()
      })
      // 取消意见反馈展示
      this.$refs['Feedback'].handleHide()
    },
    /**
     * 处理鼠标滑入事件
     */
    handleMouseenter() {
      logger.send({
        tag: 'action',
        content: {
          msg: '显示头部导航栏'
        }
      })
      this.showHeader = true
      this.showMore = false
    },
    hideHeader() {
      this.showHeader = false
    },
    /**
     * 处理鼠标移出事件
     */
    handleMouseleave() {
      logger.send({
        tag: 'action',
        content: {
          msg: '移出头部导航栏'
        }
      })
      if (!this.$refs.assignmentBoxRef.showAssignmentBox) this.showHeader = false
    },

    /**
     * 更新声网下行网络质量状态
     */
    updateNetworkQuality(downlinkNetworkQuality) {
      // @log-ignore
      this.downlinkNetworkQuality = downlinkNetworkQuality
    },
    /**
     * more里面的查看报告按钮是否显示
     */
    async initShowReportBtn() {
      const res = await lookExamReportApi({
        planId: this.planInfo.id, // 课次id
        platform: '3' // pc端
      })
      if (!res || res.code != 0) {
        return
      }
      this.reportBtnVisible = res?.data?.showReportEnter === '1' // '1': 展示 '0':不展示
    }
  }
}
</script>
<style lang="scss" scoped>
.header-wrapper {
  height: 69px;
}

.live-header {
  background: #1a1a1a;
  .wrapper {
    position: relative;
    height: 44px;
    display: flex;
    align-items: center;
    z-index: 999;
  }
  .drag-bar {
    -webkit-app-region: drag;
    position: absolute;
    z-index: -1;
    height: 44px;
    &.mac {
      left: 0;
      right: 0;
    }
    &.win {
      right: 50%;
      left: 50%;
    }
  }
  .title {
    width: 100%;
    height: 44px;
    line-height: 44px;
    font-size: 16px;
    color: #dee2e7;
    font-weight: 500;
    text-align: center;
    .plan-name {
      width: 40%;
      overflow: hidden;
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .windows-exit {
    position: absolute;
    left: 0;
    left: 12px;
    .button-exit {
      transform: rotate(180deg);
    }
    .button {
      width: 30px;
      height: 30px;
      background-size: cover;
      cursor: pointer;
    }
  }

  .right {
    position: absolute;
    right: 12px;
    z-index: 1;
    display: flex;
    align-items: center;
  }

  .icon-goback {
    width: 30px;
    height: 30px;
    background: url('~assets/images/live/icon-header-goback.png') no-repeat;
    background-size: cover;
    cursor: pointer;
  }
  .button-exit {
    background-image: url('~assets/images/live/icon-exit.png');
    &:hover {
      background-image: url('~assets/images/live/icon-exit-hover.png');
    }
  }
  .operation-button {
    display: flex;

    .item {
      margin-left: 20px;
    }

    .button {
      width: 30px;
      height: 30px;
      background-size: cover;
      cursor: pointer;
    }

    .button-exam {
      background-image: url('~assets/images/live/icon-exam2.png');
      &:hover {
        background-image: url('~assets/images/live/icon-exam2-hover.png');
      }
    }

    .button-refresh {
      background-image: url('~assets/images/live/icon-refresh.png');

      &:hover {
        background-image: url('~assets/images/live/icon-refresh-hover.png');
      }
    }

    .button-more {
      background-image: url('~assets/images/live/icon-more.png');

      &:hover {
        background-image: url('~assets/images/live/icon-more-hover.png');
      }
    }

    .button-feedback {
      cursor: pointer;
      background-image: url('~assets/images/live/icon-feedback.png');

      &:hover {
        background-image: url('~assets/images/live/icon-feedback-hover.png');
      }
    }

    .button-homework {
      background-image: url('~assets/images/live/icon-homework2.png');
      position: relative;
      &:hover {
        background-image: url('~assets/images/live/icon-homework2-hover.png');
      }
      span {
        width: 6px;
        height: 6px;
        background: linear-gradient(180deg, #ff66b6 0%, #ff3636 100%);
        display: inline-block;
        position: absolute;
        right: -5px;
        border-radius: 50%;
      }
    }
  }
}

// 更多操作区
.operation-more {
  position: absolute;
  right: 0;
  top: 38px;
  min-width: 160px;
  padding: 5px 12px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0px 1px 6px 0px rgba(188, 188, 188, 0.4);
}
</style>
