<template>
  <div class="team-switch">
    <div class="switch-wrapper">
      <div class="title">
        {{ $t('classroom.largeClass.header.teacherMessageOnly') }}
      </div>
      <div class="switch">
        <a-switch v-model="status" @change="changeTeacherOnly" />
      </div>
    </div>
  </div>
</template>
<script>
import { sensorEvent } from 'utils/sensorEvent'
import logger from 'utils/logger'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
export default {
  props: {
    planId: {
      type: Number,
      default: 0
    },
    options: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      // 开关状态
      status: false
    }
  },
  methods: {
    /**
     * 点击Teacher Only按钮事件
     */
    changeTeacherOnly() {
      this.$bus.$emit('handleSwitchMessage', this.status)
      logger.send({
        tag: 'action',
        content: {
          msg: '点击切换Teacher Only状态',
          status: this.status
        }
      })
      sensorEvent('hw_classroom_teacher_only_switch', this.options, {
        switch_type: this.status ? 1 : 0
      })
    }
  }
}
</script>
<style lang="scss" scoped>
// 视频回显开关
.team-switch {
  .switch-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 36px;
  }
  .title {
    font-size: 14px;
    color: #172b4d;
    cursor: default;
    margin-right: 10px;
  }
}
</style>
