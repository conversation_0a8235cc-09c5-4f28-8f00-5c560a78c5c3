<template>
  <div class="team-switch">
    <div class="switch-wrapper">
      <div class="title">
        {{ $t('classroom.largeClass.header.groupVideoButtonName') }}
      </div>
      <div class="switch">
        <a-switch v-model="status" :disabled="disabledStatus" @change="handleSwitchButton" />
      </div>
    </div>
  </div>
</template>
<script>
import { sensorEvent } from 'utils/sensorEvent'
import logger from 'utils/logger'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
export default {
  props: {
    planId: {
      type: Number,
      default: 0
    },
    options: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      // 开关状态
      // false: 隐藏视频回显区
      // true: 显示视频回显区
      status: false,
      // 禁用状态
      disabledStatus: false
    }
  },
  mounted() {
    this.getSwitchStatus()
    this.$bus.$on('teamSwitchDisabled', disabledStatus => {
      this.disabledStatus = disabledStatus
      console.log('TeamSwitch-teamSwitchDisabled', disabledStatus)
    })
  },
  methods: {
    // 处理按钮点击事件
    handleSwitchButton() {
      logger.send({
        tag: 'action',
        content: {
          msg: '点击切换videoGroup显示状态',
          disabledStatus: this.disabledStatus,
          status: this.status
        }
      })
      if (this.disabledStatus) {
        return
      }
      classLiveSensor.osta_group_video_change(this.status)
      console.log('TeamSwitch-handleSwitchButton', this.status)
      this.updateSwitchStatus()
    },
    // 获取按钮本地状态
    async getSwitchStatus() {
      const status = await window.thinkApi.ipc.invoke(
        'getStoreValue',
        `videoGroupSwitchStatus_${this.planId}`
      )
      this.status = status === undefined ? true : status
    },
    // 更新按钮状态
    async updateSwitchStatus() {
      await window.thinkApi.ipc.invoke(
        'setStoreValue',
        `videoGroupSwitchStatus_${this.planId}`,
        this.status
      )
      // 广播更新状态
      this.$bus.$emit('updateTeamSwitchStatus', this.status)
      // 埋点
      sensorEvent('hw_classroom_toolbar_video', this.options, {
        switch_type: this.status ? 1 : 0
      })
    }
  }
}
</script>
<style lang="scss" scoped>
// 视频回显开关
.team-switch {
  .switch-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 36px;
  }
  .title {
    font-size: 14px;
    color: #172b4d;
    cursor: default;
  }
}
</style>
