<template>
  <div class="device-test">
    <div class="device-test-wrapper" @click="handleClick">
      <div class="title">
        {{ $t('classroom.largeClass.header.deviceTestButtonName') }}
      </div>
    </div>
  </div>
</template>
<script>
export default {
  methods: {
    handleClick() {
      this.$bus.$emit('room.deviceTestShow')
    }
  }
}
</script>
<style lang="scss" scoped>
.device-test {
  .device-test-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 36px;
    cursor: pointer;
  }
  .title {
    font-size: 14px;
    color: #172b4d;
  }
}
</style>
