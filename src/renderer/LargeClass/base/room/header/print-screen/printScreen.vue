<template>
  <div class="print-screen-container" @click="handleClick"></div>
</template>

<script>
import screenshot from 'utils/screenshot'

let loading = false
export default {
  methods: {
    /**
     * 点击截屏
     */
    async handleClick() {
      this.$emit('click')
      if (loading) return
      // this.visible = false // 隐藏tips, 防止截屏时出现在图片上
      loading = true
      const res = await screenshot()
      loading = false
      if (!res.thumbnail) return
      this.$bus.$emit('screenThumbnail', res.thumbnail)
    }
  }
}
</script>

<style lang="scss" scoped>
.print-screen-container {
  position: relative;
  margin-left: 20px;
  width: 30px;
  height: 30px;
  background-image: url('./imgs/icon_screenshot.png');
  background-size: cover;
  cursor: pointer;
  border-radius: 50%;
  &:hover {
    background-image: url('./imgs/icon_screenshot_hover.png');
  }
}
</style>
