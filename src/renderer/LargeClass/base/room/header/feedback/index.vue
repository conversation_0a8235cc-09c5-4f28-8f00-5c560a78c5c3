<template>
  <div v-if="showStatus" class="feedback-wrapper">
    <div class="feedback-popup">
      <div class="popup-close" @click="handleHide"></div>
      <div class="popup-wrapper">
        <div class="popup-header">
          {{ $t('classroom.modules.feedback.headerName') }}
        </div>
        <div class="popup-contenter">
          <FeedbackOptions @update-checked-info="updateCheckedInfo" />
          <div class="feedback-textarea">
            <textarea
              v-model="content"
              maxlength="500"
              :placeholder="$t('classroom.modules.feedback.placeholder')"
            ></textarea>
          </div>
          <div class="agree-wrapper" @click="handleClickAgree">
            <div class="agree-checkbox">
              <div v-if="agreeChecked" class="icon-checked"></div>
            </div>
            {{ $t('classroom.modules.feedback.screenshotTips') }}
          </div>
          <div class="button-wrapper">
            <a-button block type="primary" shape="round" size="large" @click="handleSend">
              {{ $t('common.send') }}
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import FeedbackOptions from './feedbackOptions.vue'
import { dataURLtoBlob } from 'utils/util'
import { nativeApi } from 'utils/electronIpc'
import { getSchoolCode } from 'utils/local'
import { chatMsgPriority } from '@/LargeClass/base/interaction-handler/interaction-conf'
import Upload from 'utils/upload'
import screenshot from 'utils/screenshot'
import logger from 'utils/logger'
import { sensorEvent } from 'utils/sensorEvent'
export default {
  props: {
    options: {
      type: Object,
      default: null
    },
    rtcConfig: {
      type: Object,
      default: null
    },
    downlinkNetworkQuality: {
      type: Number,
      default: null
    }
  },
  components: {
    FeedbackOptions
  },
  data() {
    return {
      showStatus: false,
      agreeChecked: true,
      content: '',
      checkedCode: '',
      checkedName: ''
    }
  },
  mounted() {
    console.log('feedback-mounted', this.options, this.rtcConfig)
  },
  methods: {
    handleShow() {
      this.showStatus = true
    },
    handleHide() {
      this.showStatus = false
    },
    handleClickAgree() {
      this.agreeChecked = !this.agreeChecked
    },
    handleSend() {
      this.handleHide()
      this.$Message.info(this.$t('classroom.modules.feedback.sendSuccessNotice'))
      if (this.agreeChecked) {
        this.uploadScreenshot(screenshotUrl => {
          this.sendMessage(screenshotUrl)
        })
      } else {
        this.sendMessage()
      }
      this.sendLogger(
        `点击Send按钮: ${JSON.stringify({
          checkedName: this.checkedName,
          content: this.content.replace(/[\r\n]/g, ' '),
          agreeChecked: this.agreeChecked
        })}`
      )
      sensorEvent('hw_classroom_toolbar_problem', this.options, {
        problem_type: this.checkedName
      })
    },
    async sendMessage(screenshotUrl) {
      const schoolCode = await getSchoolCode()
      const deviceInfo = await nativeApi.getDeviceInfo()
      const questionMsg = this.content.replace(/[\r\n]/g, ' ')
      const content = JSON.stringify({
        type: 150,
        from: 'flv',
        name: this.options.nickName,
        msg: 'I send a feedback',
        parameter: {
          schoolCode: schoolCode,
          planId: this.options.planId,
          roomId: this.options.classId,
          studentId: this.options.stuIRCId,
          uid: this.options.stuId,
          teacherId: this.options.teacherInfo.id,
          teacherName: this.options.teacherInfo.name,
          teacherRoomId: this.rtcConfig?.teacherRoomId,
          startTime: this.options.stime,
          currenTime: new Date().getTime(),
          device: deviceInfo.platform,
          deviceVersion: deviceInfo.osVersion,
          AppVersion: deviceInfo.appVersion,
          question: this.checkedName,
          question_msg: questionMsg,
          question_url: screenshotUrl || ''
        }
      })
      console.log('sendMessage', content)
      const res = window.ChatClient.PeerChatManager.sendPeerMessage(
        [{ nickname: this.options.configs.tutorIrcId }],
        content,
        chatMsgPriority.privMsg
      )
      this.resetData()
      if (res != 0) {
        this.sendLogger(`发送反馈消息失败: ${content} irc消息发送返回状态: ${res}`, 'error')
        return
      }
      this.sendLogger(`发送反馈消息成功: ${content}`)
    },
    async uploadScreenshot(callback) {
      try {
        const { thumbnail } = await screenshot({
          thumbnailWidth: 1024,
          thumbnailQuality: 1
        })
        const thumbnailBlob = dataURLtoBlob(thumbnail)
        const upload = new Upload({
          scene: 'classFeedback'
        })
        upload.putFile({
          filePath: 'classFeedback.jpg',
          file: thumbnailBlob,
          progress: percent => {
            console.log('uploadScreenshot-progress', percent)
          },
          success: res => {
            console.log('uploadScreenshot-success', res)
            callback && callback(res.url)
            this.sendLogger(`截屏上传成功:${res.url}`)
          },
          fail: () => {
            console.log('uploadScreenshot-fail')
            callback && callback()
            this.sendLogger(`截屏上传失败`, 'error')
          }
        })
      } catch (error) {
        console.error('截屏上传报错', error)
        callback && callback()
      }
    },
    updateCheckedInfo(code, name) {
      this.checkedCode = code
      this.checkedName = name
      console.log('updateCheckedInfo', code, name)
    },
    resetData() {
      this.content = ''
      this.checkedCode = ''
      this.checkedName = ''
    },
    /**
     * 日志上报
     */
    sendLogger(msg, level = 'info') {
      logger.send({
        tag: 'liveFeedback',
        content: {
          msg: msg,
          level
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.feedback-popup {
  position: fixed;
  width: 300px;
  height: 500px;
  top: 75px;
  right: 10px;
  z-index: 999;
  border-radius: 15px;
  background: rgba(0, 0, 0, 0.8);
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    display: block;
    filter: blur(20px);
    background: rgba(0, 0, 0, 0.6);
    z-index: -1;
  }
  .popup-close {
    position: absolute;
    left: -24px;
    top: 16px;
    width: 24px;
    height: 74px;
    background-image: url('~assets/images/live/icon-close.png');
    background-size: cover;
    cursor: pointer;
  }
  .popup-wrapper {
    position: relative;
    padding: 10px;
  }
  .popup-header {
    height: 30px;
    line-height: 30px;
    margin-bottom: 10px;
    font-size: 16px;
    color: #fff;
    text-align: center;
  }
}
.agree-wrapper {
  position: relative;
  margin: 10px 0 15px 22px;
  color: #6f727b;
  cursor: pointer;
  .agree-checkbox {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 4px;
    left: -22px;
    width: 14px;
    height: 14px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    .icon-checked {
      width: 10px;
      height: 10px;
      background-image: url('~assets/images/live/icon-checked.png');
      background-size: cover;
    }
  }
}
.feedback-textarea {
  textarea {
    width: 280px;
    height: 220px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    padding: 10px;
    outline: none;
    color: #fff;
    resize: none;
  }
}
</style>
