<template>
  <div class="feedback-options">
    <div
      class="item"
      v-for="(item, index) in options"
      :key="index"
      :class="{ active: checkedCode == item.code }"
      @click="handleClickOption(item)"
    >
      <div v-if="checkedCode == item.code" class="icon-checked"></div>
      <div class="item-name">{{ item.name }}</div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    const optionNames = this.$t('classroom.modules.feedback.optionNames')
    return {
      options: [
        {
          code: 'study-question',
          name: optionNames[0]
        },
        {
          code: 'app-problem',
          name: optionNames[1]
        },
        {
          code: 'inappropriate-behavior',
          name: optionNames[2]
        },
        {
          code: 'others',
          name: optionNames[3]
        }
      ],
      checkedCode: '',
      checkedName: ''
    }
  },
  methods: {
    handleClickOption(item) {
      if (item.code == this.checkedCode) {
        this.checkedCode = ''
        this.checkedName = ''
        this.$emit('update-checked-info', '', '')
        return
      }
      this.checkedCode = item.code
      this.checkedName = item.name
      this.$emit('update-checked-info', item.code, item.name)
    }
  }
}
</script>
<style lang="scss" scoped>
.feedback-options {
  overflow: hidden;
  margin-right: -10px;
  .item {
    float: left;
    height: 32px;
    line-height: 32px;
    padding: 0 8px;
    margin: 0 10px 10px 0;
    color: #a2aab8;
    text-align: center;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    &:hover,
    &.active {
      color: #ffaa0a;
      background: rgba(255, 170, 10, 0.08);
      border: 1px solid rgba(255, 170, 10, 0.14);
    }
  }
}
</style>
