<template>
  <div v-if="showAchievement" :class="[achievementClassName]">
    <div v-if="type === 'normal'" class="achievement-container">
      <div class="coins-wrapper">
        <div :class="['coins-icon', { 'add-coin': addCoin }]"></div>
        <span class="coins-title">
          {{ $t('classroom.largeClass.coins.title') }}
        </span>
      </div>
      <div class="coins-num" v-if="!addCoin">{{ gold }}</div>
      <CountTo
        v-else
        class="coins-num"
        :startVal="startCoin"
        :endVal="endCoin"
        :duration="400"
        separator=""
        @end="addCoin = false"
      />
    </div>
    <div v-if="type === 'simple'" class="achievement-container">
      <div :class="['coins-icon-simple', { 'add-coin': addCoin }]"></div>
      <span class="count" v-if="!addCoin">{{ gold }}</span>
      <CountTo
        v-else
        class="count"
        :startVal="startCoin"
        :endVal="endCoin"
        :duration="400"
        separator=""
        @end="addCoin = false"
      />
    </div>
  </div>
</template>

<script>
import CountTo from '@/components/Common/CountTo'
import qs from 'querystringify'
import { studentCoinAndMedal } from 'api/classroom/index'
export default {
  components: {
    CountTo
  },
  props: {
    options: {
      type: Object,
      default: null
    },
    type: {
      type: String,
      default: 'simple'
    },
    showAchievement: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      gold: this.options.goldNum,
      addCoin: false,
      startCoin: 0,
      endCoin: 0
    }
  },
  computed: {
    achievementClassName() {
      if (this.type === 'normal') {
        return 'achievement-wrapper-normal'
      }
      if (this.type === 'simple') {
        return 'achievement-wrapper-simple'
      }
    }
  },
  mounted() {
    this.$bus.$on('updateAchievement', (type, num) => {
      this[type](num)
    })
    this.$bus.$on('addCoin', (isStart, coin) => {
      if (isStart) {
        this.addCoin = true
        this.startCoin = +this.gold
        this.endCoin = this.startCoin + coin
        this.gold = this.endCoin
      }
      studentCoinAndMedal({
        planId: qs.parse(window.location.search).planId
      }).then(res => {
        if (res && res.code === 0) {
          this.gold = parseInt(res?.data?.totalCoin || '')
        }
      })
    })
  },
  methods: {
    /**
     * 添加金币值
     */
    add(num) {
      this.gold = this.gold + parseInt(num)
    },

    /**
     * 设置金币值
     */
    async update(num) {
      // 如果没有传更新的金币数量，则通过接口获取
      if (!num) {
        num = await this.initMedalCoins()
      }
      this.gold = parseInt(num)
    },
    async initMedalCoins() {
      const query = qs.parse(window.location.search)
      const res = await studentCoinAndMedal({
        planId: query.planId
      })
      return res?.data?.totalCoin || ''
    }
  }
}
</script>

<style lang="scss" scoped>
.add-coin {
  animation: scaleCoin 0.15s linear infinite;
}

@keyframes scaleCoin {
  0% {
    transform: scale(1);
  }
  66% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.achievement-wrapper-simple {
  position: absolute;
  left: 1px;
  top: 4px;
  z-index: 1;
  .achievement-container {
    position: relative;
    display: flex;

    .coins-icon-simple {
      margin-top: -2px;
      z-index: 1;
      width: 22px;
      height: 22px;
      background: url('./assets/icon_coins_black.png') no-repeat;
      background-size: cover;
    }

    .count {
      display: inline-block;
      height: 18px;
      line-height: 18px;
      padding: 0 5px 0 15px;
      margin-left: -10px;
      color: #fff;
      font-size: 12px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 0px 4px 4px 0px;
    }
  }
}
.achievement-wrapper-normal {
  position: absolute;
  z-index: 20;
  right: 0;
  width: 27.5%;
  top: calc(246 / 739 * 100vh + 44px);
  .achievement-container {
    height: calc(34 / 739 * 100vh);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #2c2c2c;
    padding: 0 20px 0 8px;
    border-radius: 4px;
    .coins-wrapper {
      display: flex;
      .coins-icon {
        width: 26px;
        height: 26px;
        margin-right: 8px;
        background: #1b1b1b url(./assets/icon_coins.png) no-repeat center;
        background-size: cover;
        border-radius: 13px;
      }
      .coins-title {
        line-height: 24px;
        font-size: 14px;
        font-weight: 500;
        color: #dee2e7;
      }
    }
    .coins-num {
      font-size: 16px;
      color: #ffaa0a;
    }
  }
}
</style>
