import Vue from 'vue'
import Vuex from 'vuex'
import header from './header/header.vue'
import videoGroup from './videoGroup/index.vue'
import controller from './controller/live/controller.vue'
import roomModules from './modules/index.vue'
import RoomBase from '@thinkacademy/live-framework/components/base/room/room-base'
import { i18n } from 'locale'

Vue.use(Vuex)

export default class Room extends RoomBase {
  // 1、把直播的video初始化
  // 2、把功能条初始化
  constructor(options = {}) {
    super()
    this.options = options
    this.initRoom()
  }

  initRoom() {
    console.log('initRoom', this.options)
    const { videoGroupDom, controllerDom, headerDom, roomModulesDom } = this.options
    this.videoGroup = this.init(videoGroupDom, videoGroup)
    this.controllerVm = this.init(controllerDom, controller)
    this.headerVm = this.init(headerDom, header)
    this.roomModulesVm = this.init(roomModulesDom, roomModules)
  }

  /**
   *
   * @param {*} dom
   * @description 初始化播放器
   */
  init(dom, app) {
    const vm = this.createVueRoom(app)
    this.render(dom, vm)
    return vm
  }

  createVueRoom(app) {
    const Constructor = Vue.extend(app)
    let vm = null
    const props = this.createRoomProps()
    vm = new Constructor({
      i18n,
      propsData: props
    })
    vm.$mount()
    return vm
  }

  createRoomProps() {
    console.log('live-createRoomProps', this.options)
    const { moduleInfo, roomInfo } = this.options.roomMessage
    const opts = {
      options: roomInfo.commonOption,
      stuInfo: roomInfo.stuInfo,
      moduleInfo: moduleInfo,
      planId: roomInfo.planInfo.id,
      planName: roomInfo.planInfo.name,
      skinType: roomInfo.configs.skinType,
      isPlayBack: roomInfo.isPlayBack,
      playerOptions: roomInfo.playerOptions,
      planInfo: roomInfo.planInfo,
      rtcConfig: roomInfo.configs.rtcConfig
    }
    return opts
  }

  /*
   * 处理事件中心传递过来的信息
   */
  eventHandler(params) {
    console.log('roomInteraction', params)
  }
}
