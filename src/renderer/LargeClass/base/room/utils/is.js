const userAgent = navigator.userAgent.toLowerCase()

const isIE = () => {
  if (!!window.ActiveXObject || 'ActiveXObject' in window) {
    return 'IE'
  } else if (userAgent.indexOf('edge') > -1) {
    return 'Edge'
  }
  return false
}

const isClient = () => {
  return navigator.userAgent.toLocaleLowerCase().indexOf('xescef') != -1 ? true : false
}

const isPc = () => {
  const appAgents = /(android|iphone|symbianos|windows phone|ipod|ipad)/i
  if (userAgent.match(appAgents)) return false
  return true
}

const isInApp = () => {
  return /jzh$/.test(userAgent)
}

const isWeiXin = () => {
  return userAgent.match(/MicroMessenger/i) == 'micromessenger'
}

const isMac = () => {
  return navigator.userAgent.match(/Mac OS/)
}

const isChrome = () => {
  return navigator.userAgent.indexOf('Chrome') > -1
}

const isIos = () => {
  if (navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) return true
}

/**
 * @description 判断是否为英语课程
 */
const isEnglishCourse = subjectIds => {
  let subjectId = subjectIds.split(',')
  let isEnglish = false
  // English : (3) (24) (3, 24)
  if (subjectId.length < 3) {
    if (subjectId.length == 1) {
      isEnglish = subjectId.includes('3') || subjectId.includes('24')
    } else {
      isEnglish = subjectId.includes('3') && subjectId.includes('24')
    }
  }

  return isEnglish
}

export { isClient, isIE, isPc, isInApp, isWeiXin, isMac, isChrome, isIos, isEnglishCourse }
