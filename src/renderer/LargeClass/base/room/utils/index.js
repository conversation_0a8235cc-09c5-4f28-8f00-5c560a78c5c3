/**
 * @name countdown
 * @description 倒计时
 * @param {number} duration 倒计时秒数
 * @param {string} cb 倒计时过程中回调
 */
export const countDown = (duration, cb) => {
  return new Promise(resolve => {
    let timer = null
    if (timer) clearInterval(timer)
    timer = setInterval(() => {
      if (duration < 1) {
        resolve(duration)
        clearInterval(timer)
        return
      }
      cb(duration)
      duration--
    }, 1000)
  })
}

// base64 转为 file 对象
export const dataURLtoFile = (dataurl, filename) => {
  var arr = dataurl.split(',')
  var mime = arr[0].match(/:(.*?);/)[1]
  var bstr = atob(arr[1])
  var n = bstr.length
  var u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], filename, { type: mime })
}

export const formatSeconds = value => {
  // @log-ignore
  if (value < 0) {
    return '00:00:00'
  }
  var secondTime = parseInt(value) // 秒
  var minuteTime = 0 // 分
  var hourTime = 0 // 小时
  if (secondTime > 60) {
    //如果秒数大于60，将秒数转换成整数
    //获取分钟，除以60取整数，得到整数分钟
    minuteTime = parseInt(secondTime / 60)
    //获取秒数，秒数取佘，得到整数秒数
    secondTime = parseInt(secondTime % 60)
    //如果分钟大于60，将分钟转换成小时
    if (minuteTime > 60) {
      //获取小时，获取分钟除以60，得到整数小时
      hourTime = parseInt(minuteTime / 60)
      //获取小时后取佘的分，获取分钟除以60取佘的分
      minuteTime = parseInt(minuteTime % 60)
    }
  }
  return `${parseInt(hourTime) > 9 ? parseInt(hourTime) : '0' + parseInt(hourTime)}:${
    parseInt(minuteTime) > 9 ? parseInt(minuteTime) : '0' + parseInt(minuteTime)
  }:${parseInt(secondTime) > 9 ? parseInt(secondTime) : '0' + parseInt(secondTime)}`
}
