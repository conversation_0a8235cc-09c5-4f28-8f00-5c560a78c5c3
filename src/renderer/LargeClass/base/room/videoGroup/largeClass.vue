<template>
  <div>
    <Achievement :showAchievement="showAchievement" :options="options" type="normal" />
    <div v-show="teamSwitchStatus === true" class="video-group-wrapper class-type-large">
      <div class="video-group-contenter">
        <!-- 家长旁听 -->
        <template v-if="options.isParent">
          <RemoteVideo
            v-if="myChildInfo.onlineStatus"
            :key="myChildInfo.uid"
            :remoteStuInfo="myChildInfo"
            :showAudioWaves="showAudioWaves"
            :hideRemoteVideo="hideRemoteVideo"
            @handleRemoteVideoStatus="handleRemoteVideoStatus"
          />
          <!-- 自家孩子不在课堂中 -->
          <OffLineVideo v-else :name="myChildInfo.stuName"></OffLineVideo>
        </template>
        <!-- 学生旁听 -->
        <template v-else-if="!options.isParent && options.isAudition">
          <VideoItemPad :notice="$t('classroom.largeClass.videoGroup.localVideoAudition.notice')" />
        </template>
        <!-- 学生正式课 -->
        <template v-else>
          <LocalVideo
            :options="options"
            :localStuInfo="localStuInfo"
            :hideVideoIcon="hideLocalVideoIcon"
            @handleLocalVideoStatus="handleLocalVideoStatus"
          />
        </template>
        <template v-for="(item, index) in remoteStuInfo">
          <RemoteVideo
            :key="index"
            :remoteStuInfo="item"
            :showAudioWaves="showAudioWaves"
            :hideRemoteVideo="hideRemoteVideo"
            :isExaminationStatus="isExaminationStatus"
            @handleRemoteVideoStatus="handleRemoteVideoStatus"
          />
        </template>
        <template v-if="remoteStuPadNum > 0">
          <VideoItemPad
            v-for="item in remoteStuPadNum"
            :key="item"
            :notice="
              canShowOtherChild ? $t('classroom.largeClass.videoGroup.textConfig.matching') : ''
            "
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import Achievement from '../achievement'
import LocalVideo from './localVideo'
import RemoteVideo from './remoteVideo'
import OffLineVideo from './offLineVideo'
import RtcClass from './rtcClass'
import VideoGroup from './videoGroupClass'
import VideoItemPad from './videoItemPad'
import { getCameraStatus } from 'utils/mediaAccess'
import { isOpenTheFunc } from 'utils/initConfig'
import { RtcSensor } from 'utils/sensorEvent'
import { openExitByCoursewareChange } from 'utils/courses'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'

export default {
  components: {
    Achievement,
    LocalVideo,
    RemoteVideo,
    VideoItemPad,
    OffLineVideo
  },
  props: {
    options: {
      type: Object,
      default: null
    },
    stuInfo: {
      type: Object,
      default: null
    },
    rtcConfig: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      myChildInfo: {},
      teamSwitchStatus: null, // 视频回显状态
      localStuInfo: {
        // 本地学生信息
        stuId: this.stuInfo.id, // 学生ID
        stuName: this.stuInfo.nickName, // 学生昵称
        avatar: this.stuInfo.avatar, // 学生头像
        displayVideo: false, // 摄像头是否显示
        cameraStatus: true, // 摄像头授权状态
        level: this.stuInfo.level // 金币激励勋章等级
      },
      remoteStuInfo: [], // 远端学生信息
      speakStudentList: [], // 集体发言学生列表
      groupSpeakStatus: false, // 集体发言状态
      videoLinkStatus: false, // 视频连麦状态
      otherCameraStatus: false, // 其它摄像头使用状态
      localVideoDisplayCache: null, // 本地摄像头是否显示缓存状态
      remoteVideoFrameCache: {}, // 远端学生视频第一帧缓存
      remoteVideoMutedCache: {}, // 远端学生禁止视频缓存
      isExaminationStatus: false, // 是否是课中考试状态
      canShowOtherChild: false,
      classRtcSensor: null // 学生频道rtc方法
    }
  },
  computed: {
    // 显示金币区
    showAchievement() {
      return !this.options.isAudition && !this.teamSwitchStatus
    },
    // 本地视频推流状态
    localVideoPublishStatus() {
      return this.localVideoDisplayCache !== false && this.teamSwitchStatus
    },

    // 金币样式类型
    achievementType() {
      if (this.teamSwitchStatus) {
        return 'simple'
      } else {
        return 'normal'
      }
    },

    // 显示声纹图标
    showAudioWaves() {
      // 集体发言开启时，显示声纹图标
      return this.groupSpeakStatus
    },

    // 隐藏本地摄像头开关图标
    hideLocalVideoIcon() {
      // 视频连麦、其它摄像头占用时，隐藏本地摄像头开关图标
      if (this.videoLinkStatus || this.otherCameraStatus) {
        return true
      } else {
        return false
      }
    },

    // 强制隐藏视频
    hideRemoteVideo() {
      // 其它摄像头状态使用时强制隐藏远端视频
      // 例如: 拍照上墙互动
      return this.otherCameraStatus
    },

    // 远端学生补齐
    remoteStuPadNum() {
      if (this.remoteStuInfo.length === 3) {
        return 0
      }
      return 3 - this.remoteStuInfo.length
    }
  },
  mounted() {
    const opts = {
      options: this.options,
      stuInfo: this.stuInfo,
      rtcConfig: this.rtcConfig,
      publishStatus: !this.options.isAudition
    }
    this.rtcClass = new RtcClass(opts)
    this.videoGroup = new VideoGroup(opts)
    this.init()
    console.info('进入大班课')
    window.addEventListener('beforeunload', this.handleBeforeUnload)
  },
  beforeDestroy() {
    window.removeEventListener('beforeunload', this.handleBeforeUnload)
  },

  methods: {
    handleBeforeUnload(event) {
      console.info('刷新过')
      if (window.RTC_COMMON && window.RTC_COMMON.rtcEngine) {
        window.RTC_COMMON.rtcEngine.release()
        window.RTC_COMMON = null
      }
    },
    /**
     * 是否展示自家孩子以外的学生
     */
    async initVideoShow() {
      // 家长旁听获取云控配置信息判断是否显示其他学生
      if (this.options.isParent) {
        this.canShowOtherChild = await isOpenTheFunc('showOtherChild')
      } else {
        this.canShowOtherChild = true
      }
    },
    /**
     * 初始化
     */
    async init() {
      await this.initVideoShow()
      this.teamSwitchStatus = await this.videoGroup.getTeamSwitchStatus()
      await this.initClassRtcChannel()
      this.localStuInfo.cameraStatus = await getCameraStatus()
      this.listenBusEvent()
      this.videoGroup.sendRtcStatus({
        displayVideo: this.teamSwitchStatus
      })
    },

    /**
     * 初始化班级RTC频道
     */
    async initClassRtcChannel() {
      // 创建班级RTC频道
      const classRtcChannel = await this.rtcClass.init()

      // 本地未开启视频小组, 则停止本地视频采集
      if (!this.teamSwitchStatus) {
        this.rtcClass.muteLocalVideo(true)
        this.videoGroup.sendLogger(`初始化本地未开启视频小组`)
      }
      // 监听班级频道网络连接状态
      classRtcChannel.on('connectionStateChanged', state => {
        console.log('[hw_rtc_join_room]班级频道网络状态触发方法触发,code:', state)
        if (state == 5) {
          this.classRtcSensor.rtcSensorPush({ result: 'fail', errorType: '连接失败' })
        } else if (state == 4) {
          this.classRtcSensor.isFirstJoinChannel = false
          this.classRtcSensor.rtcSensorPush({ result: 'start' })
        }
      })
      // 监听本地加入频道
      classRtcChannel.on('localJoinChannel', () => {
        // 设置本地视频视图
        if (this.teamSwitchStatus) {
          this.setupLocalVideo()
        }
        // 家长旁听初始化自己孩子信息
        if (this.options.isParent) {
          this.setupMyChildVideo()
        }
        // 能看到其他学生时才设置远端视频
        if (this.canShowOtherChild) {
          // 设置远端视频视图
          this.setupRemoteVideo()
        }
        this.videoGroup.sendLogger('本地加入班级频道成功')
        console.log('[hw_rtc_join_room]班级频道本地加入成功')
        this.classRtcSensor.rtcSensorPush({ result: 'success' })
      })
      // 监听本地重连加入班级频道
      classRtcChannel.on('rejoinChannelSuccess', () => {
        console.log('[hw_rtc_join_room]班级频道重连本地加入成功')
        this.videoGroup.sendLogger('本地重连加入班级频道成功')
        this.classRtcSensor.rtcSensorPush({ result: 'success' })
      })
      // 监听远端离开频道
      classRtcChannel.on('remoteLeaveChannel', uid => {
        console.log('videoGroup-remoteLeaveChannel', uid)
        this.videoGroup.sendLogger(`远端学生离开频道, uid: ${uid}`)
        this.setRemoteStuVal(uid, 'onlineStatus', false)
      })

      // 监听远端加入频道
      classRtcChannel.on('remoteJoinChannel', uid => {
        console.log('videoGroup-remoteJoinChannel', uid)
        this.videoGroup.sendLogger(`远端学生加入频道, uid: ${uid}`)
        this.setRemoteStuVal(uid, 'onlineStatus', true)
        this.rtcClass.muteRemoteAudio(uid, !this.isMyChild(uid))
      })

      // 监听远端视频流状态变化
      classRtcChannel.on('remoteVideoStateChanged', (uid, state, reason) => {
        console.log('videoGroup-remoteVideoStateChanged', uid, state, reason)
        // 接收到远端视频流首帧
        if (state == 2) {
          // 记录缓存
          this.remoteVideoFrameCache[uid] = true
          // 更新远端学生状态
          this.setRemoteStuVal(uid, 'cameraStatus', true)
          this.setRemoteStuVal(uid, 'onlineStatus', true)
          this.setRemoteStuVal(uid, 'mutedVideoStatus', false) // IOS退出频道会调用mute, 当IOS再次进入频道时需要强制解开mute
          this.rtcClass.setupRemoteVideo(uid, `remote-${uid}`)
        }
        // 远端禁用视频
        if (reason === 5) {
          this.remoteVideoMutedCache[uid] = true
          this.setRemoteStuVal(uid, 'mutedVideoStatus', true)
        }
        // 远端启用视频
        if (reason === 6) {
          this.remoteVideoMutedCache[uid] = false
          this.setRemoteStuVal(uid, 'mutedVideoStatus', false)
          this.rtcClass.setupRemoteVideo(uid, `remote-${uid}`)
        }
        console.log('videoGroup-remoteVideoStateChanged', this.remoteStuInfo)
      })
      this.classRtcSensor = new RtcSensor() // 主讲频道神策埋点方法初始化
      console.log('[hw_rtc_join_room]加入班级频道触发start')
      this.classRtcSensor.rtcSensorPush({ result: 'start' })
      const joinRes = classRtcChannel.joinChannel()
      if (joinRes == 0) {
        this.videoGroup.sendLogger(`调用加入班级频道成功`)
      } else {
        this.videoGroup.sendLogger(`调用加入班级频道失败,code:${joinRes}`, {}, 'error')
        console.log('[hw_rtc_join_room]调用加入班级频道方法触发fail')
        this.classRtcSensor.rtcSensorPush({ result: 'fail', errorType: '调用加入房间接口失败' })
      }
    },

    /**
     * 监听通信事件
     */
    listenBusEvent() {
      // 设置默认摄像头
      this.$bus.$on('setDefaultVideoDevice', deviceId => {
        this.rtcClass.setVideoDevice(deviceId)
      })

      // 设置默认麦克风
      this.$bus.$on('setDefaultAudioRecordingDevice', deviceId => {
        this.rtcClass.setAudioRecordingDevice(deviceId)
      })

      // 设置默认扬声器
      this.$bus.$on('setDefaultAudioPlaybackDevice', deviceId => {
        this.rtcClass.setAudioPlaybackDevice(deviceId)
      })

      // 监听视频回显状态变化
      this.$bus.$on('updateTeamSwitchStatus', status => {
        console.log('videoGroup-updateTeamSwitchStatus', status)
        this.videoGroup.sendLogger(`收到视频回显状态变化, status: ${status}`)
        this.teamSwitchStatus = status
        if (status) {
          if (this.localVideoDisplayCache !== false) {
            this.rtcClass.muteLocalVideo(false)
            this.setupLocalVideo()
            this.videoGroup.sendRtcStatus({
              displayVideo: true
            })
          }
        } else {
          this.rtcClass.muteLocalVideo(true)
          this.hideLocalVideo()
          this.videoGroup.sendRtcStatus({
            displayVideo: false
          })
        }
      })

      // 监听摄像头使用状态事件
      this.$bus.$on('cameraStatus', status => {
        console.log('videoGroup-cameraStatus', status)
        this.$bus.$emit('teamSwitchDisabled', status) // 通知视频回显按钮禁用状态变化
        if (status) {
          this.rtcClass.muteLocalVideo(true)
        } else {
          // 如果本地没有关闭摄像头时进行推流
          if (this.localVideoPublishStatus) {
            this.rtcClass.muteLocalVideo(false)
          }
        }
        this.videoGroup.sendLogger(
          `摄像头使用状态, status: ${status},localVideoPublishStatus: ${this.localVideoPublishStatus}`
        )
      })
      // 监听视频连麦互动状态事件
      this.$bus.$on('videoLinkStatus', status => {
        console.log('videoGroup-videoLinkStatus', status)
        this.$bus.$emit('teamSwitchDisabled', status) // 通知视频回显按钮禁用状态变化
        this.videoLinkStatus = status
      })

      // 监听本地视频连麦互动状态事件
      this.$bus.$on('localVideoLinkStatus', status => {
        console.log('videoGroup-localVideoLinkStatus', status)
        this.videoGroup.sendLogger(`收到视频连麦互动状态变化, status: ${status}`)
        if (status) {
          this.rtcClass.unpublish()
          this.hideLocalVideo()
        } else {
          this.rtcClass.publish({
            // 视频推流状态
            // 本地视频关闭或视频回显区关闭, 则不推流
            publishVideo: this.localVideoPublishStatus
          })
          this.setupLocalVideo()
        }
        this.otherCameraStatus = status
      })

      // 监听集体发言互动状态事件
      this.$bus.$on('groupSpeakStatus', status => {
        console.log('videoGroup-groupSpeakStatus', status)
        this.videoGroup.sendLogger(`收到集体发言互动状态变化, status: ${status}`)
        // 设置本地集体发言状态
        this.groupSpeakStatus = status
        // 集体发言状态逻辑
        if (status) {
          this.collectiveSpeechOpen()
        } else {
          this.collectiveSpeechClose()
        }
      })
      // 监听退出直播事件
      this.$bus.$on('liveQuit', () => {
        this.rtcClass.unpublish()
        this.rtcClass.leaveChannel()
      })
      this.$bus.$on('openExitByCoursewareChange', backUrl => {
        openExitByCoursewareChange(
          this.$t('classroom.modules.header.backConfirm.exitByCoursewareIdChange'),
          this.$t('courses.confirmModal.confirm'),
          backUrl
        )
      })
      // 监听获取本地视频开关状态事件
      this.$bus.$on('getLocalDisplayVideoStatus', callback => {
        callback && callback(this.localVideoPublishStatus)
      })

      // 监听自己的金币激励勋章变化
      this.$bus.$on('chats.correctSelfMedalData', data => {
        this.localStuInfo.level = data.level
      })

      // 监听小组其他学生金币激励勋章变化
      this.$bus.$on('chats.correctMedalData', data => {
        this.setRemoteStuVal(data.userId, 'level', data.level)
      })

      // 监听课中考试状态：考试时其他学员的视频调整为头像状态，结束后恢复
      this.$bus.$on('setExaminationStatus', data => {
        this.isExaminationStatus = data
      })
    },

    /**
     * 设置本地视频视图
     */
    setupLocalVideo() {
      if (this.options.isAudition || !this.localStuInfo.cameraStatus) {
        return
      }
      this.rtcClass.setupLocalVideo('video-group-local')
      if (this.localVideoDisplayCache === false) {
        return
      }
      this.localStuInfo.displayVideo = true
    },

    /**
     * 隐藏本地视频视图
     */
    hideLocalVideo() {
      this.localStuInfo.displayVideo = false
    },
    /**
     * 构建学生信息
     */
    buildInfo(stuInfo) {
      const remoteVideoFrame = this.remoteVideoFrameCache[stuInfo.userId]
      const mutedVideoStatus = this.remoteVideoMutedCache[stuInfo.userId] || false
      return {
        showStatus: true, // 是否显示
        stuId: stuInfo.userId, // 学生ID
        stuName: stuInfo.nickName, // 学生昵称
        avatar: stuInfo.avatar, // 学生头像
        level: stuInfo.level, // 金币激励勋章等级
        cameraStatus: remoteVideoFrame ? true : false, // 摄像头打开状态, 根据视频首帧判断
        displayVideo: true, // 本地设置的远端视频显示状态 true: 显示视频 false: 隐藏视频
        mutedVideoStatus: mutedVideoStatus, // 远端视频禁止状态, 控制视频右下角眼睛图标显示隐藏 true: 已停止 false: 已开始
        onlineStatus: remoteVideoFrame ? true : false // 远端学生加入状态, 默认未加入 true: 已加入 false: 未加入
      }
    },
    /**
     * 设置远端视频视图
     */
    setupRemoteVideo() {
      this.videoGroup.queryGroupStudent({
        cameraStatus: this.localStuInfo.cameraStatus ? 1 : 2,
        addStudentCallback: stuInfo => {
          console.log('videoGroup-setupRemoteVideo', stuInfo)
          const stuObj = this.buildInfo(stuInfo)
          this.remoteStuInfo.push(stuObj)
          this.$nextTick(function() {
            this.rtcClass.muteRemoteAudio(stuInfo.userId, true)
            this.rtcClass.setupRemoteVideo(stuInfo.userId, `remote-${stuInfo.userId}`)
            // 默认关闭视频回显时，不显示Mute远端视频
            if (!this.teamSwitchStatus) {
              this.handleRemoteVideoStatus(stuInfo.userId, false)
            }
          })
        }
      })
    },
    /**
     * 设置自己孩子视频视图
     */
    setupMyChildVideo() {
      const info = {
        ...this.localStuInfo,
        userId: this.localStuInfo.stuId,
        nickName: this.localStuInfo.stuName
      }
      this.myChildInfo = this.buildInfo(info)
      this.$nextTick(function() {
        this.rtcClass.setupRemoteVideo(info.userId, `remote-${info.userId}`)
        // 默认关闭视频回显时，不显示Mute远端视频
        if (!this.teamSwitchStatus) {
          this.handleRemoteVideoStatus(info.userId, false)
        }
      })
    },
    /**
     * 启用/禁止所有远端学生视频
     */
    // muteAllRemoteVideo(mute) {
    //   this.remoteStuInfo.forEach(item => {
    //     this.rtcClass.muteRemoteVideo(item.stuId, mute)
    //     this.setRemoteStuVal(item.stuId, 'displayVideo', !mute)
    //   })
    // },

    /**
     * 开关本地视频推流状态
     */
    handleLocalVideoStatus() {
      if (this.localStuInfo.displayVideo) {
        this.rtcClass.muteLocalVideo(true)
      } else {
        this.rtcClass.muteLocalVideo(false)
        this.setupLocalVideo()
      }
      this.localStuInfo.displayVideo = !this.localStuInfo.displayVideo
      this.localVideoDisplayCache = this.localStuInfo.displayVideo
      this.videoGroup.sendRtcStatus({
        displayVideo: this.localStuInfo.displayVideo
      })
      classLiveSensor.osta_rtc_vidio_change(this.localStuInfo.displayVideo)
      this.$store.dispatch('largeClass/updateCameraStatus', this.localStuInfo.displayVideo)
      this.$bus.$emit('updateLocalDisplayVideoStatus', this.localStuInfo.displayVideo)
      this.videoGroup.sendLogger(
        `学生操作本地视频开关状态, status: ${this.localStuInfo.displayVideo}`
      )
    },

    /**
     * 开关远端视频状态
     */
    handleRemoteVideoStatus(stuId, status) {
      // 更新远端视频显示状态
      this.setRemoteStuVal(stuId, 'displayVideo', status)
      this.videoGroup.sendLogger(`学生操作远端视频开关状态, uid: ${stuId} status: ${status}`)
    },

    /**
     * 设置远端学生数据状态
     */
    setRemoteStuVal(uid, key, val) {
      if (!uid) return
      if (this.isMyChild(uid)) {
        this.$set(this.myChildInfo, key, val)
      } else {
        this.remoteStuInfo.forEach((item, index) => {
          if (item.stuId == uid) {
            this.$set(this.remoteStuInfo[index], key, val)
          }
        })
      }
      console.log('videoGroup-setRemoteStuVal', uid, key, val)
    },
    // 判断当前是否为家长旁听且id为自己孩子
    isMyChild(uid) {
      // @log-ignore
      return this.options.isParent && uid == this.options.stuId
    },
    /**
     * 集体发言打开
     */
    async collectiveSpeechOpen() {
      const speakStudentList = await this.videoGroup.querySpeakStudentList()
      // 列表为空或者已关闭集体发言状态，则不进行开启声音操作
      if (!speakStudentList.length || !this.groupSpeakStatus) {
        return
      }
      this.speakStudentList = speakStudentList
      this.speakStudentList.forEach(item => {
        this.rtcClass.muteRemoteAudio(item, false)
      })
      this.videoGroup.sendLogger(
        `集体发言拉取远端学生音频流, 远端学生列表: ${JSON.stringify(speakStudentList)}`
      )
    },

    /**
     * 集体发言关闭
     */
    collectiveSpeechClose() {
      this.speakStudentList.forEach(item => {
        this.rtcClass.muteRemoteAudio(item, true)
      })
      this.speakStudentList = []
    }
  }
}
</script>

<style lang="scss" scoped>
@import './style/index.scss';
@import './style/videoItem.scss';
</style>
