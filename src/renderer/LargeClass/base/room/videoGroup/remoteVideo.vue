<template>
  <div class="video-item video-remote" v-show="remoteStuInfo.showStatus">
    <div class="item-wrapper" v-show="remoteStuInfo.cameraStatus">
      <div v-if="showAudioWaves" class="audio-waves-wrapper">
        <AudioWaves :animation="true" size="small" skin="white" animation-type="sound" />
      </div>
      <div class="group-wrapper">
        <div v-show="showRemoteVideo" class="video-wrapper" :id="'remote-' + remoteStuInfo.stuId" />
        <div v-if="showRemoteAvatar" class="avatar-wrapper">
          <img :src="remoteStuInfo.avatar" />
        </div>
      </div>
      <div
        v-if="showCameraStatusIcon"
        class="camera-status"
        :class="[
          {
            'icon-visible': remoteStuInfo.displayVideo,
            'icon-invisible': !remoteStuInfo.displayVideo
          }
        ]"
        @click="handleRemoteVideoStatus"
      />
    </div>
    <template v-if="!remoteStuInfo.cameraStatus">
      <div class="group-wrapper">
        <div class="avatar-wrapper">
          <img :src="remoteStuInfo.avatar" />
        </div>
      </div>
    </template>
    <div class="student-name">{{ remoteStuInfo.stuName }}</div>
    <div v-if="!showAudioWaves && remoteStuInfo.level" class="medal-wrapper">
      <span class="icon" :class="remoteStuInfo.level ? `level-${remoteStuInfo.level}` : ''"></span>
      <span class="text" :class="smallLevelText">Lv{{ remoteStuInfo.level }}</span>
    </div>
  </div>
</template>

<script>
import AudioWaves from 'components/Common/AudioWaves'

export default {
  components: {
    AudioWaves
  },
  props: {
    remoteStuInfo: {
      type: Object,
      default: null
    },
    showAudioWaves: {
      type: Boolean,
      default: null
    },
    hideRemoteVideo: {
      type: Boolean,
      default: null
    },
    hideVideoIcon: {
      type: Boolean,
      default: false
    },
    // 是否在进行课中考试
    isExaminationStatus: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    showRemoteVideo() {
      return (
        this.remoteStuInfo.onlineStatus &&
        this.remoteStuInfo.displayVideo &&
        !this.remoteStuInfo.mutedVideoStatus &&
        !this.hideRemoteVideo &&
        !this.isExaminationStatus
      )
    },
    showRemoteAvatar() {
      return (
        !this.remoteStuInfo.onlineStatus ||
        !this.remoteStuInfo.displayVideo ||
        this.remoteStuInfo.mutedVideoStatus ||
        this.hideRemoteVideo ||
        this.isExaminationStatus
      )
    },
    showCameraStatusIcon() {
      return (
        this.remoteStuInfo.onlineStatus &&
        !this.remoteStuInfo.mutedVideoStatus &&
        !this.hideVideoIcon
      )
    },
    smallLevelText() {
      return `level-${this.remoteStuInfo.level}`
    }
  },
  methods: {
    handleRemoteVideoStatus() {
      this.$emit(
        'handleRemoteVideoStatus',
        this.remoteStuInfo.stuId,
        !this.remoteStuInfo.displayVideo
      )
    }
  }
}
</script>

<style lang="scss" scoped>
@import './style/videoItem.scss';
</style>
