<template>
  <div class="video-item video-local">
    <Achievement v-if="this.options.classType == 0" :options="options" type="simple" />
    <template v-if="localStuInfo.cameraStatus">
      <div class="group-wrapper">
        <div v-show="localStuInfo.displayVideo" class="video-wrapper" id="video-group-local" />
        <div v-if="!localStuInfo.displayVideo" class="avatar-wrapper">
          <img :src="localStuInfo.avatar" />
        </div>
      </div>
      <div
        class="microphone-status"
        v-if="showMicrophoneIcon"
        :class="[
          {
            'icon-microphone': localStuInfo.microphoneStatus,
            'icon-microphone-disabled': !localStuInfo.microphoneStatus
          }
        ]"
        @click="handleLocalMicrophoneStatus"
      />
      <div
        class="camera-status"
        v-if="!hideVideoIcon"
        :class="[
          { 'icon-camera': localStuInfo.displayVideo, 'icon-no-camera': !localStuInfo.displayVideo }
        ]"
        @click="handleLocalVideoStatus"
      />
    </template>
    <template v-else>
      <div class="group-wrapper">
        <div class="avatar-wrapper">
          <img :src="localStuInfo.avatar" />
        </div>
      </div>
      <div
        class="microphone-status"
        v-if="showMicrophoneIcon"
        :class="[
          {
            'icon-microphone': localStuInfo.microphoneStatus,
            'icon-microphone-disabled': !localStuInfo.microphoneStatus
          }
        ]"
        @click="handleLocalMicrophoneStatus"
      />
      <div class="camera-status icon-no-camera" @click="showNoAccessNotice" />
    </template>
    <div v-if="localStuInfo.level" class="medal-wrapper">
      <span class="icon" :class="localStuInfo.level ? `level-${localStuInfo.level}` : ''"></span>
      <span class="text" :class="smallLevelText">Lv{{ localStuInfo.level }}</span>
    </div>
    <div class="student-name">{{ localStuInfo.stuName }}</div>
    <MediaSecurityAccess ref="MediaSecurityAccessCamera" :visible="false" type="camera" />
    <MediaSecurityAccess ref="MediaSecurityAccessMicrophone" :visible="false" type="microphone" />
  </div>
</template>

<script>
import Achievement from '../achievement'
import MediaSecurityAccess from 'components/Common/MediaSecurityAccess'
import { getMicrophoneStatus } from 'utils/mediaAccess'
import { sensorEvent } from 'utils/sensorEvent'
import _debounce from 'lodash/debounce'

export default {
  components: {
    Achievement,
    MediaSecurityAccess
  },
  props: {
    options: {
      type: Object,
      default: null
    },
    localStuInfo: {
      type: Object,
      default: null
    },
    hideVideoIcon: {
      type: Boolean,
      default: false
    },
    classType: {
      type: Number,
      default: -1
    }
  },
  computed: {
    // 是否显示麦克风图标
    showMicrophoneIcon() {
      // 与视频显示逻辑一致
      // 真小班显示麦克风图标
      return !this.hideVideoIcon && this.classType == 2
    },
    smallLevelText() {
      return `level-${this.localStuInfo.level}`
    }
  },
  data() {
    return {}
  },
  methods: {
    /**
     * 开关本地视频推流状态
     */
    handleLocalVideoStatus: _debounce(function() {
      this.$emit('handleLocalVideoStatus')
      sensorEvent('hw_classroom_my_camera', this.options, {
        switch_type: this.localStuInfo.displayVideo ? 1 : 0
      })
    }, 300),

    /**
     * 开关本地麦克风状态
     */
    async handleLocalMicrophoneStatus() {
      const microphoneStatus = await getMicrophoneStatus()
      console.log('microphoneStatus', microphoneStatus)
      if (!microphoneStatus) {
        this.$refs['MediaSecurityAccessMicrophone'].checkAccess()
        return
      }
      this.$emit('handleLocalMicrophoneStatus')
    },

    /**
     * 无摄像头权限提醒
     */
    showNoAccessNotice() {
      this.$refs['MediaSecurityAccessCamera'].checkAccess()
    }
  }
}
</script>

<style lang="scss" scoped>
@import './style/videoItem.scss';
</style>
