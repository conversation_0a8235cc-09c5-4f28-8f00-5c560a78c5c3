<template>
  <div class="switch">
    <div @click="prevGroup" class="arrow-left left"></div>
    <div class="switch-content">
      <div @click="tabSwitch('video')" class="switch-btn btn-video">
        {{ $t('classroom.largeClass.videoGroup.tabNames')[0] }}
      </div>
      <div @click="tabSwitch('chat')" class="switch-btn btn-chats">
        <span>
          {{ $t('classroom.largeClass.videoGroup.tabNames')[1] }}
          <label v-if="switchKey === 'video' && hasPrivateNewMsg"></label
        ></span>
      </div>
      <div class="switch-bg" :class="switchKey == 'video' ? 'left-video' : 'left-chats'"></div>
    </div>
    <div @click="nextGroup" class="arrow-right right"></div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      switchKey: 'video',
      hasPrivateNewMsg: false
    }
  },
  props: {
    videoGroupShowStatus: {
      type: Boolean,
      default: true
    }
  },
  mounted() {
    // 监听私聊消息
    this.$bus.$on('chats.privateMessagePush', () => {
      if (this.switchKey === 'chat') {
        this.hasPrivateNewMsg = false
      } else {
        this.hasPrivateNewMsg = true
      }
    })
  },
  methods: {
    tabSwitch(key) {
      if (key === 'chat') {
        this.hasPrivateNewMsg = false
      }
      this.switchKey = key
      this.$emit('switchVideoChats', key)
      this.$bus.$emit('switchVideoAndChat', key)
    },
    prevGroup() {
      this.$emit('handlePrevPage')
    },
    nextGroup() {
      this.$emit('handleNextPage')
    }
  },
  watch: {
    videoGroupShowStatus: {
      handler(newValue) {
        if (!newValue) {
          this.tabSwitch('chat')
        } else {
          this.tabSwitch('video')
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import './style/switch.scss';
</style>
