.video-item {
  position: relative;
  width: calc(50% - 1px);
  height: calc(122 / 739 * 100vh);
  background: #2c2c2c;
  margin-bottom: 2px;
  overflow: hidden;
  &::before {
    content: '';
    display: block;
    position: absolute;
    z-index: 1;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 26px;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.7) 100%);
  }
  .audio-waves-wrapper {
    position: absolute;
    z-index: 10;
    right: 10px;
    top: 10px;
  }
  .medal-wrapper {
    .icon {
      position: absolute;
      width: 24px;
      height: 24px;
      z-index: 10;
      right: 35px;
      top: 5px;
      display: block;
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: center;
      &.level-1 {
        background-image: url('~assets/images/live/badges/small/level-1.png');
      }
      &.level-2 {
        background-image: url('~assets/images/live/badges/small/level-2.png');
      }
      &.level-3 {
        background-image: url('~assets/images/live/badges/small/level-3.png');
      }
      &.level-4 {
        background-image: url('~assets/images/live/badges/small/level-4.png');
      }
      &.level-5 {
        background-image: url('~assets/images/live/badges/small/level-5.png');
      }
      &.level-6 {
        background-image: url('~assets/images/live/badges/small/level-6.png');
      }
      &.level-7 {
        background-image: url('~assets/images/live/badges/small/level-7.png');
      }
    }

    .text {
      position: absolute;
      right: 7px;
      width: 35px;
      height: 17px;
      z-index: 2;
      top: 8px;
      border-radius: 0px 3px 3px 0px;
      color: #fff;
      line-height: 17px;
      padding-left: 7px;
      &.level-1 {
        background: rgba(74, 210, 255, 1);
      }
      &.level-2 {
        background: rgba(25, 206, 156, 1);
      }
      &.level-3 {
        background: rgba(81, 185, 255, 1);
      }
      &.level-4 {
        background: rgba(99, 206, 68, 1);
      }
      &.level-5 {
        background: rgba(98, 54, 255, 1);
      }
      &.level-6 {
        background: rgba(255, 127, 172, 1);
      }
      &.level-7 {
        background: #ff455e;
      }
    }
  }
  .group-wrapper {
    width: 100%;
    height: 100%;
  }
  .item-wrapper,
  .video-wrapper {
    width: 100%;
    height: 100%;
  }
  .avatar-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    img {
      width: 50px;
      height: 50px;
      border: 2px solid rgba(255, 255, 255, 0.2);
      border-radius: 25px;
    }
  }
  .notice-text {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #a2aab8;
    font-size: 12px;
  }
  .student-name {
    position: absolute;
    z-index: 2;
    left: 5px;
    bottom: 2px;
    width: 60%;
    height: 22px;
    line-height: 22px;
    font-size: 12px;
    color: #fff;
    overflow: hidden;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  .camera-status {
    position: absolute;
    z-index: 2;
    right: 5px;
    bottom: 2px;
    width: 22px;
    height: 22px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 4px;
    background-size: 90%;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
    &.icon-camera {
      background-image: url('./assets/icon-camera.png');
    }
    &.icon-no-camera {
      background-image: url('./assets/icon-no-camera.png');
    }
    &.icon-invisible {
      background-image: url('./assets/icon-invisible.png');
    }
    &.icon-visible {
      background-image: url('./assets/icon-visible.png');
    }
  }
  .microphone-status {
    position: absolute;
    z-index: 2;
    right: 38px;
    bottom: 2px;
    width: 22px;
    height: 22px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 4px;
    background-size: 90%;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
    &.icon-microphone {
      background-image: url('./assets/icon-microphone.png');
    }
    &.icon-microphone-disabled {
      background-image: url('./assets/icon-microphone-disabled.png');
    }
  }
}
.class-type-small {
  .video-item {
    height: calc(115 / 739 * 100vh);
  }
}
