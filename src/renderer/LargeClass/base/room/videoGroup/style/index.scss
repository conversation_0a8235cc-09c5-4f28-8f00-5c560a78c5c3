.video-group-wrapper {
  position: absolute;
  z-index: 10;
  right: 0px;
  width: 27.5%;
  .video-group-contenter {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    overflow: hidden;
  }
  // 大班
  &.class-type-large {
    height: calc(224 / 739 * 100vh);
    top: calc(246 / 739 * 100vh + 46px);
  }
  // 小班
  &.class-type-small {
    top: calc(282 / 739 * 100vh + 27px);
    background: #1a1a1a;
    .video-group-contenter {
      max-height: calc(117 / 739 * 100vh * 3);
    }
  }
}
.close-video-tag {
  width: 120px;
  height: 120px;
  background-size: 100%;
  position: relative;
  background-repeat: no-repeat;
  background-position: center;
  background-image: url('./assets/close-video.png');
  margin: 15vh auto;
  span {
    width: 150px;
    position: absolute;
    bottom: -25px;
  }
}