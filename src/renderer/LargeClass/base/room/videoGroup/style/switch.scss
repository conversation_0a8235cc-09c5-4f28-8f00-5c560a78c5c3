.switch {
  margin: calc(10 / 739 * 100vh) 0;
  display: flex;
  .switch-content {
    margin: 0 auto;
    width: 65%;
    height: calc(40 / 739 * 100vh);
    background: rgba(0, 0, 0, 0.85);
    border-radius: 24px;
    padding: calc(4 / 739 * 100vh);
    display: flex;
    position: relative;
    .switch-btn {
      width: 50%;
      height: calc(32 / 739 * 100vh);
      cursor: pointer;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      z-index: 1;
    }
    .btn-video {
      left: 4px;
    }
    .btn-chats {
      right: 4px;
      span {
        position: relative;
        label {
          width: 5px;
          height: 5px;
          background: #ff503f;
          border-radius: 50%;
          position: absolute;
          top: 3px;
          right: -15px;
        }
      }
    }
    .switch-bg {
      background: #1a1a1a;
      position: absolute;
      width: 50%;
      height: calc(32 / 739 * 100vh);
      border-radius: 20px;
      left: 4px;
      z-index: 0;
      transition: all 0.3s;
      &.left-video {
        left: 4px !important;
      }
      &.left-chats {
        left: calc(50% - 4px);
      }
    }
  }
  .arrow-left,
  .arrow-right {
    width: calc(40 / 739 * 100vh);
    height: calc(40 / 739 * 100vh);
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: cover;
    cursor: pointer;
    &.left {
      background-image: url('./assets/switch/left-arrow.png');
      margin-left: 10px;
    }
    &.right {
      background-image: url('./assets/switch/right-arrow.png');
      margin-right: 10px;
    }
  }
  .pagination {
    color: #a2aab8;
    font-size: 16px;
    margin: 0 35px;
  }
}
