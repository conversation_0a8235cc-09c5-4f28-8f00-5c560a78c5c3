<template>
  <div>
    <LargeClass
      v-if="classType === 0"
      :options="options"
      :stuInfo="stuInfo"
      :rtcConfig="rtcConfig"
    />
    <SmallClass
      v-if="classType === 1 || classType === 2"
      :options="options"
      :stuInfo="stuInfo"
      :rtcConfig="rtcConfig"
      :classType="classType"
    />
  </div>
</template>

<script>
import LargeClass from './largeClass'
import SmallClass from './smallClass'
import { chatMsgPriority } from '@/LargeClass/base/interaction-handler/interaction-conf'
import { notFocusedPush } from 'api/classroom/index'
import { ipc<PERSON>enderer } from 'electron'
import logger from 'utils/logger'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'

export default {
  components: {
    LargeClass,
    SmallClass
  },
  props: {
    options: {
      type: Object,
      default: null
    },
    stuInfo: {
      type: Object,
      default: null
    },
    rtcConfig: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      blurTimer: null,
      isWindowBlur: null,
      startTime: '',
      endTime: ''
    }
  },
  computed: {
    // 班级模式
    classType() {
      // 0: 大班 1: 伪小班 2: 真小班
      return this.options.classType
    }
  },
  mounted() {
    this.windowBlur()
  },
  methods: {
    /**
     * 大班课失焦/聚焦
     */
    windowBlur() {
      const { commonOption, configs, planInfo } = this.options
      if (this.options.isParent || this.options.isAudition) {
        return
      }
      this.focusHandler({
        studentId: this.stuInfo.id,
        tutorIrcId: configs.tutorIrcId,
        planId: planInfo.id
      })
    },
    /**
     * 上报失焦/聚焦
     */
    focusHandler({ studentId, tutorIrcId, planId }) {
      ipcRenderer.on('window_blur', (event, arg) => {
        // @log-ignore
        clearTimeout(this.blurTimer)
        if (this.isWindowBlur === arg) {
          return
        }
        const time = !arg ? 0 : 5000
        const content = JSON.stringify({
          type: '180', // 辅导端底层解析要求
          isFunction: true,
          msg: 'isBlur',
          parameter: {
            isBlur: arg
          }
        })
        this.blurTimer = setTimeout(() => {
          window.ChatClient.PeerChatManager.sendPeerMessage(
            [{ nickname: tutorIrcId }],
            content,
            chatMsgPriority.notice
          )
          if (arg) {
            this.startTime = new Date().getTime()
          } else {
            this.endTime = new Date().getTime()
            this.startTime && this.reportData({ studentId, planId })
          }
          this.isWindowBlur = arg
          console.log('[windowBlur] content:', content, 'nickname', tutorIrcId)
          logger.send({
            tag: '开小差',
            content,
            tutorIrcId
          })
          classLiveSensor.osta_app_not_focused(arg)
        }, time)
      })
    },
    /**
     * 失焦状态上报
     */
    async reportData({ studentId, planId }) {
      const { startTime, endTime } = this
      const duration = parseInt((endTime - startTime) / 1000)
      console.log('[学生端失焦] 开小差状态上报', duration)
      logger.send({
        tag: '开小差',
        content: { msg: `学生端失焦${duration}秒` }
      })
      await notFocusedPush({ planId, studentId, duration, startTime, endTime }).catch(err => {
        logger.send({
          tag: '开小差',
          content: { msg: `状态上报接口报错${JSON.stringify(err)}` },
          level: 'error'
        })
      })
    }
  }
}
</script>
