<template>
  <div class="small-class">
    <Achievement :options="options" type="normal" />
    <div class="video-group-wrapper class-type-small">
      <VideoChatsSwitch
        :videoGroupShowStatus="videoGroupShowStatus"
        @switchVideoChats="handleChangeVideoChat"
        @handlePrevPage="handlePrevPage"
        @handleNextPage="handleNextPage"
      />
      <div v-show="videoGroupShowStatus && showVideoChatType === 'video'">
        <div class="video-group-contenter">
          <LocalVideo
            v-show="localVideoShowStatus"
            :options="options"
            :localStuInfo="localStuInfo"
            :hideVideoIcon="hideLocalVideoIcon"
            :classType="classType"
            @handleLocalVideoStatus="handleLocalVideoStatus"
            @handleLocalMicrophoneStatus="handleLocalMicrophoneStatus"
          />
          <template v-for="item in remoteStuInfo">
            <RemoteVideo
              :key="item.stuId"
              :remoteStuInfo="item"
              :showAudioWaves="showAudioWaves"
              :hideRemoteVideo="hideRemoteVideo"
              :isExaminationStatus="isExaminationStatus"
              @handleRemoteVideoStatus="handleRemoteVideoStatus"
            />
          </template>
        </div>
      </div>
      <div v-if="!videoGroupShowStatus && showVideoChatType === 'video'" class="close-video-tag">
        <span>{{ $t('classroom.largeClass.videoGroup.closedNotice') }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import VideoChatsSwitch from './videoChatsSwitch'
import Achievement from '../achievement'
import LocalVideo from './localVideo'
import RemoteVideo from './remoteVideo'
import RtcClass from './rtcClass'
import VideoGroup from './videoGroupClass'
import { getCameraStatus, getMicrophoneStatus } from 'utils/mediaAccess'
import { RtcSensor } from 'utils/sensorEvent'

export default {
  components: {
    VideoChatsSwitch,
    LocalVideo,
    RemoteVideo,
    Achievement
  },
  props: {
    options: {
      type: Object,
      default: null
    },
    stuInfo: {
      type: Object,
      default: null
    },
    rtcConfig: {
      type: Object,
      default: null
    },
    classType: {
      type: Number,
      default: -1
    }
  },
  data() {
    return {
      teamSwitchStatus: null, // 视频回显状态
      showVideoChatType: 'video', // 默认展示video
      localStuInfo: {
        // 本地学生信息
        stuId: this.stuInfo.id, // 学生ID
        stuName: this.stuInfo.nickName, // 学生昵称
        avatar: this.stuInfo.avatar, // 学生头像
        displayVideo: false, // 摄像头是否显示
        cameraStatus: true, // 摄像头授权状态
        microphoneStatus: false, // 麦克风状态
        level: this.stuInfo.level // 金币激励勋章等级
      },
      remoteStuInfo: [], // 所有远端学生信息
      speakStudentList: [], // 集体发言学生列表
      groupSpeakStatus: false, // 集体发言状态
      videoLinkStatus: false, // 视频连麦状态
      otherCameraStatus: false, // 其它摄像头使用状态
      localVideoDisplayCache: null, // 本地摄像头是否显示缓存状态
      remoteAudioStatus: false, // 语音管理状态 true: 开启 false: 关闭

      pageNum: 1, // 当前页码
      pageSize: 6, // 每页学生数量(第一页少一个学生, 本地视频补位)
      classStudentMap: new Map(), // 班级所有学生Map对象, 学生基础信息缓存
      remoteLevelCache: {}, // 远端学生徽章等级缓存
      isExaminationStatus: false, // 是否是课中考试状态
      rtcSensor: null // 伪小班rtc神策方法
    }
  },
  computed: {
    // 本地视频推流状态
    localVideoPublishStatus() {
      return this.localVideoDisplayCache !== false && this.teamSwitchStatus
    },

    // 视频分组显示状态
    videoGroupShowStatus() {
      return this.teamSwitchStatus === true
    },

    // 本地视频视图显示状态
    localVideoShowStatus() {
      if (this.pageNum === 1) {
        return true
      }
      return false
    },

    // 显示声纹图标
    showAudioWaves() {
      // 集体发言开启时，显示声纹图标
      return this.groupSpeakStatus
    },

    // 隐藏本地摄像头开关图标
    hideLocalVideoIcon() {
      // 视频连麦、其它摄像头占用时，隐藏本地摄像头开关图标
      if (this.videoLinkStatus || this.otherCameraStatus) {
        return true
      } else {
        return false
      }
    },

    // 强制隐藏视频
    hideRemoteVideo() {
      // 其它摄像头状态使用时强制隐藏远端视频
      // 例如: 拍照上墙互动
      return this.otherCameraStatus
    },

    // 分页总数
    pageCount() {
      const totalCount = this.remoteStuInfo.length
      const onePageCount = this.pageSize - 1
      if (totalCount <= onePageCount) return 1
      const otherCount = totalCount - onePageCount
      return Math.floor(otherCount / this.pageSize) + (otherCount % this.pageSize > 0 ? 1 : 0) + 1
    }
  },
  mounted() {
    const opts = {
      options: this.options,
      stuInfo: this.stuInfo,
      rtcConfig: this.rtcConfig,
      classType: this.classType
    }
    this.rtcClass = new RtcClass(opts)
    this.videoGroup = new VideoGroup(opts)
    this.init()
  },
  methods: {
    /**
     * 初始化
     */
    async init() {
      this.teamSwitchStatus = await this.videoGroup.getTeamSwitchStatus()
      await this.initClassStudent()
      await this.initClassRtcChannel()
      await this.initMediaAccess()
      this.listenBusEvent()
      this.videoGroup.sendRtcStatus()
    },

    /**
     * 获取媒体权限
     */
    async initMediaAccess() {
      this.localStuInfo.cameraStatus = await getCameraStatus()
      this.localStuInfo.microphoneStatus = await getMicrophoneStatus()
      this.$bus.$emit(
        'updateLocalDisplayVideoStatus',
        this.localStuInfo.cameraStatus && this.teamSwitchStatus
      )
      this.$bus.$emit('updateMicrophoneStatus', this.localStuInfo.microphoneStatus)
    },

    /**
     * 初始化学生列表
     */
    async initClassStudent() {
      const studentList = await this.videoGroup.queryClassStudentList()
      studentList.forEach(item => {
        this.classStudentMap.set(item.userId, item)
      })
      console.log('videoGroup-initClassStudent', this.classStudentMap)
    },
    /**
     * 新增临时学生、调课转班远端学生课中加入逻辑
     */
    async updateClassStudent(uid) {
      const studentList = await this.videoGroup.queryClassStudentList()
      const student = studentList.find(s => uid == s.userId)
      if (student) {
        // 存入缓存 并且更新远端学生列表
        this.classStudentMap.set(student.userId, student)
      }

      this.handleRemoteJoinChannel(uid)
    },
    /**
     * 初始化班级RTC频道
     */
    async initClassRtcChannel() {
      // 创建班级RTC频道
      const classRtcChannel = await this.rtcClass.init()

      if (this.classType === 2) {
        // 广播真小班RtcReady消息
        this.$bus.$emit('player.smallClassRtcReady', classRtcChannel)
      }

      // 本地未开启视频小组, 则停止本地视频采集
      if (!this.teamSwitchStatus) {
        this.rtcClass.enableLocalVideo(false)
      }
      // 监听频道网络连接状态
      classRtcChannel.on('connectionStateChanged', state => {
        console.log('[hw_rtc_join_room]伪小班网络状态触发方法触发,code:', state)
        if (state == 5) {
          this.rtcSensor.rtcSensorPush({ result: 'fail', errorType: '连接失败' })
        } else if (state == 4) {
          this.rtcSensor.isFirstJoinChannel = false
          this.rtcSensor.rtcSensorPush({ result: 'start' })
        }
      })
      // 监听本地加入频道
      classRtcChannel.on('localJoinChannel', () => {
        console.log('[hw_rtc_join_room]伪小班本地加入频道成功')
        this.videoGroup.sendLogger('伪小班本地加入频道成功')
        // 设置本地视频视图
        if (this.teamSwitchStatus) {
          this.setupLocalVideo()
        }
        this.rtcSensor.rtcSensorPush({ result: 'success' })
      })
      // 监听本地重连加入班级频道
      classRtcChannel.on('rejoinChannelSuccess', () => {
        console.log('[hw_rtc_join_room]伪小班本地重连加入班级频道成功')
        this.videoGroup.sendLogger('伪小班本地重连加入班级频道成功')
        this.rtcSensor.rtcSensorPush({ result: 'success' })
      })
      // 监听远端离开频道
      classRtcChannel.on('remoteLeaveChannel', uid => {
        console.log('videoGroup-remoteLeaveChannel', uid)
        this.handleRemoteLeaveChannel(uid)
        this.videoGroup.sendLogger(`远端学生离开频道, uid: ${uid}`)
      })

      // 监听远端加入频道
      classRtcChannel.on('remoteJoinChannel', async uid => {
        console.log('videoGroup-remoteJoinChannel', uid)
        this.videoGroup.sendLogger(`远端学生加入频道, uid: ${uid}`)
        const stuInfo = this.classStudentMap.get(String(uid))
        // 缓存列表中存在此学生
        if (stuInfo) {
          this.handleRemoteJoinChannel(uid)
        } else {
          // 临时进入课堂的学生
          this.updateClassStudent(uid)
        }
      })

      // 监听远端视频流状态变化
      classRtcChannel.on('remoteVideoStateChanged', (uid, state, reason) => {
        console.log('videoGroup-remoteVideoStateChanged', uid, state, reason)
        // 接收到远端视频流首帧
        if (state == 1) {
          // 更新远端学生状态
          this.setRemoteStuVal(uid, 'cameraStatus', true)
          this.$nextTick(function() {
            this.rtcClass.setupRemoteVideo(uid, `remote-${uid}`)
          })
        }
        // 远端禁用视频
        if (reason === 5) {
          this.setRemoteStuVal(uid, 'mutedVideoStatus', true)
        }
        // 远端启用视频
        if (reason === 6) {
          this.setRemoteStuVal(uid, 'mutedVideoStatus', false)
          this.$nextTick(function() {
            this.rtcClass.setupRemoteVideo(uid, `remote-${uid}`)
          })
        }
        console.log('videoGroup-remoteVideoStateChanged', this.remoteStuInfo)
      })
      this.rtcSensor = new RtcSensor() // 伪小班神策埋点方法初始化
      console.log('[hw_rtc_join_room]伪小班加入频道触发start')
      this.rtcSensor.rtcSensorPush({ result: 'start' })
      let joinRes = classRtcChannel.joinChannel()
      if (joinRes == 0) {
        this.videoGroup.sendLogger(`伪小班调用加入频道成功`)
      } else {
        console.log('[hw_rtc_join_room]伪小班调用加入频道方法触发fail')
        this.videoGroup.sendLogger(`伪小班调用加入频道失败,code:${joinRes}`, {}, 'error')
        this.rtcSensor.rtcSensorPush({ result: 'fail', errorType: '调用加入房间接口失败' })
      }
    },

    /**
     * 监听通信事件
     */
    listenBusEvent() {
      // 设置默认摄像头
      this.$bus.$on('setDefaultVideoDevice', deviceId => {
        this.rtcClass.setVideoDevice(deviceId)
      })

      // 设置默认麦克风
      this.$bus.$on('setDefaultAudioRecordingDevice', deviceId => {
        this.rtcClass.setAudioRecordingDevice(deviceId)
      })

      // 设置默认扬声器
      this.$bus.$on('setDefaultAudioPlaybackDevice', deviceId => {
        this.rtcClass.setAudioPlaybackDevice(deviceId)
      })

      // 监听视频回显状态变化
      this.$bus.$on('updateTeamSwitchStatus', status => {
        console.log('videoGroup-updateTeamSwitchStatus', status)
        this.videoGroup.sendLogger(`收到视频回显状态变化, status: ${status}`)
        this.teamSwitchStatus = status
        if (status) {
          if (this.localVideoDisplayCache !== false) {
            this.rtcClass.enableLocalVideo(true)
            this.setupLocalVideo()
          }
        } else {
          this.rtcClass.enableLocalVideo(false)
          this.hideLocalVideo()
        }
        this.$bus.$emit('updateLocalDisplayVideoStatus', this.localVideoPublishStatus)
      })

      // 监听摄像头使用状态事件
      this.$bus.$on('cameraStatus', status => {
        console.log('videoGroup-cameraStatus', status)
        this.$bus.$emit('teamSwitchDisabled', status) // 通知视频回显按钮禁用状态变化
        if (status) {
          this.rtcClass.muteLocalVideo(true)
        } else {
          // 如果本地没有关闭摄像头时进行推流
          if (this.localVideoPublishStatus) {
            this.rtcClass.muteLocalVideo(false)
          }
        }
      })
      // 监听视频连麦互动状态事件
      this.$bus.$on('videoLinkStatus', status => {
        console.log('videoGroup-videoLinkStatus', status)
        this.$bus.$emit('teamSwitchDisabled', status) // 通知视频回显按钮禁用状态变化
        this.videoLinkStatus = status
      })

      // 监听本地视频连麦互动状态事件
      this.$bus.$on('localVideoLinkStatus', status => {
        console.log('videoGroup-localVideoLinkStatus', status)
        this.videoGroup.sendLogger(`收到视频连麦互动状态变化, status: ${status}`)
        // 真小班逻辑
        if (this.classType == 2) {
          status ? this.hideLocalVideo() : this.setupLocalVideo()
        } else {
          // 大班、伪小班逻辑
          if (status) {
            this.rtcClass.unpublish()
            this.hideLocalVideo()
          } else {
            this.rtcClass.publish({
              // 视频推流状态
              // 本地视频关闭或视频回显区关闭, 则不推流
              publishVideo: this.localVideoPublishStatus
            })
            this.setupLocalVideo()
          }
        }
        this.otherCameraStatus = status
      })

      // 监听多人上台状态变化
      // pub: 互动开启/关闭状态, status 2: 上台 3: 下台, stuId: 学生id
      this.$bus.$on('multVideoLinkStatus', ({ pub, status, stuId }) => {
        // 互动结束逻辑
        if (!pub) {
          this.videoLinkStatus = false
          this.$bus.$emit('teamSwitchDisabled', false) // 通知视频回显按钮禁用状态变化
          // 如果开启了多人语音, 则重新打开多人语音
          if (this.remoteAudioStatus) {
            this.collectiveSpeechOpen()
          }
          return
        }
        // 互动开启逻辑
        if (pub && status == 1) {
          this.videoLinkStatus = true
          this.$bus.$emit('teamSwitchDisabled', true) // 通知视频回显按钮禁用状态变化
          // 如果开启了多人语音, 则停止语音
          if (this.remoteAudioStatus) {
            this.collectiveSpeechClose()
          }
          return
        }
        // 互动中逻辑
        if (!stuId) {
          return
        }
        const isMe = stuId == this.localStuInfo.stuId ? true : false
        // 上台
        if (status == 2) {
          if (isMe) {
            this.hideLocalVideo()
          } else {
            this.setRemoteStuVal(stuId, 'displayVideo', false)
          }
        }
        // 下台
        if (status == 3) {
          if (isMe) {
            this.setupLocalVideo()
          } else {
            this.setRemoteStuVal(stuId, 'displayVideo', true)
            this.$nextTick(function() {
              this.rtcClass.setupRemoteVideo(Number(stuId), `remote-${stuId}`)
            })
          }
        }
      })

      // 监听集体发言互动状态事件
      this.$bus.$on('groupSpeakStatus', status => {
        console.log('videoGroup-groupSpeakStatus', status)
        this.videoGroup.sendLogger(`收到集体发言互动状态变化, status: ${status}`)
        // 设置本地集体发言状态
        this.groupSpeakStatus = status
        // 集体发言状态逻辑
        if (status) {
          this.collectiveSpeechOpen()
        } else {
          this.collectiveSpeechClose()
        }
      })

      // 监听语音管理状态事件
      this.$bus.$on('remoteAudioStatus', status => {
        this.videoGroup.sendLogger(`收到语音管理状态变化, status: ${status}`)
        this.remoteAudioStatus = status
        if (status) {
          this.collectiveSpeechOpen()
        } else {
          this.collectiveSpeechClose()
        }
      })

      // 监听退出直播事件
      this.$bus.$on('liveQuit', () => {
        this.rtcClass.unpublish()
        this.rtcClass.leaveChannel()
      })

      // 监听获取本地视频开关状态事件
      this.$bus.$on('getLocalDisplayVideoStatus', callback => {
        callback && callback(this.localVideoPublishStatus)
      })

      // 监听获取本地麦克风开关状态事件
      this.$bus.$on('getLocalMicrophoneStatus', callback => {
        callback && callback(this.localStuInfo.microphoneStatus)
      })

      // 监听自己的金币激励勋章变化
      this.$bus.$on('chats.correctSelfMedalData', data => {
        this.localStuInfo.level = data.level
      })

      // 监听小组其他学生金币激励勋章变化
      this.$bus.$on('chats.correctMedalData', data => {
        this.setRemoteStuVal(data.userId, 'level', data.level)
        this.remoteLevelCache[data.userId] = data.level
      })

      // 监听课中考试状态：考试时其他学员的视频调整为头像状态，结束后恢复
      this.$bus.$on('setExaminationStatus', data => {
        this.isExaminationStatus = data
      })
    },

    /**
     * 设置本地视频视图
     */
    setupLocalVideo() {
      if (!this.localStuInfo.cameraStatus) {
        return
      }
      this.rtcClass.setupLocalVideo('video-group-local')
      if (this.localVideoDisplayCache === false) {
        return
      }
      this.localStuInfo.displayVideo = true
    },

    /**
     * 切换video和chats
     */
    handleChangeVideoChat(type) {
      this.showVideoChatType = type
    },

    /**
     * 隐藏本地视频视图
     */
    hideLocalVideo() {
      this.localStuInfo.displayVideo = false
    },

    /**
     * 处理远端学生加入频道
     */
    handleRemoteJoinChannel(uid) {
      const stuInfo = this.classStudentMap.get(String(uid))
      if (!stuInfo) {
        return
      }
      if (this.videoGroup.hasUserIdByStudentList(this.remoteStuInfo, uid)) {
        return
      }

      this.remoteStuInfo.push({
        showStatus: false,
        stuId: stuInfo.userId,
        stuName: stuInfo.nickName,
        avatar: stuInfo.avatar,
        level: this.remoteLevelCache[uid] || stuInfo.level, // 学生加入优先读缓存
        cameraStatus: false, // 摄像头打开状态, 根据视频首帧判断
        displayVideo: true, // 本地设置的远端视频显示状态 true: 显示视频 false: 隐藏视频
        mutedVideoStatus: false, // 远端视频禁止状态, 控制视频右下角眼睛图标显示隐藏 true: 已停止 false: 已开始
        onlineStatus: true // 远端学生加入状态, 默认未加入 true: 已加入 false: 未加入
      })

      const hasPage = this.hasPageByUid(uid)
      if (hasPage) {
        // 设置显示状态
        this.setRemoteStuVal(uid, 'showStatus', true)
        // TODO 小班暂时采用全拉流
        // 拉取远端视频流
        // this.rtcClass.muteRemoteVideo(uid, false)
      }
      console.log('videoGroup-createRemoteStudentInfo', this.remoteStuInfo)
    },

    /**
     * 处理远端学生离开频道
     */
    handleRemoteLeaveChannel(uid) {
      let stuIndex = -1
      this.remoteStuInfo.forEach((item, index) => {
        if (item.stuId == uid) {
          stuIndex = index
        }
      })
      if (stuIndex === -1) return
      // 移除离开的学生
      this.remoteStuInfo.splice(stuIndex, 1)

      // 获取当前页学生区间
      const indexRange = this.getIndexRangeByPageNum(this.pageNum)
      // 当前页学生区间数
      let pageRangeCount = 0
      // 找到当前页未显示的远端学生, 打开显示状态
      this.remoteStuInfo.forEach((item, index) => {
        if (index >= indexRange.start && index <= indexRange.end && !item.showStatus) {
          this.$set(this.remoteStuInfo[index], 'showStatus', true)
          // TODO 小班暂时采用全拉流
          // this.rtcClass.muteRemoteVideo(item.stuId, false)
          pageRangeCount++
        }
      })
      // 如果当前页区间没有远端学生, 则切换到初始页
      if (pageRangeCount === 0) {
        this.pageNum = this.getValidPageNum(this.pageNum)
        console.log('videoGroup-getValidPageNum', this.pageNum)
        this.changePageRender()
      }

      console.log('videoGroup-handleRemoteLeaveChannel', this.remoteStuInfo)
    },

    /**
     * 启用/禁止所有远端学生视频
     */
    // muteAllRemoteVideo(mute) {
    //   this.remoteStuInfo.forEach(item => {
    //     this.rtcClass.muteRemoteVideo(item.stuId, mute)
    //     this.setRemoteStuVal(item.stuId, 'displayVideo', !mute)
    //   })
    // },

    /**
     * 开关本地视频推流状态
     */
    handleLocalVideoStatus() {
      if (this.localStuInfo.displayVideo) {
        this.rtcClass.enableLocalVideo(false)
      } else {
        this.rtcClass.enableLocalVideo(true)
      }
      this.localStuInfo.displayVideo = !this.localStuInfo.displayVideo
      this.localVideoDisplayCache = this.localStuInfo.displayVideo
      this.videoGroup.sendRtcStatus({
        displayVideo: this.localStuInfo.displayVideo
      })
      this.$bus.$emit('updateLocalDisplayVideoStatus', this.localStuInfo.displayVideo)
      this.videoGroup.sendLogger(
        `学生操作本地视频开关状态, status: ${this.localStuInfo.displayVideo}`
      )
    },

    /**
     * 开关本地音频推流状态
     */
    handleLocalMicrophoneStatus() {
      this.localStuInfo.microphoneStatus = !this.localStuInfo.microphoneStatus
      this.rtcClass.muteLocalAudio(!this.localStuInfo.microphoneStatus)
      this.$bus.$emit('updateMicrophoneStatus', this.localStuInfo.microphoneStatus)
    },

    /**
     * 开关远端视频状态
     */
    handleRemoteVideoStatus(stuId, status) {
      // 更新远端视频显示状态
      this.setRemoteStuVal(stuId, 'displayVideo', status)
      this.videoGroup.sendLogger(`学生操作远端视频开关状态, uid: ${stuId} status: ${status}`)
    },

    /**
     * 设置远端学生数据状态
     */
    setRemoteStuVal(uid, key, val) {
      if (!uid) return
      this.remoteStuInfo.forEach((item, index) => {
        if (item.stuId == uid) {
          this.$set(this.remoteStuInfo[index], key, val)
        }
      })
      console.log('videoGroup-setRemoteStuVal', uid, key, val)
    },

    /**
     * 处理上一页事件
     */
    handlePrevPage() {
      if (this.pageNum === 1) {
        return
      }
      this.pageNum--
      this.changePageRender()
    },

    /**
     * 处理下一页事件
     */
    handleNextPage() {
      if (this.pageNum >= this.pageCount) {
        return
      }
      this.pageNum++
      this.changePageRender()
    },

    /**
     * 处理翻页渲染逻辑
     */
    changePageRender() {
      // 将所有学生隐藏并停止拉流
      this.remoteStuInfo.forEach((item, index) => {
        this.$set(this.remoteStuInfo[index], 'showStatus', false)
        // TODO 小班暂时采用全拉流
        // this.$set(this.remoteStuInfo[index], 'cameraStatus', false)
        // this.rtcClass.muteRemoteVideo(item.stuId, true)
      })
      // 找出当前页学生显示并拉流
      this.remoteStuInfo.forEach((item, index) => {
        const hasPage = this.hasPageByUid(item.stuId)
        if (hasPage) {
          this.$set(this.remoteStuInfo[index], 'showStatus', true)
          // TODO 小班暂时采用全拉流
          // if (item.displayVideo) {
          //   // 如果本地没有隐藏学生视频, 则直接拉流
          //   this.rtcClass.muteRemoteVideo(item.stuId, false)
          // }
        }
      })
    },

    /**
     * 根据页码返回位置区间
     */
    getIndexRangeByPageNum(pageNum) {
      if (pageNum === 1) {
        return {
          start: 0,
          end: this.pageSize - 2
        }
      }
      return {
        start: (pageNum - 1) * this.pageSize - 1,
        end: pageNum * this.pageSize - 2
      }
    },

    /**
     * 判断学生是否在当前页
     */
    hasPageByUid(uid) {
      const indexRange = this.getIndexRangeByPageNum(this.pageNum)
      let uidIndex = -1
      this.remoteStuInfo.forEach((item, index) => {
        if (item.stuId == uid) {
          uidIndex = index
        }
      })
      if (uidIndex == -1) {
        return false
      }
      if (uidIndex >= indexRange.start && uidIndex <= indexRange.end) {
        return true
      } else {
        return false
      }
    },

    /**
     * 获取有远端学生的页码
     */
    getValidPageNum(pageNum) {
      if (pageNum <= 1) {
        return 1
      }
      // 获取当前页码学生区间
      const indexRange = this.getIndexRangeByPageNum(pageNum)
      let pageRangeCount = 0
      // 找到页码区间远端学生总数
      this.remoteStuInfo.forEach((item, index) => {
        if (index >= indexRange.start && index <= indexRange.end) {
          pageRangeCount++
        }
      })
      // 如果当前页码有远端学生则返回页码
      if (pageRangeCount > 0) {
        return pageNum
      }
      // 继续查找有远端学生的页码
      return this.getValidPageNum(pageNum - 1)
    },

    /**
     * 集体发言打开
     */
    async collectiveSpeechOpen() {
      const speakStudentList = this.getSpeakStudentList()
      speakStudentList.forEach(item => {
        this.rtcClass.muteRemoteAudio(item, false)
      })
      this.videoGroup.sendLogger(
        `拉取远端学生音频流, 远端学生列表: ${JSON.stringify(speakStudentList)}`
      )
    },

    /**
     * 集体发言关闭
     */
    collectiveSpeechClose() {
      const speakStudentList = this.getSpeakStudentList()
      speakStudentList.forEach(item => {
        this.rtcClass.muteRemoteAudio(item, true)
      })
    },

    /**
     * 获取集体发言学生列表
     */
    getSpeakStudentList() {
      let speakStudentList = []
      for (let item of this.classStudentMap.keys()) {
        speakStudentList.push(item)
      }
      return speakStudentList
    }
  }
}
</script>

<style lang="scss" scoped>
@import './style/index.scss';
@import './style/videoItem.scss';
</style>
