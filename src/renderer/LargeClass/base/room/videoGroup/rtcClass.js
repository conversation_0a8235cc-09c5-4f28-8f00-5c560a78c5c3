import {
  getCameraAccessStatus,
  getCameraAccess,
  getMicrophoneAccessStatus,
  getMicrophoneAccess
} from 'utils/mediaAccess'
import { initRtcDevices } from 'utils/deviceSettings'
export default class {
  constructor(opts = {}) {
    // @log-ignore
    this.options = opts.options
    this.stuInfo = opts.stuInfo
    this.rtcConfig = opts.rtcConfig
    this.classType = opts.classType
    this.publishStatus = opts.publishStatus === undefined ? true : opts.publishStatus
    // RTC引擎实例
    this.rtcEngine = null
    // 班级RTC频道
    this.classRtcChannel = null
  }

  /**
   * 初始化班级RTC频道
   * @returns 频道实例
   */
  async init() {
    if (!this.publishStatus) return this.createClassRtcChannel()
    const cameraAccessStatus = await getCameraAccessStatus()
    const microphoneAccessStatus = await getMicrophoneAccessStatus()
    console.log('videoGroup-getCameraAccessStatus', cameraAccessStatus)
    console.log('videoGroup-microphoneAccessStatus', microphoneAccessStatus)

    if (cameraAccessStatus === 'not-determined') {
      const cameraAccess = await getCameraAccess()
      console.log('videoGroup-getCameraAccess', cameraAccess)

      // 解决初次声网获取麦克风授权后, electron api获取麦克风权限状态不更新问题
      // 使用主动授权提示api可更新麦克风状态
      if (microphoneAccessStatus === 'not-determined') {
        const microphoneAccess = await getMicrophoneAccess()
        console.log('videoGroup-getMicrophoneAccess', microphoneAccess)
      }

      return this.createClassRtcChannel()
    } else {
      return this.createClassRtcChannel()
    }
  }

  /**
   * 创建班级RTC频道
   * @returns 频道实例
   */
  async createClassRtcChannel() {
    // 真小班使用主讲token, 大班/伪小班使用classToken
    const token = this.classType == 2 ? this.rtcConfig.token : this.rtcConfig.classToken
    const classToken = String(token)
    this.rtcEngine = window.RTC_COMMON.rtcEngine
    await initRtcDevices(this.rtcEngine) // 设置麦克风和摄像头
    this.classRtcChannel = window.RTC_COMMON.classRtcChannel = this.rtcEngine.createChannel(
      classToken
    ) // 创建班级频道
    const clientRole = this.publishStatus ? 1 : 2 // 1: 主播 2: 观众
    this.classRtcChannel.setClientRole(clientRole) // 设置主播模式
    this.classRtcChannel.setDefaultMuteAllRemoteAudioStreams(true) // 禁止所有学生音频流
    if (this.publishStatus) {
      this.classRtcChannel.muteLocalAudioStream(false) // 启用本地音频
      this.classRtcChannel.muteLocalVideoStream(false) // 启用本地视频
    }
    // this.classRtcChannel.joinChannel() // 加入班级频道
    return this.classRtcChannel
  }

  /**
   * 设置本地视频视图
   * @param {String} id
   */
  setupLocalVideo(id) {
    this.rtcEngine.setupLocalVideo(document.getElementById(id))
  }

  /**
   * 设置远端视频视图
   * @param {*} uid uid
   * @param {*} id id
   */
  setupRemoteVideo(uid, id) {
    this.classRtcChannel.setupRemoteVideo(uid, document.getElementById(id))
  }

  /**
   * 启用/禁用本地视频
   * @param {Boolean} mute
   */
  muteLocalVideo(mute) {
    this.classRtcChannel.muteLocalVideoStream(mute)
  }

  /**
   * 启用/禁用本地音频
   * @param {Boolean} mute
   */
  muteLocalAudio(mute) {
    this.classRtcChannel.muteLocalAudioStream(mute)
  }

  /**
   * 启用/禁用远端视频
   * @param {*} uid
   * @param {*} mute
   */
  muteRemoteVideo(uid, mute) {
    this.classRtcChannel.muteRemoteVideoStream(Number(uid), mute)
  }

  /**
   * 启用/禁用远端音频
   * @param {*} uid
   * @param {*} mute
   */
  muteRemoteAudio(uid, mute) {
    this.classRtcChannel.muteRemoteAudioStream(Number(uid), mute)
  }

  /**
   * 将本地音视频流发布到本频道
   */
  publish({ publishVideo = true }) {
    this.classRtcChannel.setClientRole(1) // 设置为主播模式
    this.classRtcChannel.muteLocalAudioStream(false) // 启用本地音频
    if (publishVideo) {
      this.classRtcChannel.muteLocalVideoStream(false) // 启用本地视频
    } else {
      this.classRtcChannel.muteLocalVideoStream(true) // 禁用本地视频
    }
  }

  /**
   * 取消本地音视频流发布到本频道
   */
  unpublish() {
    this.classRtcChannel.setClientRole(0) // 设置为观众模式
    this.classRtcChannel.muteLocalAudioStream(true) // 禁用本地音频
    this.classRtcChannel.muteLocalVideoStream(true) // 禁用本地视频
  }

  /**
   * 退出频道
   */
  leaveChannel() {
    this.classRtcChannel.leaveChannel()
  }

  /**
   * 启用/禁用本地视频采集
   * @param {boolean} enable
   */
  enableLocalVideo(enable) {
    // 学生进入课堂Rtc未初始化时点击视频开关报错
    this.rtcEngine?.enableLocalVideo(enable)
    this.classRtcChannel.muteLocalVideoStream(!enable)
  }

  /**
   * 设置默认摄像头
   * @param {String} deviceId 设备ID
   */
  setVideoDevice(deviceId) {
    this.rtcEngine.setVideoDevice(deviceId)
  }

  /**
   * 设置默认麦克风
   * @param {String} deviceId 设备ID
   */
  setAudioRecordingDevice(deviceId) {
    this.rtcEngine.setAudioRecordingDevice(deviceId)
  }

  /**
   * 设置默认扬声器
   * @param {String} deviceId 设备ID
   */
  setAudioPlaybackDevice(deviceId) {
    this.rtcEngine.setAudioPlaybackDevice(deviceId)
  }
}
