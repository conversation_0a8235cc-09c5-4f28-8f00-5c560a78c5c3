import { getCameraStatus, getMicrophoneStatus } from 'utils/mediaAccess'
import {
  queryClassStudentList,
  queryGroupMember,
  setRtcStatus,
  queryWhoCanSpeak
} from 'api/classroom/index'
import logger from 'utils/logger'

export default class {
  constructor(opts = {}) {
    // @log-ignore
    this.options = opts.options
    this.stuInfo = opts.stuInfo
    this.rtcConfig = opts.rtcConfig

    this.requestTimer = null
    this.groupStudentList = [] // 学生分组列表
  }

  /**
   * 上报学生RTC状态
   */
  async sendRtcStatus(options = {}) {
    // 后端status接收参数状态
    // 1:推视频、2:推音频、3:推音视频、0:断流
    let status = -1
    const cameraStatus = await getCameraStatus()
    const microphoneStatus = await getMicrophoneStatus()
    if (cameraStatus) {
      status = 1
    }
    if (microphoneStatus) {
      status = 2
    }
    if (cameraStatus && microphoneStatus) {
      status = 3
    }
    if (status == -1) {
      return
    }
    let params = {
      planId: this.options.planId,
      classId: this.options.classId,
      status: status,
      micPermission: microphoneStatus ? 1 : 2,
      cameraPermission: cameraStatus ? 1 : 2,
      micIsOpen: microphoneStatus ? 1 : 2,
      cameraIsOpen: cameraStatus ? 1 : 2
    }

    if (options.displayVideo !== undefined) {
      params['cameraIsOpen'] = options.displayVideo ? 1 : 2
    }

    await setRtcStatus(params)
  }

  /**
   * 查询学生分组信息
   * @param {Object} opts
   */
  async queryGroupStudent(opts) {
    // @log-ignore
    // 学生分组达到3人，则阻止执行后续逻辑
    if (this.groupStudentList.length >= 3) return

    try {
      const res = await queryGroupMember({
        isParentAudition: this.options.isParent ? 1 : 0,
        planId: this.options.planId,
        classId: this.options.classId,
        cameraPerm: opts.cameraStatus
      })

      const data = res.data || {}
      const list = data.list || []

      list.forEach(item => {
        // 去除重复学生
        if (!this.hasUserIdByStudentList(this.groupStudentList, item.userId)) {
          this.groupStudentList.push(item)
          // 执行添加学生信息回调
          opts.addStudentCallback && opts.addStudentCallback(item)
        }
      })

      // 分组人员满足后, 停止轮询
      if (data.isFull) {
        return
      }

      // 轮询学生分组接口
      this.delayQueryGroupStudent(opts)
    } catch (error) {
      console.error('videoGroup-queryGroupStudent-error', error)
      this.delayQueryGroupStudent(opts)
    }
  }

  /**
   * 查询班级学生列表
   */
  async queryClassStudentList() {
    const res = await queryClassStudentList({
      planId: this.options.planId,
      classId: this.options.classId
    })
    if (!res || res.code != 0) {
      return []
    }
    const studentList = res.data || []
    return studentList
  }

  /**
   * 轮询分组接口
   */
  delayQueryGroupStudent(opts) {
    this.requestTimer && clearTimeout(this.requestTimer)
    this.requestTimer = setTimeout(() => {
      this.queryGroupStudent(opts)
    }, 10000)
  }

  /**
   * 查询集体发言学生列表
   * @returns 学生列表
   */
  async querySpeakStudentList() {
    const res = await queryWhoCanSpeak({
      classId: this.options.classId,
      planId: this.options.planId
    })
    if (!res || res.code != 0) {
      return []
    }
    const data = res.data || {}
    return data.list || []
  }

  /**
   * 判断远端学生列表中是否有指定学生
   * @param {*} list
   * @param {*} stuId
   * @returns flag
   */
  hasUserIdByStudentList(list, stuId) {
    let flag = false
    list.forEach(item => {
      if (item.stuId == stuId || item.userId == stuId) {
        flag = true
      }
    })
    return flag
  }

  /**
   * 获取视频回显状态
   */
  async getTeamSwitchStatus() {
    const status = await window.thinkApi.ipc.invoke(
      'getStoreValue',
      `videoGroupSwitchStatus_${this.options.planId}`
    )
    return status === undefined ? true : status
  }

  /**
   * 日志上报
   */
  sendLogger(msg, params = {}, level = 'info') {
    logger.send({
      tag: 'rtc',
      level,
      content: {
        msg,
        params
      }
    })
  }
}
