<template>
  <div class="video-item video-remote video-parent-audition">
    <div class="notice">
      {{ $t('classroom.smallClass.videoGroup.localVideoAudition.offline') }}
    </div>
    <div class="name">{{ name }}</div>
  </div>
</template>
<script>
export default {
  props: {
    name: {
      type: String,
      default: ''
    }
  }
}
</script>
<style lang="scss" scoped>
.video-item {
  position: relative;
  width: 148px;
  height: 122px;
  margin-right: 1px;
  background-size: 240px 180px;
  overflow: hidden;
}
.video-parent-audition {
  position: relative;
  .notice {
    line-height: 122px;
    margin: auto;
    font-size: 14px;
    color: #ffffff;
    vertical-align: middle;
    text-align: center;
  }
  .name {
    position: absolute;
    text-align: right;
    bottom: 7px;
    padding: 0 0 0 4px;
    font-size: 14px;
    color: #ffffff;
    line-height: 16px;
    overflow: hidden;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
}
</style>
