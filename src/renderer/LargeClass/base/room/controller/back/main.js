import ControllerBase from '@thinkacademy/live-framework/components/base/room/controller-base'
import { debounce } from '@thinkacademy/live-framework/libs/utils/restrictor'
import { messageCenter } from '@thinkacademy/live-framework/components/framework/message-center.js'

export default class Controller extends ControllerBase {
  constructor(options) {
    super(options)
    this.video = options.video
    this.timeup = options.timeup
    this.loaded = options.loaded
    this.loadState = options.loadState
    this.getState = options.getState
    this.endState = options.endState
    this.playError = options.playError

    if (this.timeup) this.init()
  }

  init() {
    this.initVideoEvents()
  }

  /**
   * @name ControllerForBack#_init
   * @description 初始化回放控制器
   *   */
  initVideoEvents() {
    const delayStateChange = debounce(this.patchVideoStateChange, 1000).bind(this)

    // 可以播放了
    this.loadState(false)
    this.video.addEventListener('canplay', () => {
      this.loadState(true)
    })

    // 加载数据结束
    this.video.addEventListener('loadeddata', () => {
      this.loaded(this.video.duration)
    })

    // 播放
    this.video.addEventListener('play', () => {
      this.getState(false)
      this.endState(false)
    })

    // 暂停
    this.video.addEventListener('pause', () => {
      // 广播播放状态改变
      this.patchVideoStateChange(false)
      this.getState(true)
    })

    // 播放结束
    this.video.addEventListener('ended', () => {
      this.patchVideoStateChange(false)
      this.endState(true)
    })

    // 时间更新
    this.video.addEventListener('timeupdate', () => {
      delayStateChange(false)
      this.timeup(this.video.currentTime, this.video.duration)
    })

    // 加载中
    this.video.addEventListener('waiting', () => {
      this.loadState(false)
    })

    // 时间跳跃
    this.video.addEventListener('seeked', () => {
      console.log('videoplay-seeked')
      this.patchVideoStateChange(sessionStorage.getItem('refreshing') !== 'true')
    })

    // 播放出错
    this.video.addEventListener('error', e => {
      console.log('播放错误', e)
      this.playError(e)
    })
  }

  patchVideoStateChange(isSeeked) {
    messageCenter.emit('onVideoStateChange', {
      isPaused: this.video.paused,
      isEnded: this.video.ended,
      isSeeked,
      currentTime: Math.round(this.video.currentTime),
      speed: this.video.playbackRate
    })
  }
}
