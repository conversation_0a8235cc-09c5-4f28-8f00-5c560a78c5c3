.controller-container {
  width: 100%;
  height: 100%;
  user-select:none;
}
.controller-box {
  width: 100%;
  height: 100%;
  // padding-top: 8px;
  box-sizing: border-box;
  position: relative;
  background: #1A1A1A;
  bottom: 0;
  transition: bottom 0.1s ease-in-out;
  .time-area{
    display: inline-block;
    height: 100%;
    line-height: 54px;
    font-size: 14px;
    font-weight: 500;
    color: #172B4D;
    padding-left: 20px;
    // line-height: 16px;
  }
}
.playtime-box {
  width: calc(100% - 40px);
  height: 3px;
  position: absolute;
  left: 20px;
  top: 5px;
  background: #A2AAB8;
  border-radius: 4px;
  cursor: pointer;
}
.key-point {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  li {
    width: 10px;
    height: 8px;
    background: #fff;
    border-radius: 4px;
    position: absolute;
    top: 0;
  }
}
.playback-box-wrapper {
  height: 42px;
  padding-top: 8px;
}
.playback-box i,
.infrastructure-box i {
  display: inline-block;
  cursor: pointer;
}
.playback-box {
  height: 100%;
  float: left;
  font-size: 0;
  display: flex;
  align-items: center;
  margin-left: 20px;
  .play-icon, .replay-icon {
    background-image: url(../assets/icon_play_normal.png);
    width: 22px;
    height: 22px;
    background-size: cover;
    &:hover {
      background-image: url(../assets/icon_play_hover.png);
    }
  }
  .pause-icon {
    background-image: url(../assets/icon_pause_normal.png);
    width: 22px;
    height: 22px;
    background-size: cover;
    &:hover {
      background-image: url(../assets/icon_pause_hover.png);
    }
  }
  .playtime-item {
    margin-left: 20px;
    font-size: 12px;
    color: #DEE2E7;
    font-weight: 400;
  }
}
.infrastructure-box {
  height: 100%;
  float: right;
  margin-right: 20px;
  font-size: 0;
  display: flex;
  align-items: center;
  i:first-child {
    margin: 0;
  }
  .speed-box {
    position: relative;
    cursor: pointer;
    &.active {
      .speed-list {
        display: block;
      }
      .speed-icon-0-8 {
        background-image: url(../assets/icon_speed_0_8_hover.png);
      }
      .speed-icon-1 {
        background-image: url(../assets/icon_speed_1_hover.png);
      }
      .speed-icon-1-2-5 {
        background-image: url(../assets/icon_speed_1_2_5_hover.png);
      }
      .speed-icon-1-5 {
        background-image: url(../assets/icon_speed_1_5_hover.png);
      }
      .speed-icon-2 {
        background-image: url(../assets/icon_speed_2_hover.png);
      }
    }
  }
  .speed-icon {
    width: 20px;
    height: 20px;
    box-sizing: border-box;
    border-radius: 20px;
    background-size: cover;
    cursor: pointer;
    &.speed-icon-0-8 {
      background-image: url(../assets/icon_speed_0_8_normar.png);
      &:hover {
        background-image: url(../assets/icon_speed_0_8_hover.png);
      }
    }
    &.speed-icon-1 {
      background-image: url(../assets/icon_speed_1_normar.png);
      &:hover {
        background-image: url(../assets/icon_speed_1_hover.png);
      }
    }
    &.speed-icon-1-2-5 {
      background-image: url(../assets/icon_speed_1_2_5_normar.png);
      &:hover {
        background-image: url(../assets/icon_speed_1_2_5_hover.png);
      }
    }
    &.speed-icon-1-5 {
      background-image: url(../assets/icon_speed_1_5_normar.png);
      &:hover {
        background-image: url(../assets/icon_speed_1_5_hover.png);
      }
    }
    &.speed-icon-2 {
      background-image: url(../assets/icon_speed_2_normar.png);
      &:hover {
        background-image: url(../assets/icon_speed_2_hover.png);
      }
    }
  }
  .speed-wrapper {
    position: absolute;
    z-index: 9999;
    left: 50%;
    bottom: 10px;
    transform: translateX(-50%);
  }
  .speed-list {
    padding: 10px;
    margin-bottom: 30px;
    box-sizing: border-box;
    border-radius: 5px;
    background: #fff;
    box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.1);
    li {
      margin-bottom: 8px;
      width: 50px;
      height: 22px;
      line-height: 22px;
      text-align: center;
      font-size: 12px;
      color: #172B4D;
      cursor: pointer;
      &.active {
        color: #FFAA0A;
        background-color: #fff6e6;
        border-radius: 12px;
        &:hover {
          font-weight: normal;
        }
      }
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  .refresh-icon {
    background-image: url(../assets/icon_refresh_normal.png);
    background-size: cover;
    background-color: #F4F6FA;
    border-radius: 50%;
    &:hover {
      background-image: url(../assets/icon_refresh_hover.png);
      background-color: #FFF6E6;
    }
  }
  .volume-box {
    position: relative;
    height: 40px;
    margin-left: 12px;
    background-color: #F4F6FA;
    border-radius: 50%;
    // z-index: 9999;
    i {
      position: relative;
      // z-index: 2;
    }
    &.active {
      .volume-icon {
        background-image: url(../assets/icon_volume_hover.png);
        background-color: #FFF6E6;
        border-radius: 50%;
        background-size: cover;
      }
      .mute-icon {
        background-image: url(../assets/icon_mute_hover.png);
        background-color: #FFF6E6;
        border-radius: 50%;
      }
      .volume-cont {
        display: block;
      }
    }
  }
  .volume-icon {
    background-image: url(../assets/icon_volume_normal.png);
    background-size: cover;
  }
  .mute-icon {
    background-image: url(../assets/icon_mute_normal.png);
    background-size: cover;
  }
  .volume-cont {
    display: none;
    width: 80px;
    height: 190px;
    position: absolute;
    left: 50%;
    bottom: 40px;
    transform: translateX(-50%);
    z-index: 9999;
  }
  .full-icon {
    background-image: url(../assets/icon_fullscreen_normal.png);
    background-size: cover;
    background-color: #F4F6FA;
    border-radius: 50%;
    &:hover {
      background-image: url(../assets/icon_fullscreen_hover.png);
      background-color: #FFF6E6;
    }
  }
  .reduce-icon {
    background-image: url(../assets/icon_reduce_normal.png);
    background-size: cover;
    background-color: #F4F6FA;
    border-radius: 50%;
    &:hover {
      background-image: url(../assets/icon_reduce_hover.png);
      background-color: #FFF6E6;
    }
  }
}

.tips, .mark-tips {
  position: absolute;
  z-index: 1001;
  padding: 12px 16px;
  background-color: #fff;
  font-size: 12px;
  color: #212831;
  // padding: 12 16px;
  top: -53px;
  transform: translateX(-50%);
  border-radius: 5px;
  white-space: nowrap;
  box-shadow:0px 1px 10px 0px rgba(0,0,0,0.1);
}
.playbacklipbox{
  display: inline-block;
  width: 10px;
  height: 4px;
  background: #fff;
  position: absolute;
  top: 0;
  left: 10px;
  border-radius: 2px;
  cursor: pointer;
  i{
    display: none;
  }
  &:hover{
    i{
      display: block;
      position: absolute;
      background-color: #fff;
      font-size: 12px;
      color: #212831;
      padding: 12px 16px;
      top: -59px;
      transform: translateX(-50%);
      border-radius: 10px;
      box-shadow:0px 1px 10px 0px rgba(0,0,0,0.1);
      left:50%;
      white-space: nowrap;
      &::after {
        content: "";
        position: absolute;
        left: 50%;
        top: 40px;
        width: 0;
        height: 0;
        border-width: 5px;
        border-style: solid;
        border-color: #fff transparent transparent transparent;
        transform: translateX(-50%);
        bottom: -10px;
      }
    }
  }
}
