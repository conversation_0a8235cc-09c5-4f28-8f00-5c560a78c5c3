<template>
  <div class="controller-container" ref="controller">
    <div class="controller-box" ref="controllerBox">
      <div class="time-area">
        {{ curTime | formatSeconds }}/{{ (planInfo.etime - planInfo.stime) | formatSeconds }}
      </div>
      <div class="infrastructure-box">
        <RaiseHand :options="options" />
      </div>
    </div>
    <!-- 上报数据 -->
    <ClassInfoDataReport
      :moduleInfo="moduleInfo"
      :planInfo="planInfo"
      :options="options"
    ></ClassInfoDataReport>
  </div>
</template>

<script>
import { formatSeconds } from '../../utils/index'
import RaiseHand from '../components/raiseHand/index.vue'
import ClassInfoDataReport from '../components/classInfoDataReport/index.vue'
export default {
  components: {
    RaiseHand,
    ClassInfoDataReport
  },
  filters: {
    timer(value) {
      if (!value) return '00:00:00'
      value = parseInt(value)
      const h = Math.floor(value / 3600)
      const m = Math.floor((value % 3600) / 60)
      const n = value % 60
      return (h >= 10 ? h : '0' + h) + ':' + (m >= 10 ? m : '0' + m) + ':' + (n >= 10 ? n : '0' + n)
    },
    formatSeconds
  },
  props: {
    options: {
      type: Object,
      default: null
    },
    moduleInfo: {
      type: Object,
      default: null
    },
    planInfo: {
      type: Object,
      default: null
    },
    isPlayBack: {
      type: Number,
      default: 0
    }
  },

  data() {
    return {
      curTime: 0
    }
  },
  mounted() {
    console.log('room-controller-live', this.options, this.moduleInfo, this.planInfo)
    this.init()
  },
  beforeDestroy() {
    clearInterval(this.curTime)
  },
  methods: {
    async init() {
      this.startTime()
    },
    startTime() {
      setInterval(() => {
        this.curTime = new Date().getTime() / 1000 - this.planInfo.stime
      }, 1000)
    }
  }
}
</script>

<style lang="scss" scoped>
@import './middle';
</style>
