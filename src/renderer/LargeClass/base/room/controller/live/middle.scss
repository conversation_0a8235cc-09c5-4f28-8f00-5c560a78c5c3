.controller-container {
  width: 100%;
  height: 100%;
  position: relative;
  user-select: none;
}
.controller-box {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  position: relative;
  background: #1A1A1A;
  bottom: 0;
  transition: bottom 0.1s ease-in-out;
  &.playback {
    padding-top: 8px;
  }
  .time-area{
    display: inline-block;
    height: 100%;
    line-height: 42px;
    font-size: 12px;
    font-weight: 500;
    color: #fff;
    padding-left: 20px;
  }
}
.playtime-box {
  width: 100%;
  height: 4px;
  position: absolute;
  left: 0;
  top: 0;
  background: rgba(0,0,0,.1);
  cursor: pointer;
}
.playtime-bar {
  width: 0;
  height: 100%;
  background-color: #FFAA0A;
}
.key-point {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  li {
    width: 10px;
    height: 8px;
    background: #fff;
    border-radius: 4px;
    position: absolute;
    top: 0;
  }
}
.playtime-btn {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: #FF9718;
  transition: 0.2s;
  box-shadow: 0px 2px 4px 0px rgba(255, 100, 37, 0.48);
  cursor: grab;
  z-index: 10;
  &:hover {
    width: 18px;
    height: 18px;
  }
}
.point-time {
  position: absolute;
  background-color: #fff;
  font-size: 12px;
  color: #212831;
  padding: 12px 16px;
  top: -59px;
  transform: translateX(-50%);
  border-radius: 10px;
  box-shadow:0px 1px 10px 0px rgba(0,0,0,0.1);
  &::after {
    content: "";
    position: absolute;
    left: 50%;
    top: 40px;
    width: 0;
    height: 0;
    border-width: 5px;
    border-style: solid;
    border-color: #fff transparent transparent transparent;
    transform: translateX(-50%);
  }
}
.playback-box i,
.infrastructure-box i {
  display: inline-block;
  width: 40px;
  height: 40px;
  // background-size: 40px 40px;
  background-position: center center;
  cursor: pointer;
  margin-left: 12px;
}
.playback-box {
  height: 100%;
  float: left;
  font-size: 0;
  display: flex;
  align-items: center;
  .left-btn, .right-btn {
    display: inline-block;
    width: 40px;
    height: 40px;
  }
  i {
    margin-left: 20px;
  }
  .play-icon, .replay-icon {
    background-image: url(../assets/icon_play_normal.png);
    width: 40px;
    height: 40px;
    background-size: cover;
    &:hover {
      background-image: url(../assets/icon_play_hover.png);
    }
  }
  .pause-icon {
    margin-left: 20px;
    background-image: url(../assets/icon_pause_normal.png);
    width: 40px;
    height: 40px;
    background-size: cover;
    &:hover {
      background-image: url(../assets/icon_pause_hover.png);
    }
  }
  .playtime-item {
    margin-left: 20px;
    font-size: 12px;
    color: #212831;
    font-weight: 400;
    span {
      color: #5B6169;
    }
  }
}

.infrastructure-box {
  // position: relative;
  height: 100%;
  float: right;
  margin-right: 20px;
  font-size: 12px;
  display: flex;
  align-items: center;
  i:first-child {
    margin: 0;
  }
  .refresh-icon {
    background-image: url(../assets/icon_refresh_normal.png);
    background-size: cover;
    background-color: #F4F6FA;
    border-radius: 50%;
    &:hover {
      background-image: url(../assets/icon_refresh_hover.png);
      background-color: #FFF6E6;
    }
  }
  .volume-box {
    position: relative;
    height: 40px;
    margin-left: 12px;
    background-color: #F4F6FA;
    border-radius: 50%;
    i {
      position: relative;
      // z-index: 2;
    }
    &.active {
      .volume-icon {
        background-image: url(../assets/icon_volume_hover.png);
        background-color: #FFF6E6;
        border-radius: 50%;
        background-size: cover;
      }
      .mute-icon {
        background-image: url(../assets/icon_mute_hover.png);
        background-color: #FFF6E6;
        border-radius: 50%;
      }
      .volume-cont {
        display: block;
      }
    }
  }
  .volume-icon {
    background-image: url(../assets/icon_volume_normal.png);
    background-size: cover;
  }
  .mute-icon {
    background-image: url(../assets/icon_mute_normal.png);
    background-size: cover;
  }
  .volume-cont {
    display: none;
    width: 80px;
    height: 190px;
    position: absolute;
    left: 50%;
    bottom: 40px;
    transform: translateX(-50%);
    z-index: 9999;
  }
  .full-icon {
    background-image: url(../assets/icon_fullscreen_normal.png);
    background-size: cover;
    background-color: #F4F6FA;
    border-radius: 50%;
    &:hover {
      background-image: url(../assets/icon_fullscreen_hover.png);
      background-color: #FFF6E6;
    }
  }
  .reduce-icon {
    background-image: url(../assets/icon_reduce_normal.png);
    background-size: cover;
    background-color: #F4F6FA;
    border-radius: 50%;
    &:hover {
      background-image: url(../assets/icon_reduce_hover.png);
      background-color: #FFF6E6;
    }
  }
}

.tips, .mark-tips {
  position: absolute;
  z-index: 1001;
  padding: 12px 16px;
  background-color: #fff;
  font-size: 12px;
  color: #212831;
  // padding: 12 16px;
  top: -55px;
  transform: translateX(-50%);
  border-radius: 5px;
  white-space: nowrap;
  box-shadow:0px 1px 10px 0px rgba(0,0,0,0.1);
}
.playbacklipbox{
  display: inline-block;
  width: 10px;
  height: 4px;
  background: #fff;
  position: absolute;
  top: 0;
  left: 10px;
  border-radius: 2px;
  cursor: pointer;
  i{
    display: none;
  }
  &:hover{
    i{
      display: block;
      position: absolute;
      background-color: #fff;
      font-size: 12px;
      color: #212831;
      padding: 12px 16px;
      top: -59px;
      transform: translateX(-50%);
      border-radius: 10px;
      box-shadow:0px 1px 10px 0px rgba(0,0,0,0.1);
      left:50%;
      white-space: nowrap;
      &::after {
        content: "";
        position: absolute;
        left: 50%;
        top: 40px;
        width: 0;
        height: 0;
        border-width: 5px;
        border-style: solid;
        border-color: #fff transparent transparent transparent;
        transform: translateX(-50%);
        bottom: -10px;
      }
    }
  }
}
