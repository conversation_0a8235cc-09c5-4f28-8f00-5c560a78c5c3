<template></template>

<script>
import { durationPush } from 'api/classroom/index'
export default {
  props: {
    options: {
      type: Object,
      default: null
    },
    moduleInfo: {
      type: Object,
      default: null
    },
    planInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      // 持续时间戳
      durationTimestamp: new Date().getTime(),
      // 上报定时器
      timer: null,
      // 课件加载状态
      kejianStatus: 1, // 1:离线课件成功、2在线课件成功、3在线加载失败
      // IRC 历史code 出现数量 0 未知网络，1 网络不可用 ，2 服务器连接失败，3 链接中 ，4 已连接 ，5 断开链接
      ircCodeCount: {
        0: 0,
        1: 0,
        2: 0,
        3: 0,
        4: 0,
        5: 0
      },
      ircCurrentCode: 0,
      rtcRoundTripDelayed: 0, //往返延时（单位 ms)
      rtcDownlinkPacketLossRate: 0 //RTC下行丢包率 （单位float，保留两位小数，PS：0.45为 0.45%丢包率）
    }
  },
  mounted() {
    this.initEventListeners()
    this.delayDurationPush()
  },
  computed: {
    cameraStatus() {
      return this.$store.state.largeClass.cameraStatus ? 1 : 2
    }
  },
  methods: {
    // 注册监听事件
    initEventListeners() {
      this.$bus.$on('corewareLoadStatus', data => {
        this.kejianStatus = data.isLocal ? 1 : 2
      })
      this.$bus.$on('ircConnectStatus', data => {
        this.ircCodeCount[data] = this.ircCodeCount[data] + 1
        this.ircCurrentCode = data
      })
      this.$bus.$on('teacherRtcChannelStats', data => {
        // @log-ignore
        // console.log('teacherRtcChannelStats', data)
        this.rtcRoundTripDelayed = data.gatewayRtt
        this.rtcDownlinkPacketLossRate = data.rxPacketLossRate
      })
    },
    // 学生端在线时长上报
    async durationPush() {
      const planId = String(this.planInfo.id)
      // 与后端确认 interval 时间为1分钟
      const interval = 60 * 1000
      // 上报时间间隔
      const duration = 60
      // 重新记录持续时间戳
      this.durationTimestamp = new Date().getTime()

      // console.log(
      //   '上报数据',
      //   planId,
      //   duration,
      //   this.kejianStatus,
      //   this.ircCodeCount,
      //   this.ircCurrentCode,
      //   this.rtcRoundTripDelayed,
      //   this.rtcDownlinkPacketLossRate
      // )
      try {
        await durationPush({
          planId: planId,
          duration: duration,
          isParentAudition: this.options.isParent ? 1 : 0,
          kejianStatus: this.kejianStatus,
          ircCodeCount: this.ircCodeCount,
          ircCurrentCode: this.ircCurrentCode,
          rtcRoundTripDelayed: this.rtcRoundTripDelayed,
          rtcDownlinkPacketLossRate: this.rtcDownlinkPacketLossRate,
          cameraState: this.cameraStatus // 摄像头状态，1 打开，2 关闭 （0927版本新增）
        })
      } catch (e) {
        console.error('上报学生在线时长接口报错', e)
      }

      this.delayDurationPush(interval)
    },
    // 学生端在线时长延迟上报
    delayDurationPush(interval) {
      // 重置上一分钟数据
      this.resetPrevMinuteReportData()
      // 定时执行一次上报
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.durationPush()
      }, interval)
    },
    // 重置上一分钟数据
    resetPrevMinuteReportData() {
      // @log-ignore
      this.ircCodeCount = {
        0: 0,
        1: 0,
        2: 0,
        3: 0,
        4: 0,
        5: 0
      }
      this.kejianStatus = 1
      this.rtcRoundTripDelayed = 0
      this.rtcDownlinkPacketLossRate = 0
      this.ircCurrentCode = 0
    }
  }
}
</script>
