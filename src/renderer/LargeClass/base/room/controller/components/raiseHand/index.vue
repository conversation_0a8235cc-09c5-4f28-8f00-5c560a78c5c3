<template>
  <div class="raiseHand" v-if="classType">
    <div
      class="raiseHand-btn"
      :class="{ disabled: disabled }"
      @click="handleRaiseHand"
      v-if="showRaiseHand"
    >
      <i></i>
      <div class="raiseHand-name">
        {{ $t('classroom.largeClass.raiseHand.buttonName') }}
      </div>
    </div>
    <div class="countdown" v-else>
      <span class="show-time">{{ countdownTime }}s</span>
      <a-progress
        :percent="percent"
        :strokeWidth="20"
        :showInfo="false"
        strokeLinecap="square"
        :strokeColor="{
          from: 'rgba(255, 213, 24, 1)',
          to: 'rgba(255, 170, 10, 1)'
        }"
      />
    </div>
    <div class="raiseHand-tips" v-if="multVideoLinkStatus && !disabled && !fiveEnd"></div>
  </div>
</template>

<script>
import { nativeApi } from 'utils/electronIpc'
import { chatMsgPriority } from '@/LargeClass/base/interaction-handler/interaction-conf'
import { getSchoolCode } from 'utils/local'
import logger from 'utils/logger'
export default {
  props: {
    options: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      showRaiseHand: true,
      countdownTime: 10, // 10秒
      progressTime: 100, // 100秒 (进度条是100ms执行一次，那么总时长就应该是100秒)
      percent: 0,
      disabled: false, // 是否禁用举手按钮
      teacherType: 'tutor', // 举手消息发送老师类型 tutor: 辅导老师 teacher: 主讲老师
      fiveEnd: false,
      cameraStatus: null, // 本地摄像头开启状态
      microphoneStatus: null, // 麦克风开启状态
      multVideoLinkStatus: false // 多人上台互动状态
    }
  },
  computed: {
    // 班级模式
    classType() {
      // 0: 大班 1: 小班
      return this.options.classType
    }
  },
  watch: {
    multVideoLinkStatus: function(newVal) {
      if (newVal) {
        setTimeout(() => {
          this.fiveEnd = true
        }, 5000)
      } else {
        this.fiveEnd = false
      }
    }
  },
  mounted() {
    // 监听多人上台举手状态
    this.$bus.$on('raiseHandForMultVideoLink', status => {
      this.multVideoLinkStatus = status
    })
    // 监听举手按钮禁用状态
    this.$bus.$on('raiseHandDisabled', status => {
      if (status) {
        this.disabled = true
      } else {
        this.disabled = false
      }
    })
    // 更新本地摄像头开启状态
    this.$bus.$on('updateLocalDisplayVideoStatus', status => {
      console.log('raiseHand-updateLocalDisplayVideoStatus', status)
      this.cameraStatus = status
    })
    // 更新本地麦克风开启状态
    this.$bus.$on('updateMicrophoneStatus', status => {
      console.log('raiseHand-updateMicrophoneStatus', status)
      this.microphoneStatus = status
    })
    // 监听上报举手消息
    this.$bus.$on('raiseHandSendMessageToTeacher', ({ type }) => {
      this.sendPeerMessageToTeacher({ type })
    })
  },
  methods: {
    handleRaiseHand() {
      this.showRaiseHand = false
      this.setCountdown() // 这种倒计时
      this.setProgressPercent() // 设置进度条
      this.sendPeerMessage()
      this.sendLogger('学员举手')
    },
    /**
     * 发送举手消息
     */
    sendPeerMessage() {
      this.sendPeerMessageToTour()
      if (this.multVideoLinkStatus) {
        this.sendPeerMessageToTeacher({ type: 125 })
      }
    },
    /**
     * 给辅导老师发送举手消息
     */
    async sendPeerMessageToTour() {
      const schoolCode = await getSchoolCode()
      const deviceInfo = await nativeApi.getDeviceInfo()
      // 向辅导老师发送消息
      const content = {
        type: 160,
        msg: 'raiseHand',
        parameter: {
          schoolCode: schoolCode,
          planId: this.options.planId,
          roomId: this.options.classId,
          studentId: this.options.stuIRCId,
          uid: this.options.stuId,
          teacherId: this.options.teacherInfo.id,
          teacherName: this.options.teacherInfo.name,
          startTime: this.options.stime,
          currenTime: new Date().getTime(),
          device: deviceInfo.platform,
          deviceVersion: deviceInfo.osVersion,
          AppVersion: deviceInfo.appVersion
        }
      }
      window.ChatClient.PeerChatManager.sendPeerMessage(
        [{ nickname: this.options.configs.tutorIrcId }],
        JSON.stringify(content),
        chatMsgPriority.notice
      )
    },
    /**
     * 给主讲老师发送举手消息
     */
    sendPeerMessageToTeacher({ type }) {
      // 向主讲老师发送消息
      const content = {
        type: type || 125,
        status: 6,
        stuId: this.options.stuId,
        cameraIsOpen: this.cameraStatus ? 1 : 2, // 摄像头开启状态
        mikeAvailable: this.microphoneStatus ? 1 : 2 // 麦克风开启状态
      }
      window.ChatClient.PeerChatManager.sendPeerMessage(
        [{ nickname: this.options.configs.teacherIrcId }],
        JSON.stringify(content),
        chatMsgPriority.notice
      )
    },
    // 由于产品需要进度条平滑前进，这里单独提出进度条，进度条100ms前进一次，那么总时长，就应该是100s
    setProgressPercent() {
      if (this.progressTime <= 0) {
        return (this.progressTime = 100)
      } else {
        this.progressTime--
        this.percent = this.calcProgressPercent(100, this.progressTime)
        let progressTimer = setTimeout(() => {
          this.setProgressPercent()
          clearTimeout(progressTimer)
          progressTimer = null
        }, 100)
      }
    },
    // 设置倒计时
    setCountdown() {
      if (this.countdownTime <= 0) {
        this.countdownTime = 10
        this.showRaiseHand = true

        return
      } else {
        this.countdownTime--
        let tiemr = setTimeout(() => {
          this.setCountdown()
          clearTimeout(tiemr)
          tiemr = null
        }, 1000)
      }
    },
    calcProgressPercent(total, completed) {
      total = parseInt(total, 10)
      completed = parseInt(completed, 10)
      if (total === 0 || completed === 0) return 0
      const percentage = ((total - completed) / total) * 100
      return parseFloat(percentage.toFixed(2))
    },
    /**
     * 日志上报
     */
    sendLogger(msg, stage = '') {
      logger.send({
        tag: 'student.Interact',
        content: {
          msg: msg,
          interactType: 'stu_handsup',
          interactId: '',
          interactStage: stage
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.raiseHand {
  position: relative;
  background: rgba(0, 0, 0, 0.85);
  border-radius: 6px;
  height: 30px;
  width: 100px;
  padding: 0 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  &-btn {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    color: #fff;
    font-weight: 500;
    &:hover {
      color: #ffaa0a;
    }
  }
  &-name {
    flex: 1;
    text-align: center;
  }
  &-tips {
    position: absolute;
    top: -68px;
    left: -85px;
    z-index: 100;
    width: 186px;
    height: 60px;
    background: url(../../assets/raisehand-tips.png) no-repeat center center;
    background-size: 100%;
  }
  .countdown {
    display: flex;
    align-items: center;
    margin: 0 -4px;
    width: calc(100% + 8px);
    position: relative;
    .show-time {
      position: absolute;
      top: 50%;
      left: 50%;
      z-index: 1;
      transform: translate(-50%, -50%);
      font-size: 12px;
      font-weight: 500;
      color: #fff;
    }
  }
}
i {
  background: url(../../assets/raisehand.png) no-repeat center center;
  background-size: 100%;
  width: 20px;
  height: 20px;
  display: block;
}
::v-deep .ant-progress-outer {
  display: block;
}
::v-deep .ant-progress-inner {
  border-radius: 3px;
  display: block;
  background-color: transparent;
}
</style>
