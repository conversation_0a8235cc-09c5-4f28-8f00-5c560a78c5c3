import logger,{logTag}from 'utils/logger'
const isFunction = val => toString.call(val) === `[object Function]`
import checkNetwork from '@/utils/checkNetwork.js'
import { ircSensorPush } from '@/utils/sensorEvent'

/**
 * @description 日志上报 系统日志 交互日志 展现日志
 *
 */
export default class LiveLogger {
  constructor(opts) {
    this.initLogger(opts)
    this.liveInfo = {}
    this.ircHaveStartSensor = false // irc是否有开始埋点，如果没有需要补开始埋点
  }
  initLogger(opts) {
    console.log(opts, '初始化参数')
  }
  // 抛出irc消息上报日志
  eventHandler(params) {
    if(params.type=='customirc'){
      if(isFunction(this[params.data.logType])){
        this[params.data.logType](params.data.logData)
      }
    }
  }
  onLiveInfo(res) {
    this.liveInfo = res.liveInfo
  }
  // 调用初始化方法成功失败
  ircinit({code,...res}) {
    ircSensorPush({ result: 'start', liveInfo: this.liveInfo })
    this.ircHaveStartSensor = true
    if(code == 0) {
      this.sendLogger(`irc 调用初始化方法成功`)
    } else {
      this.sendLogger(`irc 调用初始化方法失败,code: ${ code }`, res, 'error')
      ircSensorPush({ result: 'fail', errorType: '初始化失败', code, liveInfo: this.liveInfo })
    }
  }
  // 设置直播信息成功失败
  setLiveInfo({code,...res}) {
    if(code == 0) {
      this.sendLogger(`irc 设置直播信息成功`)
    } else {
      this.sendLogger(`irc 设置直播信息失败,code: ${ code }`, res, 'error')
      ircSensorPush({ result: 'fail', errorType: '设置直播信息失败', code, liveInfo: this.liveInfo })
    }
  }
  // 设置配置信息成功失败
  setSdkProperties({code,...res}) {
    if(code == 0) {
      this.sendLogger(`irc 设置配置信息成功`)
    } else {
      this.sendLogger(`irc 设置配置信息失败,code: ${ code }`, res, 'error')
      ircSensorPush({ result: 'fail', errorType: '设置配置信息失败', code, liveInfo: this.liveInfo })
    }
  }
  // 调用登陆方法成功失败
  loginWithMode({code,...res}) {
    if(code == 0) {
      this.sendLogger(`irc 调用登录接口成功`)
    } else {
      this.sendLogger(`irc 调用登录接口失败,code: ${ code }`, res, 'error')
      ircSensorPush({ result: 'fail', errorType: '调用登录接口失败', code, liveInfo: this.liveInfo })
    }
  }
  // 调度服务状态回调
  onSDKProvisionStatusNotice({code, ...res}) {
    console.log(`[hw_irc_join_room]触发irc调度服务回调${code}`)
    if (!this.ircHaveStartSensor) {
      // 如果没有成对的开始埋点，需要补一个开始，因为调度服务会重试，导致一个开始点对应很多失败点
      ircSensorPush({ result: 'start', liveInfo: this.liveInfo})
    }
    // 走到调度服务，将开始点消费
    this.ircHaveStartSensor = false
    if(code == 0) {
      this.sendLogger(`irc 连接调度服务成功`)
    } else {
      this.sendLogger(`irc 连接调度服务失败,code: ${ code }`, res , 'error')
      ircSensorPush({ result: 'fail', errorType: '调度失败', code, liveInfo: this.liveInfo })
    }
  }
  // 登陆回调成功失败
  onLoginResponse({code,...res}) {
    console.log(`[hw_irc_join_room]触发登陆回调${code}`)
    if (code == 0) {
      this.sendLogger(`irc 登陆回调返回成功`)
    } else {
      this.sendLogger(`irc 登陆回调返回失败,code: ${ code }`, res, 'error')
      ircSensorPush({ result: 'fail', errorType: '登录失败', code: code, liveInfo: this.liveInfo })
    }
  }
  // 调用加入房间成功失败
  joinChatRoomsWithJoinMode({code,...res}) {
    if (code === 0) {
      this.sendLogger(`irc调用加入房间接口成功`)
    } else {
      this.sendLogger(`irc调用加入房间接口失败,code:${code}`, res, 'error')
      ircSensorPush({ result: 'fail', errorType: '调用加入房间接口失败', code, liveInfo: this.liveInfo })
    }
  }
  // 网络状态变更回调
  async onNetStatusChanged({code,...res}) {
    console.log(`[hw_irc_join_room]触发网络状态回调${code}`)
    if (code != 4) {
      // 日志
      const netStatusMap = {
        0: '未知',
        1: '网络不可用',
        2: '服务器连接失败',
        3: '服务器连接中',
        5: '服务器断开连接'
      }
      this.sendLogger(`irc 网络状态改变,${netStatusMap[code]}`, {}, code == 3 ? 'info' : 'error')
      // 神策埋点逻辑
      if (code == 0 || code == 1 || code == 2) {
        // 检测当前是否有网络
        const isOnline = await checkNetwork()
        ircSensorPush({
          result: 'fail',
          errorType: '连接失败',
          code,
          msg: `${isOnline ? '有网络' : '没有网络'}`,
          liveInfo: this.liveInfo
        })
      } else if (code == 3) {
        // 第一次触发改变状态，后续触发上报神策
          ircSensorPush({ result: 'start',liveInfo: this.liveInfo })
          this.ircHaveStartSensor = true
      }
    } else {
      this.sendLogger(`irc 网络状态改变,连接成功`)
    }
  }
  // 加入房间成功失败回调
  onJoinRoomResponse({code,...res}){
    console.log(`[hw_irc_join_room]触发加入房间回调${code}`)
    if (code == 0) {
      this.sendLogger(`irc 加入房间回调成功`)
      ircSensorPush({ result: 'success', liveInfo: this.liveInfo })
    } else {
      this.sendLogger(`irc 加入房间回调失败,code: ${ code }`, res, 'error')
      ircSensorPush({ result: 'fail', errorType: '登录加入房间失败失败', code, liveInfo: this.liveInfo })
    }
  }
  // 发送群聊消息成功失败回调
  onSendRoomMessageResponse({code,...res}){
    if (code == 0) {
      ircSensorPush({
        type: 'message',
        result: 'success',
        msg: '群聊'
      })
    } else {
      ircSensorPush({
        type: 'message',
        result: 'fail',
        errorType: '群聊消息发送失败',
        msg: '群聊',
        code,
        msgInfo: res
      })
      this.sendLogger(`irc 群聊消息发送失败,code: ${ code }`, res, 'error')
    }
  }
  // 发送涂鸦消息成功失败回调
  onSendRoomBinMessageResp({code,...res}){
    if (code == 0) {
      ircSensorPush({
        type: 'message',
        result: 'success',
        msg: '群聊二进制'
      })
    } else {
      ircSensorPush({
        type: 'message',
        result: 'fail',
        errorType: '群聊二进制消息发送失败',
        msg: '群聊二进制',
        code,
        msgInfo: res
      })
      this.sendLogger(`irc 发送二进制涂鸦消息失败,code: ${ code }`, res, 'error')
    }
  }
  // 发送私聊消息成功失败回调
  onSendPeerMessageResponse({code,...res}){
    if (code == 0) {
      ircSensorPush({
        type: 'message',
        result: 'success',
        msg: '私聊'
      })
    } else {
      ircSensorPush({
        type: 'message',
        result: 'fail',
        errorType: '私聊消息发送失败',
        msg: '私聊',
        code,
        msgInfo: res
      })
      this.sendLogger(`irc 发送私聊失败,code: ${ code }`, res , 'error')
    }
  }
  // 被顶掉回调
  onKickoutNotice(res) {
    this.sendLogger(`irc 被其他客户端顶掉:${JSON.stringify(res)}`,{},'error')
  }
  // 接收群聊消息回调
  onRecvRoomMessage(res) {
    this.sendLogger(`irc 收到群聊消息:${JSON.stringify(res)}`)
  }
  // 接收私聊消息回调
  onRecvPeerMessage(res) {
    this.sendLogger(`irc 收到私聊消息:${JSON.stringify(res)}`)
  }
  /**
   *
   * @param {String} msg 日志内容
   * @param {Object} params 日志参数
   * @param {String} level 日志登记
   * 线上日志输出
   */
  sendLogger(msg, params = {}, level = 'info') {
    logger.send({
      tag: logTag.irc,
      level,
      content: {
        msg,
        params
      }
    })
  }

}
