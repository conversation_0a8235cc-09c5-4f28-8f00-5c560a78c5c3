import './web-live.scss'
import qs from 'querystringify'
import Vue from 'vue'
import IniterBase from '@thinkacademy/live-framework/components/base/initer/initer-base'
import { isEnglishCourse } from '@thinkacademy/live-framework/libs/utils/is'
import { messageCenter } from '@thinkacademy/live-framework/components/framework/message-center'
import {
  queryBaseInfo,
  queryInitModule,
  queryShowVodAddress,
  queryPlayMetainfo
} from 'api/classroom/index'
import { queryPlaybackInfo } from 'api/playback/index'
import { getSchoolCode } from 'utils/local'
import { timeBetween, getProtocolByVideoUrl } from 'utils/util'
import { getCameraAccessStatus, getMicrophoneAccessStatus } from 'utils/mediaAccess'
import logger from 'utils/logger'
import { nativeApi } from 'utils/electronIpc'
import { getLocalConfig } from 'locale'
import { enterClassEvent } from 'utils/sensorEvent'
import { getUserInfo } from 'utils/userInfo'
import webVitals from '@/mixins/webVitals.js'
import { setTimeOffset, setPlanId } from 'plugins/sensors'
import { route_href } from 'utils/routeUtil'
let search = window.location.search

export default class WebLive extends IniterBase {
  constructor(options = {}) {
    super(options)
    this.requestParams = {}
    this.initModuleParams = {}
    this.init()
    // 性能指标上报
    webVitals.methods.initWebVitals()
  }

  /**
   * @description
   * 1、控制执行顺序
   * 2、统一Loading/异常页面
   * 3、容器整体布局
   * 4、容器位置管理与布局自适应
   * 5、player的位置
   * 6、chat的位置
   * 7、互动的位置等等
   */
  async init() {
    const beginTime = new Date().getTime()
    // 初始化dom
    const app = await this.initDom()
    this.app = app

    const query = await this.getParams()
    logger.send({
      tag: 'init',
      content: {
        msg: `初始化参数:${JSON.stringify(query)}`
      }
    })
    // 更新roomMessage
    messageCenter.emit('roomMessage', {
      space: 'params',
      data: query
    })

    this.requestParams = {
      bizId: query.bizId,
      planId: query.planId,
      courseId: query.classId,
      isParentAudition: query.isParent
    }
    this.initModuleParams = {
      planId: query.planId, // 讲次ID
      courseId: query.classId, // 课程ID
      isParentAudition: query.isParent //0 - 非家长旁听，1 - 家长旁听
    }

    // 获取getIinfo
    const roomInfo = await this.getInfo(query)
    console.log('getInfo', roomInfo)

    const moduleInfo = await this.initModule()
    if (!moduleInfo) throw Error('initModule接口报错')

    // 结束Loading
    this.hideLoading(beginTime, query.planId)
    if (!roomInfo) {
      logger.send({
        tag: 'init',
        level: 'error',
        content: {
          msg: `显示失败,原因roomInfo为false`,
        }
      })
      this.showError()
      return false
    }

    this.roomInfo = roomInfo

    const isPlayBack = roomInfo.isPlayBack
    logger.send({
      tag: 'userTrack',
      content: {
        msg: `大班-学生进入${ isPlayBack == 1 ? '回放' : '直播' }`,
        tag: 'init'
      }
    })
    // 初始化RtcEngine
    if (!isPlayBack) {
      const token = roomInfo.configs.rtcConfig.token
      await this.initRtcEngine(token)
    }
    
    app.room.style.display = 'block'

    // RTC直播
    if (!isPlayBack) {
      messageCenter.emit('instance', {
        type: 'Player',
        index: 0,
        options: {
          dom: app.video,
          ...roomInfo
        }
      })
    }
    // RTC回放逻辑
    if (isPlayBack) {
      messageCenter.emit('instance', {
        type: 'Player',
        index: 1,
        options: {
          dom: app.video,
          ...roomInfo,
          moduleInfo
        }
      })
    }
    // 更新roomMessage
    messageCenter.emit('roomMessage', {
      space: 'roomInfo',
      data: {
        ...roomInfo,
        interactionController: app.interactionController,
        interactionFullPage: app.interactionFullPage,
        interactionLeft: app.interactionLeft,
        interactionGame: app.interactionGame
      }
    })

    /*
     * 实例化信令服务
     */
    if (!isPlayBack) {
      messageCenter.emit('instance', {
        type: 'SignalService',
        options: {
          ...roomInfo
        }
      })
    }

    if (!isPlayBack) {
      messageCenter.emit('instance', {
        type: 'Chat',
        options: {
          ...roomInfo,
          dom: app.chat
        }
      })
    }

    // 更新roomMessage
    messageCenter.emit('roomMessage', {
      space: 'moduleInfo',
      data: moduleInfo
    })

    messageCenter.emit('instance', {
      type: 'Room',
      // 0直播 1回放
      index: isPlayBack,
      options: {
        videoGroupDom: app.videoGroup,
        controllerDom: app.controller,
        playBackSignDom: app.playBackSign,
        headerDom: app.headerDom,
        roomModulesDom: app.roomModulesDom,
        roomMessage: {
          roomInfo,
          moduleInfo
        }
      }
    })
    // 上报埋点日志
    this.sendLogger({ isPlayBack })
    window.addEventListener('popstate', function(event) {
      console.error('触发浏览器原生回退页面')
    })
  }

  /**
   * @description 获取参数
   *
   */
  async getParams() {
    const query = qs.parse(search)
    for (const key in query) {
      if (/^[0-9]*$/.test(query[key])) {
        query[key] = query[key] * 1
      }
    }
    window._requestHeadersData = {}
    return query
  }

  async initDom() {
    const localConfig = await getLocalConfig()
    const systemErrorConfig = localConfig.classroom.modules.systemError || {}
    const dom = `
      <div class="live-loading" id="liveLoading">
        <div class="loading-wrapper loading-default">
          <div class="loading-contenter">
            <div class="loading-logo"></div>
            <div class="loading-animation"></div>
          </div>
        </div>
      </div>
      <div class="live-error" id="liveError" style="display:none;">
        <div class="notice-wrapper">
          <div class="message">
            <div id="liveErrorMainTitle" class="message-main"></div>
            <p id="liveErrorSubTitle" class="message-sub"></p>
          </div>
          <div class="button-wrapper">
            <a href="javascript:;" class="btn-orange-primary" onclick="window.location.reload();">${systemErrorConfig.refreshButtonName}</a>
            <a href="javascript:;" class="btn-orange" onclick="window.history.go(-2);">${systemErrorConfig.backButtonName}</a>
          </div>
        </div>
      </div>
      <div class="room" id="room" style="display:none;">
        <div class="header" id="header"></div>
        <div class="video" id="video"></div>
        <div class="controller" id="controller"></div>
        <div class="video-group" id="videoGroup"></div>
        <div class="chat" id="chat"></div>
        <div class="room-modules" id="roomModules"></div>
      </div>
      <div class="interaction-controller" id="interactionController"></div>
      <div class="interaction-full-page" id="interactionFullPage"></div>
      <div class="interaction-full-left" id="interactionLeft"></div>
      <div class="interaction-game" id="interactionGame"></div>
      <div class="play-back-sign" id="playBackSign"></div>
    `
    const $ = str => document.querySelector(str)
    $('#live').innerHTML = dom
    return {
      root: $('#live'),
      liveLoading: $('#liveLoading'),
      liveError: $('#liveError'),
      liveErrorMainTitle: $('#liveErrorMainTitle'),
      liveErrorSubTitle: $('#liveErrorSubTitle'),
      room: $('#room'),
      video: $('#video'),
      loadingImg: $('#loading-img'),
      interaction: $('#interaction'),
      chat: $('#chat'),
      videoGroup: $('#videoGroup'),
      controller: $('#controller'),
      interactionController: $('#interactionController'),
      interactionFullPage: $('#interactionFullPage'),
      interactionLeft: $('#interactionLeft'),
      interactionGame: $('#interactionGame'),
      playBackSign: $('#playBackSign'),
      headerDom: $('#header'),
      roomModulesDom: $('#roomModules')
    }
  }

  /**
   * @description 获取初始化信息
   *
   */
  async getInfo(query) {
    const playBackDuration = 30 * 60
    const startDuration = 15 * 60
    let isPlayBack = 0
    let isLiveStart = 0
    let countDown = 0
    let res = await queryBaseInfo(this.requestParams)
    if (!res || res.code != 0) {
      this.errorTitleObj = {
        name: 'courseInformationError',
        code: res?.code || '',
        msg: res?.msg || '',
        requestParams: this.requestParams
      }
      logger.send({
        tag: 'http',
        level: 'error',
        content: {
          msg: `获取basic接口失败`,
          params:this.requestParams,
          res
        }
      })
      return false
    }

    if (res.code === 0) {
      res = res.data
      res.planInfo.etime = res.planInfo.endStampTime
      res.planInfo.stime = res.planInfo.startStampTime
      window.__requestBasicStartTakeClassTime = res.planInfo.startStampTime
      window._requestHeadersData.subjectIds = res.planInfo.subjectIds
      window._requestHeadersData.gradeIds = res.planInfo.gradeIds
      window._requestBasicTime = new Date().getTime()
      window._requestBasicEnterServerTime = res.nowTime
      if (res.nowTime * 1 - res.planInfo.etime * 1 > playBackDuration || query.playback == 1) {
        isPlayBack = 1
      }
      countDown = res.planInfo.stime - res.nowTime
      if (countDown < startDuration) {
        isLiveStart = 1
      }
      // 构建sensor数据
      let sensorData = {
        classId: res.stuLiveInfo.classId,
        planId: res.stuLiveInfo.planId,
        isPlayBack,
        classType: query.subPlatformType,
        isParent: !!query.isParent,
        packageId: res.planInfo.packageId,
        from: query.from,
        lessonType: query.lessonType,
        isStartClass: false
      }
      if (timeBetween(res.planInfo.startStampTime, res.planInfo.endStampTime)) {
        sensorData['isStartClass'] = true
      }
      // 进课埋点
      enterClassEvent(sensorData)
      if (isPlayBack) {
        let playBackRes = await queryPlaybackInfo(this.requestParams)
        if (!playBackRes || playBackRes.code !== 0) {
          this.errorTitleObj = {
            name: 'playbackInformationError',
            code: playBackRes.code,
            msg: playBackRes.msg,
            requestParams: this.requestParams
          }
          logger.send({
            tag: 'http',
            level: 'error',
            content: {
              msg: `无回放信息，显示error`,
              playBackRes,
              requestParams: this.requestParams
            }
          })
          return false
        }
        const goldNum = res.stuInfo.goldNum
        playBackRes.data.stuInfo.goldNum = goldNum
        res = playBackRes.data
      }
    }

    let playback = {}
    let playbackVideoInfo = {}
    if (isPlayBack) {
      // 查询回放地址接口参数
      const showVodAddressParams = {
        appId: res.configs.appId,
        fid: res.configs.fileId,
        teacherId: res.teacherInfo.id,
        planId: this.requestParams.planId,
        bizId: this.requestParams.bizId
      }
      // 查询回放地址
      const showVodAddressRes = await queryShowVodAddress(showVodAddressParams)
      if (!showVodAddressRes || showVodAddressRes.code != 0) {
        this.errorTitleObj = {
          name: 'playbackInterfaceError',
          code: showVodAddressRes?.code || '',
          msg: showVodAddressRes?.msg || '',
          requestParams: showVodAddressParams
        }
        return false
      }
      const vodAddressResult = showVodAddressRes.data.result || []
      if (!vodAddressResult.length) {
        this.errorTitleObj = {
          name: 'playbackSourceIsNull',
          url: '',
          code: '',
          msg: '',
          requestParams: showVodAddressParams
        }
        logger.send({
          tag: 'http',
          level: 'error',
          content: {
            msg: `回放地址列表为空，显示error`,
            vodAddressResult,
            showVodAddressParams
          }
        })
        return false
      }
      // 回放地址数据兼容
      if (vodAddressResult && vodAddressResult.length) {
        for (let i = 0; i <= vodAddressResult.length; i++) {
          // 获取m3u8回放地址
          if (vodAddressResult[i].address.includes('.m3u8')) {
            playbackVideoInfo = {
              address: vodAddressResult[i].address,
              protocol: getProtocolByVideoUrl(vodAddressResult[i].address)
            }
            break
          }
        }
      }

      const playbackParams = {
        playbackStatus: 1,
        ...this.requestParams
      }
      playback = await queryPlayMetainfo(playbackParams)
      if (!playback || playback.code != 0) {
        this.errorTitleObj = {
          name: 'playbackStampError',
          code: playback?.code || '',
          msg: playback?.msg || '',
          requestParams: playbackParams
        }
        logger.send({
          tag: 'http',
          level: 'error',
          content: {
            msg: `无回放打点信息，显示error`,
            playback,
            playbackParams
          }
        })
        return false
      }
      playback = playback.data
    }

    const { bizId, stuCouId } = this.requestParams
    const {
      configs = {},
      planInfo = {},
      stuInfo = {},
      counselorInfo = {},
      teacherInfo = {},
      nowTime,
      stuLiveInfo,
      liveStatus = {}
    } = res

    const fromType = query.fromtype
    // ircSwitch是否启用新irc 0不启用 1启用
    const {
      appId,
      appKey,
      ircRooms,
      fileId: videoFile,
      videoPath,
      stuIrcId,
      mainTeacherVideo,
      counselorTeacherVideo,
      reseeding,
      skinType,
      liveTypeId,
      ircSwitch,
      protocol
    } = configs
    const roomlist = Array.isArray(ircRooms) ? ircRooms : [ircRooms]
    const {
      userName: stuName,
      nickName,
      id: stuId,
      realName,
      goldNum,
      englishName,
      avatar: imgPath,
      gradeId
    } = stuInfo
    console.log('stuInfo', stuInfo)
    const { id: psId, id: password } = stuInfo
    // "mode":当前所在直播流   0辅导流， 1主讲流
    // liveTypeId: 业务id
    const {
      id: planId,
      startTime,
      endTime,
      stime,
      subjectIds,
      gradeIds,
      name: subjectName,
      seriesId,
      bigImageUrl,
      notice,
      visitNum,
      // type,
      mode,
      pattern
    } = planInfo

    const isEnglish = isEnglishCourse(subjectIds)

    if (pattern !== 1 && pattern !== 13 && bizId === 3 && fromType !== 2) {
      // 上报日志
      messageCenter.emit('logger', {
        type: 'sys',
        data: {
          logData: {
            pattern: pattern,
            type: 'Error'
          },
          logType: 'ajax'
        }
      })
    }

    // streamMode：0 课前辅导流 1 主讲 2 课后辅导流
    const { streamMode } = liveStatus

    const { classId, teamId, courseId, isAudition } = stuLiveInfo

    const { id: teacherId, name: teacherName, avatar, spellName } = teacherInfo

    const { event: events, gotoClassTime, streamTime } = playback

    const schoolCode = await getSchoolCode()

    // 播放器配置参数
    const playerOptions = {
      mode,
      appId,
      appKey,
      psId: psId.toString(),
      password: password.toString(),
      stuName,
      stuId,
      planId,
      isPlayBack,
      teacherId,
      // videoFile,
      videoPath,
      bizId,
      events,
      teacherName,
      avatar,
      startTime,
      endTime,
      subjectName,
      seriesId,
      bigImageUrl,
      notice,
      visitNum,
      spellName,
      mainTeacherVideo,
      counselorTeacherVideo,
      stuCouId,
      reseeding,
      classId,
      skinType,
      liveTypeId,
      nickName,
      isDisPath: false, // 不走调度
      videoFile: playbackVideoInfo.address || '', // 视频地址
      protocol: playbackVideoInfo.protocol || '', // 视频协议
      configs: {
        dispatch: {
          '2.0': process.env.VUE_APP_LIVE_CLASS_RTMP_DISPATCH
        },
        log: 'log-live.thethinkacademy.com',
        schoolcode: schoolCode
      }
    }

    const ircServer = configs.ircServer || {}

    // irc配置参数
    const ircInitOptions = {
      appId: appId,
      appKey,
      psId: psId.toString(),
      password: password.toString(),
      nick: stuIrcId,
      planId,
      uid: stuId || teacherId,
      roomlist,
      liveTypeId,
      ircSwitch,
      confService: ircServer.confService || '',
      logService: ircServer.logService || '',
      location: ircServer.location || ''
    }
    // irc配置日志记录
    logger.send({
      tag: 'irc',
      content: {
        msg: `ircConfig:${JSON.stringify(ircInitOptions)}`
      }
    })
    // 聊天配置参数
    const chatOptions = {
      streamMode,
      mode,
      bizId,
      subjectIds,
      gradeIds,
      goldNum,
      realName,
      nickName,
      englishName,
      stuName,
      nick: stuIrcId,
      stuId,
      roomlist,
      isPlayBack,
      planId,
      stime,
      gotoClassTime,
      streamTime,
      psId,
      imgPath,
      classId,
      teamId,
      skinType,
      courseId,
      isEnglish,
      ircSwitch
    }
    const localTime = +new Date()
    const timeOffset = nowTime * 1000 - localTime
    setTimeOffset(timeOffset)
    setPlanId(query, true)
    let classType = 0
    if ([0, 1, 2].includes(query.subPlatformType)) {
      classType = query.subPlatformType
    }
    // 通用的参数
    const commonOption = {
      planInfo,
      mode,
      stuName,
      stuId,
      isPlayBack,
      planId,
      nickName,
      realName,
      englishName,
      bizId,
      nowTime,
      stime,
      stuIRCId: stuIrcId,
      subjectIds,
      gradeIds,
      roomlist,
      goldNum,
      classId,
      teamId,
      stuCouId,
      skinType,
      psId,
      avatar,
      gradeId,
      streamMode,
      isEnglish,
      liveTypeId,
      fromType,
      timeOffset,
      counselorInfo,
      teacherInfo,
      configs,
      classType,
      isAudition: query.isParent === 1 ? true : !!isAudition, //角色为家长时，只能是旁听
      isParent: !!query.isParent
    }
    return {
      isPlayBack,
      isLiveStart,
      countDown,
      playerOptions,
      ircInitOptions,
      chatOptions,
      commonOption,
      ...res,
      bizId,
      liveType: query.type,
      stuInfo
    }
  }

  async initModule() {
    const res = await queryInitModule(this.initModuleParams)
    logger.send({
      tag: 'init',
      content: {
        msg: `获取初始化module${JSON.stringify(res)}`,
      }
    })
    const data = {}
    console.log('initModule', data)
    if (res.code === 0) {
      const arr = res.data.plugins
      for (let i = 0; i < arr.length; i++) {
        data[arr[i].moduleId] = arr[i]
      }
      return data
    }
    return data
  }

  /**
   * 上报埋点日志
   */
  async sendLogger({ isPlayBack }) {
    // 记录埋点
    if (isPlayBack) {
      return
    }
    // 点击课前准备弹窗的join class埋点
    const time = sessionStorage.getItem('click_popup_enter_classroom')
    if (time) {
      sessionStorage.removeItem('click_popup_enter_classroom')
    }
    // 点击课前准备的join class埋点
    const enterRoomData = sessionStorage.getItem('click_enter_classroom')
    if (enterRoomData) {
      sessionStorage.removeItem('click_enter_classroom')
    }

    // 课堂上报设备信息
    const cameraAccessStatus = await getCameraAccessStatus()
    const microphoneAccessStatus = await getMicrophoneAccessStatus()
    const params = {
      camera_isopen: cameraAccessStatus === 'granted' ? 1 : 0,
      microphone_isopen: microphoneAccessStatus === 'granted' ? 1 : 0
    }
    await logger.send({
      tag: 'init',
      content: {
        msg: `学生进入课堂`,
        cameraAccessStatus: params.camera_isopen,
        microphoneAccessStatus: params.microphone_isopen
      }
    })
  }

  /**
   * 隐藏Loading
   */
  hideLoading(beginTime, planId) {
    const endTime = new Date().getTime()
    logger.send({
      tag: 'init',
      content: {
        msg: `结束loading状态`
      }
    })
    this.app.liveLoading.style.display = 'none'
    // 上报加载时长
    // this.$sensors.track('enter_class_loading_duration', {
    //   beginLoadingTime: beginTime,
    //   endLoadingTime: endTime,
    //   duration: endTime - beginTime,
    //   planId: planId,
    //   isPlayback: false
    // })
  }

  /**
   * 显示错误提示
   */
  async showError() {
    const query = await this.getParams()
    const scene = 'Big Class ' + (query.playback == 1 ?  'PlayBack' : 'ClassLiving')
    const {name, code, msg, requestParams } = this.errorTitleObj
    const { uid } = await getUserInfo()
    const localConfig = await getLocalConfig()
    const nameMsg = localConfig.courses.playback[name] || ''
    const codeMsg = code ? `, Code: ${code}` : ''
    const playbackUrlMap = {
      courseInformationError: '/classroom-hub/classroom/student/basic',
      playbackInformationError: '/classroom-hub/playback/student/initEntry',
      playbackInterfaceError: '/classroom-hub/playback/student/showVodAddress',
      playbackStampError: '/classroom-hub/playback/metainfo'
    }
    const url = playbackUrlMap[name] || ''
    const urlMsg = url ? `, Url: ${url}` : ''
    const errMsg = msg ? `, Msg: ${msg}` : ''
    const uidMsg = ', Uid: ' + uid
    const paramsMsg = ', Params: ' + JSON.stringify(requestParams)
    const subMessage = `Scene: ${scene}. ` + nameMsg + codeMsg + errMsg + urlMsg + uidMsg + paramsMsg
    const systemErrorConfig = localConfig.classroom.modules.systemError || {}
   
    this.app.room.style.display = 'none'
    this.app.liveError.style.display = 'flex'
    this.app.liveErrorMainTitle.innerHTML = name === 'playbackInformationError' ? systemErrorConfig.noPlaybackMessage : systemErrorConfig.message
    this.app.liveErrorSubTitle.innerHTML = subMessage
  }

  /**
   * 初始化RtcEngine
   */
  async initRtcEngine(token) {
    const RtcEngine = window.RTC_COMMON.RtcEngine
    const userDataPath = await nativeApi.getPathByName('userData')
    const logsPath = `${userDataPath}/Logs`
    logger.send({
      tag: 'init',
      content: {
        msg: `大班实例化rtc`,
        logsPath,
        token
      }
    })
    // 初始化RTC实例
    const rtcEngine = new RtcEngine(token, {
      logsPath: logsPath
    })
    window.RTC_COMMON.rtcEngine = rtcEngine
    rtcEngine.on('error', (code, message) => {
      logger.send({
        tag: 'rtc错误',
        level: 'error',
        content: {
          msg: `错误码：${code}，错误信息：${message}`
        }
      })
    })
    // 监听rtc超时警告
    rtcEngine.on('warning', (code, message) => {
      logger.send({
        tag: 'rtc警告',
        content: {
          msg: `警告码：${code}，警告信息：${message}`
        },
        level:'warn'
      })
    })
  }
}
