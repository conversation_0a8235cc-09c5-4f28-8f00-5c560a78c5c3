#live {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  font-family: 'Helvetica', 'PingFang SC', 'sans-serif', 'Arial', '<PERSON>erd<PERSON>', 'Microsoft YaHei';
  .live-loading {
    width: 100%;
    height: 100%;
    background: #fff;
    z-index: 1;
    font-size: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .live-error {
    position: relative;
    z-index: 9999;
    width: 100%;
    height: 100%;
    background: #fff;
    font-size: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
    .notice-wrapper {
      position: relative;
      z-index: 9999;
      &::before {
        content: ' ';
        display: block;
        width: 158px;
        height: 158px;
        margin: 0 auto;
        background: url('~assets/images/bg-error-new.png') no-repeat;
        background-size: 100%;
      }
      .message {
        text-align: center;
        max-width: 640px;
        &-main {
          font-size: 18px;
          font-weight: 500;
          color: #172b4d;
          line-height: 21px;
          margin-top: 10px;
        }
        &-sub {
          margin-top: 10px;
          font-size: 12px;
          font-weight: 500;
          color: #a2aab8;
          line-height: 17px;
        }
      }
      .button-wrapper {
        margin-top: 30px;
        text-align: center;
        a {
          cursor: pointer;
          display: inline-block;
          padding: 0 20px;
          min-width: 130px;
          height: 34px;
          line-height: 34px;
          border-radius: 17px;
          font-size: 14px;
          font-weight: 500;
        }
        .btn-orange-primary {
          color: #fff;
          background: linear-gradient(45deg, #FFD518 0%, #FFAA0A 100%);
          margin-right: 40px;
        }
        .btn-orange {
          background: #fff3dc;
          color: #ffaa0a;
        }
      }
    }
  }
  #header {
    height: 44px;
    position: relative;
    z-index: 1000;
  }
  .room {
    position: relative;
    height: 100%;
    background-color: #1a1a1a;
    .video {
      height: calc(100% - 86px);
      .player-container {
        height: 100%;
      }
    }
    .controller {
      height: 42px;
      width: 72.5%;
    }
    .interaction {
      display: none;
      height: 200px;
      background-color: aqua;
    }
    .media-security-access {
      position: fixed;
      top: 25px;
      left: 0;
      right: 0;
      z-index: 800;
      border-radius: 16px;
      height: calc(100% - 86px);
      overflow: hidden;
    }
  }
  .interaction-controller {
    position: absolute;
    height: calc(100% - 86px);
    width: 72.5%;
    // 空出底部控制条高度
    bottom: 42px;
    z-index: 666; // 需要盖过 wait teacher 界面
    overflow: hidden;
    &.index-1000 {
      z-index: 1003;
    }
    &.index-99 {
      z-index: 99;
    }
  }
  .interaction-full-page,
  .interaction-game {
    position: absolute;
    width: 100%;
    height: auto;
    top: 0;
    z-index: 1;
    z-index: 1005;
    cursor:pointer;
    .gameCourseware-container{
      top: 0;
      height: 100%;
    }
  }
  .interaction-full-left {
    height: auto;
    position: absolute;
    bottom: 42px;
    width: 72.5%;
    z-index: 667;
  }
  .rank-list-container {
    position: absolute;
    right: 0;
    top: 0;
    width: 27.5%;
  }

  // Loading样式
  .loading-wrapper {
    overflow: hidden;
    .loading-contenter {
      position: relative;
      margin-left: auto;
      margin-right: auto;
    }
    .loading-logo {
      position: absolute;
      left: 15px;
      top: 15px;
    }
    .loading-animation {
      font-size: 10px;
      position: relative;
      text-indent: -9999em;
      border-top: 3px solid #fddf96;
      border-right: 3px solid #fddf96;
      border-bottom: 3px solid #fddf96;
      border-left: 3px solid #ffc330;
      -webkit-transform: translateZ(0);
      -ms-transform: translateZ(0);
      transform: translateZ(0);
      -webkit-animation: load 1.1s infinite linear;
      animation: load 1.1s infinite linear;
    }
    // 默认尺寸
    &.loading-default {
      .loading-contenter {
        width: 60px;
      }
      .loading-logo {
        width: 30px;
        height: 30px;
        background: url('~assets/images/logo-loading.png') no-repeat;
        background-size: 100%;
      }
      .loading-animation,
      .loading-animation:after {
        border-radius: 50%;
        width: 60px;
        height: 60px;
      }
    }
  }

  .interaction-full-left,
  .interaction-full-page {
    // Notification样式位置重置
    .ant-notification {
      position: fixed;
      width: 400px;
      right: 27.5% !important;
      bottom: 50px !important;
      z-index: 9999;
    }
    // 网络错误消息提示
    .notification-network-error {
      .netError {
        width: 32px;
        height: 32px;
        background: url(~assets/images/icon_nowifi_weak.png) no-repeat center center;
        background-size: 100%;
        display: inline-block;
        position: absolute;
        top: 50%;
        left: 16px;
        transform: translate(0, -50%);
      }
      .netBad {
        width: 32px;
        height: 32px;
        background: url(~assets/images/icon_badwifi_weak.png) no-repeat center center;
        background-size: 100%;
        display: inline-block;
        position: absolute;
        top: 50%;
        left: 16px;
        transform: translate(0, -50%);
      }
      .description {
        padding: 0 60px 0 40px;
      }
      .ant-notification-notice-btn {
        position: absolute;
        float: none;
        top: 50%;
        right: 16px;
        transform: translate(0, -50%);
        margin-top: 0;
      }
      .ant-notification-notice-message,
      .ant-notification-notice-close {
        display: none;
      }
    }
  }
}

@-webkit-keyframes load {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes load {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
