// 渲染进程Sentry配置管理器（直接读取文件）
const remote = require('@electron/remote')
const fs = require('fs')
const path = require('path')

class SentryConfigManager {
  constructor() {
    this.configKey = 'sentryConfig'
    // 配置文件路径
    this.configFilePath = path.join(remote.app.getPath('userData'), 'sentry-config.json')
  }

  // 获取默认配置
  getDefaultConfig() {
    const environment = process.env.VUE_APP_MODE || 'development'
    return {
      enabled: environment !== 'development',
      environment: environment,
      dsn: 'https://<EMAIL>//119',
      sampleRate: environment === 'production' ? 0.8 : 1.0,
      attachStacktrace: false,
      sendDefaultPii: false,
      debug: false,
      maxBreadcrumbs: 100,
      maxValueLength: 250,
      beforeSend: function(event, hint) {
        // 如果是异常事件但级别未定义，设置为error级别
        if (event.exception && event.exception.values && event.exception.values.length > 0) {
          event.level = 'error'
        }

        // 只记录错误和警告级别的事件
        if (event.level !== 'error' && event.level !== 'warning') {
          return null
        }

        // 添加渲染进程标签
        event.tags = {
          ...event.tags,
          process: 'renderer',
          userAgent: navigator.userAgent,
          url: window.location.href
        }

        return event
      },
      beforeBreadcrumb: function(breadcrumb, hint) {
        // 过滤敏感信息和无用的面包屑
        if (breadcrumb.category === 'fetch' || breadcrumb.category === 'xhr') {
          // 移除请求中的敏感信息
          return null
        }

        // 限制控制台日志的面包屑
        if (breadcrumb.category === 'console' && breadcrumb.level !== 'error') {
          return null
        }

        return breadcrumb
      },
      autoSessionTracking: false,
      // 性能监控配置
      tracesSampleRate: 0,
      profilesSampleRate: 0,
      // 回放配置
      replaysSessionSampleRate: 0,
      replaysOnErrorSampleRate: 0
    }
  }

  // 直接读取配置文件
  readConfigFile() {
    try {
      console.log('尝试读取配置文件:', this.configFilePath)

      // 检查文件是否存在
      if (!fs.existsSync(this.configFilePath)) {
        console.log('配置文件不存在')
        return null
      }

      // 读取文件内容
      const fileContent = fs.readFileSync(this.configFilePath, 'utf8')
      console.log('配置文件内容:', fileContent)

      // 解析JSON
      const configData = JSON.parse(fileContent)

      // 返回配置部分
      if (configData && configData.config) {
        console.log('成功读取到配置:', configData.config)
        return configData.config
      }

      console.log('配置文件格式不正确')
      return null
    } catch (error) {
      console.error('读取配置文件失败:', error.message)
      return null
    }
  }

  // 获取配置（直接读取配置文件）
  getConfig() {
    console.log('开始获取Sentry配置...')
    const environment = process.env.VUE_APP_MODE || 'development'

    // 直接读取配置文件
    try {
      console.log('尝试读取本地配置文件...')
      const fileConfig = this.readConfigFile()

      if (fileConfig) {
        console.log('使用文件配置')
        if (environment !== 'development') {
          return { ...this.getDefaultConfig(), ...fileConfig }
        } else {
          return { ...this.getDefaultConfig(), ...fileConfig, enabled: false }
        }
      }

      console.log('配置文件不存在或读取失败，使用默认配置')
      return this.getDefaultConfig()
    } catch (error) {
      console.error('读取配置出错:', error.message)
      console.log('使用默认配置')
      return this.getDefaultConfig()
    }
  }
}

export default new SentryConfigManager()
