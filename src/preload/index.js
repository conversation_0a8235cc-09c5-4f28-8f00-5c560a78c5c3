import { ipc<PERSON>enderer, contextBridge } from 'electron'
const _ipcRenderer = {
  invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args),
  on: (channel, listener) => {
    ipcRenderer.on(channel, listener)
    return _ipc<PERSON>enderer
  },
  send: (channel, ...args) => ipcRenderer.send(channel, ...args),
  removeAllListeners: channel => {
    ipcRenderer.removeAllListeners(channel)
    return _ipcRenderer
  },
  removeListener: (channel, listener) => {
    ipcRenderer.removeListener(channel, listener)
    return _ipc<PERSON>enderer
  }
}

/**
 * 日志服务
 * @param {String} level 日志级别 error、info
 * @param {Any} msg 日志信息
 * @param {String} label 标签或tag, 能区分开是哪的日志就行
 * @returns
 */
const logger = (level = 'info', ...msg) => {
  return ipcRenderer.send('application:write-logger', level, ...msg)
}
const api = {
  ipc: _ipc<PERSON><PERSON>er,
  logger
}
try {
  contextBridge.exposeInMainWorld('thinkApi', api)
} catch {
  window.thinkApi = api
}
