import RtcEngineBase from './RtcEngineBase'
import jwtDecode from 'jwt-decode'
export default class RtcChannel extends RtcEngineBase {
  constructor(rtcEngine, token) {
    super()
    this.rtcChannel = null
    this.rtcEngine = rtcEngine
    this.token = token
    const data = jwtDecode(token)
    this.channel = String(data.room)
    this.init()
  }

  init() {
    this.rtcChannel = this.rtcEngine.createChannel(this.token)
    this.eventHandler()
  }

  /**
   * 事件处理
   */
  eventHandler() {
    // 成功加入频道
    this.rtcChannel.on('joinedChannel', (channel, uid, elapsed) => {
      this.emit('joinedChannel', channel, uid, elapsed)
      this.logger('log', 'joinedChannel', `channel: ${channel}, uid: ${uid}, elapsed: ${elapsed}`)
    })
    // 本地用户加入频道成功
    this.rtcChannel.on('joinChannelSuccess', (uid, elapsed) => {
      this.emit('localJoinChannel', uid, elapsed)
      this.logger('log', 'joinChannelSuccess', `uid: ${uid}, elapsed: ${elapsed}`)
    })
    // 本地用户重连加入频道成功
    this.rtcChannel.on('rejoinChannelSuccess', (uid, elapsed) => {
      this.emit('rejoinChannelSuccess', uid, elapsed)
      this.logger('log', 'rejoinChannelSuccess', `uid: ${uid}, elapsed: ${elapsed}`)
    })
    // 离开频道回调
    this.rtcChannel.on('leaveChannel', () => {
      this.emit('localLeaveChannel')
    })
    // 远端用户加入频道
    this.rtcChannel.on('userJoined', (uid, elapsed) => {
      this.emit('remoteJoinChannel', uid, elapsed)
      this.logger('log', 'userJoined', `channel: ${this.channel}, uid: ${uid}, elapsed: ${elapsed}`)
    })
    // 远端用户离开频道
    this.rtcChannel.on('userOffline', (uid, reason) => {
      this.emit('remoteLeaveChannel', uid, reason)
      this.logger('log', 'userOffline', `channel: ${this.channel}, uid: ${uid}, reason: ${reason}`)
    })
    // 远端用户音频发送变化
    this.rtcChannel.on('remoteAudioStateChanged', (uid, state, reason, elapsed) => {
      this.emit('remoteAudioStateChanged', uid, state, reason, elapsed)
      this.logger(
        'log',
        'remoteAudioStateChanged',
        `channel: ${this.channel}, uid: ${uid}, state: ${state}, reason: ${reason}, elapsed: ${elapsed}`
      )
    })
    // 远端用户视频发送变化
    this.rtcChannel.on('remoteVideoStateChanged', (uid, state, reason, elapsed) => {
      this.emit('remoteVideoStateChanged', uid, state, reason, elapsed)
      this.logger(
        'log',
        'remoteVideoStateChanged',
        `channel: ${this.channel}, uid: ${uid}, state: ${state}, reason: ${reason}, elapsed: ${elapsed}`
      )
    })

    this.rtcChannel.on('rtmpStreamingStateChanged', (url, state, code) => {
      this.emit('rtmpStreamingStateChanged', url, state, code)
      // this.logger(
      //   'log',
      //   'rtmpStreamingStateChanged',
      //   `channel: ${this.channel}, state: ${state}, code: ${code}`
      // )
    })

    // 开启旁路推流的结果回调
    this.rtcChannel.on('streamPublished', (url, error) => {
      this.emit('streamPublished', error, url)
      // this.logger('log', 'streamPublished', `url: ${url}, state: ${error}`)
    })

    // 通话中每个用户的网络上下行 last mile 质量报告回调
    this.rtcChannel.on('networkQuality', (uid, txquality, rxquality) => {
      if (uid === 0) {
        this.emit('localNetworkQuality', txquality, rxquality) // 本地
      } else {
        this.emit('remoteNetworkQuality', txquality, rxquality) // 远端
      }
      this.emit('networkQuality', uid, txquality, rxquality)
    })
    // 网络连接状态已改变回调
    this.rtcChannel.on('connectionStateChanged', (state, reason) => {
      this.emit('connectionStateChanged', state, reason)
      this.logger(
        'log',
        'connectionStateChanged',
        `channel: ${this.channel}, state: ${state}, reason: ${reason}`
      )
    })

    this.rtcChannel.on('rtcStats', stats => {
      // 不需要日志
      this.emit('rtcStats', stats)
    })

    this.rtcChannel.on('channelError', (err, msg) => {
      this.emit('channelError', err, msg)
      this.logger('error', 'channelError: ', `channel: ${this.channel}, err: ${err}, msg: ${msg}`)
    })
    // 频道内用户是否静音
    this.rtcChannel.on('userMuteAudio', (uid, muted) => {
      this.emit('userMuteAudio', uid, muted)
      this.logger('log', 'userMuteAudio: ', `uid: ${uid}, muted: ${muted}`)
    })
    this.rtcChannel.on('streamMessage', (uid, streamId, msg) => {
      this.emit('streamMessage', uid, streamId, msg)
      // this.logger('log', 'streamMessage', `uid: ${uid}, msg: ${msg}`)
    })
    this.rtcChannel.on('streamMessageError', (uid, streamId, code, missed, cached) => {
      this.emit('streamMessageError', uid, streamId, code, missed, cached)
    })
  }

  /**
   * 设置默认视频编码配置
   * @returns res
   */
  setVideoEncoderConfiguration(options = {}) {
    options = Object.assign(
      {
        bitrate: 80,
        frameRate: 10,
        width: 160,
        height: 120
      },
      options
    )
    const state = this.rtcEngine.setVideoEncoderConfiguration(options)
    this.logger(
      'log',
      'setVideoEncoderConfiguration',
      `${JSON.stringify(options)}, state: ${state}`
    )
    return state
  }

  /**
   * 加入频道
   * @returns res
   */
  joinChannel(
    ChannelMediaOptions = {
      autoSubscribeAudio: true,
      autoSubscribeVideo: true,
      publishLocalAudio: true,
      publishLocalVideo: true
    },
    options = {}
  ) {
    this.setVideoEncoderConfiguration(options) // 设置默认视频编码配置
    const state = this.rtcChannel.joinChannel()
    this.rtcChannel.muteLocalAudioStream(!ChannelMediaOptions.publishLocalAudio)
    this.rtcChannel.muteLocalVideoStream(!ChannelMediaOptions.publishLocalVideo)
    this.logger(
      'log',
      'joinChannel',
      `channel: ${this.channel}, state: ${state}}, options: ${JSON.stringify(options)}`
    )
    return state
  }

  /**
   * 退出频道
   * @returns res
   */
  leaveChannel() {
    const state = this.rtcChannel.leaveChannel()
    this.logger('log', 'leaveChannel', `channel: ${this.channel}, res: ${state}`)
    return state
  }

  /**
   * 发布本地音视频流到本频道
   * @returns res
   */
  publish() {
    const state = this.rtcChannel.publish()
    this.logger('log', 'publish', `channel: ${this.channel}, state: ${state}`)
    return state
  }

  /**
   * 停止发布本地音视频流到本频道(v3.4.5 起废弃)
   * @returns res
   */
  //  unpublish() {
  //   const state = this.rtcChannel.unpublish()
  //   this.logger('log', 'unPublish', `channel: ${this.channel}, state: ${state}`)
  //   return state
  // }

  /**
   * 设置直播场景下的用户角色
   * @param role 用户角色 1:主播 2:观众(默认)
   * @returns res
   */
  setClientRole(role) {
    const state = this.rtcChannel.setClientRole(role)
    this.logger('log', 'setClientRole', `channel: ${this.channel}, role: ${role}, state: ${state}`)
    return state
  }
  /**
   * 设置远端视频视图
   * @param uid 用户ID
   * @param view Element
   * @returns res
   */
  setupRemoteVideo(uid, view) {
    const state = this.rtcEngine.setupRemoteVideo(uid, view, this.channel)
    this.logger('log', 'setupRemoteVideo', `channel: ${this.channel}, uid: ${uid}, state: ${state}`)
    return state
  }

  /**
   * 销毁远端视频视图
   * @param uid 用户ID
   * @param view Element
   */
  destroyRemoteVideo(uid, onFailure) {
    this.setupRemoteVideo(uid, null)
    const state = this.rtcEngine.destroyRender(uid, this.channel, onFailure)
    this.logger(
      'log',
      'destroyRemoteVideo',
      `channel: ${this.channel}, uid: ${uid}, state: ${state}`
    )
    return state
  }

  /**
   * 取消或恢复发布本地音频流
   * @param mute 取消/恢复
   * @returns res
   */
  muteLocalAudioStream(mute) {
    const state = this.rtcChannel.muteLocalAudioStream(mute)
    this.logger(
      'log',
      'muteLocalAudioStream',
      `channel: ${this.channel}, mute: ${mute}, state: ${state}`
    )
    return state
  }

  /**
   * 取消或恢复发布本地视频流
   * @param mute 取消/恢复
   * @returns res
   */
  muteLocalVideoStream(mute) {
    const state = this.rtcChannel.muteLocalVideoStream(mute)
    this.logger(
      'log',
      'muteLocalVideoStream',
      `channel: ${this.channel}, mute: ${mute}, state: ${state}`
    )
    return state
  }

  /**
   * 取消或恢复订阅指定远端用户的音频流
   * @param uid 用户ID
   * @param mute 取消订阅/恢复订阅
   * @returns res
   */
  muteRemoteAudioStream(uid, mute) {
    const state = this.rtcChannel.muteRemoteAudioStream(uid, mute)
    this.logger(
      'log',
      'muteRemoteAudioStream',
      `channel: ${this.channel}, uid: ${uid}, mute: ${mute}, state: ${state}`
    )
    return state
  }

  /**
   * 取消或恢复订阅指定远端用户的视频流
   * @param uid 用户ID
   * @param mute 取消订阅/恢复订阅
   * @returns res
   */
  muteRemoteVideoStream(uid, mute) {
    const state = this.rtcChannel.muteRemoteVideoStream(uid, mute)
    this.logger(
      'log',
      'muteRemoteVideoStream',
      `channel: ${this.channel}, uid: ${uid}, mute: ${mute}, state: ${state}`
    )
    return state
  }

  /**
   * 设置是否默认接收音频流
   * @param uid 用户ID
   * @param mute 取消订阅/恢复订阅
   * @returns res
   */
  setDefaultMuteAllRemoteAudioStreams(mute) {
    const state = this.rtcChannel.setDefaultMuteAllRemoteAudioStreams(mute)
    this.logger(
      'log',
      'setDefaultMuteAllRemoteAudioStreams',
      `channel: ${this.channel}, mute: ${mute}, res: ${state}`
    )
    return state
  }

  /**
   * 设置是否默认接收视频流
   * @param uid 用户ID
   * @param mute 取消订阅/恢复订阅
   * @returns res
   */
  setDefaultMuteAllRemoteVideoStreams(mute) {
    const state = this.rtcChannel.setDefaultMuteAllRemoteVideoStreams(mute)
    this.logger(
      'log',
      'setDefaultMuteAllRemoteVideoStreams',
      `channel: ${this.channel}, mute: ${mute}, state: ${state}`
    )
    return state
  }

  /**
   *
   * @param params 字符串 设置音频
   * @returns
   */

  setParameters(params) {
    return this.rtcEngine.setParameters(params)
  }
  /**
   * 取消或恢复订阅远端所有用户的音频流
   * @param mute 取消订阅/恢复订阅
   */
  muteAllRemoteAudioStreams(mute) {
    const state = this.rtcChannel.muteAllRemoteAudioStreams(mute)
    this.logger(
      'log',
      'muteAllRemoteAudioStreams',
      `channel: ${this.channel}, mute: ${mute}, state: ${state}`
    )
    return state
  }

  /**
   * 取消或恢复订阅远端所有用户的视频流
   * @param mute 取消订阅/恢复订阅
   */
  muteAllRemoteVideoStreams(mute) {
    const state = this.rtcChannel.muteAllRemoteVideoStreams(mute)
    this.logger(
      'log',
      'muteAllRemoteVideoStreams',
      `channel: ${this.channel}, mute: ${mute}, state: ${state}`
    )
    return state
  }

  /**
   * 增加旁路推流地址
   * @param url CDN 推流地址
   * @param transcodingEnabled 设置是否转码
   */
  addPublishStreamUrl(url, transcodingEnabled) {
    const state = this.rtcChannel.addPublishStreamUrl(url, transcodingEnabled)
    this.logger(
      'log',
      'addPublishStreamUrl',
      `url: ${url}, transcodingEnabled: ${transcodingEnabled}, state: ${state}`
    )
    return state
  }

  /**
   * 删除旁路推流地址
   * @param url 推流地址
   */
  removePublishStreamUrl(url) {
    const state = this.rtcChannel.removePublishStreamUrl(url)
    this.logger('log', 'removePublishStreamUrl', `state: ${state}`)
    return state
  }

  /**
   * 发送buffer(打点使用)
   * @param streamId 创建的视频流ID
   * @param buffer sei信息
   * @returns
   */
  sendStreamMessageWithArrayBuffer(streamId, buffer) {
    const state = this.rtcChannel.sendStreamMessageWithArrayBuffer(streamId, buffer)
    // this.logger(
    //   'log',
    //   'sendStreamMessageWithArrayBuffer',
    //   `streamId: ${streamId}, buffer:${buffer}, state: ${state}`
    // )
    return state
  }

  /**
   * 打sei时间戳
   * @param seiTime sei信息
   * @returns
   */
  sendSeiTimestamp(seiTime) {
    const state = this.rtcChannel.sendSeiTimestamp(seiTime)
    // this.logger('log', 'sendSeiTimestamp', `seiTime: ${seiTime}, state: ${state}`)
    return state
  }

  /**
   * 发送数据流
   * @param streamId 数据流 ID
   * @param msg 待发送的数据
   */
  //  sendStreamMessage(streamId, msg) {
  //   const state = this.rtcChannel.sendStreamMessage(streamId, msg)
  //   return state
  // }

  /**
   * 创建数据流
   * @param reliable reliable
   * @param ordered ordered
   */
  createDataStream(reliable, ordered) {
    const state = this.rtcChannel.createDataStream(reliable, ordered)
    this.logger('log', 'createDataStream', `state: ${state}`)
    return state
  }

  createDataStreamWithConfig(config) {
    const state = this.rtcChannel.createDataStreamWithConfig(config)
    this.logger('log', 'createDataStreamWithConfig', `state: ${state}`)
    return state
  }
  setLiveTranscoding(transcoding) {
    const state = this.rtcChannel.setLiveTranscoding(transcoding)
    this.logger(
      'log',
      'setLiveTranscoding',
      `transcoding:${JSON.stringify(transcoding)}, state: ${state}, `
    )
    return state
  }
  adjustUserPlaybackSignalVolume(uid, volume = 100) {
    return this.rtcChannel.adjustUserPlaybackSignalVolume(uid, volume)
  }
}
