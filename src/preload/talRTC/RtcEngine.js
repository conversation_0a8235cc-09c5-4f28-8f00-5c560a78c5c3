const TalRtcEngine = require('../../../tal-electron-sdk/lib/sdk/Api/index').default
import jwtDecode from 'jwt-decode'
import RtcEngineBase from './RtcEngineBase'
import RtcChannel from './RtcChannel'

export class RtcEngine extends RtcEngineBase {
  constructor(token, options) {
    super()
    this.tokenData = null
    this.channel = null
    this.appID = null
    this.rtcEngine = null
    const { logsPath } = options || {}
    this.version = '__VERSION__'
    this.token = token
    this.logFile = `${logsPath || './logs'}/agora/agora-sdk.log`
    this.fubeautyLog = `${logsPath || './logs'}/agora/FuBeauty.log`
    this.videoSourceLogFile = `${logsPath || './logs'}/agora/agora-videosource.log`
    this.init()
  }

  /**
   * 初始化
   */
  init() {
    const data = jwtDecode(this.token)
    this.tokenData = data
    this.appID = this.tokenData.attachAppid || this.tokenData.appid
    this.channel = String(this.tokenData.room)
    const rtcEngine = new TalRtcEngine()
    this.rtcEngine = rtcEngine
    rtcEngine.initWithToken(this.token, '')
    rtcEngine.setLogFile(this.logFile)
    rtcEngine.setLogFileSize(5120)
    rtcEngine.setParameters('{"rtc.enable_sole_udp_socket":true}') //
    // rtcEngine.setChannelProfile(1) // 设置频道直播场景
    // rtcEngine.setRenderMode(1) // 使用webgl渲染 1、webgl  2、软件 3、自定义
    rtcEngine.enableAudioVolumeIndication(300)
    // rtcEngine.enableWebSdkInteroperability(true)
    this.getVersion()
    this.eventHandler()
    this.logger(
      'log',
      'sdkInfo',
      `updateTime: ${this.updateTime}, agoraVersion: ${this.sdkVersion} build:${this.build}, JSSDK_Version: v${this.version}, logFile: ${this.logFile}, videoSourceLogFile: ${this.videoSourceLogFile}`
    )
    this.logger('log', `tokenData`, data)
    return rtcEngine
  }

  /**
   * 事件处理
   */
  eventHandler() {
    this.rtcEngine.on('warning', (warn, msg) => {
      this.emit('warning', warn, msg)
    })
    this.rtcEngine.on('error', (err, msg) => {
      this.emit('error', err, msg)
      this.logger('log', 'error', `err: ${err}, msg: ${msg}`)
    })
    this.rtcEngine.on('apiError', (funName, msg) => {
      this.logger('error', 'apiError', `funName: ${funName}, msg: ${msg}`)
    })
    // 成功加入频道
    this.rtcEngine.on('joinedChannel', (channel, uid, elapsed) => {
      this.emit('joinedChannel', channel, uid, elapsed)
      this.logger('log', 'joinedChannel', `channel: ${channel}, uid: ${uid}, elapsed: ${elapsed}`)
    })

    this.rtcEngine.on('groupAudioVolumeIndication', (uid, volume) => {
      // this.emit('groupAudioVolumeIndication', uid, volume)
    })
    this.rtcEngine.on('onAudioVolumes', (speakers, speakerNumber, totalVolume) => {
      this.emit('groupAudioVolumeIndication', speakers, speakerNumber, totalVolume)
    })

    // 音频设备状态已改变回调
    this.rtcEngine.on('audioDeviceStateChanged', (deviceId, deviceType, deviceState) => {
      this.emit('audioDeviceStateChanged', deviceId, deviceType, deviceState)
      this.logger(
        'log',
        'audioDeviceStateChanged',
        `deviceId: ${deviceId}, deviceType: ${deviceType}, deviceState: ${deviceState}`
      )
    })
    // 视频设备变化回调
    this.rtcEngine.on('videoDeviceStateChanged', (deviceId, deviceType, deviceState) => {
      this.emit('videoDeviceStateChanged', deviceId, deviceType, deviceState)
      this.logger(
        'log',
        'videoDeviceStateChanged',
        `deviceId: ${deviceId}, deviceType: ${deviceType}, deviceState: ${deviceState}`
      )
    })

    // 本地视频状态发送变化
    this.rtcEngine.on('localVideoStateChanged', (state, error) => {
      this.emit('localVideoStateChanged', state, error)
      this.logger('log', 'localVideoStateChanged', `state: ${state}, error: ${error}`)
    })
    // 本地音频状态发送变化
    this.rtcEngine.on('localAudioStateChanged', (state, error) => {
      this.emit('localAudioStateChanged', state, error)
      this.logger('log', 'localAudioStateChanged', `state: ${state}, error: ${error}`)
    })

    this.rtcEngine.on('userMuteVideo', (uid, muted) => {
      this.emit('userMuteVideo', uid, muted)
      this.logger('log', 'userMuteVideo: ', `uid: ${uid}, muted: ${muted}`)
    })

    // 屏幕共享对象成功加入频道回调
    this.rtcEngine.on('videoSourceJoinedSuccess', uid => {
      this.emit('videoSourceJoinedSuccess', uid)
      this.logger('log', 'videoSourceJoinedSuccess', `uid: ${uid}`)
    })
    // 屏幕共享对象离开频道回调
    this.rtcEngine.on('videoSourceLeaveChannel', () => {
      this.emit('videoSourceLeaveChannel')
      this.logger('log', 'videoSourceLeaveChannel')
    })
    // 接收到对方数据流消息的回调
    this.rtcEngine.on('streamMessage', (uid, streamId, msg, len) => {
      this.emit('streamMessage', uid, streamId, msg, len)
      // this.logger('log', 'streamMessage', `uid: ${uid}, msg: ${msg}, len: ${len}`)
    })
    this.rtcEngine.on('streamMessageError', (uid, streamId, code, missed, cached) => {
      this.emit('streamMessageError', uid, streamId, code, missed, cached)
    })
    this.rtcEngine.on('lastMileQuality', quality => {
      this.emit('lastMileQuality', quality)
    })
    this.rtcEngine.on('onBeautyExtensionEvent', (code, msg) => {
      this.emit('onBeautyExtensionEvent', code, msg)
      this.logger('log', 'onBeautyExtensionEvent', `code:${code}, msg:${msg}`)
    })
  }
  getVersion() {
    const { version, build } = this.rtcEngine.getVersion()
    this.sdkVersion = version
    this.build = build
    return version
  }
  /**
   * 创建频道实例
   * @param token Token
   * @param channel 频道名称
   * @returns 频道实例
   */
  createChannel(token = this.token) {
    return new RtcChannel(this.rtcEngine, token)
  }
  /**
   *
   *
   *
   */
  enableBeautyExtension({ licenseURL, licenseKey, resDir }) {
    this.rtcEngine.enableBeautyExtension(true, {
      logPath: this.fubeautyLog,
      vendorId: 2,
      licenseURL,
      licenseKey,
      resDir
    })
    this.logger(
      'log',
      'enableBeautyExtension',
      `licenseURL: ${licenseURL}, licenseKey: ${licenseKey},resDir:${resDir}`
    )
  }
  destroyBeautyExtension() {
    this.rtcEngine.enableBeautyExtension(false, {
      vendorId: 2,
      licenseURL: 'test',
      licenseKey: 'test',
      resDir: 'test'
    })
  }
  setBeautyExtensionOptions(resPath, category = 0) {
    this.rtcEngine.setBeautyExtensionOptions({
      category,
      resPath
    })
    this.logger('log', 'setBeautyExtensionOptions', `resPath: ${resPath}, category: ${category}`)
  }

  /**
   *
   * @param params 字符串 设置音频
   * @returns
   */
  setParameters(params) {
    return this.rtcEngine.setParameters(params)
  }
  /**
   * 设置本地视频视图
   * @param view Element
   * @returns res
   */
  setupLocalVideo(view) {
    const state = this.rtcEngine.setupLocalVideo(view)
    this.logger('log', 'setupLocalVideo', `state: ${state}`)
    return state
  }

  /**
   * 销毁本地视频视图
   */
  destroyLocalVideo() {
    const state = this.rtcEngine.destroyRender('local', this.channel)
    this.logger('log', 'destroyLocalVideo', `state: ${state}`)
    return state
  }

  /**
   * 设置远端视频视图
   * @param uid 用户ID
   * @param view Element
   * @returns res
   */
  setupRemoteVideo(uid, view) {
    const state = this.rtcEngine.setupRemoteVideo(uid, view, this.channel)
    this.logger('log', 'setupRemoteVideo', `channel: ${this.channel}, uid: ${uid}, state: ${state}`)
    return state
  }

  /**
   * 销毁远端视频视图
   * @param uid 用户ID
   * @param view Element
   */
  destroyRemoteVideo(uid, onFailure) {
    const state = this.rtcEngine.destroyRender(uid, this.channel, onFailure)
    this.logger('log', 'destroyRemoteVideo', `channel: ${this.channel}, uid: ${uid}`)
    return state
  }

  /**
   * 关闭视频模块
   */
  disableVideo() {
    const state = this.rtcEngine.disableVideo()
    this.logger('log', 'disableVideo', `state: ${state}`)
    return state
  }

  /**
   * 启用视频模块
   */
  enableVideo() {
    const state = this.rtcEngine.enableVideo()
    this.logger('log', 'enableVideo', `state: ${state}`)
    return state
  }

  /**
   * 关闭音频模块
   * @returns
   */
  disableAudio() {
    const state = this.rtcEngine.disableAudio()
    this.logger('log', 'disableAudio', `state: ${state}`)
    return state
  }

  /**
   * 启用音频模块
   * @returns
   */
  enableAudio() {
    const state = this.rtcEngine.enableAudio()
    this.logger('log', 'enableAudio', `state: ${state}`)
    return state
  }
  /**
   * 开/关本地视频采集
   * @param enable 开关
   */
  enableLocalVideo(enable) {
    const state = this.rtcEngine.enableLocalVideo(enable)
    this.logger('log', 'enableLocalVideo', `enable: ${enable} state: ${state}`)
    return state
  }
  enableLocalAudio(enable) {
    const state = this.rtcEngine.enableLocalAudio(enable)
    this.logger('log', 'enableLocalAudio', `enable: ${enable} state: ${state}`)
    return state
  }

  /**
   * 设置视频设备
   * @param deviceId 设备ID
   */
  setVideoDevice(deviceId) {
    const state = this.rtcEngine.setVideoDevice(deviceId)
    this.logger('log', 'setVideoDevice', `deviceId: ${deviceId} state: ${state}`)
    return state
  }

  /**
   * 获取视频设备列表
   * @returns 设备列表
   */
  getVideoDevices() {
    const devices = this.rtcEngine.getVideoDevices()
    this.logger('log', 'getVideoDevices', `devices: ${JSON.stringify(devices)}`)
    return devices
  }

  /**
   * 获取当前的视频设备
   * @returns 设备ID
   */
  getCurrentVideoDevice() {
    let deviceId = this.rtcEngine.getCurrentVideoDevice()
    if (deviceId == 'AgoraCapInvalid') {
      this.logger('log', 'getCurrentVideoDevice', `AgoraCapInvalid`)
      return ''
    }
    this.logger('log', 'getCurrentVideoDevice', `deviceId: ${deviceId}`)
    return deviceId
  }

  /**
   * 设置音频采集设备
   * @param deviceId 设备ID
   */
  setAudioRecordingDevice(deviceId) {
    const state = this.rtcEngine.setAudioRecordingDevice(deviceId)
    this.logger('log', 'setAudioRecordingDevice', `deviceId: ${deviceId} state: ${state}`)
    return state
  }

  /**
   * 获取音频采集设备列表
   * @returns 设备列表
   */
  getAudioRecordingDevices() {
    const devices = this.rtcEngine.getAudioRecordingDevices()
    this.logger('log', 'getAudioRecordingDevices', `devices: ${JSON.stringify(devices)}`)
    return devices
  }

  /**
   * 获取当前的音频采集设备
   * @returns 设备ID
   */
  getCurrentAudioRecordingDevice() {
    const deviceId = this.rtcEngine.getCurrentAudioRecordingDevice()
    this.logger('log', 'getCurrentAudioRecordingDevice', `deviceId: ${deviceId}`)
    return deviceId
  }
  getDefaultAudioRecordingDevices() {
    const deviceInfo = this.rtcEngine.getDefaultAudioRecordingDevices()
    this.logger('log', 'getDefaultAudioRecordingDevices', `deviceId: ${deviceInfo.deviceid}`)
    return deviceInfo
  }
  /**
   * 设置音频播放设备设备
   * @param deviceId 设备ID
   */
  setAudioPlaybackDevice(deviceId) {
    const state = this.rtcEngine.setAudioPlaybackDevice(deviceId)
    this.logger('log', 'setAudioPlaybackDevice', `deviceId: ${deviceId} state: ${state}`)
    return state
  }

  /**
   * 获取音频播放设备列表
   * @returns 设备列表
   */
  getAudioPlaybackDevices() {
    const devices = this.rtcEngine.getAudioPlaybackDevices()
    this.logger('log', 'getAudioPlaybackDevices', `devices: ${JSON.stringify(devices)}`)
    return devices
  }

  /**
   * 获取当前的音频播放设备
   * @returns 设备ID
   */
  getCurrentAudioPlaybackDevice() {
    const deviceId = this.rtcEngine.getCurrentAudioPlaybackDevice()
    this.logger('log', 'getCurrentAudioRecordingDevice', `deviceId: ${deviceId}`)
    return deviceId
  }
  getDefaultAudioPlaybackDevices() {
    const deviceInfo = this.rtcEngine.getDefaultAudioPlaybackDevices()
    this.logger('log', 'getDefaultAudioPlaybackDevices', `deviceId: ${deviceInfo.deviceid}`)
    return deviceInfo
  }
  /**
   * 是否开启采集声卡音频
   * @param enable
   * @param deviceName
   * @returns
   */
  enableLoopbackRecording(enable = false, deviceName = null) {
    const state = this.rtcEngine.enableLoopbackRecording(enable, deviceName)
    this.logger('log', 'enableLoopbackRecording', `enable: ${enable}, deviceName:${deviceName}`)
    return state
  }

  /**
   * 设置视窗内容显示模式
   * @param uid | "local" | "videosource"
   * @param mode: 0 | 1
   * @param channelId
   * @returns
   */
  setupViewContentMode(uid, mode, channelId) {
    const state = this.rtcEngine.setupViewContentMode(uid, mode, channelId)
    this.logger('log', 'setupViewContentMode', `uid: ${uid}, mode:${mode}, channelId: ${channelId}`)
    return state
  }

  /**
   * 发送buffer(打点使用)
   * @param streamId 创建的视频流ID
   * @param buffer sei信息
   * @returns
   */
  sendStreamMessageWithArrayBuffer(streamId, buffer) {
    const state = this.rtcEngine.sendStreamMessageWithArrayBuffer(streamId, buffer)
    // this.logger(
    //   'log',
    //   'sendStreamMessageWithArrayBuffer',
    //   `streamId: ${streamId}, buffer:${buffer}, state: ${state}`
    // )
    return state
  }

  /**
   * 发送数据流
   * @param streamId 数据流 ID
   * @param msg 待发送的数据
   */
  //  sendStreamMessage(streamId, msg) {
  //   const state = this.rtcEngine.sendStreamMessage(streamId, msg)
  //   // this.logger('log', 'sendStreamMessage	', `streamId: ${streamId}, msg:${msg}`)
  //   return state
  // }

  /**
   * 创建数据流
   * @param reliable reliable
   * @param ordered ordered
   */
  createDataStream(reliable, ordered) {
    const state = this.rtcEngine.createDataStream(reliable, ordered)
    this.logger('log', 'createDataStream', `state: ${state}`)
    return state
  }

  /**
   * 增加旁路推流地址
   * @param url CDN 推流地址
   * @param transcodingEnabled 设置是否转码
   */
  addPublishStreamUrl(url, transcodingEnabled) {
    const state = this.rtcEngine.addPublishStreamUrl(url, transcodingEnabled)
    this.logger('log', 'addPublishStreamUrl', `state: ${state}`)
    return state
  }

  /**
   * 删除旁路推流地址
   * @param url 推流地址
   */
  removePublishStreamUrl(url) {
    const state = this.rtcEngine.removePublishStreamUrl(url)
    this.logger('log', 'removePublishStreamUrl', `state: ${state}`)
    return state
  }

  setLiveTranscoding(transcoding) {
    const state = this.rtcEngine.setLiveTranscoding(transcoding)
    this.logger(
      'log',
      'setLiveTranscoding',
      `state: ${state}, transcoding:${JSON.stringify(transcoding)}`
    )
    return state
  }
  /**
   * key: 'local' | 'videosource' | number, channelId:string | undefined
   * @returns
   */
  resizeRender(key, channelId) {
    channelId = channelId || this.channel
    const state = this.rtcEngine.resizeRender(key, channelId)
    this.logger('log', 'resizeRender', `state: ${state}，key:${key},channelId:${channelId}`)
    return
  }

  release(sync) {
    if (!this.rtcEngine) {
      return
    }
    const state = this.rtcEngine.release(sync)
    this.rtcEngine = null
    this.logger('log', 'release', `state: ${state}`)
    return state
  }
  /******************************************************
   *  屏幕共享
   ******************************************************/
  /**
   * 初始化屏幕共享
   * @param rtcEngine
   * @param token
   */
  videoSourceInit(token) {
    if (!token) {
      this.logger('error', 'videoSourceInit', `miss token`)
      throw new Error('miss token')
    }
    this.rtcEngine.videoSourceInitialize(token) // 初始化 videoSource 对象
    // this.rtcEngine.videoSourceSetChannelProfile(1) // 设置 videoSource 的频道场景 0：通信场景（默认） 1：直播场景 2：游戏模式
    this.rtcEngine.videoSourceSetLogFile(this.videoSourceLogFile) // 设置屏幕共享对象的日志
  }

  /**
   * 初始化 videoSource 对象
   * @param token
   * @returns
   */
  videoSourceInitialize(token) {
    if (!token) {
      this.logger('error', 'videoSourceInitialize', `miss token`)
      throw new Error('miss token')
    }
    this.logger('log', 'videoSourceInitialize  ', `token: ${token}`)
    return this.rtcEngine.videoSourceInitialize(token)
  }

  /**
   * 加入频道
   * @param token
   * @returns
   */
  videoSourceJoin() {
    this.logger('log', 'videoSourceJoin')
    return this.rtcEngine.videoSourceJoin()
  }
  /**
   * 控制声音大小
   */
  adjustUserPlaybackSignalVolume(uid, volume) {
    this.logger('log', 'adjustUserPlaybackSignalVolume', `uid: ${uid} volume: ${volume}`)
    return this.rtcEngine.adjustUserPlaybackSignalVolume(uid, volume)
  }
  adjustPlaybackSignalVolume(volume) {
    this.logger('log', 'adjustPlaybackSignalVolume', `volume: ${volume}`)
    return this.rtcEngine.adjustPlaybackSignalVolume(volume)
  }

  /**
   * videoSource 离开频道
   * @returns
   */
  videoSourceLeave() {
    return this.rtcEngine.videoSourceLeave()
  }

  /**
   * 启用音频模块（默认为关闭状态）
   * @returns
   */
  videoSourceEnableAudio() {
    return this.rtcEngine.videoSourceEnableAudio()
  }

  /**
   * 释放 videoSource 对象
   * @returns
   */
  videoSourceRelease() {
    const state = this.rtcEngine.videoSourceRelease()
    this.logger('log', 'videoSourceRelease', `state: ${state}`)
    return state
  }

  /**
   * 设置 videoSource 的渲染器
   * @param view
   * @returns
   */
  setupLocalVideoSource(view) {
    const state = this.rtcEngine.setupLocalVideoSource(view)
    this.logger('log', 'setupLocalVideoSource', `state: ${state}`)
    return state
  }

  /**
   * 设置摄像头流的编码配置
   * @param profile
   * @param swapWidthAndHeight
   * @returns
   */
  videoSourceSetVideoProfile(profile, swapWidthAndHeight = false) {
    const state = this.rtcEngine.videoSourceSetVideoProfile(profile, swapWidthAndHeight)
    this.logger(
      'log',
      'videoSourceSetVideoProfile',
      `profile: ${profile}, swapWidthAndHeight: ${swapWidthAndHeight}, state: ${state}`
    )
    return state
  }

  /**
   * 设置频道场景
   * @param profile
   * @returns
   */
  videoSourceSetChannelProfile(profile) {
    const state = this.rtcEngine.videoSourceSetChannelProfile(profile)
    this.logger('log', 'videoSourceSetChannelProfile', `profile: ${profile}, state: ${state}`)
    return state
  }

  /**
   * 获取窗口信息
   * @param options
   * @returns
   */
  getScreenWindowsInfo(callback) {
    return this.rtcEngine.getScreenWindowsInfo(callback)
  }

  /**
   * 获取屏幕信息
   * @returns
   */
  getScreenDisplaysInfo(callback) {
    return this.rtcEngine.getScreenDisplaysInfo(callback)
  }

  /**
   * 开启预览共享屏幕
   * @returns
   */
  startScreenCapturePreview() {
    return this.rtcEngine.startScreenCapturePreview()
  }

  /**
   * 停止预览共享屏幕
   * @returns
   */
  stopScreenCapturePreview() {
    return this.rtcEngine.stopScreenCapturePreview()
  }

  /**
   * 通过窗口 ID 共享窗口
   * @param windowSymbol
   * @param rect
   * @param param
   * @returns
   */
  startScreenCaptureByWindow(windowSymbol, rect, param) {
    if (!windowSymbol) {
      throw new Error('miss windowSymbol')
    }
    return this.rtcEngine.startScreenCaptureByWindow(windowSymbol, rect, param)
  }

  /**
   * 通过指定区域共享屏幕
   * @param screenSymbol
   * @param rect
   * @param param
   * @returns
   */
  startScreenCaptureByScreen(screenSymbol, rect, param) {
    if (!screenSymbol) {
      throw new Error('miss screenSymbol')
    }
    return this.rtcEngine.startScreenCaptureByScreen(screenSymbol, rect, param)
  }

  /**
   * 停止共享屏幕
   * @returns
   */
  stopScreenCapture() {
    return this.rtcEngine.stopScreenCapture()
  }
  stopScreenCapture2() {
    return this.rtcEngine.stopScreenCapture2()
  }

  /**
   * 更新屏幕共享的编码参数配置
   * @param param
   * @returns
   */
  updateScreenCaptureParameters(param) {
    return this.rtcEngine.updateScreenCaptureParameters(param)
  }

  /**
   * 设置屏幕共享内容类型
   * @param hint
   * @returns
   */
  setScreenCaptureContentHint(hint) {
    return this.rtcEngine.setScreenCaptureContentHint(hint)
  }

  /**
   * 对屏幕共享流开启双流模式
   * @param enable
   * @returns
   */
  videoSourceEnableDualStreamMode(enable) {
    return this.rtcEngine.videoSourceEnableDualStreamMode(enable)
  }

  /**
   * 通过 JSON 配置 SDK 提供技术预览或特别定制功能
   * @param parameter
   * @returns
   */
  videoSourceSetParameters(parameter) {
    return this.rtcEngine.videoSourceSetParameter(parameter)
  }

  /**
   * 更新共享区域
   * @param rect
   * @returns
   */
  videoSourceUpdateScreenCaptureRegion(rect) {
    return this.rtcEngine.videoSourceUpdateScreenCaptureRegion(rect)
  }

  /**
   * 屏幕共享开启声卡采集音频
   * @param enabled
   * @returns
   */
  videoSourceEnableLoopbackRecording(enabled = true) {
    return this.rtcEngine.videoSourceEnableLoopbackRecording(enabled)
  }

  /**
   * 通过屏幕信息共享屏幕
   * @param screenSymbol
   * @param rect
   * @param param
   * @returns
   */
  videoSourceStartScreenCaptureByScreen(screenSymbol, rect, param) {
    if (!screenSymbol) {
      throw new Error('miss screenSymbol')
    }
    return this.rtcEngine.videoSourceStartScreenCaptureByScreen(screenSymbol, rect, param)
  }
  videoSourceStartScreenCaptureByDisplayId(displayId, rect, param) {
    if (!displayId) {
      throw new Error('miss displayId')
    }
    return this.rtcEngine.videoSourceStartScreenCaptureByDisplayId(displayId, rect, param)
  }
  /**
   * 通过窗口信息共享屏幕
   * @param windowSymbol
   * @param rect
   * @param param
   * @returns
   */
  videoSourceStartScreenCaptureByWindow(windowSymbol, rect, param) {
    if (!windowSymbol) {
      throw new Error('miss screenSymbol')
    }
    return this.rtcEngine.videoSourceStartScreenCaptureByWindow(windowSymbol, rect, param)
  }

  /**
   * 更新共享屏幕的编码配置
   * @param param
   * @returns
   */
  videoSourceUpdateScreenCaptureParameters(param) {
    return this.rtcEngine.videosourceUpdateScreenCaptureParameters(param)
  }

  /**
   * 设置共享屏幕的内容类型
   * @param hint
   * @returns
   */
  videoSourceSetScreenCaptureContentHint(hint) {
    return this.rtcEngine.videosourceSetScreenCaptureContentHint(hint)
  }

  subscribe(uid, view) {
    return this.rtcEngine.subscribe(uid, view)
  }

  /**
   * 开启网络测速
   * @returns
   */
  enableLastmileTest() {
    return this.rtcEngine.enableLastmileTest()
  }
  /**
   * 关闭网络测速
   * @returns
   */
  disableLastmileTest() {
    return this.rtcEngine.disableLastmileTest()
  }
  /**
   * 设置美颜
   * @returns
   */
  setBeautyEffectOptions(enable = false, options = {}) {
    return this.rtcEngine.setBeautyEffectOptions(enable, options)
  }
}
